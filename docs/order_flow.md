```mermaid
flowchart TD
    A(発注数入力ボタンを押す) --> B{生鮮商品?}
    B -->|Yes| C["生鮮商品は発注できません"ダイアログ表示]
    B -->|No| D{発注推奨数可能?}
    D -->|No| E["本部より商品手配を行います（手発注不可）。ご不明な点はエリアSVへ。"ダイアログ表示]
    D -->|Yes| F{発注停止商品?}
    F -->|Yes| G["この商品は発注停止されています。発注できません"ダイアログ表示]

    ave -->|No| H[発注数入力ダイアログ]
    H --> I[発注数入力]
    I --> I2{インライン検証 *1}
    I2 --> |Fail| I
    I2 --> |Pass| J[発注確定ボタン有効化]
    J -->  J2[発注確定ボタン押下]
    J2 --> k{値の検証　*2}
    k --> |値が大きすぎる or 2週間売れ数0|k2{発注しますか.発注数を再入力してください}
    k --> |値が適正|k3{発注しますか}

    k2 --> |入力　確定| z
    k3 --> |確定| z
    k2 --> |いいえ| I
    k3 --> |いいえ| I



    z(発注数更新)

```

# \*1 インライン検証

発注数入力フィールドのバリデーション
全てパスした場合のみ確定ボタンが有効になる

- 空白は無効
- 0 は常に有効
- 数値以外は無効
- 0 始まりは無効
- 発注単位の倍数以外は無効
- JAN と入力値の頭 3 桁が同じ場合は無効
- 3 桁以上でゾロ目は無効
- 品薄の場合、品薄最大発注数以上の入力は無効

品薄最大発注数の定義

- 品薄最大発注数が発注単位より少ない場合、発注入力最大数は発注単位と等しい
- 品薄最大発注数が発注単位より多い場合、発注入力最大数はそれから一番近い発注単位数の倍数（品薄最大発注数 ~/発注単位）\* 発注単位

# \*2 値の検証

- グループ別最大発注数が 0 か、入力発注数が 0 の場合
  - 最大発注数は単品別最大発注数を使う
- 違う場合
  - 最大発注数は　 2 週間平均売れ数 x グループ別最大発注数

計算された最大発注数と発注入力数を比較する

# 参考

https://docs.google.com/spreadsheets/d/1YFtWqTXFXuqz4oIyMQsqGi12tuN8DN1e/edit#gid=1284074622
