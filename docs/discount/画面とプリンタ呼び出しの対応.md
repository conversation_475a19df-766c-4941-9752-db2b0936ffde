# 画面ごとに何をするか整理する

# プリンタースキャン画面

プリンター横の BD Address をスキャナでスキャンし、`PortSetting`に設定する

BD Address は`0016A444E23D`のような値なので、`PortSetting`に設定する前に`Bluetooth:00:16:A4:44:E2:3D`のように整形する

> BCPUtil.createConnectionAddress メソッドを作った

`BcpControl.setPortSetting`で`PortSetting`に設定する

`BcpControl.openPort`で接続を確立する。

失敗した場合、戻り値が`false`になる

成功した場合のみ、ラベル発行画面に遷移させること。

# ラベル発行画面

印刷時に

# 設定画面

ここはプリンタのメソッドを呼び出すことはないので安心
しかし、`BcpControl`のインスタンスは破棄してはいけないので、watch しておくこと。

# その他

上記 3 つの画面で`BcpControl`のインスタンスを watch し、watch が外れ破棄する際は、`BcpControl.closePort`を必ず実行すること。
Riverpod の`ref.onDispose`などで可能。
