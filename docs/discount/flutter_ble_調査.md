# Flutter Bluetoot接続 調査

# 目的

- 値下げアプリでBLEプリンタに接続する間に、ペアリングする必要がある
- まず、`bondedDevices`を確認し、ペアリング済み端末かどうかチェック
    - `flutter_blue_plus`の`bondedDevices`をスキャンしたBD ADRESSと比較
- Androidでは`createBond`を呼び出す
    - `flutter_blue_plus.BluetoothDevice.pair`メソッドで可能
    - ITG650では、OSがペアリングダイアログを表示する
    - ITG400では、`createBond`呼び出した後、数秒してからペアリングされる

# 参考

[Bluetooth通信実装のセキュリティ観点を4ステップ + 1で理解する - Flatt Security Blog](https://blog.flatt.tech/entry/ble_security)
[React NativeアプリにおけるBLE通信を学ぶ - Qiita](https://qiita.com/nacam403/items/fc5ee4a3658efb2c8a0b#android%E3%81%AB%E3%81%8A%E3%81%91%E3%82%8B%E3%83%9C%E3%83%B3%E3%83%87%E3%82%A3%E3%83%B3%E3%82%B0%E3%81%AFcreatebond%E3%82%92%E4%BD%BF%E3%81%A3%E3%81%9F%E6%96%B9%E3%81%8C%E3%82%B9%E3%83%A0%E3%83%BC%E3%82%BA)
