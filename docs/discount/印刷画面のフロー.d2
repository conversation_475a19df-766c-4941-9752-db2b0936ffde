OfflineIssuePage.shape: circle
OnlineIssuePage.shape: circle
SetPrinterPage.shape: circle
DiscountLabelSettingPage.shape: circle

printerControllerProvider -> SetPrinterPage: 'watch' {style.stroke-width: 4}
printerControllerProvider -> SetPrinterPage: 'listen'
printerControllerProvider -> SetPrinterPage: 'read' {style.stroke-dash: 4}
selectedDiscountPrinterProvider -> SetPrinterPage: 'watch' {style.stroke-width: 5}
selectedDiscountPrinterProvider -> SetPrinterPage: 'read' {style.stroke-dash: 4}
getDiscountConfigProvider -> SetPrinterPage: 'watch' {style.stroke-width: 5}

discountSettingPageControllerProvider -> DiscountLabelSettingPage: 'watch' {style.stroke-width: 4}

printerControllerProvider -> OnlineIssuePage: 'watch' {style.stroke-width: 4}
printerControllerProvider -> OnlineIssuePage: 'read' {style.stroke-dash: 4}
printerControllerProvider -> OnlineIssuePage: 'listen'

onlineIssuePageControllerProvider -> OnlineIssuePage: 'watch' {style.stroke-width: 4}
discountProductCodeProvider -> OnlineIssuePage: 'watch' {style.stroke-width: 4}
discountSearchStateProvider -> OnlineIssuePage: 'read' {style.stroke-dash: 4}
printerControllerProvider -> OnlineIssuePage: 'read' {style.stroke-dash: 4}

offlineIssuePageControllerProvider -> OfflineIssuePage: 'watch' {style.stroke-width: 4}
printerControllerProvider -> OfflineIssuePage: 'watch' {style.stroke-width: 4}
printerControllerProvider -> OfflineIssuePage: 'read' {style.stroke-dash: 4}
printerControllerProvider -> OfflineIssuePage: 'listen'
discountProductCodeProvider -> OfflineIssuePage: 'listen'
discountProductCodeProvider -> OfflineIssuePage: 'read' {style.stroke-dash: 4}

getDiscountConfigProvider -> discountSettingPageControllerProvider: 'watch' {style.stroke-width: 4}
discountServiceProvider -> discountSettingPageControllerProvider: 'read' {style.stroke-dash: 4}

getDiscountConfigProvider -> offlineIssuePageControllerProvider: 'watch' {style.stroke-width: 4}
getDiscountConfigProvider -> onlineIssuePageControllerProvider: 'watch' {style.stroke-width: 4}
onlineIssuePageControllerProvider -> discountSearchStateProvider: 'read' {style.stroke-dash: 4}
onlineIssuePageControllerProvider -> discountProductCodeProvider: 'read' {style.stroke-dash: 4}
discountItemSearchProvider -> onlineIssuePageControllerProvider: 'read' {style.stroke-dash: 4}

discountRepositoryProvider -> getDiscountConfigProvider: 'read' {style.stroke-dash: 4}
discountRepositoryProvider -> discountServiceProvider: 'read' {style.stroke-dash: 4}
discountRepositoryProvider -> discountItemSearchProvider: 'read' {style.stroke-dash: 4}
