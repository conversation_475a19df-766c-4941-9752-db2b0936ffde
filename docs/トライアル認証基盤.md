# トライアル社員認証基盤

check endpointはあるが、機能していない (泣)

# 認証結果ステータス

`successed`フィールドに以下のどれかのフラグが入る

| フラグ | 意味                   |
|-----|----------------------|
| 0   | ログイン認証が成功しました        |
| 1   | アカウントまたパスワードが間違っています |
| 2   | 臨時パスワードログイン          |
| 3   | パスワードは期限を過ぎました       |
| 4   | パラメータが渡されていない        |
| 5   | 初期化のパスワード            |
| 6   | パスワードの有効期限は5日間過ぎました  |
| 7   | アカウントがロックされている       |
| 8   | システムエラー              |

# レスポンス例

```json

{
  "successed": "0",
  "message": "Succeeded!",
  "user": {
    "id": 6214,
    "account": "********",
    "name": "徐 小",
    "email": "<EMAIL>",
    "type": {
      "typeid": 1,
      "typename": "社員"
    },
    "group": {
      "groupcode": 3887,
      "groupname": "ジェイ・フーズ"
    },
    "jobs": [
      {
        "orgcode": 14252,
        "orgname": "T.R.E.-China 瀋陽 ITO事業部 ITO第十一課",
        "positioncode": 150,
        "positionname": "ソフトウェア エンジニア",
        "mainflag": true
      }
    ],
    "privileges": [
      "a",
      "b"
    ]
  },
  "certificate": "kkk",
  "certificatekey": "kkk"
}

```