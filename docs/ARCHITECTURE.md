# Flutter Project Policy

# Goal

# Authentication

use Firebase Auth

Never reinvent the user management function!

## Flutter Web

Here is the official Flutter web admin sample code
https://github.com/flutter/samples/tree/main/experimental/web_dashboard

### Renderer

use `CanvasKit renderer`

Always use Canvas, as some widgets will not work with HTML renderer

https://docs.flutter.dev/development/platform-integration/web/renderers

### Responsive

choice & use `AdaptiveScaffold` or `AdaptiveLayout`

https://pub.dev/packages/flutter_adaptive_scaffold

Please consider responsiveness from the beginning of the project.
In web app, it is required to operate on various screen sizes.

### Routing

use `go_router`

The package that supports the Navigator v2 API and is officially maintained by Flutter

### Font

use `Google Font`

Using the system font is not allowed because it will cause notational shaking.

# test

- Always Rus Test in CI
- Test critical areas 　(e.g. Domain model)
- Create maximum value at minimum cost
