# ITG650

PACER4 優先サポート端末 IT-G650 の物理キーイベントのメモ

[ラグドスマートハンディターミナル　 IT-G650 | CASIO](https://www.casio.com/jp/handheld-terminals/products/it-g650/)

F1

```
(physicalKey: PhysicalKeyboardKey#c0083(usbHidUsage: "0x000c0083", debugName: "Media Last"), logicalKey: LogicalKeyboardKey#00801(keyId: "0x100000801", keyLabel: "F1", debugName: "F1"), character: null, timeStamp: 1:44:03.964656)
```

F2

```
(physicalKey: PhysicalKeyboardKey#c009c(usbHidUsage: "0x000c009c", debugName: "Channel Up"), logicalKey: LogicalKeyboardKey#00802(keyId: "0x100000802", keyLabel: "F2", debugName: "F2"), character: null, timeStamp: 1:44:54.975391)
```

F3

```
(usbHidUsage: "0x000c009d", debugName: "Channel Down"), logicalKey: LogicalKeyboardKey#00803(keyId: "0x100000803", keyLabel: "F3", debugName: "F3"), character: null, timeStamp: 1:45:11.800348)
```

F4

```
(usbHidUsage: "0x1100000194", debugName: "Key with ID 0x1100000194"), logicalKey: LogicalKeyboardKey#00804(keyId: "0x100000804", keyLabel: "F4", debugName: "F4"), character: null, timeStamp: 1:45:28.893201)
```

左トリガーキー

```
(physicalKey: PhysicalKeyboardKey#00222(usbHidUsage: "0x1100000222", debugName: "Key with ID 0x1100000222"), logicalKey: LogicalKeyboardKey#003f4(keyId: "0x11000003f4", keyLabel: "", debugName: "Key with ID 0x011000003f4"), character: null, timeStamp: 1:47:21.691121)
```

右トリガーキー

```
(usbHidUsage: "0x11000002fe", debugName: "Key with ID 0x11000002fe"), logicalKey: LogicalKeyboardKey#003f3(keyId: "0x11000003f3", keyLabel: "", debugName: "Key with ID 0x011000003f3"), character: null, timeStamp: 1:47:36.277065)
```

# imosion

左トリガーキー

```
KeyDownEvent#4dc1f(physicalKey: PhysicalKeyboardKey#58a19(usbHidUsage: "0x11000002f8", debugName: "Key with ID 0x11000002f8"), logicalKey: LogicalKeyboardKey#08172(keyId: "0x1100000123", keyLabel: "", debugName: "Key with ID 0x01100000123"), character: null, timeStamp: 0:00:15.313020)
```

右トリガーキー

```
KeyDownEvent#240b0(physicalKey: PhysicalKeyboardKey#5b7a8(usbHidUsage: "0x11000002f9", debugName: "Key with ID 0x11000002f9"), logicalKey: LogicalKeyboardKey#0b205(keyId: "0x1100000124", keyLabel: "", debugName: "Key with ID 0x01100000124"), character: null, timeStamp: 0:00:15.351870)
```
