name: Lint & Analyze & Test

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
  workflow_dispatch:

jobs:
  analyze:
    # ドラフトPRでは実行しない
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest

    steps:
      - name: set ssh
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          name: id_rsa
          known_hosts: ${{ secrets.KNOWN_HOSTS }}

      - uses: actions/checkout@v4

      - uses: jdx/mise-action@v2
        with:
          install_args: "-j 2"
          install: true # [default: true] run `mise install`
          cache: true # [default: true] cache mise using GitHub's cache
          # automatically write this .tool-versions file
          experimental: true # [default: false] enable experimental features

      - name: Install Melos
        uses: bluefireteam/melos-action@v3

      - name: analyze
        run: melos analyze --fatal-infos -c 2

      - name: format
        run: melos format --line-length 120 -o none --set-exit-if-changed

      - name: Run unit tests
        run: melos run test

      # - name: coverage
      #   uses: VeryGoodOpenSource/very_good_coverage@v1
      #   with:
      #     path: ".package/coverage/lcov.info"
      #     min_coverage: 20
      #     exclude: "**/*_observer.dart **/change.dart"
