# 技術スタック
- Dart 3.5.3
- Flutter 3.24.3
- Riverpod 3.0.0-dev.3以上、4.0.0未満の最新版
- その他の使用技術などは`.mise.toml`を参照すること

<!-- # ディレクトリ構成
./
├ utils/
│ └ exceptions.dart
└ main.dart -->

<!-- # ドメイン知識
-  -->

# コーディング規則
- コメントは必要に応じて記述し、記述する場合は日本語で記述する
- メソッド名や関数名は動詞形にする
- 変数名や定数名、クラス名、フィールド名、引数名は名詞形にする
- マジックナンバーは禁止し、定数として定義する
- 例えば、`if (status == 1)`のようなコードは避け、`const int STATUS_ACTIVE = 1; if (status == STATUS_ACTIVE)`のようにする
- ただしそれがタイプコードならばenumを定義して列挙子として定義する
- 例えば、男性、女性、その他といった性別ならば`Gender`というenumを定義し、`Gender.male`、`Gender.female`、`Gender.other`のように使用する


# 自動テストコード規則
- テストコードは`<対象ファイル名>_test.dart`の命名規則に従う
- テストコードは`test/`ディレクトリ以下に配置する
- 例えば棚卸のdomain層に当たるAuditJobクラスのテストコードは`packages/pacer_app/test/src/features/stocktake/domain/audit_job_test.dart`に配置する
- テストコードは`group`と`test`を使用して、テストケースを論理的にグループ化する
- テストコードは`setUp`と`tearDown`を使用して、テストケースの前後に共通の初期化とクリーンアップを行う
- ただし、テストコード全体で共通の初期化とクリーンアップが必要な場合は、`setUpAll`と`tearDownAll`を使用する
- テストコードは`expect`を使用して、期待される結果を検証する
- テストコードは1つのテストケースで1つの機能をテストする
- 例えばテスト対象内に条件分岐がある場合は、条件分岐ごとにテストケースを分ける
- テストコードはブラックボックステストの自動化の観点に立ち、外部から見える振る舞いをテストする
- テストコード内で使用するmockはmocktailを使用する
- テストコードがriverpodのProviderを使用する場合は、テスト用のコンテナとして`packages/pacer_app/test/src/utils/create_container.dart`の`createTestContainer`を使用する
- テストスイートの説明文、テストケースの説明文は日本語で記述する

# Agentが実行するコマンドについて
- 依存関係の調整は`.mise.toml`にある`mise run bs`を使用すること
- protoファイルの更新に伴うコード生成ならば`.mise.toml`にある`mise run buf`を使用すること
- それ以外のコード生成ならば`.mise.toml`にある`mise run gen`を使用すること
- プロジェクトの一時ファイルをクリーンアップする場合は`.mise.toml`にある`mise run clean`を使用すること

<!-- ## エラーハンドリング
エラーハンドリングは以下の原則に従ってください：
-  -->
