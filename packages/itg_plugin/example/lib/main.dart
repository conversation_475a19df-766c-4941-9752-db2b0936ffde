import 'dart:async';
import 'dart:developer';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:itg_plugin/itg_plugin.dart';
import 'package:itg_plugin/type.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  DartPluginRegistrant.ensureInitialized();

  ItgPlugin().openScanner();

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _platformVersion = 'Unknown';
  bool? _isScannerOpen;
  OutputType? _outputType;
  CenteringWindowMode? _centeringWindowMode;
  LightMode? _lightMode;
  TriggerKeyMode? _triggerKeyMode;

  final _itgPlugin = ItgPlugin();

  @override
  void initState() {
    super.initState();
    initPlatformState();
    scannerOpenState();
    updateCenteringWindowModeState();
    updateOutputTypeState();
    updateLightModeState();
    updateTriggerKeyModeState();
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initPlatformState() async {
    String platformVersion;

    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    try {
      platformVersion = await _itgPlugin.getPlatformVersion() ?? 'Unknown platform version';
    } on PlatformException {
      platformVersion = 'Failed to get platform version.';
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    setState(() {
      _platformVersion = platformVersion;
    });
  }

  Future<void> scannerOpenState() async {
    bool? isScannerOpen;

    try {
      isScannerOpen = await _itgPlugin.isScannerOpen();
    } on PlatformException {
      _isScannerOpen = null;
    }

    if (!mounted) return;

    setState(() {
      _isScannerOpen = isScannerOpen;
    });
  }

  Future<void> updateOutputTypeState() async {
    OutputType? outputType;

    try {
      outputType = await _itgPlugin.getOutputType();
    } on PlatformException {
      outputType = null;
    }

    if (!mounted) return;

    setState(() {
      _outputType = outputType;
    });
  }

  Future<void> updateCenteringWindowModeState() async {
    CenteringWindowMode? centeringWindowMode;

    try {
      centeringWindowMode = await _itgPlugin.getCenteringWindow();
    } on PlatformException {
      centeringWindowMode = null;
    }

    if (!mounted) return;

    setState(() {
      _centeringWindowMode = centeringWindowMode;
    });
  }

  Future<void> updateTriggerKeyModeState() async {
    TriggerKeyMode? triggerKeyMode;

    try {
      triggerKeyMode = await _itgPlugin.getTriggerKeyEnable();
    } on PlatformException {
      triggerKeyMode = null;
    }

    if (!mounted) return;

    setState(() {
      _triggerKeyMode = triggerKeyMode;
    });
  }

  Future<void> updateLightModeState() async {
    LightMode? lightmode;

    try {
      lightmode = await _itgPlugin.getLightMode();
    } on PlatformException {
      lightmode = null;
    }

    if (!mounted) return;

    setState(() {
      _lightMode = lightmode;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(useMaterial3: true, colorSchemeSeed: Colors.amber),
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Plugin example app'),
        ),
        body: ListView(
          children: [
            const Text('端末の現在の情報'),
            Text(
              'Running on: $_platformVersion\n',
              style: const TextStyle(fontSize: 13),
            ),
            Text(
              style: const TextStyle(fontSize: 13),
              'isScannerOpen: ${_isScannerOpen ?? false ? 'オープン済み' : 'オープンしていません'}\n',
            ),
            Text(
              style: const TextStyle(fontSize: 13),
              'triggerKeyMode: ${_triggerKeyMode?.title}\n',
            ),
            Text(
              style: const TextStyle(fontSize: 13),
              'getOutputType: ${_outputType?.title}\n',
            ),
            Text(
              style: const TextStyle(fontSize: 13),
              'centeringWindowMode: ${_centeringWindowMode?.title}\n',
            ),
            Text(
              style: const TextStyle(fontSize: 11),
              ' getlightMode: ${_lightMode?.title}\n',
            ),
            const Divider(
              thickness: 5,
            ),
            const Text('端末操作'),
            SwitchListTile(
              title: Text('現在、トリガーキーは${_triggerKeyMode?.title}です'),
              value: _triggerKeyMode?.value == 1,
              onChanged: (input) async {
                log('toggle TriggerKeyMode');
                var result = await _itgPlugin.setTriggerKeyEnable(
                  (input ? TriggerKeyMode.enable : TriggerKeyMode.disable),
                );
                log('set結果:$result');
                await updateTriggerKeyModeState();
                var getTriggerKeyEnable = await _itgPlugin.getTriggerKeyEnable();
                log('更新後の値:${getTriggerKeyEnable?.title}');
              },
            ),
            SwitchListTile(
              title: Text('現在、中心読みは${_centeringWindowMode?.title}です'),
              value: _centeringWindowMode?.value == 1,
              onChanged: (input) async {
                log('toggle CenteringWindowMode');
                var result = await _itgPlugin
                    .setCenteringWindow((input ? CenteringWindowMode.enable : CenteringWindowMode.disable));
                log('set結果:$result');
                await updateCenteringWindowModeState();
                var getCenteringWindow = await _itgPlugin.getCenteringWindow();
                log('更新後の値:${getCenteringWindow.toString()}');
              },
            ),
            const Text('ライトモード変更'),
            DropdownButton<LightMode>(
              isExpanded: true,
              icon: const Icon(Icons.light),
              onChanged: (lightMode) async {
                log('onPressed setLightMode');
                if (lightMode == null) return;
                var result = await _itgPlugin.setLightMode(lightMode);
                log('set結果:$result');
                await updateLightModeState();
                var getLightMode = await _itgPlugin.getLightMode();
                log('更新後の値:$getLightMode');
              },
              value: _lightMode,
              items: LightMode.values
                  .map((LightMode lightMode) => DropdownMenuItem<LightMode>(
                        value: lightMode,
                        child: Text('${lightMode.value}  ${lightMode.title}'),
                      ))
                  .toList(),
            ),
            const Text('出力方式変更'),
            DropdownButton<OutputType>(
              isExpanded: true,
              icon: const Icon(Icons.print),
              onChanged: (outputType) async {
                log('onPressed setLightMode');
                if (outputType == null) return;
                var result = await _itgPlugin.setOutputType(outputType);
                log('set結果:$result');
                await updateOutputTypeState();
                var getOutputType = await _itgPlugin.getOutputType();
                log('更新後の値:$getOutputType');
              },
              value: _outputType,
              items: OutputType.values
                  .map(
                    (OutputType outputType) => DropdownMenuItem<OutputType>(
                      value: outputType,
                      child: Text('${outputType.value}  ${outputType.title}'),
                    ),
                  )
                  .toList(),
            ),
            const TextField(
              decoration: InputDecoration(labelText: 'スキャナーテスト用テキストフィールド'),
            ),
            TextButton.icon(
              icon: const Icon(Icons.refresh),
              style: TextButton.styleFrom(backgroundColor: Colors.green),
              onPressed: () async {
                await _itgPlugin.setDefaultAll();
                await initPlatformState();
                await scannerOpenState();
                await updateCenteringWindowModeState();
                await updateOutputTypeState();
              },
              label: const Text('スキャナー設定をデフォルトに戻す'),
            ),
          ],
        ),
      ),
    );
  }
}
