
buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()
        mavenCentral()

    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"


    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        flatDir{
            dirs 'libs'
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
