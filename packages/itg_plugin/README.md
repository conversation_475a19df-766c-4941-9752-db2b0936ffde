# itg_plugin

カシオSDKのFlutterプラグイン

# 利用法

`pubspec.yaml`に依存関係を追加

```yaml
dependencies:

  itg_plugin:
    git:
      url: **************:retail-ai-inc/itg_plugin.git
```

`flutter pub get`

githubにssh鍵を登録していれば成功する


`android/itg_plugin_local`を配置する

`android/build.gradle`にflatDirを追加

```
allprojects {
    repositories {
        google()
        mavenCentral()
        flatDir{
            dirs 'libs'
        }
    }
}
```

`android/app/build.gradle`にimplementationを追加

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation fileTree(dir: 'libs', include: ['*.aar'])
}

`android/app/src/main/AndroidManifest.xml` の`manifest`に
`xmlns:tools="http://schemas.android.com/tools"`を追加

`application`に`tools:replace="android:label"`を追加

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    >

   <application
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        tools:replace="android:label"
        >
```


# リリースビルド

appレベルの
compileSdkVersion
buildToolsVersion
targetSdkVersion
を合わせておく

minSdkVersionは動かしたい最小のバージョン
ITG400なら、23

# ITG400用 APK

AABをビルドした後、それからbundletoolでAPKを生成する
直接APKを作成しようとすると必ずエラーになる

[aar のビルドで local の .aar ファイルを依存に使えない - nashcft](https://scrapbox.io/nashcft/aar_%E3%81%AE%E3%83%93%E3%83%AB%E3%83%89%E3%81%A7_local_%E3%81%AE_.aar_%E3%83%95%E3%82%A1%E3%82%A4%E3%83%AB%E3%82%92%E4%BE%9D%E5%AD%98%E3%81%AB%E4%BD%BF%E3%81%88%E3%81%AA%E3%81%84)

# 参考

[Androidのビルドバリアントをイチから理解する | フューチャー技術ブログ](https://future-architect.github.io/articles/20210120/)
