package jp.co.trialnet.itg_plugin

import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import jp.casio.ht.devicelibrary.ScannerLibrary
import jp.casio.ht.devicelibrary.SystemLibrary


// 使いたいカシオのAPIを列挙
@Suppress("EnumEntryName")
enum class Methods {
    getPlatformVersion,

    getScanResultAsClass,

    getScanResult,

    // bool
    isScannerOpen,

    // int
    openScanner,

    // int
    closeScanner,

    // int
    getOutputType,

    // int outputType int
    setOutputType,

    // int
    getCenteringWindow,

    // int int
    setCenteringWindow,

    // int
    getLightMode,

    // int lightMode int
    setLightMode,

    // int
    getTriggerKeyEnable,

    // int int
    setTriggerKeyEnable,
    setDefaultAll,

    // ナビゲーションバー表示関連
    getNavigationBarState,
    setNavigationBarState,

    // 読み取り結果 末尾設定
    // 末尾に改行コードを設定することで、スキャナーからの入力あることを検知したい
    setSuffix,

    getSuffix,

}

/** ItgPlugin */
class ItgPlugin : FlutterPlugin, MethodCallHandler {
    /// FlutterとネイティブAndroid間の通信を行うMethodChannel

    private lateinit var scanner: ScannerLibrary
    private lateinit var system: SystemLibrary

    /// このローカルリファレンスは、Flutter Engineにプラグインを登録し、
    /// Flutter EngineがActivityから切り離されたときに登録を解除する役割を果たします。
    /// Flutter EngineがActivityから切り離されたときに登録を解除する。
    private lateinit var channel: MethodChannel


    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        // スキャナー初期化
        scanner = ScannerLibrary()
        system = SystemLibrary()
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "itg_plugin")
        channel.setMethodCallHandler(this)
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            Methods.getPlatformVersion.name -> {
                result.success("Android ${android.os.Build.VERSION.RELEASE}")
            }

            Methods.getScanResultAsClass.name -> {
                var scanResult = ScannerLibrary.ScanResult()
                scanner.getScanResult(scanResult)

                val map = mapOf(
                    "length" to scanResult.length,
                    "time" to scanResult.time,
                    "value" to scanResult.value,
                    "symbologyID" to scanResult.symbologyID,
                    )

                result.success(map)
            }

            Methods.getScanResult.name -> {
                var scanResult = ScannerLibrary.ScanResult()
                scanner.getScanResult(scanResult)

                result.success(scanResult.value)
            }

            Methods.isScannerOpen.name -> {
                val isScannerOpen = scanner.isScannerOpen
                result.success(isScannerOpen)
            }

            Methods.openScanner.name -> {
                val resultNum = scanner.openScanner()
                // todo エラーハンドリング？
                result.success(resultNum)
            }

            Methods.closeScanner.name -> {
                val resultNum = scanner.closeScanner()
                // todo エラーハンドリング？
                result.success(resultNum)
            }

            Methods.getOutputType.name -> {
                val outputType = scanner.outputType
                result.success(outputType)
            }

            Methods.setOutputType.name -> {
                val option = call.arguments as Int
                val resultNum = scanner.setOutputType(option)
                result.success(resultNum)
            }

            Methods.getCenteringWindow.name -> {
                val centeringWindowMode = scanner.centeringWindow
                result.success(centeringWindowMode)
            }

            Methods.setCenteringWindow.name -> {
                val option = call.arguments as Int
                val resultNum = scanner.setCenteringWindow(option)
                result.success(resultNum)
            }

            Methods.getLightMode.name -> {
                val lightMode = scanner.lightMode
                result.success(lightMode)
            }

            Methods.setLightMode.name -> {
                val option = call.arguments as Int
                val resultNum = scanner.setLightMode(option)
                result.success(resultNum)
            }

            Methods.getTriggerKeyEnable.name -> {
                val triggerKeyEnableMode = scanner.triggerKeyEnable
                result.success(triggerKeyEnableMode)
            }

            Methods.setTriggerKeyEnable.name -> {
                val option = call.arguments as Int
                val resultNum = scanner.setTriggerKeyEnable(option)
                result.success(resultNum)
            }

            Methods.setDefaultAll.name -> {
                val resultNum = scanner.setDefaultAll()
                result.success(resultNum)
            }

            Methods.getSuffix.name -> {
                val suffix = scanner.suffix
                result.success(suffix)
            }

            Methods.setSuffix.name -> {
                val option = call.arguments as Int
                val resultNum = scanner.setSuffix(option)
                result.success(resultNum)
            }

            Methods.getNavigationBarState.name -> {
                val isShow = system.navigationBarState
                result.success(isShow)
            }

            Methods.setNavigationBarState.name -> {
                val option = call.arguments as Boolean
                system.navigationBarState = option
            }

            else -> {
                result.notImplemented()
            }
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }
}