import 'package:itg_plugin/type.dart';

import 'itg_plugin_platform_interface.dart';

class ItgPlugin {
  Future<String?> getPlatformVersion() {
    return ItgPluginPlatform.instance.getPlatformVersion();
  }

  /// スキャンしたバーコードを返す
  Future<ScanResult?> getScanResultAsClass() {
    return ItgPluginPlatform.instance.getScanResultAsClass();
  }

  /// スキャンしたバーコードを返す
  Future<List<int>?> getScanResult() {
    return ItgPluginPlatform.instance.getScanResult();
  }

// バーコードスキャナーを使用するアプリケーションの起動時にコールしてください。
// オープンしていない状態で ScannerLibrary クラスの関数をコールすると正しく動作しないことが
// あります。
  Future<CasioResult?> openScanner() {
    return ItgPluginPlatform.instance.openScanner();
  }

  Future<CasioResult?> closeScanner() {
    return ItgPluginPlatform.instance.closeScanner();
  }

  Future<bool?> isScannerOpen() {
    return ItgPluginPlatform.instance.isScannerOpen();
  }

  Future<CasioResult?> setOutputType(OutputType outputType) {
    return ItgPluginPlatform.instance.setOutputType(outputType);
  }

  Future<OutputType?> getOutputType() {
    return ItgPluginPlatform.instance.getOutputType();
  }

  Future<LightMode?> getLightMode() {
    return ItgPluginPlatform.instance.getLightMode();
  }

  Future<CasioResult?> setLightMode(LightMode lightMode) {
    return ItgPluginPlatform.instance.setLightMode(lightMode);
  }

  Future<CenteringWindowMode?> getCenteringWindow() {
    return ItgPluginPlatform.instance.getCenteringWindow();
  }

  Future<CasioResult?> setCenteringWindow(CenteringWindowMode centeringWindowMode) {
    return ItgPluginPlatform.instance.setCenteringWindow(centeringWindowMode);
  }

  Future<TriggerKeyMode?> getTriggerKeyEnable() {
    return ItgPluginPlatform.instance.getTriggerKeyEnable();
  }

  Future<CasioResult?> setTriggerKeyEnable(TriggerKeyMode triggerKeyEnableMode) {
    return ItgPluginPlatform.instance.setTriggerKeyEnable(triggerKeyEnableMode);
  }

  Future<SuffixType?> getSuffix() {
    return ItgPluginPlatform.instance.getSuffix();
  }

  Future<CasioResult?> setSuffix(SuffixType suffixType) {
    return ItgPluginPlatform.instance.setSuffix(suffixType);
  }

  /// 全てのバーコードスキャナー設定をデフォルトに戻します。
  Future<CasioResult?> setDefaultAll() {
    return ItgPluginPlatform.instance.setDefaultAll();
  }

  // @override
  Future<void> setNavigationBarState(bool state) async {
    return ItgPluginPlatform.instance.setNavigationBarState(state);
  }

  // @override
  Future<bool?> getNavigationBarState() async {
    return ItgPluginPlatform.instance.getNavigationBarState();
  }
}
