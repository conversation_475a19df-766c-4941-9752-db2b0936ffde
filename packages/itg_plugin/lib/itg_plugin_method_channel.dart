import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:itg_plugin/type.dart';

import 'itg_plugin_platform_interface.dart';

/// An implementation of [ItgPluginPlatform] that uses method channels.
class MethodChannelItgPlugin extends ItgPluginPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('itg_plugin');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }

  @override
  Future<ScanResult?> getScanResultAsClass() async {
    final resultMap = await methodChannel.invokeMapMethod<String, dynamic>('getScanResultAsClass');

    return switch (resultMap) {
      final _? => ScanResult.fromJson(resultMap),
      _ => null,
    };
  }

  @override
  Future<List<int>?> getScanResult() async {
    final resultList = await methodChannel.invokeListMethod<int>('getScanResult');

    return resultList;
  }

  @override
  Future<CasioResult?> openScanner() async {
    final resultNum = await methodChannel.invokeMethod<int>('openScanner');
    return CasioResult.fromValue(resultNum!);
  }

  @override
  Future<CasioResult?> closeScanner() async {
    final resultNum = await methodChannel.invokeMethod<int>('closeScanner');
    return CasioResult.fromValue(resultNum!);
  }

  @override
  Future<bool?> isScannerOpen() async {
    final isScannerOpen = await methodChannel.invokeMethod<bool>('isScannerOpen');
    return isScannerOpen;
  }

  @override
  Future<OutputType?> getOutputType() async {
    final outputType = await methodChannel.invokeMethod<int>('getOutputType');
    return OutputType.fromValue(outputType!);
  }

  @override
  Future<CasioResult?> setOutputType(OutputType outputType) async {
    final resultNum = await methodChannel.invokeMethod<int>('setOutputType', outputType.value);
    return CasioResult.fromValue(resultNum!);
  }

  @override
  Future<LightMode?> getLightMode() async {
    final lightMode = await methodChannel.invokeMethod<int>('getLightMode');
    return LightMode.fromValue(lightMode!);
  }

  @override
  Future<CasioResult?> setLightMode(LightMode lightMode) async {
    final resultNum = await methodChannel.invokeMethod<int>('setLightMode', lightMode.value);
    return CasioResult.fromValue(resultNum!);
  }

  @override
  Future<CenteringWindowMode?> getCenteringWindow() async {
    final centeringWindowModeValue = await methodChannel.invokeMethod<int>('getCenteringWindow');
    return CenteringWindowMode.fromValue(centeringWindowModeValue!);
  }

  @override
  Future<CasioResult?> setCenteringWindow(CenteringWindowMode centeringWindowMode) async {
    final resultNum = await methodChannel.invokeMethod<int>('setCenteringWindow', centeringWindowMode.value);
    return CasioResult.fromValue(resultNum!);
  }

  @override
  Future<TriggerKeyMode?> getTriggerKeyEnable() async {
    final triggerKeyMode = await methodChannel.invokeMethod<int>('getTriggerKeyEnable');
    return TriggerKeyMode.fromValue(triggerKeyMode!);
  }

  @override
  Future<CasioResult?> setTriggerKeyEnable(TriggerKeyMode triggerKeyEnableMode) async {
    final resultNum = await methodChannel.invokeMethod<int>('setTriggerKeyEnable', triggerKeyEnableMode.value);
    return CasioResult.fromValue(resultNum!);
  }

  @override
  Future<SuffixType?> getSuffix() async {
    final result = await methodChannel.invokeMethod<int>('getSuffix');
    return SuffixType.fromValue(result!);
  }

  @override
  Future<CasioResult?> setSuffix(SuffixType suffixType) async {
    final resultNum = await methodChannel.invokeMethod<int>('setSuffix', suffixType.value);
    return CasioResult.fromValue(resultNum!);
  }

  @override
  Future<CasioResult?> setDefaultAll() async {
    final resultNum = await methodChannel.invokeMethod<int>('setDefaultAll');
    return CasioResult.fromValue(resultNum!);
  }

  @override
  Future<void> setNavigationBarState(bool state) async {
    return await methodChannel.invokeMethod<void>(
      'setNavigationBarState',
      state,
    );
  }

  @override
  Future<bool?> getNavigationBarState() async {
    return await methodChannel.invokeMethod<bool>('getNavigationBarState');
  }
}
