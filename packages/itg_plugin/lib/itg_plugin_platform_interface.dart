import 'package:itg_plugin/type.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'itg_plugin_method_channel.dart';

abstract class ItgPluginPlatform extends PlatformInterface {
  /// Constructs a ItgPluginPlatform.
  ItgPluginPlatform() : super(token: _token);

  static final Object _token = Object();

  static ItgPluginPlatform _instance = MethodChannelItgPlugin();

  /// The default instance of [ItgPluginPlatform] to use.
  ///
  /// Defaults to [MethodChannelItgPlugin].
  static ItgPluginPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [ItgPluginPlatform] when
  /// they register themselves.
  static set instance(ItgPluginPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<ScanResult?> getScanResultAsClass() {
    throw UnimplementedError('getScanResultAsClass() has not been implemented.');
  }

  Future<List<int>?> getScanResult() {
    throw UnimplementedError('getScanResult() has not been implemented.');
  }

  Future<CasioResult?> openScanner() {
    throw UnimplementedError('openScanner() has not been implemented.');
  }

  Future<CasioResult?> closeScanner() {
    throw UnimplementedError('closeScanner() has not been implemented.');
  }

  Future<bool?> isScannerOpen() {
    throw UnimplementedError('isScannerOpen() has not been implemented.');
  }

  Future<OutputType?> getOutputType() {
    throw UnimplementedError('getOutputType() has not been implemented.');
  }

  /// スキャナの読み取り結果の出力方式を設定
  /// ScannerLibrary.CONSTANT.OUTPUT.KEY に設定してキーボード出力にする
  Future<CasioResult?> setOutputType(OutputType outputType) {
    throw UnimplementedError('setOutputType() has not been implemented.');
  }

  Future<CenteringWindowMode?> getCenteringWindow() {
    throw UnimplementedError('getCenteringWindow() has not been implemented.');
  }

  Future<CasioResult?> setCenteringWindow(CenteringWindowMode centeringWindowMode) {
    throw UnimplementedError('setCenteringWindow() has not been implemented.');
  }

  Future<LightMode?> getLightMode() {
    throw UnimplementedError('getLightMode() has not been implemented.');
  }

  Future<CasioResult?> setLightMode(LightMode lightMode) {
    throw UnimplementedError('setLightMode() has not been implemented.');
  }

  Future<TriggerKeyMode?> getTriggerKeyEnable() {
    throw UnimplementedError('getTriggerKeyEnable() has not been implemented.');
  }

  Future<CasioResult?> setTriggerKeyEnable(TriggerKeyMode triggerKeyEnableMode) {
    throw UnimplementedError('setTriggerKeyEnable() has not been implemented.');
  }

  /// 出力の末尾に設定された文字を取得する
  Future<SuffixType?> getSuffix() {
    throw UnimplementedError('getSuffix() has not been implemented.');
  }

  /// 出力の末尾に文字を設定する
  Future<CasioResult?> setSuffix(SuffixType suffixType) {
    throw UnimplementedError('setLightMode() has not been implemented.');
  }

  Future<CasioResult?> setDefaultAll() {
    throw UnimplementedError('setDefaultAll() has not been implemented.');
  }

  Future<void> setNavigationBarState(bool state) {
    throw UnimplementedError('setNavigationBarState() has not been implemented.');
  }

  Future<bool?> getNavigationBarState() {
    throw UnimplementedError('getNavigationBarState() has not been implemented.');
  }
}
