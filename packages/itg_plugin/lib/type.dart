import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

/// casio sdkの返却値
enum CasioResult {
  success(0),
  errorUnsupported(-1),
  errorParameter(-2),
  errorNotopened(-3);

  const CasioResult(this.value);
  final int value;

  factory CasioResult.fromValue(int value) {
    return values.firstWhere((e) => e.value == value);
  }
}

enum TriggerKeyMode {
  disable(0, '無効'),
  enable(1, '有効');

  const TriggerKeyMode(this.value, this.title);
  final int value;
  final String title;

  factory TriggerKeyMode.fromValue(int value) {
    return values.firstWhere((e) => e.value == value);
  }
}

// 中心読みモードが有効かどうか デフォルト無効0
enum CenteringWindowMode {
  disable(0, '無効'),
  enable(1, '有効');

  const CenteringWindowMode(this.value, this.title);
  final int value;
  final String title;

  factory CenteringWindowMode.fromValue(int value) {
    return values.firstWhere((e) => e.value == value);
  }
}

/// ライトモード
enum LightMode {
  // 点灯しません
  allOff(0, '点灯しません'),
  // エイマーのみ点灯します
  aimerOn(1, 'エイマーのみ点灯'),
  // イルミネーションのみ点灯
  illuminationOn(2, 'イルミネーションのみ点灯'),
  // エイマーとイルミネーションが点灯
  allOn(3, 'エイマーとイルミネーションが点灯');

  const LightMode(this.value, this.title);
  final int value;
  final String title;

  factory LightMode.fromValue(int value) {
    return values.firstWhere((e) => e.value == value);
  }
}

/// バーコードスキャナの出力方式
enum OutputType {
  clip(0, 'クリップボード出力'),
  key(1, 'キーボード出力'),
  user(2, 'ユーザーメッセージ出力'),
  broadcast(3, 'ブロードキャスト出力');

  const OutputType(this.value, this.title);
  final int value;
  final String title;

  factory OutputType.fromValue(int value) {
    return values.firstWhere((e) => e.value == value);
  }
}

/// バーコードスキャナの出力末尾
enum SuffixType {
  none(0, 'Suffixなし'),
  lf(1, 'LF(0x0A)を付与'),
  tab(2, 'TAB(0x09)を付与'),
  tabLf(3, 'TAB+LFを付与');

  const SuffixType(this.value, this.title);
  final int value;
  final String title;

  factory SuffixType.fromValue(int value) {
    return values.firstWhere((e) => e.value == value);
  }
}

/// スキャン結果
@immutable
class ScanResult extends Equatable {
  const ScanResult({
    this.length = 0,
    this.time = 0,
    this.value = const [],
    this.symbologyID = 0,
  });

  /// バーコードの長さ
  final int length;

  /// 読取り時間
  final int time;

  /// 読み取ったバーコードのバイト配列
  final List<int>? value;

  /// 読み取ったバーコードのID
  final int symbologyID;

  factory ScanResult.fromJson(Map<String, dynamic> json) {
    return ScanResult(
      length: json['length'] as int,
      time: json['time'] as int,
      value: json['value'] as List<int>?,
      symbologyID: json['symbologyID'] as int,
    );
  }

  @override
  List<Object?> get props => [length, time, value, symbologyID];
}
