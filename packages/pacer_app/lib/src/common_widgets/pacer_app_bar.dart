import 'package:flutter/material.dart';

/// PACERアプリで使用するAppBar
/// 通常のAppBarと異なる点は、高さや色など
class PacerAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// init
  const PacerAppBar({
    super.key,
    required this.title,
    required this.context,
  });

  /// タイトル
  final Widget? title;

  /// コンテキスト
  final BuildContext context;

  @override
  Size get preferredSize => Size.fromHeight(
        (Theme.of(context).textTheme.titleMedium?.fontSize ?? kToolbarHeight) + 8,
      );

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return AppBar(
      backgroundColor: colors.primary,
      titleTextStyle: texts.titleMedium?.copyWith(color: colors.onPrimary),
      title: title,
      automaticallyImplyLeading: false,
      centerTitle: true,
    );
  }
}
