import 'package:flutter/material.dart';

import '../themes/app_color_scheme.dart';

/// メニュー画面で使用するプロジェクトボタン
/// このコンポーネントは最大幅を埋めます
/// デフォルトの[TextStyle]が内部に提供されています
class CommonMenuButton extends StatelessWidget {
  /// 標準のコンストラクター
  const CommonMenuButton({
    super.key,
    required this.child,
    required this.onPressed,
    this.count,
    this.prefix,
    this.isLoading = false,
  });

  /// ボタンの中心に表示される内容、デフォルトの[TextStyle]が内部に提供されています
  final Widget child;

  /// ボタンの左側に表示される内容
  final Widget? prefix;

  /// ボタンの右側に表示される内容は、通常は処理待ちの数を示します
  final int? count;

  /// クリック時のコールバック
  final VoidCallback onPressed;

  /// 読み込み中かどうか
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;
    return Stack(
      children: [
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: onPressed,
            style: ButtonStyle(
              minimumSize: const WidgetStatePropertyAll(Size(0, 70)),
              side: WidgetStatePropertyAll(
                BorderSide(
                  color: colors.line,
                ),
              ),
              padding: const WidgetStatePropertyAll(
                EdgeInsets.symmetric(vertical: 23),
              ),
              backgroundColor: WidgetStatePropertyAll(colors.button),
              textStyle: WidgetStatePropertyAll(
                texts.titleLarge?.copyWith(
                  color: colors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            child: isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(),
                  )
                : child,
          ),
        ),
        if (prefix != null)
          Positioned(
            top: 0,
            bottom: 0,
            left: 23,
            child: Center(
              child: prefix,
            ),
          ),
        if (count != null)
          Positioned(
            top: 0,
            bottom: 0,
            right: 23,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                decoration: BoxDecoration(
                  color: colors.primary,
                  borderRadius: BorderRadius.circular(100),
                ),
                child: Text(
                  count.toString(),
                  style: texts.titleLarge?.copyWith(color: colors.onPrimary),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
