// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

void showSnackBar(
  BuildContext context,
  String message, {
  String buttonLabel = 'OK',
  bool isErrorStyle = false,
}) {
  final texts = Theme.of(context).textTheme;
  final colors = Theme.of(context).colorScheme;
  WidgetsBinding.instance.addPostFrameCallback((_) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: switch (isErrorStyle) {
          true => colors.error,
          false => colors.onSurface,
        },
        content: Text(
          message,
          style: texts.bodyMedium?.copyWith(color: Colors.white),
        ),
      ),
    );
  });
}
