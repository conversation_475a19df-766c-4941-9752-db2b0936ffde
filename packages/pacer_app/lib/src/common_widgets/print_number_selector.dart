import 'package:flutter/material.dart';

/// 印刷枚数選択セレクター
class PrintNumberSelector extends StatelessWidget {
  /// init
  const PrintNumberSelector({
    super.key,
    this.title = '枚数',
    required this.selectingPrintNumber,
    required this.onSelect,
  });

  /// タイトル
  final String title;

  /// 選択中の枚数
  final int? selectingPrintNumber;

  /// 選択時のコールバック
  final ValueChanged<int> onSelect;

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      style: ElevatedButton.styleFrom(shape: const BeveledRectangleBorder()),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          Text(
            ' $selectingPrintNumber 枚',
            textAlign: TextAlign.right,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
      onPressed: () => showMenu(
        context: context,
        position: const RelativeRect.fromLTRB(100, 100, 100, 100),
        items: List.generate(
          30,
          (index) => PopupMenuItem(
            value: index,
            onTap: () => onSelect(index + 1),
            child: Wrap(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 20),
                  child: Text((index + 1).toString()),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
