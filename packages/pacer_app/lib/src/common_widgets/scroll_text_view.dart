import 'package:flutter/material.dart';

///　スクロール式Text
class ScrollTextView extends StatefulWidget {
  /// 初期化
  const ScrollTextView({
    super.key,
    this.text,
    this.textStyle,
    this.maxShowLines = 1,
    this.edgeInsets,
    this.isShowScrollbar = false,
    required this.scrollController,
  });

  ///  表示内容
  final String? text;

  ///  textのstyle、サイズとか色とか
  final TextStyle? textStyle;

  /// 最大表示文字の行数
  final int maxShowLines;

  /// padding情報
  final EdgeInsets? edgeInsets;

  /// true：ずっとScrollbarを表示されます。
  /// false：Scrollしてるとき、Scrollbarを表示されます。
  final bool isShowScrollbar;

  /// scrollController
  final ScrollController scrollController;

  @override
  State<ScrollTextView> createState() => _ScrollTextViewState();
}

class _ScrollTextViewState extends State<ScrollTextView> {
  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    final style = widget.textStyle ?? texts.titleLarge;
    final padding = widget.edgeInsets ?? const EdgeInsets.all(1);

    final maxLines = widget.maxShowLines < 1 ? 1 : widget.maxShowLines;

    final height = maxLines * (style?.fontSize ?? 0) * (style?.height ?? 0) + padding.top + padding.bottom;

    return Container(
      padding: padding,
      height: height,
      child: Scrollbar(
        thumbVisibility: widget.isShowScrollbar,
        controller: widget.scrollController,
        child: SingleChildScrollView(
          controller: widget.scrollController,
          child: Text(
            widget.text ?? '',
            style: style,
          ),
        ),
      ),
    );
  }
}
