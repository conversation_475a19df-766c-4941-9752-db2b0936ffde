// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

class OutlinedBadgeButton extends StatelessWidget {
  const OutlinedBadgeButton({
    super.key,
    required this.label,
    this.icon,
    required this.count,
    required this.onPressed,
  });
  final String label;
  final Icon? icon;
  final int count;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Badge.count(
        alignment: AlignmentDirectional.topStart,
        isLabelVisible: count != 0,
        count: count,
        child: switch (icon) {
          null => OutlinedButton(
              style: OutlinedButton.styleFrom(
                backgroundColor: colors.secondaryContainer,
              ),
              onPressed: onPressed,
              child: Text(
                label,
                style: TextStyle(color: colors.onSecondaryContainer),
              ),
            ),
          final icon => OutlinedButton.icon(
              style: OutlinedButton.styleFrom(
                backgroundColor: colors.secondaryContainer,
              ),
              onPressed: onPressed,
              icon: icon,
              label: Text(
                label,
                style: TextStyle(color: colors.onSecondaryContainer),
              ),
            ),
        },
      ),
    );
  }
}
