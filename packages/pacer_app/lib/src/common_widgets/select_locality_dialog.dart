import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../themes/app_color_scheme.dart';

/// ローカリティが_specialLocalityの値である場合、その値を単独で選択することができます。
/// それ以外の場合は複数選択が可能です。
const _specialLocality = '産地は商品に記載';

/// 選択された産地のダイアログを表示します。商品情報から返される産地リストと「その他」ボタンが含まれています。
/// 「その他」ボタンをクリックすると、全画面Dialogが開きます。
/// [productLocalityList] 商品情報から返される産地リスト
/// [onChanged] 産地が変更された際のコールバックイベント
/// [onOtherClick] 【その他】がクリックされたときのコールバック
void selectLocalityDialog({
  required WidgetRef ref,
  required List<String> productLocalityList,
  required void Function(String) onChanged,
  required void Function() onOtherClick,
}) {
  showDialog<void>(
    barrierDismissible: false,
    context: ref.context,
    builder: (context) {
      return _LocalityDialog(
        productLocalityList: productLocalityList,
        onChanged: onChanged,
        onOtherClick: onOtherClick,
      );
    },
  );
}

class _LocalityDialog extends HookConsumerWidget {
  const _LocalityDialog({
    required this.productLocalityList,
    required this.onChanged,
    required this.onOtherClick,
  });

  /// 商品情報から返される産地リスト
  final List<String> productLocalityList;

  /// 産地が変更された際のコールバックイベント
  final void Function(String) onChanged;

  /// 【その他】がクリックされたときのコールバック
  final void Function() onOtherClick;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    // 選ばれた産地
    final selectingLocalitySet = useState(<String>{});

    // [_specialLocality]に選ばれた場合、他のオプションを再度クリックしても選択されません。
    final isSpecialLocalitySelected = useMemoized(
      () {
        return selectingLocalitySet.value.contains(_specialLocality);
      },
      [selectingLocalitySet.value],
    );

    return AlertDialog(
      title: const Text('選択産地'),
      contentPadding: const EdgeInsets.all(8),
      content: ListView.separated(
        // 「その他」を追加表示する必要があります
        itemCount: productLocalityList.length + 1,
        shrinkWrap: true,
        itemBuilder: (BuildContext context, int index) {
          // 最後のオプションには「その他」のボタンを表示する必要があります。
          if (index == productLocalityList.length) {
            return GestureDetector(
              onTap: () {
                context.pop();
                onOtherClick();
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                child: Text(
                  'その他',
                  style: texts.titleLarge,
                ),
              ),
            );
          }
          final locality = productLocalityList[index];
          final isSelected = selectingLocalitySet.value.contains(locality);
          return CheckboxListTile(
            value: isSelected,
            dense: true,
            // 特別地域が選択された際には、
            // その特別地域のみが有効になり、
            // 他の選択肢は自動的に選択状態をクリアされ、選択できなくなります。
            enabled: !isSpecialLocalitySelected || locality == _specialLocality,
            onChanged: (checked) {
              final newLocalitySet = Set<String>.from(selectingLocalitySet.value);
              if (checked ?? false) {
                if (locality == _specialLocality) {
                  newLocalitySet.clear();
                }
                newLocalitySet.add(locality);
              } else {
                newLocalitySet.remove(locality);
              }
              selectingLocalitySet.value = newLocalitySet;
            },
            title: Text(
              locality,
              style: texts.titleLarge,
            ),
          );
        },
        separatorBuilder: (BuildContext context, int index) {
          return Divider(
            color: colors.line,
          );
        },
      ),
      actions: [
        TextButton(
          onPressed: () => context.pop(),
          child: const Text('キャンセル'),
        ),
        TextButton(
          onPressed: () {
            // コンマで区切られたデータを分割します。
            // リストに整理します
            // コンマを使用して文字列に結合します。
            final localitySet = <String>{};
            for (final locality in selectingLocalitySet.value) {
              localitySet.addAll(locality.split('､'));
            }
            final locality = localitySet.join('､');
            onChanged(locality);
            context.pop();
          },
          child: const Text('確定'),
        ),
      ],
    );
  }
}
