// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// ミニアプリのセカンダリー画面（home画面以外）の戻るボタンの共通UI
class PacerBackButton extends StatelessWidget {
  const PacerBackButton({
    super.key,
    this.onPressed,
  });

  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return TextButton.icon(
      onPressed: onPressed ?? context.pop,
      icon: const Icon(Icons.arrow_back_ios, size: 14),
      label: Text(
        '戻る',
        style: texts.titleMedium?.copyWith(
          color: colors.primary,
        ),
      ),
    );
  }
}
