// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

import '../localization/string_hardcoded.dart';
import '../routing/app_router.dart';

/// Placeholder widget showing a message and CTA to go back to the home screen.
class EmptyPlaceholderWidget extends StatelessWidget {
  const EmptyPlaceholderWidget({super.key, required this.message});
  final String message;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.zero,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(message, textAlign: TextAlign.center),
            const Spacer(),
            TextButton(
              onPressed: () => const HomeRoute().go(context),
              child: Text('Go Home'.hardcoded),
            ),
          ],
        ),
      ),
    );
  }
}
