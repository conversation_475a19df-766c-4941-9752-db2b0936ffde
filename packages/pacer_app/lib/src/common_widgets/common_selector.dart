import 'package:flutter/material.dart';

import '../themes/app_color_scheme.dart';

/// ドロップダウンリストセレクター
class CommonSelector<T> extends StatelessWidget {
  /// 標準コンストラクタ
  const CommonSelector({
    super.key,
    required this.candidates,
    required this.value,
    required this.onChanged,
    required this.itemTextBuilder,
  });

  /// すべての選択可能なリスト
  final List<T> candidates;

  /// 現在選択している値
  final T? value;

  /// 原則に従って値が返された後のコールバック
  final void Function(T?) onChanged;

  /// 単一プロジェクトのコンストラクター
  final String Function(T? candidate) itemTextBuilder;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    // showMenuが閉じた後、自動的にフォーカスが戻るため、focusScopeNodeはこの動作を防止するために使用されます
    final focusScopeNode = FocusScopeNode();
    final anchorKey = GlobalKey();
    return ColoredBox(
      color: colors.button,
      child: FocusScope(
        node: focusScopeNode,
        child: TextField(
          key: anchorKey,
          controller: TextEditingController(
            text: itemTextBuilder(value),
          ),
          style: texts.titleLarge,
          onTap: () {
            showMenu(
              context: context,
              color: colors.button,
              position: RelativeRect.fromLTRB(
                100,
                _getOffsetY(anchorKey),
                80,
                100,
              ),
              items: candidates
                  .map(
                    (candidate) => PopupMenuItem(
                      onTap: () => onChanged(candidate),
                      value: candidate,
                      child: Wrap(
                        children: [
                          Text(
                            itemTextBuilder(candidate),
                            style: texts.titleLarge,
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
            ).then((value) => focusScopeNode.requestFocus(FocusNode()));
          },
          readOnly: true,
          decoration: InputDecoration(
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: colors.primary.withOpacity(0),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: colors.primary.withOpacity(0),
              ),
            ),
            contentPadding: const EdgeInsets.all(10),
            hintText: '選択してください',
            hintStyle: texts.bodyLarge?.copyWith(color: colors.subText),
            prefixIconConstraints: const BoxConstraints(),
            suffixIcon: const Icon(Icons.arrow_drop_down),
            border: const OutlineInputBorder(),
            errorStyle: const TextStyle(fontSize: 0, height: 0),
          ),
        ),
      ),
    );
  }

  double _getOffsetY(GlobalKey anchorKey) {
    final renderObject = anchorKey.currentContext?.findRenderObject();
    if (renderObject == null) {
      return 200;
    }
    final renderBox = renderObject as RenderBox;

    return renderBox.localToGlobal(Offset(0, renderBox.size.height)).dy;
  }
}
