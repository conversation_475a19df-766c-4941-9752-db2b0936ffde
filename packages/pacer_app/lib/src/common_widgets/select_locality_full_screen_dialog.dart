import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../features/fixed_assets/presentation/common_widgets/dialog_helpers.dart';
import 'pacer_back_button.dart';
import 'select_locality_dialog.dart';

/// 多文字索引リストがあるフルスクリーンダイアログから産地を複数選択するために表示します。
/// このダイアログの「産地は商品に記載」という項目は強制的に単一選択を要求せず，[selectLocalityDialog]とは異なります。
/// [allLocalityList] すべての産地、その中にはユーザーが索引を付けた文字があります。
/// [top10LocalityList] トップ10の産地リスト
/// [onChanged] 産地が変更された際のコールバックイベント
void showSelectFullLocalityDialog({
  required BuildContext context,
  required List<String> allLocalityList,
  required List<String> top10LocalityList,
  required void Function(String) onChanged,
}) {
  showDialog<void>(
    context: context,
    builder: (context) {
      return Dialog.fullscreen(
        child: _FullLocalityList(
          allLocalityList: allLocalityList,
          top10LocalityList: top10LocalityList,
          onChanged: onChanged,
        ),
      );
    },
  );
}

class _FullLocalityList extends HookConsumerWidget {
  _FullLocalityList({
    required this.allLocalityList,
    required this.top10LocalityList,
    required this.onChanged,
  });

  /// すべての産地、その中にはユーザーが索引を付けた文字があります。
  final List<String> allLocalityList;

  /// トップ10の産地リスト。
  /// このダイアログの「産地は商品に記載」という項目は強制的に単一選択を要求せず。
  final List<String> top10LocalityList;
  final void Function(String) onChanged;

  final indexLetter = ['ア', 'カ', 'サ', 'タ', 'ナ', 'ハ', 'マ', 'ヤ', 'ラ', 'ワ'];

  final ItemScrollController itemScrollController = ItemScrollController();
  final ItemPositionsListener itemPositionsListener = ItemPositionsListener.create();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    final selectedLocalitySet = useState(<String>{});
    final isTop10Shown = useState(false);
    final shownLocality = useState(allLocalityList);

    useEffect(
      () {
        if (shownLocality.value.isEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            showAlertDialog(
              context: context,
              title: 'CyPLATEのコードではTOP10を表示することができません。必要な場合は商品のJANコードで取得してください。',
            );
          });
          shownLocality.value = allLocalityList;
          isTop10Shown.value = false;
        }
        return;
      },
      [shownLocality.value],
    );

    useEffect(
      () {
        selectedLocalitySet.value = {};
        return;
      },
      [isTop10Shown.value],
    );

    return Dialog.fullscreen(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('産地一覧'),
          actions: [
            ElevatedButton(
              onPressed: () => {
                shownLocality.value = isTop10Shown.value ? allLocalityList : top10LocalityList,
                isTop10Shown.value = !isTop10Shown.value,
              },
              style: ButtonStyle(
                elevation: WidgetStateProperty.all(0),
                backgroundColor: WidgetStateProperty.all(colors.primary),
                foregroundColor: WidgetStateProperty.all(colors.onPrimary),
              ),
              child: Text(
                isTop10Shown.value ? '全て' : 'TOP10',
                style: texts.titleLarge?.copyWith(
                  color: colors.onPrimary,
                ),
              ),
            ),
            const Gap(10),
          ],
        ),
        bottomNavigationBar: BottomAppBar(
          height: isTop10Shown.value ? null : 120,
          child: Column(
            children: [
              Visibility(
                visible: !isTop10Shown.value,
                child: Row(
                  children: indexLetter
                      .map(
                        (char) => Expanded(
                          child: TextButton(
                            onPressed: () => itemScrollController.jumpTo(
                              index: shownLocality.value.indexOf(char),
                            ),
                            style: ButtonStyle(
                              padding: WidgetStateProperty.all(
                                EdgeInsets.zero,
                              ),
                            ),
                            child: Text(
                              char,
                              style: texts.titleLarge?.copyWith(color: colors.primary),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const PacerBackButton(),
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: () => selectedLocalitySet.value = {},
                        child: const Text('取消'),
                      ),
                      const Gap(10),
                      ElevatedButton(
                        onPressed: () {
                          if (selectedLocalitySet.value.isEmpty) {
                            showAlertDialog(
                              context: context,
                              title: '産地を省略することはできません。',
                            );
                            return;
                          }
                          onChanged(selectedLocalitySet.value.join('､'));
                          context.pop();
                        },
                        style: ButtonStyle(
                          elevation: WidgetStateProperty.all(0),
                          backgroundColor: WidgetStateProperty.all(colors.primary),
                          foregroundColor: WidgetStateProperty.all(colors.onPrimary),
                        ),
                        child: const Text('確定'),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
        body: ScrollablePositionedList.builder(
          itemScrollController: itemScrollController,
          itemPositionsListener: itemPositionsListener,
          itemCount: shownLocality.value.length,
          itemBuilder: (context, index) {
            final char = shownLocality.value[index];
            final isSelected = selectedLocalitySet.value.contains(char);
            if (indexLetter.contains(char)) {
              return ListTile(title: Text(char));
            }
            return CheckboxListTile(
              dense: true,
              value: isSelected,
              onChanged: (_) => _onCheckBoxChange(
                checked: isSelected,
                value: char,
                selectedLocalitySet: selectedLocalitySet,
              ),
              title: Text(char, style: texts.titleLarge),
            );
          },
        ),
      ),
    );
  }

  void _onCheckBoxChange({
    required bool checked,
    required String value,
    required ValueNotifier<Set<String>> selectedLocalitySet,
  }) {
    final updatedSet = Set<String>.from(selectedLocalitySet.value);
    if (checked) {
      updatedSet.remove(value);
    } else {
      updatedSet.add(value);
    }
    selectedLocalitySet.value = updatedSet;
  }
}
