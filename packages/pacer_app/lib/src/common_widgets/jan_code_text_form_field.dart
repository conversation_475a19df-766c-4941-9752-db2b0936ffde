import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../features/device/application/pacer_service.dart';
import '../themes/app_color_scheme.dart';
import '../utils/adaptive_number_input_type.dart';

/// JanCodeを入力orスキャンTextField
class JanCodeTextFormField extends HookConsumerWidget {
  /// init
  const JanCodeTextFormField(
    this.prefixText, {
    super.key,
    this.autofocus = false,
    required this.controller,
    required this.focusNode,
    this.onFieldSubmitted,
    this.onScanCodeReceived,
    this.onInputCodeReceived,
    required this.routeFullPath,
    this.lineColor,
    this.textStyle,
    this.backGroundColor,
    this.contentPadding,
    this.inputLimitNumber = 32,
  });

  /// 20と26 JANは処理する必要があります」となります
  static final List<int> toProcessJanCodeLengths = [20, 26];

  /// textFieldのautofocusフラグ
  final bool autofocus;

  /// 左表示タイトル
  final String prefixText;

  /// controller
  final TextEditingController controller;

  /// focusNode
  final FocusNode focusNode;

  /// textFieldの編集完了まだscan完了のcallback
  final void Function(String)? onFieldSubmitted;

  /// scan完了のcallback
  final void Function(String)? onScanCodeReceived;

  /// textFieldの編集完了のcallback
  final void Function(String)? onInputCodeReceived;

  /// routeのcurrent path、合わない場合、上記callbackしない。
  final String routeFullPath;

  /// lineの色
  final Color? lineColor;

  /// 文字のStyle
  final TextStyle? textStyle;

  /// 背景色
  final Color? backGroundColor;

  /// Padding情報
  final EdgeInsetsGeometry? contentPadding;

  /// 最大入力桁数
  final int inputLimitNumber;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    /// Codeをscanの監視func
    ref.listen(scanCodeProvider, (_, next) {
      final location = GoRouterState.of(context).fullPath;
      if (location == routeFullPath) {
        final code = next.asData?.valueOrNull;
        if (code == null) return;

        focusNode.unfocus();
        _handleProductCode(value: code, isScan: true);
      }
    });

    return Focus(
      focusNode: focusNode,
      child: TextFormField(
        onTapOutside: (_) => focusNode.unfocus(),
        autofocus: autofocus,
        controller: controller,
        textAlign: TextAlign.end,
        onFieldSubmitted: (String value) => _handleProductCode(value: value, isScan: false),
        keyboardType: TextInputType.number.withEnter(),
        maxLines: 2,
        minLines: 1,
        style: textStyle ?? texts.titleMedium,
        decoration: InputDecoration(
          contentPadding: contentPadding ?? const EdgeInsets.all(10),
          prefixIcon: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              prefixText,
              style: textStyle ?? texts.titleMedium,
            ),
          ),
          prefixIconConstraints: const BoxConstraints(),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: lineColor ?? colorScheme.line),
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide(color: lineColor ?? colorScheme.line),
          ),
          errorStyle: const TextStyle(fontSize: 0, height: 0),
          filled: backGroundColor != null,
          fillColor: backGroundColor,
        ),
        inputFormatters: [
          LengthLimitingTextInputFormatter(inputLimitNumber),
          FilteringTextInputFormatter.digitsOnly,
        ],
      ),
    );
  }

  /// controller.textに設置
  /// コールバックを実行
  void _handleProductCode({required String value, required bool isScan}) {
    onFieldSubmitted?.call(value);
    isScan ? onScanCodeReceived?.call(value) : onInputCodeReceived?.call(value);
  }
}
