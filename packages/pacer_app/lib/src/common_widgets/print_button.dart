import 'package:flutter/material.dart';

/// 印刷FAB
class PrintFAB extends StatelessWidget {
  /// init
  const PrintFAB({
    super.key,
    required this.isLoading,
    required this.onPressed,
  });

  /// ローディング中か
  final bool isLoading;

  /// 押下時のコールバック
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return switch ((isLoading, onPressed)) {
      (true, _) => const FloatingActionButton(
          onPressed: null,
          child: CircularProgressIndicator(),
        ),
      (false, null) => const SizedBox.shrink(),
      (false, _) => FloatingActionButton.extended(
          onPressed: onPressed,
          label: const Text('印刷'),
          icon: const Icon(Icons.print),
        ),
    };
  }
}
