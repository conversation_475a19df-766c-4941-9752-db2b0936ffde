import 'dart:async';

import 'package:flutter/material.dart';

import '../exceptions/app_exception.dart';
import '../localization/string_hardcoded.dart';

/// ダイアログのキー
const kDialogDefaultKey = Key('dialog-default-key');

/// ダイアログを表示する
Future<bool?> showAlertDialog({
  required BuildContext context,
  required String title,
  Key? key,
  String? content,
  String? cancelActionText,
  String defaultActionText = 'OK',
  bool barrierDismissible = false,
  bool enableHack = false,
}) async {
  return showDialog(
    barrierDismissible: barrierDismissible,
    context: context,
    builder: (context) => GestureDetector(
      onLongPress: enableHack ? () => Navigator.of(context).pop(false) : null,
      child: AlertDialog(
        key: key,
        title: Text(title),
        content: content != null ? Text(content) : null,
        actions: [
          if (cancelActionText != null)
            OutlinedButton(
              child: Text(cancelActionText),
              onPressed: () => Navigator.of(context).pop(false),
            ),
          OutlinedButton(
            key: kDialogDefaultKey,
            child: Text(defaultActionText),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    ),
  );
}

/// 例外を通知するダイアログを表示する
Future<void> showExceptionAlertDialog({
  required BuildContext context,
  required String title,
  required AppException exception,
}) =>
    showAlertDialog(
      context: context,
      title: title,
      content: exception.message,
      defaultActionText: 'OK'.hardcoded,
    );
