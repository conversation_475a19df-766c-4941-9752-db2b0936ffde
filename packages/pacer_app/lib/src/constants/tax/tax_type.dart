import 'package:shinise_core_client/common/v2/common.pb.dart' as buf;

/// 税区分
enum TaxType {
  /// 外税
  exclusive,

  /// 内税
  inclusive,

  /// 非課税
  taxFree,

  /// その他
  other;

  /// gRPCのTaxTypeを取得する
  /// [buf.TaxType]を[TaxType]に変換する
  /// [buf.TaxType]が[buf.TaxType.TAX_TYPE_EXCLUDING_TAX]の場合は[TaxType.exclusive]を返す
  /// [buf.TaxType]が[buf.TaxType.TAX_TYPE_INCLUDING_TAX]の場合は[TaxType.inclusive]を返す
  /// [buf.TaxType]が[buf.TaxType.TAX_TYPE_NON_TAXABLE]の場合は[TaxType.taxFree]を返す
  /// [buf.TaxType]が[buf.TaxType.TAX_TYPE_UNSPECIFIED]の場合は[TaxType.other]を返す
  /// [buf.TaxType]が[buf.TaxType]以外の場合は[UnimplementedError]を投げる
  factory TaxType.fromBuf(buf.TaxType taxType) => switch (taxType) {
        buf.TaxType.TAX_TYPE_EXCLUDING_TAX => TaxType.exclusive,
        buf.TaxType.TAX_TYPE_INCLUDING_TAX => TaxType.inclusive,
        buf.TaxType.TAX_TYPE_NON_TAXABLE => TaxType.taxFree,
        buf.TaxType.TAX_TYPE_UNSPECIFIED => TaxType.other,
        buf.TaxType() => throw UnimplementedError(),
      };

  /// [TaxType]を[buf.TaxType]に変換する
  /// [TaxType.exclusive]の場合は[buf.TaxType.TAX_TYPE_EXCLUDING_TAX]を返す
  /// [TaxType.inclusive]の場合は[buf.TaxType.TAX_TYPE_INCLUDING_TAX]を返す
  /// [TaxType.taxFree]の場合は[buf.TaxType.TAX_TYPE_NON_TAXABLE]を返す
  /// [TaxType.other]の場合は[buf.TaxType.TAX_TYPE_UNSPECIFIED]を返す
  buf.TaxType toBuf() => switch (this) {
        TaxType.exclusive => buf.TaxType.TAX_TYPE_EXCLUDING_TAX,
        TaxType.inclusive => buf.TaxType.TAX_TYPE_INCLUDING_TAX,
        TaxType.taxFree => buf.TaxType.TAX_TYPE_NON_TAXABLE,
        TaxType.other => buf.TaxType.TAX_TYPE_UNSPECIFIED
      };
}

extension TaxTypeExtension on TaxType {
  /// 文字列を取得する。
  String get name {
    switch (this) {
      case TaxType.exclusive:
        return '外税';
      case TaxType.inclusive:
        return '内税';
      case TaxType.taxFree:
        return '非課税';
      case TaxType.other:
        return 'その他';
    }
  }

  /// マークを取得する。外税なら「(税抜)」、内税なら「(税込)」を返す
  String get mark => switch (this) { TaxType.exclusive => '(税抜)', _ => '(税込)' };
}

extension TaxTypeStringExtension on String {
  /// 文字列を取得する。
  /// 文字列が「外税」の場合はTaxType.exclusiveを返す。
  /// 文字列が「内税」の場合はTaxType.inclusiveを返す。
  /// 文字列が「非課税」の場合はTaxType.taxFreeを返す。
  /// 文字列が「その他」の場合はTaxType.otherを返す。
  /// それ以外の場合はTaxType.otherを返す。
  TaxType get taxType {
    switch (this) {
      case '外税':
        return TaxType.exclusive;
      case '内税':
        return TaxType.inclusive;
      case '非課税':
        return TaxType.taxFree;
      case 'その他':
        return TaxType.other;
      default:
        return TaxType.other;
    }
  }
}

extension TaxTypeIntExtension on int {
  TaxType get taxType {
    switch (this) {
      case 0:
        return TaxType.exclusive;
      case 1:
        return TaxType.inclusive;
      case 2:
        return TaxType.taxFree;
      case 3:
        return TaxType.other;
      default:
        return TaxType.other;
    }
  }
}
