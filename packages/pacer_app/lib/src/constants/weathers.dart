import 'package:collection/collection.dart';

import '../../gen/assets.gen.dart';

/// shinise 天気情報とアイコンの場所
enum Weather {
  /// 晴れ
  sunny(code: '0001', title: '晴れ'),

  /// 晴時々曇
  sunnyPartlyCloudy(code: '0002', title: '晴時々曇'),

  /// 晴時々雨
  sunnyPartlyRainy(code: '0003', title: '晴時々雨'),

  /// 晴時々雪
  sunnyPartlySnowy(code: '0004', title: '晴時々雪'),

  /// 晴のち曇
  sunnyThenCloudy(code: '0005', title: '晴のち曇'),

  /// 晴のち雨
  sunnyThenRainy(code: '0006', title: '晴のち雨'),

  /// 晴のち雪
  sunnyThenSnowy(code: '0007', title: '晴のち雪'),

  /// 曇り
  cloudy(code: '0008', title: '曇り'),

  /// 曇時々晴
  cloudyPartlySunny(code: '0009', title: '曇時々晴'),

  /// 曇時々雨
  cloudyPartlyRainy(code: '0010', title: '曇時々雨'),

  /// 曇時々雪
  cloudyPartlySnowy(code: '0011', title: '曇時々雪'),

  /// 曇のち晴'
  cloudyThenSunny(code: '0012', title: '曇のち晴'),

  /// 曇のち雨
  cloudyThenRainy(code: '0013', title: '曇のち雨'),

  /// 曇のち雪
  cloudyThenSnowy(code: '0014', title: '曇のち雪'),

  /// 雨
  rainy(code: '0015', title: '雨'),

  /// 雨時々晴
  rainyPartlySunny(code: '0016', title: '雨時々晴'),

  /// 雨時々曇
  rainyPartlyCloudy(code: '0017', title: '雨時々曇'),

  /// 雨時々雪
  rainyPartlySnowy(code: '0018', title: '雨時々雪'),

  /// 雨のち晴
  rainyThenSunny(code: '0019', title: '雨のち晴'),

  /// 雨のち曇
  rainyThenCloudy(code: '0020', title: '雨のち曇'),

  /// 雨のち雪
  rainyThenSnowy(code: '0021', title: '雨のち雪'),

  /// 雨で暴風を伴う
  rainyAndWindy(code: '0022', title: '雨で暴風を伴う'),

  /// 雪
  snowy(code: '0023', title: '雪'),

  /// 雪時々晴
  snowyPartlySunny(code: '0024', title: '雪時々晴'),

  /// 雪時々曇
  snowyPartlyCloudy(code: '0025', title: '雪時々曇'),

  /// 雪時々雨
  snowyPartlyRainy(code: '0026', title: '雪時々雨'),

  /// 雪のち晴
  snowyThenSunny(code: '0027', title: '雪のち晴'),

  /// 雪のち曇
  snowyThenCloudy(code: '0028', title: '雪のち曇'),

  /// 雪のち雨
  snowyThenRainy(code: '0029', title: '雪のち雨'),

  /// 暴風雪
  snowStorm(code: '0030', title: '暴風雪'),

  /// 暴風雨
  storm(code: '0031', title: '暴風雨'),
  ;

  const Weather({required this.code, required this.title});

  /// 天気コード
  final String code;

  /// 天気名（日本語）
  final String title;

  /// 天気コードから[Weather]を検索
  static Weather? getByCode(String code) => Weather.values.firstWhereOrNull((e) => e.code == code);

  /// 天気名（日本語）から[Weather]を検索
  static Weather? getByTitle(String title) => Weather.values.firstWhereOrNull((e) => e.title == title);

  /// お天気アイコンAssetのpathを取得する
  String iconPath() => switch (this) {
        sunny => Assets.weatherIcon.sunny.path,
        sunnyPartlyCloudy => Assets.weatherIcon.sunnyPartlyCloudy.path,
        sunnyPartlyRainy => Assets.weatherIcon.sunnyPartlyRainy.path,
        sunnyPartlySnowy => Assets.weatherIcon.sunnyPartlySnowy.path,
        sunnyThenCloudy => Assets.weatherIcon.sunnyThenCloudy.path,
        sunnyThenRainy => Assets.weatherIcon.sunnyThenRainy.path,
        sunnyThenSnowy => Assets.weatherIcon.sunnyThenSnowy.path,
        cloudy => Assets.weatherIcon.cloudy.path,
        cloudyPartlySunny => Assets.weatherIcon.cloudyPartlySunny.path,
        cloudyPartlyRainy => Assets.weatherIcon.cloudyPartlyRainy.path,
        cloudyPartlySnowy => Assets.weatherIcon.cloudyPartlySnowy.path,
        cloudyThenSunny => Assets.weatherIcon.cloudyThenSunny.path,
        cloudyThenRainy => Assets.weatherIcon.cloudyThenRainy.path,
        cloudyThenSnowy => Assets.weatherIcon.cloudyThenSnowy.path,
        rainy => Assets.weatherIcon.rainy.path,
        rainyPartlySunny => Assets.weatherIcon.rainyPartlySunny.path,
        rainyPartlyCloudy => Assets.weatherIcon.rainyPartlyCloudy.path,
        rainyPartlySnowy => Assets.weatherIcon.rainyPartlySnowy.path,
        rainyThenSunny => Assets.weatherIcon.rainyThenSunny.path,
        rainyThenCloudy => Assets.weatherIcon.rainyThenCloudy.path,
        rainyThenSnowy => Assets.weatherIcon.rainyThenSnowy.path,
        rainyAndWindy => Assets.weatherIcon.rainyAndWindy.path,
        snowy => Assets.weatherIcon.snowy.path,
        snowyPartlySunny => Assets.weatherIcon.snowyPartlySunny.path,
        snowyPartlyCloudy => Assets.weatherIcon.snowyPartlyCloudy.path,
        snowyPartlyRainy => Assets.weatherIcon.snowyPartlyRainy.path,
        snowyThenSunny => Assets.weatherIcon.snowyThenSunny.path,
        snowyThenCloudy => Assets.weatherIcon.snowyThenCloudy.path,
        snowyThenRainy => Assets.weatherIcon.snowyThenRainy.path,
        snowStorm => Assets.weatherIcon.snowStorm.path,
        storm => Assets.weatherIcon.storm.path,
      };
}
