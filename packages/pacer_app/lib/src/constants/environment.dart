/// 定数値
/// インスタンスを作成しないよう、抽象クラスを使用
library;

import 'dart:io';

import 'package:flutter/services.dart';

abstract class Env {
  // 実験・開発中の機能を使う際に用いるフラグ
  static const bool isExperimental = bool.fromEnvironment('IS_EXPERIMENTAL', defaultValue: false);

  /// WebAPIサーバーのURLとAPI key
  static const tengenURL = String.fromEnvironment('TENGEN_URL');
  static const tengenKey = String.fromEnvironment('TENGEN_KEY');
  static const isEnableMock = bool.fromEnvironment('MOCK');

  /// Scanditのライセンスキー
  /// [appName]に応じたライセンスキーを返す
  /// [appName]が'nonmdm'を含む場合は、非MDM用のライセンスキーを返す
  static String scanditLicenseKey({required String appName}) {
    const scanditLicenseKey = String.fromEnvironment('SCANDIT_LICENSE_KEY');
    const nonmdmScanditLicenseKey = String.fromEnvironment('NONMDM_SCANDIT_LICENSE_KEY');

    return switch (appName.contains('nonmdm')) {
      true => nonmdmScanditLicenseKey,
      false => scanditLicenseKey,
    };
  }

  /// shinise-coreのエンドポイント
  static Uri get _shiniseCoreURL => Uri.parse(const String.fromEnvironment('SHINISE_CORE_URL'));

  /// shiniseのエンドポイント
  static Uri get _shiniseURL => Uri.parse(const String.fromEnvironment('SHINISE_URL'));

  /// API基本URL。主にこのエンドポイントにリクエストする
  static Uri getApiBaseUrl({bool isExperimental = false}) =>
      switch (isExperimental) { true => _shiniseURL, false => _shiniseCoreURL };

  static Uri get stocktakeDBURL =>
      Uri.parse('${const String.fromEnvironment('SHINISE_CORE_URL')}/~trial/TANAOROSI/HTML/Data/DATA/data.zip');

  static Uri freshStocktakeDBURL(String storeCode) {
    final zeroPaddedStoreCode = storeCode.padLeft(4, '0');
    final apiBaseUrl = Env.getApiBaseUrl();
    return Uri.parse(
      '$apiBaseUrl/download/pfinv/Data//DATA_$zeroPaddedStoreCode/data_$zeroPaddedStoreCode.zip',
    );
  }

  /// 本番環境か
  /// AndroidではFlavorで判別できるが、
  /// iOSではFlavorを使っていないので、FLAVOR環境変数で判別する
  static bool get inProduction {
    if (Platform.isIOS) {
      const iosEnv = String.fromEnvironment('FLAVOR');
      return iosEnv == 'prod';
    }
    return (appFlavor == 'prod') || (appFlavor == 'nonmdmProd');
  }

  /// テスト環境か
  static bool get inNotProduction => !inProduction;
}
