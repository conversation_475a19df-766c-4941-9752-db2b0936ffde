<?xml version="1.0" encoding="utf-8"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" d2Version="v0.6.3-HEAD" preserveAspectRatio="xMidYMid meet" viewBox="0 0 3236 1427"><svg id="d2-svg" class="d2-2950824435" width="3236" height="1427" viewBox="-101 -112 3236 1427"><rect x="-101.000000" y="-112.000000" width="3236.000000" height="1427.000000" rx="0.000000" class=" fill-N7" stroke-width="0" /><style type="text/css"><![CDATA[
.d2-2950824435 .text {
	font-family: "d2-2950824435-font-regular";
}
@font-face {
	font-family: d2-2950824435-font-regular;
	src: url("data:application/font-woff;base64,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");
}
.appendix-icon {
	filter: drop-shadow(0px 0px 32px rgba(31, 36, 58, 0.1));
}
.d2-2950824435 .text-bold {
	font-family: "d2-2950824435-font-bold";
}
@font-face {
	font-family: d2-2950824435-font-bold;
	src: url("data:application/font-woff;base64,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");
}
.d2-2950824435 .text-italic {
	font-family: "d2-2950824435-font-italic";
}
@font-face {
	font-family: d2-2950824435-font-italic;
	src: url("data:application/font-woff;base64,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");
}]]></style><style type="text/css"><![CDATA[.shape {
  shape-rendering: geometricPrecision;
  stroke-linejoin: round;
}
.connection {
  stroke-linecap: round;
  stroke-linejoin: round;
}
.blend {
  mix-blend-mode: multiply;
  opacity: 0.5;
}

		.d2-2950824435 .fill-N1{fill:#0A0F25;}
		.d2-2950824435 .fill-N2{fill:#676C7E;}
		.d2-2950824435 .fill-N3{fill:#9499AB;}
		.d2-2950824435 .fill-N4{fill:#CFD2DD;}
		.d2-2950824435 .fill-N5{fill:#DEE1EB;}
		.d2-2950824435 .fill-N6{fill:#EEF1F8;}
		.d2-2950824435 .fill-N7{fill:#FFFFFF;}
		.d2-2950824435 .fill-B1{fill:#0D32B2;}
		.d2-2950824435 .fill-B2{fill:#0D32B2;}
		.d2-2950824435 .fill-B3{fill:#E3E9FD;}
		.d2-2950824435 .fill-B4{fill:#E3E9FD;}
		.d2-2950824435 .fill-B5{fill:#EDF0FD;}
		.d2-2950824435 .fill-B6{fill:#F7F8FE;}
		.d2-2950824435 .fill-AA2{fill:#4A6FF3;}
		.d2-2950824435 .fill-AA4{fill:#EDF0FD;}
		.d2-2950824435 .fill-AA5{fill:#F7F8FE;}
		.d2-2950824435 .fill-AB4{fill:#EDF0FD;}
		.d2-2950824435 .fill-AB5{fill:#F7F8FE;}
		.d2-2950824435 .stroke-N1{stroke:#0A0F25;}
		.d2-2950824435 .stroke-N2{stroke:#676C7E;}
		.d2-2950824435 .stroke-N3{stroke:#9499AB;}
		.d2-2950824435 .stroke-N4{stroke:#CFD2DD;}
		.d2-2950824435 .stroke-N5{stroke:#DEE1EB;}
		.d2-2950824435 .stroke-N6{stroke:#EEF1F8;}
		.d2-2950824435 .stroke-N7{stroke:#FFFFFF;}
		.d2-2950824435 .stroke-B1{stroke:#0D32B2;}
		.d2-2950824435 .stroke-B2{stroke:#0D32B2;}
		.d2-2950824435 .stroke-B3{stroke:#E3E9FD;}
		.d2-2950824435 .stroke-B4{stroke:#E3E9FD;}
		.d2-2950824435 .stroke-B5{stroke:#EDF0FD;}
		.d2-2950824435 .stroke-B6{stroke:#F7F8FE;}
		.d2-2950824435 .stroke-AA2{stroke:#4A6FF3;}
		.d2-2950824435 .stroke-AA4{stroke:#EDF0FD;}
		.d2-2950824435 .stroke-AA5{stroke:#F7F8FE;}
		.d2-2950824435 .stroke-AB4{stroke:#EDF0FD;}
		.d2-2950824435 .stroke-AB5{stroke:#F7F8FE;}
		.d2-2950824435 .background-color-N1{background-color:#0A0F25;}
		.d2-2950824435 .background-color-N2{background-color:#676C7E;}
		.d2-2950824435 .background-color-N3{background-color:#9499AB;}
		.d2-2950824435 .background-color-N4{background-color:#CFD2DD;}
		.d2-2950824435 .background-color-N5{background-color:#DEE1EB;}
		.d2-2950824435 .background-color-N6{background-color:#EEF1F8;}
		.d2-2950824435 .background-color-N7{background-color:#FFFFFF;}
		.d2-2950824435 .background-color-B1{background-color:#0D32B2;}
		.d2-2950824435 .background-color-B2{background-color:#0D32B2;}
		.d2-2950824435 .background-color-B3{background-color:#E3E9FD;}
		.d2-2950824435 .background-color-B4{background-color:#E3E9FD;}
		.d2-2950824435 .background-color-B5{background-color:#EDF0FD;}
		.d2-2950824435 .background-color-B6{background-color:#F7F8FE;}
		.d2-2950824435 .background-color-AA2{background-color:#4A6FF3;}
		.d2-2950824435 .background-color-AA4{background-color:#EDF0FD;}
		.d2-2950824435 .background-color-AA5{background-color:#F7F8FE;}
		.d2-2950824435 .background-color-AB4{background-color:#EDF0FD;}
		.d2-2950824435 .background-color-AB5{background-color:#F7F8FE;}
		.d2-2950824435 .color-N1{color:#0A0F25;}
		.d2-2950824435 .color-N2{color:#676C7E;}
		.d2-2950824435 .color-N3{color:#9499AB;}
		.d2-2950824435 .color-N4{color:#CFD2DD;}
		.d2-2950824435 .color-N5{color:#DEE1EB;}
		.d2-2950824435 .color-N6{color:#EEF1F8;}
		.d2-2950824435 .color-N7{color:#FFFFFF;}
		.d2-2950824435 .color-B1{color:#0D32B2;}
		.d2-2950824435 .color-B2{color:#0D32B2;}
		.d2-2950824435 .color-B3{color:#E3E9FD;}
		.d2-2950824435 .color-B4{color:#E3E9FD;}
		.d2-2950824435 .color-B5{color:#EDF0FD;}
		.d2-2950824435 .color-B6{color:#F7F8FE;}
		.d2-2950824435 .color-AA2{color:#4A6FF3;}
		.d2-2950824435 .color-AA4{color:#EDF0FD;}
		.d2-2950824435 .color-AA5{color:#F7F8FE;}
		.d2-2950824435 .color-AB4{color:#EDF0FD;}
		.d2-2950824435 .color-AB5{color:#F7F8FE;}.appendix text.text{fill:#0A0F25}.md{--color-fg-default:#0A0F25;--color-fg-muted:#676C7E;--color-fg-subtle:#9499AB;--color-canvas-default:#FFFFFF;--color-canvas-subtle:#EEF1F8;--color-border-default:#0D32B2;--color-border-muted:#0D32B2;--color-neutral-muted:#EEF1F8;--color-accent-fg:#0D32B2;--color-accent-emphasis:#0D32B2;--color-attention-subtle:#676C7E;--color-danger-fg:red;}.sketch-overlay-B1{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-B2{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-B3{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B6{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AA2{fill:url(#streaks-dark);mix-blend-mode:overlay}.sketch-overlay-AA4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AA5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AB4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AB5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N1{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-N2{fill:url(#streaks-dark);mix-blend-mode:overlay}.sketch-overlay-N3{fill:url(#streaks-normal);mix-blend-mode:color-burn}.sketch-overlay-N4{fill:url(#streaks-normal);mix-blend-mode:color-burn}.sketch-overlay-N5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N6{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N7{fill:url(#streaks-bright);mix-blend-mode:darken}.light-code{display: block}.dark-code{display: none}]]></style><g id="Legend"><g class="shape" ><rect x="0.000000" y="29.000000" width="692.000000" height="584.000000" class=" stroke-B1 fill-B4" style="stroke-width:2;" /></g><text x="346.000000" y="16.000000" class="text fill-N1" style="text-anchor:middle;font-size:28px">Legend</text></g><g id="checkLocalityConfirmControllerProvider"><g class="shape" ><rect x="1637.000000" y="461.000000" width="367.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1820.500000" y="499.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">checkLocalityConfirmControllerProvider</text><title>産地チェッ-責任者確認</title></g><g id="checkLocalityHistoryControllerProvider"><g class="shape" ><rect x="1215.000000" y="461.000000" width="362.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1396.000000" y="499.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">checkLocalityHistoryControllerProvider</text><title>産地チェッ</title></g><g id="checkLocalityServiceProvider"><g class="shape" ><rect x="1253.000000" y="100.000000" width="289.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1397.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">checkLocalityServiceProvider</text><title>provider生成コード</title></g><g id="checkLocalityConfirmHistoryControllerProvider"><g class="shape" ><rect x="736.000000" y="461.000000" width="419.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="945.500000" y="499.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">checkLocalityConfirmHistoryControllerProvider</text><title>責任者確認-履歴取得</title></g><g id="checkLocalityControllerProvider"><g class="shape" ><rect x="2440.000000" y="100.000000" width="310.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2595.000000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">checkLocalityControllerProvider</text><title>産地チェッ</title></g><g id="scanCodeProvider"><g class="shape" ><rect x="2810.000000" y="100.000000" width="207.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2913.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">scanCodeProvider</text><title>カメラやバーコードスキャナでスキャンしたコードの状態</title></g><g id="CheckLocalityTopPage"><g class="shape" ><ellipse rx="160.500000" ry="160.500000" cx="2068.500000" cy="1032.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2068.500000" y="1038.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">CheckLocalityTopPage</text><title>産地チェックメニュー</title></g><g id="CheckLocalityHistoryPage"><g class="shape" ><ellipse rx="178.500000" ry="178.500000" cx="1669.500000" cy="1032.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1669.500000" y="1038.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">CheckLocalityHistoryPage</text><title>産地チェッ履歴画面</title></g><g id="_BottomBar"><g class="shape" ><ellipse rx="85.500000" ry="85.500000" cx="1345.500000" cy="1032.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1345.500000" y="1038.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">_BottomBar</text></g><g id="CheckLocalityConfirmPage"><g class="shape" ><ellipse rx="182.000000" ry="182.000000" cx="946.000000" cy="1032.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="946.000000" y="1037.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">CheckLocalityConfirmPage</text><title>責任者確認</title></g><g id="CheckLocalityPage"><g class="shape" ><ellipse rx="142.000000" ry="142.000000" cx="2835.000000" cy="494.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2835.000000" y="499.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">CheckLocalityPage</text><title>産地チェッ画面</title></g><g id="_LocalityTable"><g class="shape" ><ellipse rx="97.500000" ry="97.500000" cx="2534.500000" cy="493.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2534.500000" y="499.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">_LocalityTable</text></g><g id="_LocalityDropdownButton"><g class="shape" ><ellipse rx="156.500000" ry="156.500000" cx="2220.500000" cy="493.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2220.500000" y="499.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">_LocalityDropdownButton</text></g><g id="Legend.Type"><g class="shape" ><rect x="30.000000" y="405.000000" width="345.000000" height="178.000000" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="202.500000" y="393.000000" class="text fill-N1" style="text-anchor:middle;font-size:24px">Type</text></g><g id="Legend.Arrows"><g class="shape" ><rect x="417.000000" y="70.000000" width="245.000000" height="487.000000" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="539.500000" y="58.000000" class="text fill-N1" style="text-anchor:middle;font-size:24px">Arrows</text></g><g id="Legend.Type.Widget"><g class="shape" ><ellipse rx="59.000000" ry="59.000000" cx="119.000000" cy="494.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="119.000000" y="499.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Widget</text></g><g id="Legend.Type.Provider"><g class="shape" ><rect x="238.000000" y="461.000000" width="107.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="291.500000" y="499.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Provider</text></g><g id="Legend.Arrows.&#34;.&#34;"><g class="shape" ><rect x="479.000000" y="100.000000" width="49.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="503.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">.</text></g><g id="Legend.Arrows.&#34;..&#34;"><g class="shape" ><rect x="487.000000" y="461.000000" width="54.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="514.000000" y="499.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">..</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[0]"><marker id="mk-2177206569" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" class="connection fill-B2" stroke-width="2" /> </marker><path d="M 529.310064 154.751480 C 600.549988 211.899994 601.104980 321.700012 532.341973 456.934457" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /><text x="600.500000" y="303.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">read:</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[1]"><marker id="mk-3488378134" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" class="connection fill-B1" stroke-width="2" /> </marker><path d="M 518.034196 167.339847 C 538.049988 214.300003 538.291992 321.700012 519.029659 456.540200" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2950824435)" /><text x="538.000000" y="317.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">listen</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[2]"><marker id="mk-3519660172" markerWidth="16.000000" markerHeight="20.000000" refX="10.000000" refY="10.000000" viewBox="0.000000 0.000000 16.000000 20.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 16.000000,10.000000 0.000000,20.000000" class="connection fill-B1" stroke-width="4" /> </marker><path d="M 488.073705 168.259770 C 468.450012 214.300003 471.734985 321.700012 504.016115 453.700378" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /><text x="471.000000" y="318.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">watch:</text></g><g id="(checkLocalityConfirmControllerProvider -&gt; CheckLocalityTopPage)[0]"><path d="M 1867.620344 528.838877 C 2043.650024 665.400024 2088.250000 710.000000 2088.250000 725.000000 C 2088.250000 740.000000 2087.000000 814.599976 2082.597130 866.025515" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityConfirmControllerProvider -&gt; CheckLocalityTopPage)[1]"><path d="M 1835.295512 528.834982 C 1894.500000 665.400024 1909.500000 710.000000 1909.500000 725.000000 C 1909.500000 740.000000 1923.199951 820.000000 1975.739489 896.699987" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityHistoryControllerProvider -&gt; CheckLocalityHistoryPage)[0]"><path d="M 1431.602686 529.139793 C 1565.500000 665.400024 1599.500000 710.000000 1599.500000 725.000000 C 1599.500000 740.000000 1603.199951 812.400024 1615.998486 855.292248" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityHistoryControllerProvider -&gt; CheckLocalityHistoryPage)[1]"><path d="M 1413.631954 528.795037 C 1480.750000 665.400024 1497.750000 710.000000 1497.750000 725.000000 C 1497.750000 740.000000 1510.800049 817.799988 1560.634940 885.774091" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityHistoryControllerProvider -&gt; _BottomBar)[0]"><path d="M 1360.891048 529.133617 C 1226.199951 665.400024 1192.000000 710.000000 1192.000000 725.000000 C 1192.000000 740.000000 1213.199951 832.200012 1294.150682 955.153398" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityHistoryControllerProvider -&gt; _BottomBar)[1]"><path d="M 1348.901669 528.202223 C 1166.500000 665.400024 1120.500000 710.000000 1120.500000 725.000000 C 1120.500000 740.000000 1153.599976 834.200012 1283.218174 968.125727" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityConfirmHistoryControllerProvider -&gt; CheckLocalityConfirmPage)[0]"><path d="M 945.500000 530.000000 C 945.500000 665.400024 945.500000 710.000000 945.500000 725.000000 C 945.500000 740.000000 945.599976 810.000000 945.929999 843.000350" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityConfirmControllerProvider -&gt; _BottomBar)[0]"><path d="M 1789.519879 529.253691 C 1669.900024 665.400024 1639.500000 710.000000 1639.500000 725.000000 C 1639.500000 740.000000 1594.000000 835.799988 1417.501290 974.671512" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityConfirmControllerProvider -&gt; _BottomBar)[1]"><path d="M 1786.106672 528.434796 C 1653.099976 665.400024 1619.500000 710.000000 1619.500000 725.000000 C 1619.500000 740.000000 1577.599976 835.200012 1413.062675 973.427060" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityControllerProvider -&gt; CheckLocalityPage)[0]"><path d="M 2664.933282 166.841640 C 2759.850098 214.300003 2787.600098 300.799988 2799.348695 349.197560" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /></g><g id="(scanCodeProvider -&gt; CheckLocalityPage)[0]"><path d="M 2913.500000 167.500000 C 2913.500000 214.300003 2907.800049 301.799988 2886.437609 357.267269" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityControllerProvider -&gt; CheckLocalityPage)[1]"><path d="M 2655.999064 166.469936 C 2742.250000 214.300003 2769.199951 301.600006 2787.715643 356.211804" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityControllerProvider -&gt; CheckLocalityPage)[2]"><path d="M 2648.943049 166.564700 C 2724.850098 214.300003 2751.000000 302.399994 2776.394229 360.336463" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityControllerProvider -&gt; CheckLocalityPage)[3]"><path d="M 2641.868304 166.675198 C 2707.449951 214.300003 2733.000000 303.399994 2766.117089 365.470886" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityControllerProvider -&gt; CheckLocalityPage)[4]"><path d="M 2634.767004 166.803341 C 2690.050049 214.300003 2715.199951 304.399994 2756.869519 370.614582" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityControllerProvider -&gt; CheckLocalityPage)[5]"><path d="M 2627.628122 166.949407 C 2672.649902 214.300003 2697.600098 305.399994 2748.650979 375.762392" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityControllerProvider -&gt; CheckLocalityPage)[6]"><path d="M 2620.437298 167.109448 C 2655.250000 214.300003 2680.399902 306.399994 2742.440819 380.925818" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityControllerProvider -&gt; _LocalityTable)[0]"><path d="M 2572.422678 167.185045 C 2542.300049 214.300003 2534.600098 308.799988 2534.981656 392.000042" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityControllerProvider -&gt; _LocalityDropdownButton)[0]"><path d="M 2460.560661 165.988841 C 2268.899902 214.300003 2220.600098 297.000000 2220.960012 333.000200" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityServiceProvider -&gt; checkLocalityConfirmControllerProvider)[0]"><path d="M 1544.428561 165.150792 C 1764.699951 214.100006 1820.500000 321.700012 1820.500000 453.500000" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityServiceProvider -&gt; checkLocalityHistoryControllerProvider)[0]"><path d="M 1396.950823 168.499597 C 1396.199951 214.300003 1396.000000 321.700012 1396.000000 453.500000" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /></g><g id="(checkLocalityServiceProvider -&gt; checkLocalityConfirmHistoryControllerProvider)[0]"><path d="M 1249.812263 163.515030 C 1006.950012 213.781006 945.500000 321.700012 945.500000 453.500000" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2950824435)" /></g><g transform="translate(1988 445)" class="appendix-icon"><title>産地チェッ-責任者確認</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1561 445)" class="appendix-icon"><title>産地チェッ</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1526 84)" class="appendix-icon"><title>provider生成コード</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1139 445)" class="appendix-icon"><title>責任者確認-履歴取得</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2734 84)" class="appendix-icon"><title>産地チェッ</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(3001 84)" class="appendix-icon"><title>カメラやバーコードスキャナでスキャンしたコードの状態</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2166 903)" class="appendix-icon"><title>産地チェックメニュー</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1780 890)" class="appendix-icon"><title>産地チェッ履歴画面</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1059 887)" class="appendix-icon"><title>責任者確認</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2919 378)" class="appendix-icon"><title>産地チェッ画面</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><mask id="d2-2950824435" maskUnits="userSpaceOnUse" x="-101" y="-112" width="3236" height="1427">
<rect x="-101" y="-112" width="3236" height="1427" fill="white"></rect>
<rect x="304.500000" y="-12.000000" width="83" height="36" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1675.500000" y="483.500000" width="290" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1253.500000" y="483.500000" width="285" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1291.500000" y="122.500000" width="212" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="774.500000" y="483.500000" width="342" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2478.500000" y="122.500000" width="233" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2848.500000" y="122.500000" width="130" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1988.000000" y="1022.000000" width="161" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1576.000000" y="1022.000000" width="187" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1302.000000" y="1022.000000" width="87" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="850.000000" y="1021.500000" width="192" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2767.500000" y="483.500000" width="135" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2482.500000" y="483.000000" width="104" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2126.500000" y="483.000000" width="188" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="178.000000" y="369.000000" width="49" height="31" fill="rgba(0,0,0,0.75)"></rect>
<rect x="504.500000" y="34.000000" width="70" height="31" fill="rgba(0,0,0,0.75)"></rect>
<rect x="94.000000" y="483.500000" width="50" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="260.500000" y="483.500000" width="62" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="501.500000" y="122.500000" width="4" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="509.500000" y="483.500000" width="9" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="583.000000" y="287.000000" width="35" height="21" fill="black"></rect>
<rect x="520.000000" y="301.000000" width="36" height="21" fill="black"></rect>
<rect x="449.000000" y="302.000000" width="44" height="21" fill="black"></rect>
</mask></svg></svg>