// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_locality_confirm_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkLocalityConfirmHistoryControllerHash() => r'69453d96a144cb394c720a69e3330446667fc856';

/// 責任者確認-履歴取得
///
/// Copied from [checkLocalityConfirmHistoryController].
@ProviderFor(checkLocalityConfirmHistoryController)
final checkLocalityConfirmHistoryControllerProvider = AutoDisposeFutureProvider<ConfirmHistory>.internal(
  checkLocalityConfirmHistoryController,
  name: r'checkLocalityConfirmHistoryControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$checkLocalityConfirmHistoryControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CheckLocalityConfirmHistoryControllerRef = AutoDisposeFutureProviderRef<ConfirmHistory>;
String _$checkLocalityConfirmStateHash() => r'0fa71873ffdc730bad7936a5104134358bfba4fe';

/// 責任者確認-責任者確認の実行、およびその状態を管理する
///
/// Copied from [CheckLocalityConfirmState].
@ProviderFor(CheckLocalityConfirmState)
final checkLocalityConfirmStateProvider =
    AutoDisposeNotifierProvider<CheckLocalityConfirmState, AsyncValue<void>>.internal(
  CheckLocalityConfirmState.new,
  name: r'checkLocalityConfirmStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkLocalityConfirmStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckLocalityConfirmState = AutoDisposeNotifier<AsyncValue<void>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
