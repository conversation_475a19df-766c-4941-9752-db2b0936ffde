// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unconfirm_item_count_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchUnconfirmItemCountHash() => r'42adc7c7d3ad63fc355dc44c3c21a892541c37cb';

/// Get the total number of cases that need to be confirmed today
///
/// Copied from [fetchUnconfirmItemCount].
@ProviderFor(fetchUnconfirmItemCount)
final fetchUnconfirmItemCountProvider = AutoDisposeFutureProvider<int>.internal(
  fetchUnconfirmItemCount,
  name: r'fetchUnconfirmItemCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fetchUnconfirmItemCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchUnconfirmItemCountRef = AutoDisposeFutureProviderRef<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
