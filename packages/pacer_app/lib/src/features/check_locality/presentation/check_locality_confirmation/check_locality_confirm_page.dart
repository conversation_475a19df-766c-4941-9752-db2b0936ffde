import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../common_widgets/alert_dialogs.dart';
import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../common_widgets/pacer_back_button.dart';
import '../../../../common_widgets/snack_bars.dart';
import '../../../../localization/app_localizations_context.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../application/check_locality_service.dart';
import '../../domain/confirm_history.dart';
import 'check_locality_confirm_controller.dart';
import 'unconfirm_item_count_state.dart';

/// 責任者確認
/// 原産国確認ページでは商品の原産国を追加・
/// 更新することができ、この商品は担当者ページで確認することができます
class CheckLocalityConfirmPage extends ConsumerWidget {
  /// コンストラクタ関数
  const CheckLocalityConfirmPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final confirmHistory = ref.watch(checkLocalityConfirmHistoryControllerProvider);
    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: Text(context.loc.checkThePIC),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: switch (confirmHistory) {
            AsyncData(:final value) => _ConfirmTable(history: value),
            AsyncError() => const SizedBox.shrink(),
            _ => const Center(child: CircularProgressIndicator()),
          },
        ),
      ),
      bottomNavigationBar: const _BottomBar(),
    );
  }
}

class _ConfirmTable extends ConsumerWidget {
  const _ConfirmTable({required this.history});

  final ConfirmHistory history;

  static const _displayDayCount = 7;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    return Table(
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      columnWidths: {
        0: const FractionColumnWidth(0.16),
        ...Map.fromIterables(
          Iterable.generate(_displayDayCount, (k) => k + 1),
          Iterable.generate(
            _displayDayCount,
            (v) => const FractionColumnWidth(0.12),
          ),
        ),
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        TableRow(
          children: [
            const SizedBox.shrink(),
            ...Iterable.generate(
              _displayDayCount,
              (i) => _ColumnHeader(
                date: history.endDate.subtract(
                  Duration(days: _displayDayCount - 1 - i),
                ),
                isLast: i == _displayDayCount - 1,
              ),
            ),
          ],
        ),
        ...history.rows.map(
          (e) => TableRow(
            children: [
              _RowHeader(e.headline.displayText(context)),
              ...e.days.mapIndexed(
                (i, d) => _Cell(
                  value: d,
                  isLast: i == e.days.length - 1,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _RowHeader extends StatelessWidget {
  const _RowHeader(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
        ),
      ),
    );
  }
}

/// day of week は 1 から始まる
enum Weekday {
  /// 不明
  unknown,

  /// 月曜
  monday,

  /// 火曜
  tuesday,

  /// 水曜
  wednesday,

  /// 木曜
  thursday,

  /// 金曜
  friday,

  /// 土曜
  saturday,

  /// 日曜
  sunday;

  /// 表示色
  Color? get color => switch (this) {
        Weekday.saturday => Colors.blue,
        Weekday.sunday => Colors.red,
        _ => null,
      };
}

class _ColumnHeader extends StatelessWidget {
  const _ColumnHeader({required this.date, required this.isLast});

  final DateTime date;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        DateFormat.E(context.loc.localeName).format(date),
        textAlign: TextAlign.center,
        style: texts.titleMedium?.copyWith(
          fontWeight: isLast ? FontWeight.bold : null,
          color: Weekday.values[date.weekday].color,
        ),
      ),
    );
  }
}

class _Cell extends StatelessWidget {
  const _Cell({required this.value, required this.isLast});

  final int value;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          value.toString(),
          textAlign: TextAlign.right,
          style: texts.titleMedium?.copyWith(
            fontWeight: isLast ? FontWeight.bold : null,
          ),
        ),
      ),
    );
  }
}

class _BottomBar extends ConsumerWidget {
  const _BottomBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final confirmHistory = ref.watch(checkLocalityConfirmHistoryControllerProvider).asData?.value;
    final responsiblePerson = ref.watch(checkLocalityServiceProvider).caller?.userCode ?? '';
    final unconfirmCount = ref.watch(fetchUnconfirmItemCountProvider);
    final notifier = ref.watch(checkLocalityConfirmStateProvider.notifier);
    return BottomAppBar(
      child: Row(
        children: [
          const PacerBackButton(),
          const Spacer(),
          switch (unconfirmCount) {
            AsyncData(isLoading: false, :final value) when value > 0 => TextButton(
                onPressed: () => _showConfirmDialog(
                  ref,
                  content: confirmHistory?.contains(
                            responsiblePerson: responsiblePerson,
                          ) ??
                          false
                      ? context.loc.confirmSamePIC
                      : context.loc.confirmCheckLocality,
                  onConfirmed: () async {
                    await notifier.confirmLocalityCheckAsResponsible(
                      onSuccess: () => showSnackBar(
                        context,
                        context.loc.success,
                      ),
                    );
                  },
                ),
                child: Text(context.loc.confirmation),
              ),
            _ => const SizedBox.shrink(),
          },
        ],
      ),
    );
  }

  Future<void> _showConfirmDialog(
    WidgetRef ref, {
    required String content,
    VoidCallback? onConfirmed,
  }) async {
    final context = ref.context;

    final confirmed = await showAlertDialog(
          context: context,
          title: context.loc.message,
          content: content,
          cancelActionText: context.loc.no,
          defaultActionText: context.loc.yes,
        ) ??
        false;

    if (confirmed) onConfirmed?.call();
  }
}
