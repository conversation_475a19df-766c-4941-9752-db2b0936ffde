import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../app/application/global_loading_service.dart';
import '../../application/check_locality_service.dart';
import '../../domain/confirm_history.dart';
import 'unconfirm_item_count_state.dart';

part 'check_locality_confirm_controller.g.dart';

/// 責任者確認-履歴取得
@riverpod
Future<ConfirmHistory> checkLocalityConfirmHistoryController(
  CheckLocalityConfirmHistoryControllerRef ref,
) {
  return ref.watch(checkLocalityServiceProvider).getConfirmHistory();
}

/// 責任者確認-責任者確認の実行、およびその状態を管理する
@riverpod
class CheckLocalityConfirmState extends _$CheckLocalityConfirmState {
  @override
  AsyncValue<void> build() {
    ref.showGlobalLoading();
    return const AsyncData(null);
  }

  /// 責任者確認-確認ボタン-
  /// 責任者として、産地チェック作業を確定する
  Future<void> confirmLocalityCheckAsResponsible({
    VoidCallback? onSuccess,
  }) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(globalLoadingServiceProvider.notifier).wrap(
            ref.read(checkLocalityServiceProvider).confirmLocalityCheckAsResponsible(),
          ),
    );
    if (!state.hasError) {
      ref
        ..invalidate(fetchUnconfirmItemCountProvider)
        ..invalidate(checkLocalityConfirmHistoryControllerProvider);
      onSuccess?.call();
    }
  }
}
