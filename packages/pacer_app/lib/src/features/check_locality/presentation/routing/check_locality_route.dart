import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../routing/app_router.dart';
import '../check_locality/check_locality_page.dart';
import '../check_locality_confirmation/check_locality_confirm_page.dart';
import '../check_locality_history/check_locality_history_page.dart';
import '../top_page/check_locality_top_page.dart';

/// 産地チェックメニューroute
class CheckLocalityTopPageRoute extends GoRouteData {
  /// コンストラクタ関数
  const CheckLocalityTopPageRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const CheckLocalityTopPage();
}

/// 産地チェックroute
class CheckLocalityRoute extends GoRouteData {
  /// コンストラクタ関数
  const CheckLocalityRoute([this.code]);

  /// 編集項目のCyPLATE CODEまたはJAN CODE
  /// ホームページからアクセスした場合、これは null です
  final String? code;

  @override
  Widget build(BuildContext context, GoRouterState state) => CheckLocalityPage(code);

  /// 表示中のページが、産地チェック画面であればtrue。異なればfalse。
  bool isEqualFullPath(BuildContext context) {
    final state = GoRouterState.of(context);
    return location == state.fullPath;
  }
}

/// 産地チェックroute
class CheckLocalityHistoryRoute extends GoRouteData {
  /// コンストラクタ関数
  const CheckLocalityHistoryRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const CheckLocalityHistoryPage();
}

/// 産地チェック/責任者確認route
class CheckLocalityConfirmRoute extends GoRouteData {
  /// コンストラクタ関数
  const CheckLocalityConfirmRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const CheckLocalityConfirmPage();
}
