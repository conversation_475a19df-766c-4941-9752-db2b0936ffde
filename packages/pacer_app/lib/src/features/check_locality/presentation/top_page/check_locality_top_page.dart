import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../exceptions/app_exception.dart';
import '../../../../localization/app_localizations_context.dart';
import '../../../../routing/app_router.dart';
import '../../../../utils/async_value_ui.dart';
import '../check_locality_confirmation/unconfirm_item_count_state.dart';
import '../routing/check_locality_route.dart';

/// 産地チェックメニュー
class CheckLocalityTopPage extends HookConsumerWidget {
  /// コンストラクタ関数
  const CheckLocalityTopPage({super.key});

  static const _confirmDisplayDuration = Duration(seconds: 3);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final unconfirmCount = ref.watch(fetchUnconfirmItemCountProvider);
    final timerRef = useState<Timer?>(null);

    ref.listen(fetchUnconfirmItemCountProvider, (_, state) {
      state.showAlertDialogOnError(context);
    });

    useEffect(
      () => () => timerRef.value?.cancel(),
      [],
    );

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: Text(context.loc.checkLocalityMenu),
      ),
      body: switch (unconfirmCount) {
        AsyncLoading() => const Center(child: CircularProgressIndicator()),
        AsyncError(:final AppException error) => Center(child: Text(error.message)),
        AsyncError(:final error) => Center(child: Text('$error')),
        AsyncData(value: final value && > 0) when timerRef.value != null => _ConfirmMessage(value),
        AsyncData(value: > 0) => _Menu(
            onPressConfirm: () {
              /// (仕様)未確認項目が0より大きい場合、未確認項目数を
              /// 画面に一定時間表示後、責任者確認画面に遷移
              timerRef.value = Timer(_confirmDisplayDuration, () {
                const CheckLocalityConfirmRoute().go(context);
                timerRef.value = null;
              });
            },
          ),
        AsyncData(value: <= 0) => _Menu(
            onPressConfirm: () {
              const CheckLocalityConfirmRoute().go(context);
            },
          ),
        _ => const SizedBox.shrink()
      },
      bottomNavigationBar: BottomAppBar(
        child: Row(
          children: [
            TextButton(
              onPressed: () => switch (timerRef.value) {
                null => context.pop(),
                _ => null,
              },
              child: Text(
                context.loc.end,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}

class _ConfirmMessage extends StatelessWidget {
  const _ConfirmMessage(this.unconfirmCount);

  final int unconfirmCount;

  @override
  Widget build(BuildContext context) {
    final textStyle = Theme.of(context).textTheme.titleMedium;
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 64),
        child: Row(
          children: [
            Text(
              context.loc.numberOfUnconfirmedItems,
              style: textStyle,
            ),
            const Spacer(flex: 2),
            Text(
              unconfirmCount.toString(),
              style: textStyle?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            const Spacer(),
            Text(
              context.loc.item,
              style: textStyle,
            ),
          ],
        ),
      ),
    );
  }
}

class _Menu extends ConsumerWidget {
  const _Menu({required this.onPressConfirm});

  final VoidCallback onPressConfirm;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final buttonStyle = OutlinedButton.styleFrom(
      padding: const EdgeInsets.all(24),
      side: BorderSide(color: Theme.of(context).colorScheme.primary),
      textStyle: Theme.of(context).textTheme.bodyLarge,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          OutlinedButton(
            onPressed: () => const CheckLocalityRoute().go(context),
            style: buttonStyle,
            child: Text(context.loc.checkLocality),
          ),
          OutlinedButton(
            onPressed: onPressConfirm,
            style: buttonStyle,
            child: Text(context.loc.checkPersonInCharge),
          ),
        ],
      ),
    );
  }
}
