import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../exceptions/app_exception.dart';
import '../../application/check_locality_service.dart';
import '../../domain/code_type.dart';
import '../../domain/locality_item.dart';
import '../check_locality_confirmation/unconfirm_item_count_state.dart';

part 'check_locality_controller.g.dart';

/// 産地チェッ
@riverpod
class CheckLocalityController extends _$CheckLocalityController {
  @override
  FutureOr<LocalityItem?> build() {
    return null;
  }

  /// JAN/CyPLATEスキャン・入力-産地情報を取る
  Future<void> searchItem(String? code, {VoidCallback? onSuccess}) async {
    if (code == null || code.isEmpty) {
      // textFieldを手動で空にしたケース
      await reset();
      return;
    }

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(checkLocalityServiceProvider).searchItem(code),
    );
    if (!state.hasError) {
      onSuccess?.call();
    }
  }

  /// 産地名を更新
  Future<void> updateLocality(String code, String localityName) async {
    state = AsyncData(
      state.value?.copyWith(
        detail: state.value?.detail.copyWith(name: localityName),
      ),
    );
  }

  /// 確定ボタン

  /// 産地チェック-確定ボタン-
  /// 1. -産地チェックデータを保存
  /// 2. 一般ユーザが産地チェック作業を確定する
  Future<void> confirmLocalityCheckAfterSave({
    required LocalityCodeType scannedCodeType,
    required bool isUpdate,
    required LocalityItem? localityItem,
    VoidCallback? onSuccess,
  }) async {
    final service = ref.read(checkLocalityServiceProvider);
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      if (localityItem == null) throw RequiredFieldIsNotFilledInException();
      final localityName = localityItem.detail.name?.split('､');
      if (localityName == null) throw RequiredFieldIsNotFilledInException();
      await service.saveItem(locality: localityItem, isUpdate: isUpdate);
      // CyPLATEコードがスキャンされていて、産地に変更がある場合のみ配信APIを呼び出す
      if (scannedCodeType.isCyPlate && isUpdate) {
        await service.confirmLocalityCheck(
          locality: localityItem,
          cyPlateType: scannedCodeType.stringValue,
          localityNames: localityName,
        );
      }
      return null;
    });
    if (!state.hasError) {
      ref.invalidate(fetchUnconfirmItemCountProvider);
      onSuccess?.call();
    }
  }

  /// reset data on screen
  Future<void> reset() async {
    if (state.value == null) return;
    state = const AsyncData(null);
  }
}
