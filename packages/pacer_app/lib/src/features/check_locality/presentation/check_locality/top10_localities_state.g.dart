// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'top10_localities_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchTop10LocalitiesHash() => r'23a32412646f1387d159f0ea3db590f7707df648';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// 産地チェック-産地一覧画面-Top10産地一覧を取得する
///
/// Copied from [fetchTop10Localities].
@ProviderFor(fetchTop10Localities)
const fetchTop10LocalitiesProvider = FetchTop10LocalitiesFamily();

/// 産地チェック-産地一覧画面-Top10産地一覧を取得する
///
/// Copied from [fetchTop10Localities].
class FetchTop10LocalitiesFamily extends Family {
  /// 産地チェック-産地一覧画面-Top10産地一覧を取得する
  ///
  /// Copied from [fetchTop10Localities].
  const FetchTop10LocalitiesFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'fetchTop10LocalitiesProvider';

  /// 産地チェック-産地一覧画面-Top10産地一覧を取得する
  ///
  /// Copied from [fetchTop10Localities].
  FetchTop10LocalitiesProvider call({
    required String productCode,
  }) {
    return FetchTop10LocalitiesProvider(
      productCode: productCode,
    );
  }

  @visibleForOverriding
  @override
  FetchTop10LocalitiesProvider getProviderOverride(
    covariant FetchTop10LocalitiesProvider provider,
  ) {
    return call(
      productCode: provider.productCode,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(FutureOr<List<String>> Function(FetchTop10LocalitiesRef ref) create) {
    return _$FetchTop10LocalitiesFamilyOverride(this, create);
  }
}

class _$FetchTop10LocalitiesFamilyOverride implements FamilyOverride {
  _$FetchTop10LocalitiesFamilyOverride(this.overriddenFamily, this.create);

  final FutureOr<List<String>> Function(FetchTop10LocalitiesRef ref) create;

  @override
  final FetchTop10LocalitiesFamily overriddenFamily;

  @override
  FetchTop10LocalitiesProvider getProviderOverride(
    covariant FetchTop10LocalitiesProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// 産地チェック-産地一覧画面-Top10産地一覧を取得する
///
/// Copied from [fetchTop10Localities].
class FetchTop10LocalitiesProvider extends AutoDisposeFutureProvider<List<String>> {
  /// 産地チェック-産地一覧画面-Top10産地一覧を取得する
  ///
  /// Copied from [fetchTop10Localities].
  FetchTop10LocalitiesProvider({
    required String productCode,
  }) : this._internal(
          (ref) => fetchTop10Localities(
            ref as FetchTop10LocalitiesRef,
            productCode: productCode,
          ),
          from: fetchTop10LocalitiesProvider,
          name: r'fetchTop10LocalitiesProvider',
          debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fetchTop10LocalitiesHash,
          dependencies: FetchTop10LocalitiesFamily._dependencies,
          allTransitiveDependencies: FetchTop10LocalitiesFamily._allTransitiveDependencies,
          productCode: productCode,
        );

  FetchTop10LocalitiesProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productCode,
  }) : super.internal();

  final String productCode;

  @override
  Override overrideWith(
    FutureOr<List<String>> Function(FetchTop10LocalitiesRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchTop10LocalitiesProvider._internal(
        (ref) => create(ref as FetchTop10LocalitiesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productCode: productCode,
      ),
    );
  }

  @override
  ({
    String productCode,
  }) get argument {
    return (productCode: productCode,);
  }

  @override
  AutoDisposeFutureProviderElement<List<String>> createElement() {
    return _FetchTop10LocalitiesProviderElement(this);
  }

  FetchTop10LocalitiesProvider _copyWith(
    FutureOr<List<String>> Function(FetchTop10LocalitiesRef ref) create,
  ) {
    return FetchTop10LocalitiesProvider._internal(
      (ref) => create(ref as FetchTop10LocalitiesRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      productCode: productCode,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is FetchTop10LocalitiesProvider && other.productCode == productCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FetchTop10LocalitiesRef on AutoDisposeFutureProviderRef<List<String>> {
  /// The parameter `productCode` of this provider.
  String get productCode;
}

class _FetchTop10LocalitiesProviderElement extends AutoDisposeFutureProviderElement<List<String>>
    with FetchTop10LocalitiesRef {
  _FetchTop10LocalitiesProviderElement(super.provider);

  @override
  String get productCode => (origin as FetchTop10LocalitiesProvider).productCode;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
