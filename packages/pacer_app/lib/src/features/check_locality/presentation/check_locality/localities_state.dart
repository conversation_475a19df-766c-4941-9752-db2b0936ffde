import 'package:collection/collection.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../app/application/global_loading_service.dart';
import '../../application/check_locality_service.dart';

part 'localities_state.g.dart';

/// 産地チェック-産地一覧画面-産地一覧を取得する
@riverpod
Future<List<String>> fetchLocalities(FetchLocalitiesRef ref) async {
  ref.showGlobalLoading();
  final localities = await ref.read(checkLocalityServiceProvider).fetchLocalities();
  return localities.map((e) => e.localityName).whereNot((e) => e.isEmpty).toList();
}
