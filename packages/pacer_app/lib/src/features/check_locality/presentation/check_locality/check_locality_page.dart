import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../common_widgets/pacer_back_button.dart';
import '../../../../common_widgets/select_locality_dialog.dart';
import '../../../../common_widgets/select_locality_full_screen_dialog.dart';
import '../../../../common_widgets/snack_bars.dart';
import '../../../../localization/app_localizations_context.dart';
import '../../../../routing/app_router.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/adaptive_number_input_type.dart';
import '../../../../utils/async_value_ui.dart';
import '../../../device/application/pacer_service.dart';
import '../../../device/presentation/scan_window.dart';
import '../../domain/code_type.dart';
import '../../domain/locality_item.dart';
import '../../domain/product_code.dart';
import '../routing/check_locality_route.dart';
import 'check_locality_controller.dart';
import 'localities_state.dart';
import 'top10_localities_state.dart';

/// 産地チェッ画面
class CheckLocalityPage extends HookConsumerWidget {
  /// コンストラクタ関数
  const CheckLocalityPage(this.code, {super.key});

  /// 編集項目のCyPLATE CODEまたはJAN CODE
  /// ホームページからアクセスした場合、これは null です
  final String? code;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productCodeEditingController = useTextEditingController();

    useEffect(
      () {
        productCodeEditingController.text = code ?? '';
        return null;
      },
      [code],
    );

    ref
      ..listen(
        scanCodeProvider,
        (_, state) {
          final isEqualFullPath = const CheckLocalityRoute().isEqualFullPath(context);
          if (!isEqualFullPath) {
            return;
          }

          _onScan(
            ref,
            code: state.value ?? '',
            onSuccess: () {
              productCodeEditingController.text = ProductCode.parse(state.value ?? '').value;
            },
          );
        },
      )
      ..listen(
        checkLocalityControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      );
    final localityItem = ref.watch(checkLocalityControllerProvider);

    final bottomBarButtonStyle = ButtonStyle(
      textStyle: WidgetStateProperty.resolveWith(
        (states) => Theme.of(context).textTheme.titleMedium?.copyWith(
              color: states.contains(WidgetState.disabled)
                  ? Theme.of(context).colorScheme.disabled
                  : Theme.of(context).colorScheme.primary,
            ),
      ),
    );
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: PacerAppBar(
          context: context,
          title: Text(context.loc.checkLocalityFruit),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: switch (localityItem) {
              AsyncLoading() => const Center(child: CircularProgressIndicator()),
              _ => _LocalityTable(
                  localityItem: localityItem.value,
                  controller: productCodeEditingController,
                ),
            },
          ),
        ),
        bottomNavigationBar: BottomAppBar(
          child: Row(
            children: [
              const PacerBackButton(),
              TextButton(
                onPressed: () => _reset(
                  ref,
                  onReset: productCodeEditingController.clear,
                ),
                style: bottomBarButtonStyle,
                child: Text(context.loc.revocation),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => const CheckLocalityHistoryRoute().go(context),
                style: bottomBarButtonStyle,
                child: Text(context.loc.history),
              ),
              TextButton(
                onPressed: () => _confirmLocalityCheckAfterSave(
                  ref,
                  onSuccess: () {
                    productCodeEditingController.clear();

                    showSnackBar(
                      context,
                      context.loc.success,
                    );
                  },
                ),
                style: bottomBarButtonStyle,
                child: Text(context.loc.confirm),
              ),
            ],
          ),
        ),
        floatingActionButton: ScanFloatingIconButton(
          onScan: (code) {
            _onScan(
              ref,
              code: code,
              onSuccess: () {
                productCodeEditingController.text = ProductCode.parse(code).value;
              },
            );
          },
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        resizeToAvoidBottomInset: false,
      ),
    );
  }

  Future<void> _confirmLocalityCheckAfterSave(
    WidgetRef ref, {
    VoidCallback? onSuccess,
  }) async {
    final notifier = ref.read(checkLocalityControllerProvider.notifier);
    final localityItem = ref.read(checkLocalityControllerProvider);

    await notifier.confirmLocalityCheckAfterSave(
      localityItem: localityItem.value,
      isUpdate: localityItem.value?.isUpdate ?? true,
      scannedCodeType: LocalityCodeType.fromCode(
        localityItem.value?.detail.code ?? '',
      ),
      onSuccess: onSuccess,
    );
  }

  Future<void> _reset(
    WidgetRef ref, {
    VoidCallback? onReset,
  }) async {
    await ref.read(checkLocalityControllerProvider.notifier).reset();
    onReset?.call();
  }

  Future<void> _onScan(
    WidgetRef ref, {
    required String code,
    VoidCallback? onSuccess,
  }) async {
    await ref.read(checkLocalityControllerProvider.notifier).searchItem(code, onSuccess: onSuccess);
  }
}

class _LocalityTable extends HookConsumerWidget {
  const _LocalityTable({
    required this.localityItem,
    required this.controller,
  });

  final LocalityItem? localityItem;
  final TextEditingController controller;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final allLocalityList = ref.watch(fetchLocalitiesProvider).asData?.value;
    final top10LocalityList = ref
        .watch(
          fetchTop10LocalitiesProvider(
            productCode: localityItem?.detail.code ?? '',
          ),
        )
        .asData
        ?.value;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.25),
        1: FractionColumnWidth(0.75),
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        TableRow(
          children: [
            _ItemHeader(context.loc.janOrCyplateCode),
            _ScanField(
              controller: controller,
              onFieldSubmitted: (inputCode) {
                ref.read(checkLocalityControllerProvider.notifier).searchItem(
                  inputCode,
                  onSuccess: () {
                    controller.text = ProductCode.parse(inputCode).value;
                  },
                );
              },
            ),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.itemName),
            _ItemValue(localityItem?.detail.productName),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.specification),
            _ItemValue(localityItem?.detail.specification),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.locality),
            TextButton(
              child: Text(localityItem?.detail.name ?? ''),
              onPressed: () {
                final productCode = ref.read(checkLocalityControllerProvider).value?.detail.code;
                if (productCode == null || productCode.isEmpty) {
                  showSnackBar(
                    context,
                    context.loc.requestInputJanMessage,
                    isErrorStyle: true,
                  );
                  return;
                }
                if (allLocalityList == null) {
                  return;
                }
                selectLocalityDialog(
                  ref: ref,
                  productLocalityList: localityItem?.localityNameOptions ?? [],
                  onChanged: (locality) => _onChangedLocality(ref, locality),
                  onOtherClick: () => showSelectFullLocalityDialog(
                    context: ref.context,
                    allLocalityList: allLocalityList,
                    top10LocalityList: top10LocalityList ?? <String>[],
                    onChanged: (locality) => _onChangedLocality(ref, locality),
                  ),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  void _onChangedLocality(WidgetRef ref, String locality) {
    if (locality.isEmpty) return;
    if (localityItem case final LocalityItem nonNullLocality) {
      final itemCode = nonNullLocality.detail.code;
      ref.read(checkLocalityControllerProvider.notifier).updateLocality(itemCode, locality);
    }
  }
}

class _ItemHeader extends StatelessWidget {
  const _ItemHeader(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.only(right: 8),
        child: Text(
          text,
          style: texts.titleMedium,
          textAlign: TextAlign.right,
          maxLines: 2,
          overflow: TextOverflow.visible,
        ),
      ),
    );
  }
}

class _ItemValue extends StatelessWidget {
  const _ItemValue(this.text);

  final String? text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text ?? '',
        style: texts.titleMedium,
      ),
    );
  }
}

class _ScanField extends StatelessWidget {
  const _ScanField({
    required this.controller,
    required this.onFieldSubmitted,
  });

  final TextEditingController controller;

  final void Function(String)? onFieldSubmitted;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.fill,
      child: ColoredBox(
        color: colors.button,
        child: TextFormField(
          controller: controller,
          keyboardType: TextInputType.number.withEnter(),
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            filled: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 4),
            fillColor: colors.button,
            focusColor: colors.button,
            hintText: 'スキャンまたは入力',
            hintStyle: texts.bodyLarge?.copyWith(color: colors.subText),
            border: InputBorder.none,
          ),
          onFieldSubmitted: onFieldSubmitted,
          inputFormatters: [
            LengthLimitingTextInputFormatter(26),
            FilteringTextInputFormatter.digitsOnly,
          ],
        ),
      ),
    );
  }
}
