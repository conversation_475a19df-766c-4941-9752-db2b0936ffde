// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_locality_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkLocalityControllerHash() => r'd0974804e34e2b571e32e54608df3ea85e30595f';

/// 産地チェッ
///
/// Copied from [CheckLocalityController].
@ProviderFor(CheckLocalityController)
final checkLocalityControllerProvider =
    AutoDisposeAsyncNotifierProvider<CheckLocalityController, LocalityItem?>.internal(
  CheckLocalityController.new,
  name: r'checkLocalityControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkLocalityControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckLocalityController = AutoDisposeAsyncNotifier<LocalityItem?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
