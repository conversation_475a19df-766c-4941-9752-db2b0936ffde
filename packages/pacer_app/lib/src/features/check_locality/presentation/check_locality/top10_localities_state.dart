import 'package:collection/collection.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../app/application/global_loading_service.dart';
import '../../application/check_locality_service.dart';

part 'top10_localities_state.g.dart';

/// 産地チェック-産地一覧画面-Top10産地一覧を取得する
@riverpod
Future<List<String>> fetchTop10Localities(
  FetchTop10LocalitiesRef ref, {
  required String productCode,
}) async {
  ref.showGlobalLoading();
  final top10localities = await ref.watch(checkLocalityServiceProvider).fetchTop10Localities(
        productCode: productCode,
      );

  return top10localities.map((e) => e.localityName).whereNot((e) => e.isEmpty).toList();
}
