// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'localities_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchLocalitiesHash() => r'6b2c03d7858e8ee641effc9651b13931ad80ebfe';

/// 産地チェック-産地一覧画面-産地一覧を取得する
///
/// Copied from [fetchLocalities].
@ProviderFor(fetchLocalities)
final fetchLocalitiesProvider = AutoDisposeFutureProvider<List<String>>.internal(
  fetchLocalities,
  name: r'fetchLocalitiesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fetchLocalitiesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchLocalitiesRef = AutoDisposeFutureProviderRef<List<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
