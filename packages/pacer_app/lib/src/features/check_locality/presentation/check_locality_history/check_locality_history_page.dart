import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/alert_dialogs.dart';
import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../common_widgets/pacer_back_button.dart';
import '../../../../localization/app_localizations_context.dart';
import '../../../../routing/app_router.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/async_value_ui.dart';
import '../../domain/locality_detail.dart';
import '../check_locality/check_locality_controller.dart';
import '../routing/check_locality_route.dart';
import 'check_locality_history_controller.dart';

/// 産地チェッ履歴画面
class CheckLocalityHistoryPage extends HookConsumerWidget {
  /// コンストラクタ関数
  const CheckLocalityHistoryPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(
      checkLocalityHistoryControllerProvider,
      (_, state) => state.showAlertDialogOnError(context),
    );
    final histories = ref.watch(checkLocalityHistoryControllerProvider);
    final selectedIndex = useState<int?>(null);
    final loc = context.loc;

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: Text('${loc.checkLocality} ${loc.history}'),
      ),
      body: SafeArea(
        child: switch (histories) {
          AsyncLoading() => const Center(child: CircularProgressIndicator()),
          AsyncData(:final value) => _ListBody(value: value, selectedIndex: selectedIndex),
          _ => const SizedBox.shrink(),
        },
      ),
      bottomNavigationBar: _BottomBar(selectedIndex: selectedIndex.value),
    );
  }
}

class _ListBody extends StatelessWidget {
  const _ListBody({
    required this.value,
    required this.selectedIndex,
  });

  final List<LocalityDetail> value;
  final ValueNotifier<int?> selectedIndex;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(context.loc.historyDescription(value.length)),
        ),
        const Gap(8),
        const _Divider(),
        Expanded(
          child: ListView.separated(
            itemBuilder: (_, index) {
              if (index == value.length) {
                // 最後尾は_Dividerのみの表示なのでListTile不要
                return const SizedBox.shrink();
              }
              return _HistoryListTile(
                localityDetail: value[index],
                isSelected: selectedIndex.value == index,
                onPressed: () {
                  if (selectedIndex.value == index) {
                    selectedIndex.value = null;
                  } else {
                    selectedIndex.value = index;
                  }
                },
              );
            },
            // 最後尾に_Dividerを表示するため+1
            itemCount: value.length + 1,
            separatorBuilder: (_, __) => const _Divider(),
          ),
        ),
      ],
    );
  }
}

class _Divider extends StatelessWidget {
  const _Divider();

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: 1,
      color: Theme.of(context).colorScheme.sub,
    );
  }
}

class _HistoryListTile extends StatelessWidget {
  const _HistoryListTile({
    required this.localityDetail,
    this.onPressed,
    this.isSelected = true,
  });

  final LocalityDetail localityDetail;
  final bool isSelected;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final loc = context.loc;
    final productName = '${loc.itemName}: ${localityDetail.productName}';
    final localityNames = '${loc.locality}: ${localityDetail.name ?? ''}';
    final message = '''
$productName
$localityNames''';

    return Tooltip(
      textStyle: theme.tooltipTheme.textStyle?.copyWith(
        // tooltipThemeのdefaultはbodyMedium
        fontSize: theme.textTheme.bodyLarge?.fontSize,
      ),
      message: message,
      child: ListTile(
        onTap: onPressed,
        style: ListTileStyle.list,
        tileColor: switch (isSelected) {
          false => null,
          true => theme.colorScheme.button,
        },
        title: Text(
          productName,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          localityNames,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}

class _BottomBar extends ConsumerWidget {
  const _BottomBar({required this.selectedIndex});

  final int? selectedIndex;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final histories = ref.watch(checkLocalityHistoryControllerProvider);
    return BottomAppBar(
      child: Row(
        children: [
          const PacerBackButton(),
          const Spacer(),
          switch (histories) {
            AsyncData(:final value) => TextButton(
                onPressed: switch (selectedIndex) {
                  final index? => () => _showEditDialog(ref, value[index].code),
                  null => null,
                },
                child: Text(context.loc.edit),
              ),
            _ => const SizedBox.shrink(),
          },
          switch (histories) {
            AsyncData(:final value) => TextButton(
                onPressed: switch (selectedIndex) {
                  final index? => () => _showDeleteDialog(ref, value[index].code),
                  null => null,
                },
                child: Text(context.loc.delete),
              ),
            _ => const SizedBox.shrink(),
          },
        ],
      ),
    );
  }

  Future<void> _showDeleteDialog(WidgetRef ref, String itemCode) async {
    final context = ref.context;

    final allowDelete = await showAlertDialog(
          context: context,
          title: context.loc.message,
          content: context.loc.deleteConfirmMessage,
          cancelActionText: context.loc.no,
          defaultActionText: context.loc.yes,
        ) ??
        false;

    if (allowDelete) {
      await ref.read(checkLocalityHistoryControllerProvider.notifier).deleteItem(itemCode);
    }
  }

  Future<void> _showEditDialog(WidgetRef ref, String itemCode) async {
    final context = ref.context;

    final allowEdit = await showAlertDialog(
          context: context,
          title: context.loc.message,
          content: context.loc.editConfirmMessage,
          cancelActionText: context.loc.no,
          defaultActionText: context.loc.yes,
        ) ??
        false;
    if (!context.mounted) return;
    if (allowEdit) {
      final context = ref.context;
      await ref.read(checkLocalityControllerProvider.notifier).searchItem(itemCode);
      if (context.mounted) CheckLocalityRoute(itemCode).go(context);
    }
  }
}
