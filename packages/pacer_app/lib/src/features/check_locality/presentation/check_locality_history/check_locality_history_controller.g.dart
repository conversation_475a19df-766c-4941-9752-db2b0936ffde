// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_locality_history_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkLocalityHistoryControllerHash() => r'1275c48e19a02719f8faa6a8c6c2a19adf33a842';

/// 産地チェッ
///
/// Copied from [CheckLocalityHistoryController].
@ProviderFor(CheckLocalityHistoryController)
final checkLocalityHistoryControllerProvider =
    AutoDisposeAsyncNotifierProvider<CheckLocalityHistoryController, List<LocalityDetail>>.internal(
  CheckLocalityHistoryController.new,
  name: r'checkLocalityHistoryControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkLocalityHistoryControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckLocalityHistoryController = AutoDisposeAsyncNotifier<List<LocalityDetail>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
