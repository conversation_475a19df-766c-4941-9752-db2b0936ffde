import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../application/check_locality_service.dart';
import '../../domain/locality_detail.dart';
import '../check_locality_confirmation/unconfirm_item_count_state.dart';

part 'check_locality_history_controller.g.dart';

/// 産地チェッ
@riverpod
class CheckLocalityHistoryController extends _$CheckLocalityHistoryController {
  @override
  Future<List<LocalityDetail>> build() {
    return ref.watch(checkLocalityServiceProvider).listHistory();
  }

  /// 地域項目を削除する
  Future<void> deleteItem(String itemCode) async {
    state = const AsyncLoading();
    await ref.read(checkLocalityServiceProvider).deleteItem(itemCode);
    ref
      ..invalidateSelf()
      ..invalidate(fetchUnconfirmItemCountProvider);
    await future;
  }
}
