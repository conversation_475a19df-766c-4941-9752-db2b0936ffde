import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/connect_common/v1/connect_common.pbgrpc.dart';
import 'package:shinise_core_client/locality_check/v1/v1.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/code_type.dart';
import '../domain/confirm_history.dart';
import '../domain/locality.dart';
import '../domain/locality_detail.dart';
import '../domain/locality_item.dart';
import '../domain/product_code.dart';
import 'handle_grpc_error.dart';

part 'check_locality_repository.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
CheckLocalityRepository checkLocalityRepository(
  CheckLocalityRepositoryRef ref,
) {
  return CheckLocalityRepository();
}

/// 産地チェッリポジトリ
class CheckLocalityRepository {
  static const _timeout = Duration(seconds: 20);
  final _uri = Env.getApiBaseUrl();
  final _callOptions = CallOptions(timeout: _timeout);

  /// JAN/CyPLATEスキャン・入力-産地情報を取る
  Future<LocalityItem> searchItem({
    required ProductCode code,
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetLocalityInformationRequest(
      storeCode: storeCode,
      productOrCyplateCode: code.value,
      cyplateType: LocalityCodeType.fromCode(code.value).stringValue,
    );

    final stub = LocalityCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.getLocalityInformation(request);
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
      return LocalityItem.fromGrpc(code.value, response);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 産地チェック-産地一覧画面-産地一覧を取得する
  Future<List<Locality>> fetchLocalities({
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetLocalitiesRequest(storeCode: storeCode);

    final stub = LocalityCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.getLocalities(request);
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
      return response.localityList.map(Locality.fromGrpc).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 産地チェック-産地一覧画面-TOP10産地一覧を取得する
  Future<List<Locality>> fetchTop10Localities({
    required String productCode,
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetTop10LocalitiesRequest(
      storeCode: storeCode,
      productCode: productCode,
    );

    final stub = LocalityCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.getTop10Localities(request);
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
      return response.localityList.map(Locality.fromTop10Grpc).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確定ボタン-産地チェックデータを保存
  Future<void> saveItem({
    required LocalityItem locality,
    required bool isUpdate,
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = SaveDataRequest(
      storeCode: storeCode,
      productOrCyplateCode: locality.detail.code,
      productName: locality.detail.productName,
      localityName: locality.detail.name,
      isUpdate: isUpdate,
      isCyplate: LocalityCodeType.fromCode(locality.detail.code).isCyPlate,
    );

    final stub = LocalityCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.saveData(request);
      if (response.code != '000') {
        UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ローカリティチェック履歴を取得する
  Future<List<LocalityDetail>> listHistory({
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetHistoryRequest(storeCode: storeCode);

    final stub = LocalityCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.getHistory(request);
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
      return response.localityInfo.map(LocalityDetail.fromHistoryGrpc).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 地域項目を削除する
  Future<void> deleteItem({
    required String itemCode,
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = DeleteRequest(storeCode: storeCode, productCode: itemCode);

    final stub = LocalityCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      await stub.delete(request);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 責任者確認-履歴取得
  Future<ConfirmHistory> getConfirmHistory({
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = ResponsiblePersonConfirmationRequest(storeCode: storeCode);

    final stub = LocalityCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.responsiblePersonConfirmation(request);
      if (response.notConfirmInfo.isEmpty) {
        throw const GrpcError.notFound();
      }
      return ConfirmHistory.fromGrpc(response);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 産地チェック確認量を取る
  Future<int> getUnconfirmItemCount({
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetConfirmCountRequest(storeCode: storeCode);

    final stub = LocalityCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.getConfirmCount(request);
      if (response.notConfirmInfo.isEmpty) {
        throw const GrpcError.notFound();
      }
      return response.notConfirmInfo.first.notConfirmCount;
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 責任者確認-確認ボタン-
  /// 責任者として、産地チェック作業を確定する
  Future<void> confirmLocalityCheckAsResponsible({
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = ResponsibleConfirmationRequest(storeCode: storeCode);

    final stub = LocalityCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      await stub.responsibleConfirmation(request);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 産地チェック画面：一般ユーザが産地チェック作業を確定する
  Future<void> confirmLocalityCheck({
    required String cyPlateCode,
    required String cyPlateType,
    required String productName,
    required String specName,
    required String storeCode,
    required Iterable<String> localityNames,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetCsvLocalityCheckRequest(
      storeCode: storeCode,
      cyPlateCode: cyPlateCode,
      productName: productName,
      localityName: localityNames,
      specName: specName,
      cyPlateType: cyPlateType,
    );

    final stub = ConnectCommonServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.getCsvLocalityCheck(request);
      if (response.code != '000') {
        UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
