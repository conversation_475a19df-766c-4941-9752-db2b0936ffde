// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_locality_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkLocalityRepositoryHash() => r'64f840f9ae0c7b1bdad3ebcab1e966e90985f864';

/// provider生成コード
///
/// Copied from [checkLocalityRepository].
@ProviderFor(checkLocalityRepository)
final checkLocalityRepositoryProvider = Provider<CheckLocalityRepository>.internal(
  checkLocalityRepository,
  name: r'checkLocalityRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkLocalityRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CheckLocalityRepositoryRef = ProviderRef<CheckLocalityRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
