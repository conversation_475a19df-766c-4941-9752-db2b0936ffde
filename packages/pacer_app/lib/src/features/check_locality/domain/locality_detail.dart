import 'package:shinise_core_client/locality_check/v1/v1.dart';

import '../../../exceptions/app_exception.dart';

/// 産地アイテム
class LocalityDetail {
  /// コンストラクタ関数
  LocalityDetail({
    required this.code,
    required this.productName,
    required this.specification,
    this.name,
  });

  /// grpc 応答からのコンストラクター関数
  factory LocalityDetail.fromGrpc(
    String code,
    GetLocalityInformationResponse_LocalityInfo info,
  ) {
    return switch (info) {
      GetLocalityInformationResponse_LocalityInfo(
        productName: == '',
      ) ||
      GetLocalityInformationResponse_LocalityInfo(
        specName: == '',
      ) =>
        throw ProductNotFoundException(),
      _ => LocalityDetail(
          code: code,
          productName: info.productName,
          specification: info.specName,
          name: info.localityName.isEmpty ? null : info.localityName,
        )
    };
  }

  /// grpc 応答からのコンストラクター関数
  factory LocalityDetail.fromHistoryGrpc(GetHistoryResponse_LocalityInfo info) {
    return switch (info) {
      GetHistoryResponse_LocalityInfo(
        productName: == '',
      ) =>
        throw ProductNotFoundException(),
      _ => LocalityDetail(
          code: info.productCode,
          productName: info.productName,
          specification: '',
          name: info.localityName.isEmpty ? null : info.localityName,
        )
    };
  }

  /// CyPLATEのCODE/JANCODE
  final String code;

  /// 商品名
  final String productName;

  /// 規格
  final String specification;

  /// 産地名
  final String? name;

  /// copy with
  LocalityDetail copyWith({
    String? name,
    String? code,
    String? productName,
    String? specification,
  }) {
    return LocalityDetail(
      name: name ?? this.name,
      code: code ?? this.code,
      productName: productName ?? this.productName,
      specification: specification ?? this.specification,
    );
  }
}
