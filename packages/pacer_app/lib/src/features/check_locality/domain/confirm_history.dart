import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/locality_check/v1/v1.dart';

import '../../../localization/app_localizations_context.dart';

/// 確認履歴
class ConfirmHistory {
  /// コンストラクタ関数
  const ConfirmHistory({
    required this.rows,
    required this.endDate,
    required this.unconfirmCount,
    required this.operators,
  });

  /// grpc 応答からのコンストラクター関数
  factory ConfirmHistory.fromGrpc(ResponsiblePersonConfirmationResponse grpc) {
    return ConfirmHistory(
      rows: grpc.dayList.map(HistoryRow.fromGrpc).toList(),
      endDate: DateFormat('yyyy/MM/dd').tryParse(grpc.notConfirmInfo.first.endDate) ?? DateTime.now(),
      unconfirmCount: grpc.notConfirmInfo.first.notConfirmCount,
      operators: grpc.registerList.map((e) => e.operator).toList(),
    );
  }

  /// 確認表の各行のデータ
  final List<HistoryRow> rows;

  /// 終了日
  final DateTime endDate;

  /// 産地チェック確認量
  final int unconfirmCount;

  /// 登録作業者リスト
  final List<String> operators;

  /// 作業者一覧リストに、責任者が含まれる場合はtrue.
  /// [responsiblePerson]: 責任者の社員番号.
  bool contains({required String responsiblePerson}) {
    return operators.contains(responsiblePerson);
  }
}

/// 確認表の各行のデータ
class HistoryRow {
  /// コンストラクタ関数
  const HistoryRow({required this.headline, required this.days});

  /// grpc 応答からのコンストラクター関数
  factory HistoryRow.fromGrpc(
    ResponsiblePersonConfirmationResponse_DayList grpc,
  ) {
    return HistoryRow(
      headline: RowHeadline.fromGrpc(grpc.tytle),
      days: [
        grpc.firstDay,
        grpc.secondDay,
        grpc.thirdDay,
        grpc.forthDay,
        grpc.fifthDay,
        grpc.sixthDay,
        grpc.seventhDay,
      ],
    );
  }

  /// 行見出し
  final RowHeadline headline;

  /// 過去 1 週間の曜日データ
  final List<int> days;
}

/// 0-件数 1-単一 2-複数 3-修正件数
enum RowHeadline {
  /// 件数
  total,

  /// 単一
  single,

  /// 複数
  plural,

  /// 修正件数
  modified;

  /// 表示テキスト
  factory RowHeadline.fromGrpc(
    ResponsiblePersonConfirmationResponse_DayList_TytleInfo tytle,
  ) =>
      switch (tytle) {
        ResponsiblePersonConfirmationResponse_DayList_TytleInfo.TYTLE_INFO_NUMBER => total,
        ResponsiblePersonConfirmationResponse_DayList_TytleInfo.TYTLE_INFO_SINGLE => single,
        ResponsiblePersonConfirmationResponse_DayList_TytleInfo.TYTLE_INFO_MULTIPLE => plural,
        ResponsiblePersonConfirmationResponse_DayList_TytleInfo.TYTLE_INFO_NUM_OF_MODIFICATIONS => modified,
        _ => total,
      };

  /// 表示テキスト
  String displayText(BuildContext context) => switch (this) {
        total => context.loc.total,
        single => context.loc.single,
        plural => context.loc.plural,
        modified => context.loc.totalModified,
      };
}
