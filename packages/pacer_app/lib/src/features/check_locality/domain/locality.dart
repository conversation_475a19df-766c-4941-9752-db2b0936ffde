import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/locality_check/v1/v1.dart';

/// 産地チェック-産地一覧
class Locality extends Equatable {
  /// constructor
  const Locality({
    required this.localityId,
    required this.localityName,
  });

  /// factory constructor
  factory Locality.fromGrpc(
    GetLocalitiesResponse_LocalityList response,
  ) =>
      Locality(
        localityId: response.id,
        localityName: response.localityName,
      );

  /// top10 factory constructor
  factory Locality.fromTop10Grpc(
    GetTop10LocalitiesResponse_LocalitiesNameInfo response,
  ) =>
      Locality(
        localityId: '',
        localityName: response.localityName,
      );

  /// fake constructor
  factory Locality.fake(int index) => Locality(
        localityId: index.toString(),
        localityName: '産地$index',
      );

  /// 産地id
  final String localityId;

  /// 産地名
  final String localityName;

  @override
  List<Object?> get props => [
        localityId,
        localityName,
      ];
}
