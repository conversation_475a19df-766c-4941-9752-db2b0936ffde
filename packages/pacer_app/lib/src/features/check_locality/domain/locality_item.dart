import 'package:shinise_core_client/locality_check/v1/v1.dart';

import 'locality_detail.dart';

/// 地域
class LocalityItem {
  /// コンストラクタ関数
  LocalityItem({
    required this.detail,
    required this.localityNameOptions,
    required this.currentlyRegisteredLocalityName,
  });

  /// grpc 応答からのコンストラクター関数
  factory LocalityItem.fromGrpc(
    String code,
    GetLocalityInformationResponse response,
  ) {
    return LocalityItem(
      detail: LocalityDetail.fromGrpc(code, response.localityInfo),
      localityNameOptions: response.localityNameInfo.map((e) => e.localityName).toList(),
      currentlyRegisteredLocalityName: response.localityInfo.localityName,
    );
  }

  /// 産地アイテム
  final LocalityDetail detail;

  /// 産地名
  final List<String> localityNameOptions;

  /// 現在登録されている産地名.
  /// この値はcopyWithで更新不要.
  final String currentlyRegisteredLocalityName;

  /// ユーザが登録済産地を更新する場合はtrue.
  /// 登録済産地と[LocalityDetail.name]が異なる場合はfalse.
  bool get isUpdate => currentlyRegisteredLocalityName != detail.name;

  /// copy with
  LocalityItem copyWith({
    LocalityDetail? detail,
    List<String>? localityNameOptions,
  }) {
    return LocalityItem(
      detail: detail ?? this.detail,
      localityNameOptions: localityNameOptions ?? this.localityNameOptions,
      currentlyRegisteredLocalityName: currentlyRegisteredLocalityName,
    );
  }
}
