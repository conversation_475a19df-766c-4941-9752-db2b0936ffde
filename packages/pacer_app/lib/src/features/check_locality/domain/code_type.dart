/// CyPLATEの種類
enum LocalityCodeType {
  ///商品コード
  jan,

  ///旧版CyPLATE
  oldCyplate,

  ///新版CyPLATE
  newCyplate;

  factory LocalityCodeType.fromCode(String code) => switch ((int.tryParse(code), code.length)) {
        (null, 6) => oldCyplate,
        (_, 18) => newCyplate,
        _ => jan,
      };

  /// APIに送信される文字列値
  String get stringValue => switch (this) { jan => 'JANCODE', oldCyplate => 'ESLCODE', newCyplate => 'ESL_HS_CODE' };

  /// CyPLATE商品フラグ
  bool get isCyPlate => this != jan;
}
