import '../../../exceptions/app_exception.dart';
import '../../../utils/string_extensions_itf14.dart';

/// 産地チェックアプリで使用する商品コードクラス。
class ProductCode {
  const ProductCode._(this.value);

  /// Factory constructor
  factory ProductCode.parse(String source) {
    // 桁数が20 or 26桁の場合は、先頭から13桁を抜き出す
    final code = switch (source.length) {
      20 || 26 => source.substring(0, 13),
      _ => source,
    };
    return switch ((code, BigInt.tryParse(code))) {
      (String(isEmpty: true), _) => const ProductCode._(''),
      // ITFJANは専用の処理を通す
      (String(length: 14), _) => ProductCode._(code.ean13FromITF14),
      // 00始まり11桁JANは変換なしで通す
      (String(length: 13), final value) when value.toString().length == 11 => ProductCode._(value.toString()),
      // 13桁の25開始JANは後ろ6桁を0に変換
      (String(length: 13), final value) when value.toString().length == 13 && value.toString().startsWith('25') =>
        ProductCode._(
          value.toString().substring(0, 7).padRight(13, '0'),
        ),
      (_, null) => throw WrongProductCodeException(),
      (_, final value) => ProductCode._(value.toString())
    };
  }

  /// [ProductCode.parse]によって、parseされた文字列。
  final String value;
}
