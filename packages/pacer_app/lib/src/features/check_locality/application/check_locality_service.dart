import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../../authentication/domain/app_user.dart';
import '../data/check_locality_repository.dart';
import '../domain/confirm_history.dart';
import '../domain/locality.dart';
import '../domain/locality_detail.dart';
import '../domain/locality_item.dart';
import '../domain/product_code.dart';

part 'check_locality_service.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
CheckLocalityService checkLocalityService(CheckLocalityServiceRef ref) {
  return CheckLocalityService(ref);
}

/// 産地チェッサービス
class CheckLocalityService {
  /// コンストラクタ関数
  CheckLocalityService(this.ref);

  /// Riverpod ref
  final Ref ref;
  CheckLocalityRepository get _repository => ref.read(checkLocalityRepositoryProvider);

  /// See also [AppUser].
  AppUser? get caller => ref.read(authRepositoryProvider).currentUser;

  /// JAN/CyPLATEスキャン・入力-産地情報を取る
  Future<LocalityItem> searchItem(String itemCode) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.searchItem(
      code: ProductCode.parse(itemCode),
      caller: caller,
      storeCode: store.code,
    );
  }

  /// 産地チェック-産地一覧画面-産地一覧を取得する
  Future<List<Locality>> fetchLocalities() {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.fetchLocalities(caller: caller, storeCode: store.code);
  }

  /// 産地チェック-産地一覧画面-Top10産地一覧を取得する
  Future<List<Locality>> fetchTop10Localities({
    required String productCode,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.fetchTop10Localities(
      caller: caller,
      storeCode: store.code,
      productCode: productCode,
    );
  }

  /// 確定ボタン-産地チェックデータを保存
  Future<void> saveItem({
    required LocalityItem locality,
    required bool isUpdate,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final localityName = locality.detail.name ?? '';

    if (localityName.isEmpty) {
      throw OmittingLocalityException();
    }

    final store = caller.clockInStore;

    return _repository.saveItem(
      locality: locality,
      isUpdate: isUpdate,
      caller: caller,
      storeCode: store.code,
    );
  }

  /// 産地チェック画面-確定ボタン-
  /// 一般ユーザが産地チェック作業を確定する
  Future<void> confirmLocalityCheck({
    required LocalityItem locality,
    required String cyPlateType,
    required Iterable<String> localityNames,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    if (locality.detail.code.isEmpty) {
      throw RequiredFieldIsNotFilledInException();
    }

    final localityName = locality.detail.name ?? '';

    if (localityName.isEmpty) {
      throw OmittingLocalityException();
    }

    final store = caller.clockInStore;

    return _repository.confirmLocalityCheck(
      cyPlateCode: locality.detail.code,
      cyPlateType: cyPlateType,
      productName: locality.detail.productName,
      specName: locality.detail.specification,
      storeCode: store.code,
      localityNames: localityNames,
      caller: caller,
    );
  }

  /// ローカリティチェック履歴を取得する
  Future<List<LocalityDetail>> listHistory() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.listHistory(caller: caller, storeCode: store.code);
  }

  /// 地域項目を削除する
  Future<void> deleteItem(String itemCode) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.deleteItem(
      itemCode: itemCode,
      caller: caller,
      storeCode: store.code,
    );
  }

  /// 責任者確認-履歴取得
  /// Get a list of cases that need or have been confirmed within 7 days
  Future<ConfirmHistory> getConfirmHistory() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.getConfirmHistory(
      caller: caller,
      storeCode: store.code,
    );
  }

  /// 産地チェック確認量を取る
  /// Get the total number of cases that need to be confirmed today
  Future<int> getUnconfirmItemCount() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.getUnconfirmItemCount(
      caller: caller,
      storeCode: store.code,
    );
  }

  /// 責任者確認-確認ボタン-
  /// 責任者として、産地チェック作業を確定する
  Future<void> confirmLocalityCheckAsResponsible() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.confirmLocalityCheckAsResponsible(
      caller: caller,
      storeCode: store.code,
    );
  }
}
