// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_locality_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkLocalityServiceHash() => r'33cc0cd99e3562fb8bbd2768221587626e1d5900';

/// provider生成コード
///
/// Copied from [checkLocalityService].
@ProviderFor(checkLocalityService)
final checkLocalityServiceProvider = Provider<CheckLocalityService>.internal(
  checkLocalityService,
  name: r'checkLocalityServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkLocalityServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CheckLocalityServiceRef = ProviderRef<CheckLocalityService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
