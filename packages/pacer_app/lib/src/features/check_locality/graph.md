Legend: {
  Type: {
    Widget.shape: circle
    Provider.shape: rectangle
  }
  Arrows: {
    "." -> "..": read: {style.stroke-dash: 4}
    "." -> "..": listen
    "." -> "..": watch: {style.stroke-width: 4}
  }
}

checkLocalityConfirmControllerProvider: "checkLocalityConfirmControllerProvider"
checkLocalityConfirmControllerProvider.shape: rectangle
checkLocalityConfirmControllerProvider.tooltip: "産地チェッ-責任者確認"
checkLocalityHistoryControllerProvider: "checkLocalityHistoryControllerProvider"
checkLocalityHistoryControllerProvider.shape: rectangle
checkLocalityHistoryControllerProvider.tooltip: "産地チェッ"
checkLocalityServiceProvider: "checkLocalityServiceProvider"
checkLocalityServiceProvider.shape: rectangle
checkLocalityServiceProvider.tooltip: "provider生成コード"
checkLocalityConfirmHistoryControllerProvider: "checkLocalityConfirmHistoryControllerProvider"
checkLocalityConfirmHistoryControllerProvider.shape: rectangle
checkLocalityConfirmHistoryControllerProvider.tooltip: "責任者確認-履歴取得"
checkLocalityControllerProvider: "checkLocalityControllerProvider"
checkLocalityControllerProvider.shape: rectangle
checkLocalityControllerProvider.tooltip: "産地チェッ"
scanCodeProvider: "scanCodeProvider"
scanCodeProvider.shape: rectangle
scanCodeProvider.tooltip: "カメラやバーコードスキャナでスキャンしたコードの状態"
CheckLocalityTopPage.shape: circle
CheckLocalityTopPage.tooltip: "産地チェックメニュー"
CheckLocalityHistoryPage.shape: circle
CheckLocalityHistoryPage.tooltip: "産地チェッ履歴画面"
_BottomBar.shape: circle
CheckLocalityConfirmPage.shape: circle
CheckLocalityConfirmPage.tooltip: "責任者確認"
_BottomBar.shape: circle
CheckLocalityPage.shape: circle
CheckLocalityPage.tooltip: "産地チェッ画面"
_LocalityTable.shape: circle
_LocalityDropdownButton.shape: circle

checkLocalityConfirmControllerProvider -> CheckLocalityTopPage: {style.stroke-width: 4}
checkLocalityConfirmControllerProvider -> CheckLocalityTopPage
checkLocalityHistoryControllerProvider -> CheckLocalityHistoryPage: {style.stroke-width: 4}
checkLocalityHistoryControllerProvider -> CheckLocalityHistoryPage
checkLocalityHistoryControllerProvider -> _BottomBar: {style.stroke-width: 4}
checkLocalityHistoryControllerProvider -> _BottomBar: {style.stroke-dash: 4}
checkLocalityConfirmHistoryControllerProvider -> CheckLocalityConfirmPage: {style.stroke-width: 4}
checkLocalityConfirmControllerProvider -> _BottomBar: {style.stroke-width: 4}
checkLocalityConfirmControllerProvider -> _BottomBar: {style.stroke-dash: 4}
checkLocalityControllerProvider -> CheckLocalityPage: {style.stroke-width: 4}
scanCodeProvider -> CheckLocalityPage
checkLocalityControllerProvider -> CheckLocalityPage
checkLocalityControllerProvider -> CheckLocalityPage: {style.stroke-dash: 4}
checkLocalityControllerProvider -> CheckLocalityPage: {style.stroke-dash: 4}
checkLocalityControllerProvider -> CheckLocalityPage: {style.stroke-dash: 4}
checkLocalityControllerProvider -> CheckLocalityPage: {style.stroke-dash: 4}
checkLocalityControllerProvider -> CheckLocalityPage: {style.stroke-dash: 4}
checkLocalityControllerProvider -> _LocalityTable: {style.stroke-dash: 4}
checkLocalityControllerProvider -> _LocalityDropdownButton: {style.stroke-dash: 4}
checkLocalityServiceProvider -> checkLocalityConfirmControllerProvider: {style.stroke-width: 4}
checkLocalityServiceProvider -> checkLocalityHistoryControllerProvider: {style.stroke-width: 4}
checkLocalityServiceProvider -> checkLocalityConfirmHistoryControllerProvider: {style.stroke-width: 4}
