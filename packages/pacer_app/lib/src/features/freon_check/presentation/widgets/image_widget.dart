import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../exceptions/app_exception.dart';
import '../../application/freon_check_service.dart';

/// select image module
class ImageWidget extends HookConsumerWidget {
  /// init
  const ImageWidget({
    super.key,
    required this.imagePath,
    required this.imageName,
    this.size,
    this.isCacheFileImage = false,
    required this.periodCode,
  });

  /// 写真のPath
  final String imagePath;

  /// 写真名
  final String imageName;

  /// 期間id
  final int periodCode;

  ///　true:写真先は撮影機能から取得したcache path
  ///  false:写真先はlocalです
  final bool isCacheFileImage;

  /// image size
  final double? size;

  static const defaultErrorMessage = '写真のダウンロードに失敗しました。';
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isCacheFileImage) {
      return FutureBuilder<File?>(
        future: _loadCacheImageFile(imagePath),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            final error = snapshot.error;
            final errorMessage = (error is UnknownException) ? error.message : defaultErrorMessage;
            return Center(child: Text(errorMessage));
          } else {
            final file = snapshot.requireData;
            return file != null
                ? Image.file(
                    file,
                    width: size,
                    height: size,
                    fit: BoxFit.cover,
                  )
                : const Text('data');
          }
        },
      );
    } else {
      return FutureBuilder<Uint8List?>(
        future: _loadSavedImageData(
          ref,
          imageUrlPath: imagePath,
          imageName: imageName,
        ),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            final error = snapshot.error;
            final errorMessage = (error is UnknownException) ? error.message : defaultErrorMessage;
            return Center(
              child: SizedBox(
                width: size,
                height: size,
                child: FittedBox(child: Text(errorMessage)),
              ),
            );
          } else {
            final file = snapshot.requireData;
            return file != null
                ? Image.memory(
                    file,
                    width: size,
                    height: size,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Center(
                        child: SizedBox(
                          width: size,
                          height: size,
                          child: const FittedBox(child: Text('写真のデータが無効です。')),
                        ),
                      );
                    },
                  )
                : const Text('data');
          }
        },
      );
    }
  }

  /// 撮影機能からcache fileの写真pathで、写真情報を取得
  Future<File?> _loadCacheImageFile(String path) async {
    final savedImage = File(path);
    final isExists = savedImage.existsSync();
    if (isExists) {
      return savedImage;
    }
    throw Exception('An error occurred while fetching data');
  }

  /// localに保存される写真を取得
  Future<Uint8List?> _loadSavedImageData(
    WidgetRef ref, {
    required String imageUrlPath,
    required String imageName,
  }) async {
    final freonCheckService = ref.watch(freonCheckServiceProvider);

    final imageData = await freonCheckService.getImageByUrl(
      imageUrlPath: imageUrlPath,
      imageName: imageName,
      periodCode: periodCode,
    );
    return imageData;
  }
}
