import 'dart:async';

import 'package:flutter/material.dart';

import 'image_widget.dart';

/// 画像を拡大して表示するダイアログ
Future<void> displayImageWithZoomDialog(
  BuildContext context, {
  /// 写真名
  required String imageName,

  /// 写真path(api取得用）
  required String imagePath,

  /// 期間id
  required int periodCode,

  /// 削除ボタンを表示
  bool isShowDelete = false,

  /// 写真先（true：写真先は撮影機能から取得したcache path。false:写真先はlocalです）
  bool isCacheFileImage = false,

  /// 削除ボタンのcallback
  VoidCallback? onDeletePressed,

  /// OKボタンのcallback
  VoidCallback? onOKPressed,
}) async {
  final texts = Theme.of(context).textTheme;
  const padding = 40;
  final maxWidth = MediaQuery.sizeOf(context).width - padding;

  await showDialog<void>(
    context: context,
    builder: (context) {
      return AlertDialog(
        insetPadding: EdgeInsets.zero,
        content: SizedBox(
          width: maxWidth,
          height: maxWidth,
          child: ImageWidget(
            imagePath: imagePath,
            imageName: imageName,
            isCacheFileImage: isCacheFileImage,
            periodCode: periodCode,
          ),
        ),
        actions: [
          if (isShowDelete)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                onDeletePressed?.call();
              },
              icon: const Icon(Icons.delete),
              label: Text(
                '削除',
                style: texts.headlineSmall,
              ),
            ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              onOKPressed?.call();
            },
            label: Text(
              'OK',
              style: texts.headlineSmall,
            ),
          ),
        ],
      );
    },
  );
}
