import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../pages/form/controller/register_images_state.dart';
import '../pages/select_location_page/controller/freon_check_period_state.dart';
import 'display_image_with_zoom_dialog.dart';
import 'image_widget.dart';

/// select image
class GalleryPicker extends HookConsumerWidget {
  /// init
  const GalleryPicker({
    super.key,
    this.isRegister = false,
    this.size = 80,
    this.onAdd,
    this.onRemove,
    this.max = 0,
  });

  ///　false:設備問題は削除type true:設備問題は登録type
  final bool isRegister;

  /// add callback
  final VoidCallback? onAdd;

  /// remove callback
  final ValueChanged<int>? onRemove;

  /// max image
  final int max;

  /// image size
  final double size;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedImageState = ref.watch(registerImagesStateProvider);

    final periodCode = ref.watch(freonCheckPeriodStateProvider)?.periodCode ?? 0;

    final theme = Theme.of(context);

    return GridView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: size,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: selectedImageState.registerImages.length + 1,
      itemBuilder: (context, i) {
        if (i == selectedImageState.registerImages.length) {
          return Offstage(
            offstage: !isRegister || (max != 0 && selectedImageState.registerImages.length >= max),
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: AspectRatio(
                aspectRatio: 1,
                child: Container(
                  margin: const EdgeInsets.only(
                    top: 5,
                    right: 5,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: theme.dividerColor,
                    ),
                  ),
                  child: Icon(
                    Icons.add,
                    color: theme.dividerColor,
                  ),
                ),
              ),
              onTap: () async {
                onAdd?.call();
              },
            ),
          );
        }
        return Padding(
          padding: const EdgeInsets.only(top: 5, right: 5),
          child: Stack(
            clipBehavior: Clip.none,
            children: <Widget>[
              GestureDetector(
                onTap: () {
                  displayImageWithZoomDialog(
                    context,
                    imageName: selectedImageState.registerImages[i].imageName,
                    imagePath: selectedImageState.registerImages[i].imagePath,
                    isShowDelete: isRegister,
                    isCacheFileImage: isRegister,
                    onDeletePressed: () {
                      ref.read(registerImagesStateProvider.notifier).deleteSelectImageFile(i);
                      onRemove?.call(i);
                    },
                    periodCode: periodCode,
                  );
                },
                child: ImageWidget(
                  imageName: selectedImageState.registerImages[i].imageName,
                  imagePath: selectedImageState.registerImages[i].imagePath,
                  size: size,
                  isCacheFileImage: isRegister,
                  periodCode: periodCode,
                ),
              ),
              if (isRegister)
                Positioned(
                  top: -17,
                  right: -17,
                  child: Offstage(
                    offstage: !isRegister,
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Container(
                          height: 16,
                          width: 16,
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.clear,
                              size: 10,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      onTap: () {
                        ref.read(registerImagesStateProvider.notifier).deleteSelectImageFile(i);
                        onRemove?.call(i);
                      },
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
