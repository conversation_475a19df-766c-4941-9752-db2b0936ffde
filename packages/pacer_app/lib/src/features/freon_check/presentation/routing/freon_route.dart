import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../pages/form/freon_check_register_page.dart';
import '../pages/history_logs/freon_check_history_page.dart';
import '../pages/location/freon_check_equipment_detail_page.dart';
import '../pages/select_location_page/select_location_page.dart';
import '../pages/step/freon_check_location_equiments_page.dart';

/// 場所一覧、選択画面
class FreonSelectLocationRoute extends GoRouteData {
  /// init
  const FreonSelectLocationRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const FreonSelectLocationPage();
}

/// 場所の設備一覧画面
class FreonCheckLocationEquipmentsRoute extends GoRouteData {
  /// init
  const FreonCheckLocationEquipmentsRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const FreonCheckLocationEquipmentsPage();
}

/// 設備詳細一覧
class FreonCheckEquipmentDetailRoute extends GoRouteData {
  /// init
  const FreonCheckEquipmentDetailRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const FreonCheckEquipmentDetailPage();
}

/// フロンチェック履歴一覧
class FreonCheckHistoryRoute extends GoRouteData {
  /// init
  const FreonCheckHistoryRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const FreonCheckHistoryPage();
}

/// フロン点検 問題登録画面
class FreonCheckRegisterRoute extends GoRouteData {
  /// init
  const FreonCheckRegisterRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const FreonCheckRegisterPage();
}
