// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_current_location_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckCurrentLocationStateHash() => r'6cb4aa38f20c6c84c16823886f938702d6c176cd';

///  選択してる場所情報
///
/// Copied from [FreonCheckCurrentLocationState].
@ProviderFor(FreonCheckCurrentLocationState)
final freonCheckCurrentLocationStateProvider =
    AutoDisposeNotifierProvider<FreonCheckCurrentLocationState, LocationProcess?>.internal(
  FreonCheckCurrentLocationState.new,
  name: r'freonCheckCurrentLocationStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckCurrentLocationStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckCurrentLocationState = AutoDisposeNotifier<LocationProcess?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
