import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/freon_check_register.dart';
import '../../../config/strings.dart';
import '../controller/form_data_controller.dart';
import 'custom_text_field.dart';
import 'select_status_item.dart';

/// 点検-画面 table
class FreonCheckFormTable extends ConsumerWidget {
  /// constructor
  const FreonCheckFormTable({
    super.key,
    required this.freonCheckRegister,
    required this.isRegister,
  });

  /// 登録画面情報
  final FreonCheckRegister freonCheckRegister;

  /// 登録type
  final bool isRegister;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final inputEquipmentNumber = ref.watch(inputEquipmentNumberStateProvider);
    final inputErrorCode = ref.watch(inputErrorCodeStateProvider);
    final inputRemark = ref.watch(inputRemarkStateProvider);

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.32), // 1列目は全体の50%
        1: FractionColumnWidth(0.68), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(6),
        ),
      ),
      children: [
        TableRow(
          children: [
            /// 場所
            const _ItemNameText(kLocation),
            _ValueText(freonCheckRegister.placeName ?? ''),
          ],
        ),
        TableRow(
          children: [
            /// 設備名
            const _ItemNameText(kEquipmentName),
            _ValueText(freonCheckRegister.equipmentName ?? ''),
          ],
        ),
        TableRow(
          children: [
            /// 状態
            const _ItemNameText(kState),
            SelectStatusItem(isRegister: isRegister),
          ],
        ),
        TableRow(
          children: [
            /// 設備NO
            const _ItemNameText(kEquipmentNo),
            ColoredBox(
              color: colors.button,
              child: CustomTextField(
                isReadonly: !isRegister,
                inputText: inputEquipmentNumber,
                onChanged: (value) => ref.read(inputEquipmentNumberStateProvider.notifier).updateNumber(value),
              ),
            ),
          ],
        ),
        TableRow(
          children: [
            /// エラーコード
            _ItemNameText(
              kErrorCode,
              textStyle: texts.bodyMedium,
            ),
            ColoredBox(
              color: colors.button,
              child: CustomTextField(
                isReadonly: !isRegister,
                inputText: inputErrorCode,
                onChanged: (value) => ref.read(inputErrorCodeStateProvider.notifier).updateNumber(value),
              ),
            ),
          ],
        ),
        TableRow(
          children: [
            /// 備考（必須入力ではありません）
            const _ItemNameText(kRemarks),
            ColoredBox(
              color: colors.button,
              child: CustomTextField(
                isReadonly: !isRegister,
                inputText: inputRemark,
                hintText: kRemarksTip,
                onChanged: (value) => ref.read(inputRemarkStateProvider.notifier).updateNumber(value),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text, {this.textStyle});

  final String text;

  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
        child: switch (text.isEmpty) {
          true => const Text(''),
          false => FittedBox(
              alignment: Alignment.centerLeft,
              fit: BoxFit.scaleDown,
              child: Text(
                maxLines: 1,
                text,
                style: textStyle ?? texts.bodyLarge,
                textAlign: TextAlign.right,
              ),
            ),
        },
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
        ),
      ),
    );
  }
}
