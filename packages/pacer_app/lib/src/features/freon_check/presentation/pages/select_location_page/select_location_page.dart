import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../routing/app_router.dart';
import '../../../../../utils/async_util.dart';
import '../../../../../utils/keep_alive.dart';
import '../../../../../utils/use_router_effect.dart';
import '../../../../acceptance_inspection/presentation/widgets/bottom_tab_bar.dart';
import '../../../application/freon_check_service.dart';
import '../../../domain/location_process.dart';
import '../../config/strings.dart';
import '../../routing/freon_route.dart';
import 'controller/freon_check_current_location_state.dart';
import 'controller/freon_check_local_db_sync_state.dart';
import 'controller/freon_check_locations_state.dart';
import 'controller/freon_check_period_state.dart';
import 'widgets/simple_inspection_location_cell.dart';
import 'widgets/simple_inspection_location_list_header.dart';

/// フロン点検
class FreonSelectLocationPage extends HookConsumerWidget {
  /// init
  const FreonSelectLocationPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  /// マスタtableの更新が失敗するときメッセージ
  static const updateErrorMessage = '設備マスタの取得に失敗しました。再度アプリを開き直してください。';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    /// 期間情報
    final periodState = ref.watch(freonCheckPeriodStateProvider);

    /// 未登録データがあるかとかのフラグ
    final hasUnregistered = ref.watch(getHasUnregisteredDataProvider);

    /// 場所情報（list<LocationProcess>）
    final locationListState = useState<List<LocationProcess>>([]);

    useEffect(
      () {
        delayed(() {
          /// table情報を更新
          ref.read(freonCheckLocalDBSyncStateProvider.notifier).syncTables();
        });
        return null;
      },
      [],
    );

    /// 画面1（期間以内の場所リスト）のroute path
    const equipmentDetail = '/freon';

    /// 画面1（期間以内の場所リスト）はroot path
    const equipmentDetailPreviousRootPath = '/freon/';
    useRouterEffect(context, (previous, current) {
      if (current == equipmentDetail && previous.contains(equipmentDetailPreviousRootPath)) {
        ref.read(freonCheckLocationsStateProvider.notifier).refreshWorkProgressAndePlaceState();
      }
    });

    ref
      ..keepOnly(freonCheckCurrentLocationStateProvider)
      ..listen(freonCheckLocalDBSyncStateProvider, (_, state) {
        switch (state) {
          case AsyncData():
            {
              ref.read(freonCheckLocationsStateProvider.notifier).update();
            }
          case AsyncLoading():
            {
              log('globalLoadingServiceProvider is loading');
            }
          case AsyncError():
            {
              /// テーブルの更新が失敗しても、画面で操作を続けることができます。
              ref.read(freonCheckLocationsStateProvider.notifier).update();
              _showErrorMessageBySnackBar(updateErrorMessage, ref);
            }
        }
      })
      ..listen(freonCheckLocationsStateProvider, (_, state) {
        switch (state) {
          case AsyncData(:final value?):
            {
              locationListState.value = value;
              ref.invalidate(getHasUnregisteredDataProvider);
            }
          case AsyncError():
            {
              _showErrorMessageBySnackBar('フロン点検情報の更新に失敗しました。', ref);
            }
          case _:
            {
              log('globalLoadingServiceProvider is loading');
            }
        }
      });
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: colors.primary,
        titleTextStyle: texts.titleMedium?.copyWith(color: colors.onPrimary),
        title: const Text(kSimpleInspectionTitle),
        automaticallyImplyLeading: false,
        centerTitle: true,
        actions: [
          switch (hasUnregistered) {
            AsyncLoading() => const Center(child: CircularProgressIndicator()),
            AsyncData<bool>(:final value) when value => IconButton(
                color: colors.onPrimary,
                icon: const Icon(Icons.delete),
                tooltip: 'データベースを再ダウンロード可能にする',
                onPressed: () {
                  _reacquisitionMaster(ref);
                },
              ),
            AsyncData<bool>() => const SizedBox.shrink(),
            AsyncError() => const SizedBox.shrink(),
          },
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: _lineHeight,
          horizontal: _edge,
        ),
        child: Column(
          children: [
            SimpleInspectionLocationListHeader(
              freonPeriodName: periodState?.periodName ?? '',
            ),
            Expanded(
              child: ListView.separated(
                itemBuilder: (context, index) {
                  final locationProcess = locationListState.value[index];

                  return SimpleInspectionLocationCell(
                    onPressed: () {
                      const FreonCheckLocationEquipmentsRoute().go(context);
                      ref.read(freonCheckCurrentLocationStateProvider.notifier).update(locationProcess);
                    },
                    locationProcess: locationProcess,
                  );
                },
                separatorBuilder: (context, _) => const Gap(6),
                itemCount: locationListState.value.length,
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomTabBar(
        leading: PacerBackButton(onPressed: () => context.pop()),
      ),
    );
  }

  /// 画面共通SnackBar、エラーなど表示するため
  void _showErrorMessageBySnackBar(String text, WidgetRef ref) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.error,
        content: Text(text),
      ),
    );
  }

  /// master tableを再取得
  Future<void> _reacquisitionMaster(WidgetRef ref) async {
    final ok = await showAlertDialog(
      context: ref.context,
      title: '未登録のデータが残っていますが、一度削除すると復旧ができません。削除してもよろしいですか？',
      cancelActionText: 'キャンセル',
      defaultActionText: '削除',
    );

    if (ok ?? false) {
      /// table情報を更新
      await ref.read(freonCheckLocalDBSyncStateProvider.notifier).syncTablesAndDeleteCache();
    }
  }
}
