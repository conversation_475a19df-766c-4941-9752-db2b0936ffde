// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'current_equipment_detail.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentEquipmentDetailHash() => r'9f439d626352f4897cffcce9d707abb68f0fe1c1';

/// 設備の結果情報
///
/// Copied from [CurrentEquipmentDetail].
@ProviderFor(CurrentEquipmentDetail)
final currentEquipmentDetailProvider = AutoDisposeNotifierProvider<CurrentEquipmentDetail, EquipmentDetail?>.internal(
  CurrentEquipmentDetail.new,
  name: r'currentEquipmentDetailProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$currentEquipmentDetailHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentEquipmentDetail = AutoDisposeNotifier<EquipmentDetail?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
