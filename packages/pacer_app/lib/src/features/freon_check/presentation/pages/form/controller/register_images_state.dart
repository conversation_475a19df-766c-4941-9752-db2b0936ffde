import 'package:clock/clock.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../domain/equipment_register_image_job.dart';
import '../../../../domain/freon_check_image.dart';

part 'register_images_state.g.dart';

/// 登録画面：写真情報state
@riverpod
class RegisterImagesState extends _$RegisterImagesState {
  /// init
  @override
  EquipmentRegisterImageJob build() => const EquipmentRegisterImageJob(
        readOnly: true,
        registerImages: [],
      );

  /// readOnlyと写真リストを更新
  void updateEquipmentRegisterImageJob({
    bool readOnly = true,
    List<FreonCheckImage>? registerImages,
  }) {
    final registerImageJob = state;
    state = registerImageJob.copyWith(
      readOnly: readOnly,
      imagePaths: registerImages,
    );
  }

  /// 写真を追加
  void addSelectImageFile(String imageFilePath) {
    final registerImageJob = state;
    final imageFiles = List<FreonCheckImage>.from(registerImageJob.registerImages);

    /// 写真を追加する時imageNameはtimestamp
    final imageName = (clock.now().millisecondsSinceEpoch ~/ 1000).toString();
    imageFiles.add(
      FreonCheckImage(
        imageName: imageName,
        imagePath: imageFilePath,
      ),
    );
    state = registerImageJob.copyWith(
      imagePaths: imageFiles,
      isUploadedImage: false,
    );
  }

  /// 写真を消します
  Future<void> deleteSelectImageFile(int index) async {
    final registerImageJob = state;
    final imageFiles = registerImageJob.registerImages;
    if (imageFiles.length > index) {
      imageFiles.removeAt(index);
    }
    state = registerImageJob.copyWith(imagePaths: imageFiles);
  }
}
