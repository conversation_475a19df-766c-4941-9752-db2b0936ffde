import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';

/// text field
class CustomTextField extends HookConsumerWidget {
  /// constructor
  const CustomTextField({
    super.key,
    required this.inputText,
    this.onFieldSubmitted,
    this.onChanged,
    this.hintText = '',
    this.autofocus = false,
    this.onPressed,
    this.focusNode,
    this.keyboardType,
    this.isReadonly = true,
  });

  /// ヒントテキスト
  final String hintText;

  /// 入力テキスト
  final String inputText;

  /// 入力時のコールバック
  final void Function(String)? onChanged;

  /// submit call back
  final void Function(String)? onFieldSubmitted;

  /// ボタン押下時のコールバック
  final VoidCallback? onPressed;

  /// オートフォーカス
  final bool autofocus;

  /// フォーカスノード
  final FocusNode? focusNode;

  /// keyboard type
  final TextInputType? keyboardType;

  /// 編集フラグ
  final bool isReadonly;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textEditingController = useTextEditingController();
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    useEffect(
      () {
        textEditingController.text = inputText;

        return;
      },
    );

    return Focus(
      child: TextFormField(
        readOnly: isReadonly,
        focusNode: focusNode,
        controller: textEditingController,
        keyboardType: keyboardType,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        decoration: InputDecoration(
          filled: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          fillColor: colors.button,
          focusColor: colors.button,
          hintText: hintText,
          hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
          counterText: '',
        ),
        onFieldSubmitted: submit,
        autofocus: autofocus,
      ),
      onFocusChange: (hasFocus) {
        if (!hasFocus) {
          changeFormatText(textEditingController);
        }
      },
    );
  }

  /// 数値に変換する
  void changeNumber(TextEditingController controller) {
    onPressed?.call();
  }

  /// change check
  void changeCheckFormatText(
    BuildContext context,
    TextEditingController controller,
  ) {
    onChanged?.call(controller.text);
  }

  /// 入力値を変換する
  void changeFormatText(TextEditingController controller) {
    onChanged?.call(controller.text);
  }

  /// done
  void submit(String inputValue) {
    onFieldSubmitted?.call(inputValue);
  }
}
