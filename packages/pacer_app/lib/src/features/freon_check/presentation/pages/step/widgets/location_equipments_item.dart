import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../domain/location_equipment.dart';
import '../../../config/strings.dart';
import '../../../widgets/image_widget.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';

/// 一覧表示
class LocationEquipmentsItem extends ConsumerWidget {
  /// 標準コンストラクタ
  const LocationEquipmentsItem({
    super.key,
    required this.equipment,
    required this.onPressed,
  });

  /// クリックイベントのコールバック
  final VoidCallback onPressed;

  /// 場所の設備情報
  final LocationEquipment equipment;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final containerColor = colors.surface;

    final textColor = colors.onSurface;

    final textTheme = texts.bodyMedium?.copyWith(
      color: textColor,
      height: 2,
    );

    final periodCode = ref.watch(freonCheckPeriodStateProvider)?.periodCode ?? 0;

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: containerColor,
          border: Border.fromBorderSide(
            BorderSide(
              color: colors.outlineVariant,
            ),
          ),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(8, 8, 12, 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: switch (equipment.equipmentImagePath.isNotEmpty && equipment.equipmentImageName.isNotEmpty) {
                    true => ImageWidget(
                        imagePath: equipment.equipmentImagePath,
                        imageName: equipment.equipmentImageName,
                        size: 85,
                        periodCode: periodCode,
                      ),
                    false => const SizedBox(
                        width: 85,
                        height: 85,
                      ),
                  },
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      equipment.equipmentName ?? '',
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      kNumberOfQuestions.replaceAll(
                        '***',
                        equipment.questionCountString,
                      ),
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    RichText(
                      text: TextSpan(
                        text: '$kState:',
                        style: textTheme,
                        children: <TextSpan>[
                          TextSpan(
                            text: equipment.equipmentCheckStatus.message,
                            style: texts.bodyMedium?.copyWith(
                              color: switch (equipment.equipmentCheckStatus) {
                                EquipmentCheckStatus.notStarted => null,
                                EquipmentCheckStatus.problem => colors.error,
                                _ => colors.primary,
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
