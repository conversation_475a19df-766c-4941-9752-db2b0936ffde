// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_history_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$equipmentHistoryStateHash() => r'dfbb80ab4973f474f73c1028f5f3c91b3ecd7d30';

/// 設備の履歴情報State
///
/// Copied from [EquipmentHistoryState].
@ProviderFor(EquipmentHistoryState)
final equipmentHistoryStateProvider =
    AutoDisposeAsyncNotifierProvider<EquipmentHistoryState, List<EquipmentDetail>>.internal(
  EquipmentHistoryState.new,
  name: r'equipmentHistoryStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$equipmentHistoryStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EquipmentHistoryState = AutoDisposeAsyncNotifier<List<EquipmentDetail>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
