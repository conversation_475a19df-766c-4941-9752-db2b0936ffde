// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_local_db_sync_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckLocalDBSyncStateHash() => r'72d3d2bcfffd33d18c3b08702b9080e8b32bf9f2';

/// master table情報を更新するstate
///
/// Copied from [FreonCheckLocalDBSyncState].
@ProviderFor(FreonCheckLocalDBSyncState)
final freonCheckLocalDBSyncStateProvider =
    AutoDisposeNotifierProvider<FreonCheckLocalDBSyncState, AsyncValue<void>>.internal(
  FreonCheckLocalDBSyncState.new,
  name: r'freonCheckLocalDBSyncStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckLocalDBSyncStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckLocalDBSyncState = AutoDisposeNotifier<AsyncValue<void>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
