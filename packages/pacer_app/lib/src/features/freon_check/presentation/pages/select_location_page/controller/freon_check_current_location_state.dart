import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../domain/location_process.dart';

part 'freon_check_current_location_state.g.dart';

///  選択してる場所情報
@riverpod
class FreonCheckCurrentLocationState extends _$FreonCheckCurrentLocationState {
  @override
  LocationProcess? build() {
    return null;
  }

  /// 選択してる場所情報を更新
  void update(LocationProcess? locationProcess) {
    if (locationProcess != null) {
      state = locationProcess;
    }
  }
}
