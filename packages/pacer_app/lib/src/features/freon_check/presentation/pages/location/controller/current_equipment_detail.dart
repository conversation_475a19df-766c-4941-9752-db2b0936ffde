import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../domain/equipment_detail.dart';

part 'current_equipment_detail.g.dart';

/// 設備の結果情報
@riverpod
class CurrentEquipmentDetail extends _$CurrentEquipmentDetail {
  @override
  EquipmentDetail? build() => null;

  /// change state
  void updateState(EquipmentDetail? equipmentDetail) {
    log('updateState');
    state = equipmentDetail;
  }
}
