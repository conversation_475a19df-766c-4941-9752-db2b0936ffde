import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../exceptions/app_exception.dart';
import '../../../../../routing/app_router.dart';
import '../../../../../utils/keep_alive.dart';
import '../../../../../utils/use_router_effect.dart';
import '../../../../acceptance_inspection/presentation/widgets/bottom_tab_bar.dart';
import '../../../domain/equipment_detail.dart';
import '../../../domain/equipment_detail_state_enum.dart';
import '../../../domain/equipment_place_state_enum.dart';
import '../../config/strings.dart';
import '../../routing/freon_route.dart';
import '../../widgets/display_image_with_zoom_dialog.dart';
import '../../widgets/image_widget.dart';
import '../../widgets/out_line_round_text_button.dart';
import '../select_location_page/controller/freon_check_current_location_state.dart';
import '../select_location_page/controller/freon_check_period_state.dart';
import 'controller/current_equipment_detail.dart';
import 'controller/equipment_detail_state.dart';
import 'controller/equipment_register_state.dart';
import 'widgets/equipment_detail_item.dart';

/// フロン点検、設備画面
class FreonCheckEquipmentDetailPage extends HookConsumerWidget {
  /// init
  const FreonCheckEquipmentDetailPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;

    /// 画面３（設備詳細）のroute path
    const equipmentDetail = '/freon/target/detail';

    /// 画面３（設備詳細）のroot path
    const equipmentDetailPreviousRootPath = '/freon/target/detail/';

    final equipmentDetailState = ref.watch(equipmentDetailStateProvider);

    final currentLocation = ref.watch(freonCheckCurrentLocationStateProvider);

    final periodCode = ref.watch(freonCheckPeriodStateProvider)?.periodCode ?? 0;
    ref
      ..keepOnly(currentEquipmentDetailProvider)
      ..listen(equipmentRegisterStateProvider, (_, state) {
        switch (state) {
          case AsyncData(:final value) when value:
            {
              _showMessageBySnackBar('登録しました', ref);
            }
          case AsyncLoading():
            {
              log('globalLoadingServiceProvider is loading');
            }
          case AsyncError(:final error):
            {
              final errorMessage = (error is AppException) ? error.message : '更新が失敗しました';
              _showErrorMessageBySnackBar(errorMessage, ref);
            }
          case _:
            {}
        }
      });

    useRouterEffect(context, (previous, current) {
      if (current == equipmentDetail && previous.contains(equipmentDetailPreviousRootPath)) {
        ref.invalidate(equipmentDetailStateProvider);
      }
    });

    return Scaffold(
      appBar: PacerAppBar(
        title: const Text(kSimpleInspectionTitle),
        context: context,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: _lineHeight,
          horizontal: _edge,
        ),
        child: switch (equipmentDetailState) {
          AsyncLoading() => const Center(child: CircularProgressIndicator()),
          AsyncData(:final value?) => Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ColoredBox(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  child: Text(currentLocation?.placeName ?? ''),
                ),
                const Gap(3),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _ImageItem(
                      imagePath: value.equipmentImageDetail?.acceptableImagePath ?? '',
                      imageName: value.equipmentImageDetail?.acceptableImageName ?? '',
                      periodCode: periodCode,
                    ),
                    _ImageItem(
                      imagePath: value.equipmentImageDetail?.unacceptableImagePath ?? '',
                      imageName: value.equipmentImageDetail?.unacceptableImageName ?? '',
                      periodCode: periodCode,
                      isLeftIcon: false,
                    ),
                  ],
                ),
                const Gap(6),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        _onRegistrationNoProblem(
                          ref,
                          placeState: value.equipmentPlaceStateEnum,
                          detailState: value.equipmentDetailStateEnum,
                          equipmentDetail: value.equipmentDetailList?.first,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.cyan,
                      ),
                      child: Text(
                        kNoProblem,
                        style: texts.bodyLarge?.copyWith(color: Colors.white),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        _onRegistrationProblem(
                          ref,
                          placeState: value.equipmentPlaceStateEnum,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                      child: Text(
                        kLoginProblem,
                        style: texts.bodyLarge?.copyWith(color: Colors.white),
                      ),
                    ),
                  ],
                ),
                const Gap(8),
                switch (value.equipmentDetailList?.isNotEmpty ?? false) {
                  true => Expanded(
                      child: ListView.separated(
                        itemBuilder: (context, index) {
                          final equipmentDetail = value.equipmentDetailList?[index];
                          return EquipmentDetailItem(
                            equipment: equipmentDetail,
                            onPressed: () {
                              ref.read(currentEquipmentDetailProvider.notifier).updateState(equipmentDetail);
                              const FreonCheckRegisterRoute().go(context);
                            },
                          );
                        },
                        separatorBuilder: (context, _) => const Gap(6),
                        itemCount: value.equipmentDetailList?.length ?? 0,
                      ),
                    ),
                  false => Text(
                      kNoLogs,
                      style: texts.bodyLarge,
                    ),
                },
              ],
            ),
          _ => const SizedBox.shrink(),
        },
      ),
      bottomNavigationBar: BottomTabBar(
        leading: PacerBackButton(
          onPressed: () => context.pop(),
        ),
        actions: [
          OutlineRoundTextButton(
            kHistoryLogs,
            onPressed: () => const FreonCheckHistoryRoute().go(context),
          ),
        ],
      ),
    );
  }

  /// 問題なしを登録
  void _onRegistrationNoProblem(
    WidgetRef ref, {
    required EquipmentPlaceStateEnum placeState,
    required EquipmentDetailStateEnum detailState,
    required EquipmentDetail? equipmentDetail,
  }) {
    switch (placeState) {
      case EquipmentPlaceStateEnum.isInProgress when detailState == EquipmentDetailStateEnum.isNotExist:
        {
          ref.read(equipmentRegisterStateProvider.notifier).registerNoProblem();
        }
      case EquipmentPlaceStateEnum.isNotExist:
        {
          _showErrorMessageBySnackBar(
            '点検期間以外のため、問題なしにできません。',
            ref,
          );
        }
      case EquipmentPlaceStateEnum.isCompleted:
        {
          _showErrorMessageBySnackBar(
            '既に作業済みです。修正が必要な場合は「途中報告」から行ってください。',
            ref,
          );
        }
      case _ when detailState == EquipmentDetailStateEnum.isNormal:
        {
          _showErrorMessageBySnackBar(
            '点検履歴があるため、問題なしにできません。',
            ref,
          );
        }
      case _:
        {}
    }
  }

  /// 問題あるを登録
  void _onRegistrationProblem(
    WidgetRef ref, {
    required EquipmentPlaceStateEnum placeState,
  }) {
    switch (placeState) {
      case EquipmentPlaceStateEnum.isCompleted:
        {
          _showErrorMessageBySnackBar(
            '既に作業済みです。修正が必要な場合は「途中報告」から行ってください。',
            ref,
          );
        }
      case EquipmentPlaceStateEnum.isInProgress:
        {
          ref.read(currentEquipmentDetailProvider.notifier).updateState(null);
          const FreonCheckRegisterRoute().go(ref.context);
        }
      case EquipmentPlaceStateEnum.isNotExist:
        {
          _showErrorMessageBySnackBar('点検期間以外のため、問題登録できません。', ref);
        }
    }
  }

  /// 画面共通SnackBar、エラーなど表示するため
  void _showErrorMessageBySnackBar(String text, WidgetRef ref) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.error,
        content: Text(text),
      ),
    );
  }

  /// 画面共通SnackBar、表示するため
  void _showMessageBySnackBar(String text, WidgetRef ref) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.secondary,
        content: Text(text),
      ),
    );
  }
}

class _ImageItem extends StatelessWidget {
  const _ImageItem({
    this.isLeftIcon = true,
    required this.imagePath,
    required this.imageName,
    required this.periodCode,
  });

  /// is left icon
  final bool isLeftIcon;

  /// image url
  final String imagePath;

  /// image name
  final String imageName;

  /// 期間ID
  final int periodCode;
  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    return Row(
      children: [
        Text(
          isLeftIcon ? '○' : '×',
          style: texts.titleLarge?.copyWith(
            color: isLeftIcon ? colors.primary : colors.error,
            fontSize: isLeftIcon ? 20 : 30,
            fontWeight: FontWeight.bold,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 8),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: switch (imagePath.isNotEmpty && imageName.isNotEmpty) {
              true => GestureDetector(
                  onTap: () {
                    displayImageWithZoomDialog(
                      context,
                      imagePath: imagePath,
                      imageName: imageName,
                      periodCode: periodCode,
                    );
                  },
                  child: ImageWidget(
                    imagePath: imagePath,
                    imageName: imageName,
                    size: 85,
                    periodCode: periodCode,
                  ),
                ),
              false => const SizedBox(
                  width: 85,
                  height: 85,
                ),
            },
          ),
        ),
      ],
    );
  }
}
