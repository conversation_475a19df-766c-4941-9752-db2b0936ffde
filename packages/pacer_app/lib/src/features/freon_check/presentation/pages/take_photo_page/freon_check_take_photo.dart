import 'dart:developer';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../form/controller/register_images_state.dart';

/// CameraApp is the Main Application.
class FreonCheckCameraApp extends ConsumerStatefulWidget {
  /// Default Constructor
  const FreonCheckCameraApp({super.key, required this.camera});

  /// 指定されたカメラ
  final CameraDescription camera;

  @override
  ConsumerState<FreonCheckCameraApp> createState() => _CameraAppState();
}

class _CameraAppState extends ConsumerState<FreonCheckCameraApp> {
  late CameraController _controller;
  late Future<void> _initializeControllerFuture;

  File? imageFile;

  @override
  void initState() {
    super.initState();
    // To display the current output from the Camera,
    // create a CameraController.
    _controller = CameraController(
      // Get a specific camera from the list of available cameras.
      widget.camera,
      // Define the resolution to use.
      ResolutionPreset.medium,
      enableAudio: false,
    );

    // Next, initialize the controller. This returns a Future.
    _initializeControllerFuture = _controller.initialize();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('撮影してください'),
        automaticallyImplyLeading: false,
      ),
      floatingActionButton: switch (imageFile) {
        final _? => FloatingActionButton.extended(
            onPressed: () {
              setState(() {
                imageFile = null;
              });
            },
            label: const Text('再撮影'),
            icon: const Icon(Icons.camera_alt),
          ),
        null => FloatingActionButton(
            onPressed: () async {
              if (imageFile != null) {
                setState(() {
                  imageFile = null;
                });
                await _initializeControllerFuture;

                return;
              }

              try {
                // Ensure that the camera is initialized.
                await _initializeControllerFuture;

                // Attempt to take a picture and then get the location
                // where the image file is saved.
                final image = await _controller.takePicture();

                log('image: ${image.path}');
                setState(() {
                  imageFile = File(image.path);
                });
              } catch (e) {
                // If an error occurs, log the error to the console.
                debugPrint(e.toString());
              }
            },
            child: const Icon(Icons.camera_alt),
          ),
      },
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const BackButton(),
            if (imageFile case final file?)
              ElevatedButton.icon(
                onPressed: () async {
                  ref.read(registerImagesStateProvider.notifier).addSelectImageFile(file.path);

                  /// 撮影するとき、ネットワークは良くないので、アップロードしない
                  Navigator.of(context).pop();
                },
                icon: const Icon(Icons.check),
                label: const Text('登録'),
                style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(Colors.blue),
                  foregroundColor: WidgetStateProperty.all(Colors.white),
                  elevation: WidgetStateProperty.all(8),
                ),
              )
            else
              const SizedBox(),
          ],
        ),
      ),
      body: SafeArea(
        child: Center(
          child: FutureBuilder<void>(
            future: _initializeControllerFuture,
            builder: (context, snapshot) {
              switch (imageFile) {
                case final file?:
                  return Image.file(file);
                case _:
                  switch (snapshot.connectionState) {
                    case ConnectionState.done:
                      return CameraPreview(_controller);
                    case _:
                      return const Center(child: CircularProgressIndicator());
                  }
              }
            },
          ),
        ),
      ),
    );
  }
}
