import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../config/strings.dart';
import '../../../widgets/out_line_round_text_button.dart';

/// 場所設備リスト画面のtab bar
class LocationEquipmentsTabBar extends ConsumerWidget {
  /// constructor
  const LocationEquipmentsTabBar({
    super.key,
    required this.onRefreshPressed,
    required this.onIntermediateReportPressed,
    required this.onFinalReportPressed,
    this.isCurrentTimeInPeriod,
  });

  /// 完了ボタン、途中ボタンの表示
  final bool? isCurrentTimeInPeriod;

  /// 最新取得ボタンtouchのCallback
  final VoidCallback onRefreshPressed;

  /// 途中ボタンtouchのCallback
  final VoidCallback onIntermediateReportPressed;

  /// 完了ボタンtouchのCallback
  final VoidCallback onFinalReportPressed;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BottomAppBar(
      child: Row(
        children: [
          Expanded(
            child: BackButton(
              onPressed: () => context.pop(),
            ),
          ),
          if (isCurrentTimeInPeriod != null)
            Expanded(
              flex: 2,
              child: OutlineRoundTextButton(
                kGetNewData,
                padding: const EdgeInsets.symmetric(horizontal: 6),
                onPressed: onRefreshPressed,
              ),
            ),
          if (isCurrentTimeInPeriod ?? false)
            Expanded(
              flex: 2,
              child: OutlineRoundTextButton(
                kIntermediateReport,
                padding: const EdgeInsets.symmetric(horizontal: 6),
                onPressed: onIntermediateReportPressed,
              ),
            ),
          if (isCurrentTimeInPeriod ?? false)
            Expanded(
              flex: 2,
              child: OutlineRoundTextButton(
                kFinalReport,
                padding: const EdgeInsets.symmetric(horizontal: 6),
                onPressed: onFinalReportPressed,
              ),
            ),
        ],
      ),
    );
  }
}
