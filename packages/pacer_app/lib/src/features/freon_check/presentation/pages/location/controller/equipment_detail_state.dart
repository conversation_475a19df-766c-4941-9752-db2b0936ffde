import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../../../domain/equipment_detail_state_enum.dart';
import '../../../../domain/location_equipment_detail.dart';
import '../../select_location_page/controller/freon_check_current_location_state.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';
import '../../step/controller/freon_check_select_equipment_state.dart';

part 'equipment_detail_state.g.dart';

/// 初期化設備詳細情報取得State
@riverpod
class EquipmentDetailState extends _$EquipmentDetailState {
  @override
  Future<LocationEquipmentDetail?> build() async {
    ref.showGlobalLoading();
    try {
      final freonCheckService = ref.read(freonCheckServiceProvider);

      final periodCode = ref.read(freonCheckPeriodStateProvider)?.periodCode;
      if (periodCode == null) {
        throw UnknownException('期間情報の取得が失敗しました。');
      }
      final placeCode = ref.read(freonCheckCurrentLocationStateProvider)?.placeCode;
      if (placeCode == null) {
        throw UnknownException('場所情報の取得が失敗しました。');
      }

      final equipmentCode = ref.read(freonCheckSelectEquipmentStateProvider)?.equipmentCode;
      if (equipmentCode == null) {
        throw UnknownException('設備情報の取得が失敗しました。');
      }

      final placeState = await freonCheckService.getPeriodOfEquipment(
        placeCode: placeCode,
        periodCode: periodCode,
      );

      final equipmentImageDetail = await freonCheckService.getEquipmentImageDetail(equipmentCode: equipmentCode);

      final equipmentDetailList = await freonCheckService.listEquipmentDetail(
        placeCode: placeCode,
        periodCode: periodCode,
        equipmentCode: equipmentCode,
      );
      final equipmentDetailStateEnum = switch (equipmentDetailList?.isEmpty ?? true) {
        true => EquipmentDetailStateEnum.isNotExist,
        false => EquipmentDetailStateEnum.isNormal,
      };

      return LocationEquipmentDetail(
        equipmentPlaceStateEnum: placeState,
        equipmentDetailStateEnum: equipmentDetailStateEnum,
        equipmentImageDetail: equipmentImageDetail,
        equipmentDetailList: equipmentDetailList,
      );
    } on Exception catch (e) {
      log(e.toString());
      return null;
    }
  }
}
