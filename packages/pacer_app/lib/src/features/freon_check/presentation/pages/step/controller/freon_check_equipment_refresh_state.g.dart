// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_equipment_refresh_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckEquipmentRefreshStateHash() => r'b5ddd7e95f50dc78697f6ae04c84453d9422ebd8';

/// 最新情報を更新するstate
///
/// Copied from [FreonCheckEquipmentRefreshState].
@ProviderFor(FreonCheckEquipmentRefreshState)
final freonCheckEquipmentRefreshStateProvider =
    AutoDisposeNotifierProvider<FreonCheckEquipmentRefreshState, AsyncValue<bool>>.internal(
  FreonCheckEquipmentRefreshState.new,
  name: r'freonCheckEquipmentRefreshStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckEquipmentRefreshStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckEquipmentRefreshState = AutoDisposeNotifier<AsyncValue<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
