import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../../../domain/freon_check_register.dart';
import '../../location/controller/current_equipment_detail.dart';
import '../../select_location_page/controller/freon_check_current_location_state.dart';
import '../../step/controller/freon_check_select_equipment_state.dart';
import 'form_data_controller.dart';
import 'register_images_state.dart';
import 'register_states_state.dart';

part 'freon_check_register_state.g.dart';

/// 初期化設備登録情報取得State
@riverpod
class FreonCheckRegisterState extends _$FreonCheckRegisterState {
  @override
  Future<FreonCheckRegister?> build() async {
    ref.showGlobalLoading();
    try {
      final place = ref.read(freonCheckCurrentLocationStateProvider);
      if (place == null) {
        throw UnknownException('場所情報の取得が失敗しました。');
      }

      final equipment = ref.read(freonCheckSelectEquipmentStateProvider);
      if (equipment == null) {
        throw UnknownException('設備情報の取得が失敗しました。');
      }

      final freonCheckService = ref.read(freonCheckServiceProvider);
      final equipmentStateList = await freonCheckService.listRegisterStates(
        equipmentCode: equipment.equipmentCode,
      );

      final equipmentDetail = ref.read(currentEquipmentDetailProvider);
      final historyResultId = equipmentDetail?.historyResultId;

      final isUploaded = switch (historyResultId) {
        null => false,
        _ => await freonCheckService.getIsUploaded(
            historyResultId: historyResultId,
          ),
      };

      /// 最大写真数
      final maxImagesCount = await freonCheckService.getMaxPhoto(equipment.equipmentCode);

      final freonCheckRegister = FreonCheckRegister.fromMap(
        locationProcess: place,
        locationEquipment: equipment,
        registerStates: equipmentStateList,
        isUploaded: isUploaded,
        maxImagesCount: maxImagesCount,
      );

      /// state listを更新
      ref.read(registerStatesStateProvider.notifier).updateList(freonCheckRegister.registerStates ?? []);

      /// 履歴問題の場合
      if (historyResultId != null) {
        final registeredDetail = await freonCheckService.getEquipmentRegisteredDetail(historyResultId);

        final codeList = registeredDetail?.listEquipmentStateCodes();
        final nameList = codeList == null ? null : freonCheckRegister.listStatusName(codeList);

        ref.read(selectStatusStateProvider.notifier).updateSelectStatusByNameList(nameList);
        ref.read(inputEquipmentNumberStateProvider.notifier).updateNumber(registeredDetail?.equipmentNumber ?? '');
        ref.read(inputErrorCodeStateProvider.notifier).updateNumber(registeredDetail?.errorCode ?? '');

        ref.read(inputRemarkStateProvider.notifier).updateNumber(registeredDetail?.comment ?? '');

        ref.read(registerImagesStateProvider.notifier).updateEquipmentRegisterImageJob(
              registerImages: registeredDetail?.registerImages,
            );
      } else {
        ref.read(registerImagesStateProvider.notifier).updateEquipmentRegisterImageJob(
              readOnly: false,
            );
      }

      return freonCheckRegister;
    } on Exception catch (e) {
      log(e.toString());
    }
    return null;
  }
}
