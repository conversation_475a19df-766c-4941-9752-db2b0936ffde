import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';
import 'register_images_state.dart';

part 'register_delete_state.g.dart';

/// 登録してる設備問題を削除State
@riverpod
class RegisterDeleteState extends _$RegisterDeleteState {
  @override
  AsyncValue<bool> build() {
    ref.showGlobalLoading();
    return const AsyncData(false);
  }

  /// 登録している設備問題を削除
  Future<void> delete({required int historyResultId}) async {
    state = const AsyncValue.loading();
    try {
      final freonCheckService = ref.read(freonCheckServiceProvider);

      final periodCode = ref.read(freonCheckPeriodStateProvider)?.periodCode;
      if (periodCode == null) {
        throw UnknownException('期間情報の取得が失敗しました。');
      }

      final registerImagesState = ref.read(registerImagesStateProvider);
      for (final image in registerImagesState.registerImages) {
        await freonCheckService.deleteImageFromLocalSandbox(
          imageName: image.imageName,
          periodCode: periodCode,
        );
      }
      await freonCheckService.deleteRegisteredProblem(
        historyResultId: historyResultId,
      );

      state = const AsyncValue.data(true);
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }
}
