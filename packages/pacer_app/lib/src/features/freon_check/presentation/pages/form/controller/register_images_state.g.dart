// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_images_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$registerImagesStateHash() => r'8635f98a9b8f024e438da85f0067a97b7c36f262';

/// 登録画面：写真情報state
///
/// Copied from [RegisterImagesState].
@ProviderFor(RegisterImagesState)
final registerImagesStateProvider =
    AutoDisposeNotifierProvider<RegisterImagesState, EquipmentRegisterImageJob>.internal(
  RegisterImagesState.new,
  name: r'registerImagesStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$registerImagesStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RegisterImagesState = AutoDisposeNotifier<EquipmentRegisterImageJob>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
