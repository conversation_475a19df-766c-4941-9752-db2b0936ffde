import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../config/strings.dart';
import '../controller/form_data_controller.dart';
import '../controller/register_states_state.dart';
import 'checkbox_dialog.dart';

/// select status item
class SelectStatusItem extends ConsumerWidget {
  /// init
  const SelectStatusItem({
    super.key,
    required this.isRegister,
  });

  /// 画面登録状態(true:登録->編集できる　false：削除->編集できない。)
  final bool isRegister;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final anchorKey = GlobalKey();

    return ColoredBox(
      color: colors.button,
      child: TextField(
        key: anchorKey,
        controller: TextEditingController(
          text: ref.watch(selectStatusStateProvider),
        ),
        onTap: () async {
          if (!isRegister) return;

          final list = ref.read(registerStatesStateProvider);
          await showDialog<void>(
            barrierDismissible: false,
            context: context,
            builder: (context) => Dialog(
              shape: const OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
              child: CheckboxDialog(
                registerStateList: list,
              ),
            ),
          );
        },
        readOnly: true,
        decoration: InputDecoration(
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          contentPadding: const EdgeInsets.all(10),
          hintText: kPleaseSelect,
          hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
          prefixIconConstraints: const BoxConstraints(),
          suffixIcon: const Icon(Icons.arrow_drop_down),
          border: const OutlineInputBorder(),
          errorStyle: const TextStyle(fontSize: 0, height: 0),
        ),
      ),
    );
  }
}
