import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../../../domain/equipment_report_type_enum.dart';
import '../../select_location_page/controller/freon_check_current_location_state.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';
import 'freon_check_equipments_records_state.dart';

part 'freon_check_finish_report_state.g.dart';

/// 最新情報を更新するstate
@riverpod
class FreonCheckFinishReportState extends _$FreonCheckFinishReportState {
  @override
  AsyncValue<bool> build() {
    ref.showGlobalLoading();
    return const AsyncData(false);
  }

  /// 完了報告
  Future<void> finishReport() async {
    state = const AsyncValue.loading();
    try {
      final periodCode = ref.read(freonCheckPeriodStateProvider)?.periodCode;
      if (periodCode == null) {
        throw UnknownException('期間情報の取得が失敗しました。');
      }
      final placeCode = ref.read(freonCheckCurrentLocationStateProvider)?.placeCode;
      if (placeCode == null) {
        throw UnknownException('場所情報の取得が失敗しました。');
      }
      final freonCheckService = ref.read(freonCheckServiceProvider);

      final records = ref.read(freonCheckEquipmentsRecordsStateProvider).value;

      final equipmentsRecords = records?.locationEquipments ?? [];
      if (equipmentsRecords.isEmpty) {
        throw UnknownException('設備情報の取得が失敗しました。');
      }

      /// image情報を更新し、local　DBから取得して、APIで情報を更新
      await freonCheckService.finishReportEquipment(
        placeCode: placeCode.toString(),
        periodCode: periodCode,
      );

      /// tableは(t_d_checkresultとt_d_placestate)最新情報を更新する
      await freonCheckService.updateCheckResultPlace(
        periodCode: periodCode,
        placeCode: placeCode.toString(),
        equipmentReportType: EquipmentReportTypeEnum.isFinishReport,
      );
      state = const AsyncValue.data(false);
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }
}
