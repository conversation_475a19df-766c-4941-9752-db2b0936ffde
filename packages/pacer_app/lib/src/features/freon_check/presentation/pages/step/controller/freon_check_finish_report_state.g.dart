// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_finish_report_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckFinishReportStateHash() => r'e12c6306741b62cf7b4cf1329471ab9cb23fbda9';

/// 最新情報を更新するstate
///
/// Copied from [FreonCheckFinishReportState].
@ProviderFor(FreonCheckFinishReportState)
final freonCheckFinishReportStateProvider =
    AutoDisposeNotifierProvider<FreonCheckFinishReportState, AsyncValue<bool>>.internal(
  FreonCheckFinishReportState.new,
  name: r'freonCheckFinishReportStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckFinishReportStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckFinishReportState = AutoDisposeNotifier<AsyncValue<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
