import 'package:flutter/material.dart';

import '../../../config/strings.dart';
import 'simple_inspection_table.dart';

/// header
class SimpleInspectionLocationListHeader extends StatelessWidget {
  /// init
  const SimpleInspectionLocationListHeader({
    super.key,
    required this.freonPeriodName,
  });

  /// 期間名（例　2023年2回目 (7/1~9/30)）
  final String freonPeriodName;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyLarge?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SimpleInspectionTable(freonPeriodName: freonPeriodName),
        const SizedBox(
          height: 8,
        ),
        Container(
          decoration: BoxDecoration(
            color: colors.surfaceContainerHighest,
            border: Border.fromBorderSide(
              BorderSide(
                color: colors.outlineVariant,
              ),
            ),
          ),
          margin: const EdgeInsets.only(bottom: 12),
          child: IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      kLocation,
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    kStep,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    kState,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
