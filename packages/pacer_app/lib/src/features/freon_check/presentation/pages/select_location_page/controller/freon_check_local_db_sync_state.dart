import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import 'freon_check_period_state.dart';

part 'freon_check_local_db_sync_state.g.dart';

/// master table情報を更新するstate
@riverpod
class FreonCheckLocalDBSyncState extends _$FreonCheckLocalDBSyncState {
  @override
  AsyncValue<void> build() {
    ref.showGlobalLoading();
    return const AsyncData(null);
  }

  /// Local DBの情報を更新
  Future<void> syncTables() async {
    state = const AsyncValue.loading();
    try {
      state = await AsyncValue.guard(() async {
        await ref.read(freonCheckServiceProvider).updateMasterTables();
      });
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// Local DBの情報を更新と該当以外期間の写真cacheを削除
  Future<void> syncTablesAndDeleteCache() async {
    state = const AsyncValue.loading();
    try {
      state = await AsyncValue.guard(() async {
        final freonCheckPeriod = ref.read(freonCheckPeriodStateProvider);

        if (freonCheckPeriod == null) {
          log('error ,画面に何も表示されない');
          throw UnknownException('該当期間の取得が失敗しました');
        }
        final freonCheckService = ref.read(freonCheckServiceProvider);

        /// master以外tableのデータを消します。
        await freonCheckService.deleteNonMasterTableData();

        /// 写真情報を消します。
        await freonCheckService.deleteFolderFromLocalSandbox();

        /// master tableのデータを消し、再取得
        await freonCheckService.updateMasterTables();
      });
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }
}
