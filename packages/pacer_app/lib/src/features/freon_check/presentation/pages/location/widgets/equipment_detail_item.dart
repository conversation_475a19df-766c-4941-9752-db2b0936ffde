import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../domain/equipment_detail.dart';
import '../../../config/strings.dart';
import '../../../widgets/image_widget.dart';
import '../../select_location_page/controller/freon_check_current_location_state.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';

/// 設備詳細情報
class EquipmentDetailItem extends ConsumerWidget {
  /// 標準コンストラクタ
  const EquipmentDetailItem({
    super.key,
    this.equipment,
    required this.onPressed,
  });

  /// クリックイベントのコールバック
  final VoidCallback onPressed;

  /// 場所
  final EquipmentDetail? equipment;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final containerColor = colors.surface;

    final textColor = colors.onSurface;

    final textTheme = texts.bodyMedium?.copyWith(
      color: textColor,
    );

    final placeName = ref.read(freonCheckCurrentLocationStateProvider)?.placeName ?? '';

    final periodCode = ref.watch(freonCheckPeriodStateProvider)?.periodCode ?? 0;

    final imagePath = equipment?.imagePath ?? '';
    final imageName = equipment?.imageName ?? '';

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: containerColor,
          border: Border.fromBorderSide(
            BorderSide(
              color: colors.outlineVariant,
            ),
          ),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(8, 8, 12, 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: switch (imagePath.isNotEmpty && imageName.isNotEmpty) {
                    true => ImageWidget(
                        imagePath: imagePath,
                        imageName: imageName,
                        size: 85,
                        periodCode: periodCode,
                      ),
                    false => const SizedBox(
                        width: 85,
                        height: 85,
                      ),
                  },
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$kLocation:  $placeName',
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '$kEquipmentNo: ${equipment?.equipmentNumber ?? ''}',
                      style: textTheme,
                    ),
                    Text(
                      '$kState: ${equipment?.equipmentState ?? ''}',
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '$kLoginTime: ${equipment?.createTimeString ?? ''}',
                      style: textTheme,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
