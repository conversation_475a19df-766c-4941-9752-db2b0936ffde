import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../exceptions/app_exception.dart';
import '../../../../app/application/global_loading_service.dart';
import '../../../application/freon_check_service.dart';
import '../../../domain/equipment_detail.dart';
import '../select_location_page/controller/freon_check_current_location_state.dart';
import '../select_location_page/controller/freon_check_period_state.dart';
import '../step/controller/freon_check_select_equipment_state.dart';

part 'freon_check_history_state.g.dart';

/// 設備の履歴情報State
@riverpod
class EquipmentHistoryState extends _$EquipmentHistoryState {
  @override
  Future<List<EquipmentDetail>> build() async {
    ref.showGlobalLoading();
    try {
      final freonCheckService = ref.read(freonCheckServiceProvider);

      final periodCode = ref.read(freonCheckPeriodStateProvider)?.periodCode;
      if (periodCode == null) {
        throw UnknownException('期間情報の取得が失敗しました。');
      }
      final placeCode = ref.read(freonCheckCurrentLocationStateProvider)?.placeCode;
      if (placeCode == null) {
        throw UnknownException('場所情報の取得が失敗しました。');
      }

      final equipmentCode = ref.read(freonCheckSelectEquipmentStateProvider)?.equipmentCode;
      if (equipmentCode == null) {
        throw UnknownException('設備情報の取得が失敗しました。');
      }

      final histories = await freonCheckService.listEquipmentDetails(
        placeCode: placeCode,
        periodCode: periodCode,
        equipmentCode: equipmentCode,
      );

      return histories;
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
      return <EquipmentDetail>[];
    }
  }
}
