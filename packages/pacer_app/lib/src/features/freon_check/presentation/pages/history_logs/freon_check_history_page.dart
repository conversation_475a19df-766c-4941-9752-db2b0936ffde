import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../common_widgets/pacer_back_button.dart';
import '../../../../acceptance_inspection/presentation/widgets/bottom_tab_bar.dart';
import '../../config/strings.dart';
import '../location/widgets/equipment_detail_item.dart';
import 'freon_check_history_state.dart';

/// 過去履歴一覧
class FreonCheckHistoryPage extends HookConsumerWidget {
  /// init
  const FreonCheckHistoryPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final historyState = ref.watch(equipmentHistoryStateProvider);

    ref.listen(equipmentHistoryStateProvider, (_, state) {
      switch (state) {
        case AsyncError():
          {
            _showErrorMessageBySnackBar('履歴情報の取得に失敗しました。', ref);
          }
        case _:
          {}
      }
    });
    return Scaffold(
      appBar: PacerAppBar(
        title: const Text(kSimpleInspectionTitle),
        context: context,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: _lineHeight,
          horizontal: _edge,
        ),
        child: switch (historyState) {
          AsyncLoading() => const Center(child: CircularProgressIndicator()),
          AsyncData(value: final histories) when histories.isNotEmpty => Column(
              children: [
                _Header(),
                Expanded(
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      return EquipmentDetailItem(
                        equipment: histories[index],
                        onPressed: () {},
                      );
                    },
                    separatorBuilder: (context, _) => const Gap(6),
                    itemCount: histories.length,
                  ),
                ),
              ],
            ),
          AsyncData(value: final histories) when histories.isEmpty => Column(
              children: [
                _Header(),
                const Center(
                  child: Text('履歴がありません'),
                ),
              ],
            ),
          _ => const SizedBox.shrink(),
        },
      ),
      bottomNavigationBar: BottomTabBar(
        leading: PacerBackButton(
          onPressed: () => context.pop(),
        ),
      ),
    );
  }

  /// 画面共通SnackBar、エラーなど表示するため
  void _showErrorMessageBySnackBar(String text, WidgetRef ref) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.error,
        content: Text(text),
      ),
    );
  }
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Text(
          '○',
          style: texts.titleLarge?.copyWith(
            color: colors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          '×',
          style: texts.titleLarge?.copyWith(
            color: colors.error,
            fontSize: 30,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
