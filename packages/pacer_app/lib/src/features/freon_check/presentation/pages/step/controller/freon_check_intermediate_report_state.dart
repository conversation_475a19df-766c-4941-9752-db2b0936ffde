import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../../../domain/equipment_report_type_enum.dart';
import '../../select_location_page/controller/freon_check_current_location_state.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';

part 'freon_check_intermediate_report_state.g.dart';

/// 最新情報を更新するstate
@riverpod
class FreonCheckIntermediateReportState extends _$FreonCheckIntermediateReportState {
  @override
  AsyncValue<bool> build() {
    ref.showGlobalLoading();
    return const AsyncData(false);
  }

  /// 途中報告
  Future<void> intermediateReport() async {
    state = const AsyncValue.loading();
    try {
      final periodCode = ref.read(freonCheckPeriodStateProvider)?.periodCode;
      if (periodCode == null) {
        throw UnknownException('期間情報の取得が失敗しました。');
      }
      final placeCode = ref.read(freonCheckCurrentLocationStateProvider)?.placeCode;
      if (placeCode == null) {
        throw UnknownException('場所情報の取得が失敗しました。');
      }

      final freonCheckService = ref.read(freonCheckServiceProvider);

      /// image情報を更新し、local　DBから取得して、APIで情報を更新
      await freonCheckService.intermediateReportEquipment(
        placeCode: placeCode.toString(),
        periodCode: periodCode,
      );

      /// tableは(t_d_checkresultとt_d_placestate)最新情報を更新する
      await freonCheckService.updateCheckResultPlace(
        periodCode: periodCode,
        placeCode: placeCode.toString(),
        equipmentReportType: EquipmentReportTypeEnum.isIntermediateReport,
      );

      state = const AsyncValue.data(true);
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }
}
