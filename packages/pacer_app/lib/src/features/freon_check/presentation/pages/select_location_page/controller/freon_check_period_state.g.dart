// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_period_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckPeriodStateHash() => r'89d0d98f02b87bae686237500d3b7a6ef566d7f2';

///  該当期間
///
/// Copied from [FreonCheckPeriodState].
@ProviderFor(FreonCheckPeriodState)
final freonCheckPeriodStateProvider = AutoDisposeNotifierProvider<FreonCheckPeriodState, FreonPeriod?>.internal(
  FreonCheckPeriodState.new,
  name: r'freonCheckPeriodStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckPeriodStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckPeriodState = AutoDisposeNotifier<FreonPeriod?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
