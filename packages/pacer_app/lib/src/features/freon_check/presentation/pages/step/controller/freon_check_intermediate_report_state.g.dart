// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_intermediate_report_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckIntermediateReportStateHash() => r'352992909c47fda32c0646a756529d8e06a98273';

/// 最新情報を更新するstate
///
/// Copied from [FreonCheckIntermediateReportState].
@ProviderFor(FreonCheckIntermediateReportState)
final freonCheckIntermediateReportStateProvider =
    AutoDisposeNotifierProvider<FreonCheckIntermediateReportState, AsyncValue<bool>>.internal(
  FreonCheckIntermediateReportState.new,
  name: r'freonCheckIntermediateReportStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckIntermediateReportStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckIntermediateReportState = AutoDisposeNotifier<AsyncValue<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
