// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'equipment_detail_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$equipmentDetailStateHash() => r'ff7bfb00f2e322a07d6e50f6e445c1071f35ea88';

/// 初期化設備詳細情報取得State
///
/// Copied from [EquipmentDetailState].
@ProviderFor(EquipmentDetailState)
final equipmentDetailStateProvider =
    AutoDisposeAsyncNotifierProvider<EquipmentDetailState, LocationEquipmentDetail?>.internal(
  EquipmentDetailState.new,
  name: r'equipmentDetailStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$equipmentDetailStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EquipmentDetailState = AutoDisposeAsyncNotifier<LocationEquipmentDetail?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
