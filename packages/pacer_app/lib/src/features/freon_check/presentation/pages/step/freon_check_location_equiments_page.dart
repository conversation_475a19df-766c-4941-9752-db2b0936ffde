import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../routing/app_router.dart';
import '../../../../../utils/keep_alive.dart';
import '../../../../../utils/use_router_effect.dart';
import '../../../domain/location_equipment.dart';
import '../../config/strings.dart';
import '../../routing/freon_route.dart';
import '../select_location_page/controller/freon_check_current_location_state.dart';
import 'controller/freon_check_equipment_refresh_state.dart';
import 'controller/freon_check_equipments_records_state.dart';
import 'controller/freon_check_finish_report_state.dart';
import 'controller/freon_check_intermediate_report_state.dart';
import 'controller/freon_check_select_equipment_state.dart';
import 'widgets/location_equipments_item.dart';
import 'widgets/location_equipments_tab_bar.dart';

/// フロン点検
class FreonCheckLocationEquipmentsPage extends HookConsumerWidget {
  /// init
  const FreonCheckLocationEquipmentsPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final equipmentsRecords = ref.watch(freonCheckEquipmentsRecordsStateProvider);

    final currentLocation = ref.watch(freonCheckCurrentLocationStateProvider);

    /// 画面２（設備リスト）のroute path
    const equipmentDetail = '/freon/target';

    /// 画面２（設備リスト）はroot path
    const equipmentDetailPreviousRootPath = '/freon/target/';
    useRouterEffect(context, (previous, current) {
      if (current == equipmentDetail && previous.contains(equipmentDetailPreviousRootPath)) {
        ref.invalidate(freonCheckEquipmentsRecordsStateProvider);
      }
    });
    ref
      ..keepOnly(freonCheckSelectEquipmentStateProvider)
      ..listen(freonCheckEquipmentRefreshStateProvider, (_, state) {
        switch (state) {
          case AsyncData(:final value) when value:
            {
              /// 画面情報を更新する
              ref.invalidate(freonCheckEquipmentsRecordsStateProvider);
            }
          case AsyncData(:final value) when !value:
            {
              _showMessageBySnackBar('データがありません', ref);
            }
          case AsyncLoading():
            {
              log('globalLoadingServiceProvider is loading');
            }
          case AsyncError():
            {
              _showErrorMessageBySnackBar('設備リストの更新に失敗しました。', ref);
            }
          case _:
            {}
        }
      })
      ..listen(freonCheckIntermediateReportStateProvider, (_, state) {
        switch (state) {
          case AsyncData():
            {
              _showMessageBySnackBar('状態を作業中に変更しました。', ref);
              log('途中報告ができました');
            }
          case AsyncLoading():
            {
              log('globalLoadingServiceProvider is loading');
            }
          case AsyncError():
            {
              _showErrorMessageBySnackBar('途中報告ができません。', ref);
            }
        }
      })
      ..listen(freonCheckFinishReportStateProvider, (_, state) {
        switch (state) {
          case AsyncData():
            {
              _showMessageBySnackBar('状態を作業完了に変更しました。', ref);
              log('完了報告ができました');
            }
          case AsyncLoading():
            {
              log('globalLoadingServiceProvider is loading');
            }
          case AsyncError():
            {
              _showErrorMessageBySnackBar('完了報告ができません。', ref);
            }
        }
      });

    return Scaffold(
      appBar: PacerAppBar(
        title: const Text(kSimpleInspectionTitle),
        context: context,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: _lineHeight,
          horizontal: _edge,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ColoredBox(
              color: Theme.of(context).colorScheme.primaryContainer,
              child: Text(currentLocation?.placeName ?? ''),
            ),
            const Gap(3),
            Expanded(
              child: switch (equipmentsRecords) {
                AsyncLoading() => const Center(child: CircularProgressIndicator()),
                AsyncError() => const SizedBox.shrink(),
                AsyncData(:final value) => ListView.separated(
                    itemBuilder: (context, index) {
                      final locationEquipment = value.locationEquipments[index];
                      return LocationEquipmentsItem(
                        onPressed: () {
                          ref
                              .read(
                                freonCheckSelectEquipmentStateProvider.notifier,
                              )
                              .update(locationEquipment);
                          const FreonCheckEquipmentDetailRoute().go(context);
                        },
                        equipment: locationEquipment,
                      );
                    },
                    separatorBuilder: (context, _) => const Gap(6),
                    itemCount: value.locationEquipments.length,
                  ),
              },
            ),
          ],
        ),
      ),
      bottomNavigationBar: LocationEquipmentsTabBar(
        isCurrentTimeInPeriod: equipmentsRecords.value?.isCurrentTimeInPeriod,
        onRefreshPressed: () {
          ref.read(freonCheckEquipmentRefreshStateProvider.notifier).freshCheckResult();
        },
        onIntermediateReportPressed: () {
          ref.read(freonCheckIntermediateReportStateProvider.notifier).intermediateReport();
        },
        onFinalReportPressed: () {
          final isNotAllDone = equipmentsRecords.value?.locationEquipments
                  .any((equipment) => equipment.equipmentCheckStatus == EquipmentCheckStatus.notStarted) ??
              true;
          if (isNotAllDone) {
            _showErrorMessageBySnackBar('未点検設備があるため、完了報告できません', ref);
            return;
          }

          ref.read(freonCheckFinishReportStateProvider.notifier).finishReport();
        },
      ),
    );
  }

  /// 画面共通SnackBar、エラーなど表示するため
  void _showErrorMessageBySnackBar(String text, WidgetRef ref) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.error,
        content: Text(text),
      ),
    );
  }

  /// 画面共通SnackBar、表示するため
  void _showMessageBySnackBar(String text, WidgetRef ref) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.secondary,
        content: Text(text),
      ),
    );
  }
}
