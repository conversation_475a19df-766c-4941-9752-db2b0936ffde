import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../select_location_page/controller/freon_check_current_location_state.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';

part 'freon_check_equipment_refresh_state.g.dart';

/// 最新情報を更新するstate
@riverpod
class FreonCheckEquipmentRefreshState extends _$FreonCheckEquipmentRefreshState {
  @override
  AsyncValue<bool> build() {
    ref.showGlobalLoading();
    return const AsyncData(false);
  }

  /// 画面に表示される情報を取得
  Future<void> freshCheckResult() async {
    state = const AsyncValue.loading();
    try {
      final periodCode = ref.read(freonCheckPeriodStateProvider)?.periodCode;
      if (periodCode == null) {
        throw UnknownException('期間情報の取得が失敗しました。');
      }
      final placeCode = ref.read(freonCheckCurrentLocationStateProvider)?.placeCode;
      if (placeCode == null) {
        throw UnknownException('場所情報の取得が失敗しました。');
      }

      final freonCheckService = ref.read(freonCheckServiceProvider);
      final shouldReload = await freonCheckService.refreshCheckResult(
        placeCode: placeCode,
        periodCode: periodCode,
      );

      state = AsyncValue.data(shouldReload);
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }
}
