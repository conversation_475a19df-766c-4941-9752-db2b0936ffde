import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../domain/location_process.dart';

/// 一覧表示
class SimpleInspectionLocationCell extends ConsumerWidget {
  /// 標準コンストラクタ
  const SimpleInspectionLocationCell({
    super.key,
    required this.locationProcess,
    required this.onPressed,
  });

  /// クリックイベントのコールバック
  final VoidCallback onPressed;

  /// 場所
  final LocationProcess locationProcess;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final containerColor = colors.surface;

    final textColor = colors.onSurface;

    final textTheme = texts.bodyMedium?.copyWith(
      color: textColor,
      height: 2,
    );

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: containerColor,
          border: Border.fromBorderSide(
            BorderSide(
              color: colors.outlineVariant,
            ),
          ),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text(
                    locationProcess.placeName ?? '',
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ),
              VerticalDivider(
                width: 1,
                thickness: 1,
                color: colors.outlineVariant,
              ),
              Expanded(
                flex: 2,
                child: Text(
                  locationProcess.progress ?? '',
                  textAlign: TextAlign.center,
                  style: textTheme,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              VerticalDivider(
                width: 1,
                thickness: 1,
                color: colors.outlineVariant,
              ),
              Expanded(
                flex: 2,
                child: Text(
                  locationProcess.placeState ?? '',
                  textAlign: TextAlign.center,
                  style: textTheme,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
