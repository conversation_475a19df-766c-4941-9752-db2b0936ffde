import 'dart:developer';

import 'package:clock/clock.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../../../domain/equipment_register_job.dart';
import '../../select_location_page/controller/freon_check_current_location_state.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';
import '../../step/controller/freon_check_select_equipment_state.dart';

part 'equipment_register_state.g.dart';

/// 問題なしを登録State
@riverpod
class EquipmentRegisterState extends _$EquipmentRegisterState {
  @override
  AsyncValue<bool> build() {
    ref.showGlobalLoading();
    return const AsyncData(false);
  }

  /// 問題なしを登録
  Future<void> registerNoProblem() async {
    state = const AsyncValue.loading();
    try {
      final freonCheckService = ref.read(freonCheckServiceProvider);
      final periodCode = ref.read(freonCheckPeriodStateProvider)?.periodCode;
      if (periodCode == null) {
        throw UnknownException('期間情報の取得が失敗しました。');
      }
      final placeCode = ref.read(freonCheckCurrentLocationStateProvider)?.placeCode;
      if (placeCode == null) {
        throw UnknownException('場所情報の取得が失敗しました。');
      }

      final equipment = ref.read(freonCheckSelectEquipmentStateProvider);
      if (equipment == null) {
        throw UnknownException('設備情報の取得が失敗しました。');
      }

      /// insertする時historyResultIdはtimestamp
      final historyResultId = clock.now().millisecondsSinceEpoch ~/ 1000;
      final job = EquipmentRegisterJob(
        historyResultId: historyResultId,
        periodCode: periodCode,
        placeCode: placeCode,
        equipmentCode: equipment.equipmentCode,
        errorCode: '',
        comment: '',
        isCanceled: false,
        isUploaded: false,
      );

      await freonCheckService.registerNoProblem(
        periodCode: periodCode,
        placeCode: placeCode.toString(),
        equipmentCode: equipment.equipmentCode,
        equipmentRegisterJob: job,
      );

      state = const AsyncValue.data(true);
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }
}
