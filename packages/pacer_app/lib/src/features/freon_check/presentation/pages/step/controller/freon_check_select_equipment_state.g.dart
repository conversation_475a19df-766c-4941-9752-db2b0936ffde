// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_select_equipment_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckSelectEquipmentStateHash() => r'c72dfe400d4787fc53c46612339dd899b3a95d70';

///  選択してる設備
///
/// Copied from [FreonCheckSelectEquipmentState].
@ProviderFor(FreonCheckSelectEquipmentState)
final freonCheckSelectEquipmentStateProvider =
    AutoDisposeNotifierProvider<FreonCheckSelectEquipmentState, LocationEquipment?>.internal(
  FreonCheckSelectEquipmentState.new,
  name: r'freonCheckSelectEquipmentStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckSelectEquipmentStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckSelectEquipmentState = AutoDisposeNotifier<LocationEquipment?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
