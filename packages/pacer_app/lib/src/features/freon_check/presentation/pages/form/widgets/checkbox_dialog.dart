import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../domain/freon_check_register_state_detail.dart';
import '../controller/form_data_controller.dart';
import '../controller/register_states_state.dart';

/// checkbox dialog
class CheckboxDialog extends HookConsumerWidget {
  /// init
  const CheckboxDialog({
    super.key,
    required this.registerStateList,
  });

  /// 状態情報リスト
  final List<FreonCheckRegisterStateDetail> registerStateList;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statusListState = useState<List<FreonCheckRegisterStateDetail>>(
      List.from(registerStateList),
    );

    return SizedBox(
      height: 300,
      child: Column(
        children: <Widget>[
          Expanded(
            child: ListView.builder(
              itemCount: statusListState.value.length,
              itemBuilder: (BuildContext context, int index) {
                final info = statusListState.value[index];

                return CheckboxListTile(
                  title: Text(info.statusName),
                  value: info.isSelected,
                  onChanged: (bool? value) {
                    final newItem = info.copyWith(isSelected: !info.isSelected);
                    final array = statusListState.value;
                    array[index] = newItem;
                    statusListState.value = [...array];
                  },
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 8, 8, 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context, false);
                  },
                  child: const Text('キャンセル'),
                ),
                ElevatedButton(
                  onPressed: () {
                    ref.read(registerStatesStateProvider.notifier).updateList(statusListState.value);
                    final selectString = ref.read(registerStatesStateProvider.notifier).getSelectNameString();
                    ref.read(selectStatusStateProvider.notifier).updateSelectStatus(selectString);
                    Navigator.pop(context, true);
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
