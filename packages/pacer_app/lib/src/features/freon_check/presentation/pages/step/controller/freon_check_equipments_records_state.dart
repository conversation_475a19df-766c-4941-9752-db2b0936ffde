import 'dart:developer';

import 'package:clock/clock.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../../../domain/location_equipment.dart';
import '../../select_location_page/controller/freon_check_current_location_state.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';

part 'freon_check_equipments_records_state.g.dart';

/// 現在時間は期間以内、初期化設備情報State
@riverpod
class FreonCheckEquipmentsRecordsState extends _$FreonCheckEquipmentsRecordsState {
  @override
  Future<
      ({
        bool isCurrentTimeInPeriod,
        List<LocationEquipment> locationEquipments,
      })> build() async {
    ref.showGlobalLoading();
    try {
      final freonCheckService = ref.read(freonCheckServiceProvider);

      final periodCode = ref.read(freonCheckPeriodStateProvider)?.periodCode;
      if (periodCode == null) {
        throw UnknownException('期間情報の取得が失敗しました。');
      }

      /// 現在時間は期間以内（現在の時間はperiodCodeの期間を確認）
      final isCurrentTimeInPeriod = await freonCheckService.countOfPeriod(
            periodCode: periodCode,
            currentDateTime: clock.now(),
          ) >
          0;

      final placeCode = ref.read(freonCheckCurrentLocationStateProvider)?.placeCode;
      if (placeCode == null) {
        throw UnknownException('場所情報の取得が失敗しました。');
      }
      final equipments = await freonCheckService.listLocationEquipments(
            placeCode: placeCode,
            periodCode: periodCode,
          ) ??
          [];

      return (
        isCurrentTimeInPeriod: isCurrentTimeInPeriod,
        locationEquipments: equipments,
      );
    } on Exception catch (e) {
      log(e.toString());
      return (
        isCurrentTimeInPeriod: false,
        locationEquipments: <LocationEquipment>[],
      );
    }
  }
}
