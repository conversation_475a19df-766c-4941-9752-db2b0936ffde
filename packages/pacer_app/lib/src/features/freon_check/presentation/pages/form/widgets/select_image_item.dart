import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../widgets/gallery_picker.dart';

/// select image module
class SelectImageItem extends HookConsumerWidget {
  /// init
  const SelectImageItem({
    super.key,
    required this.isRegister,
    this.maxImagesCount = 4,
    this.onAdd,
  });

  /// 画面登録状態(true:登録->編集できる　false：削除->編集できない。)
  final bool isRegister;

  /// 写真添付最大数
  final int maxImagesCount;

  /// add callback
  final VoidCallback? onAdd;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    final max = maxImagesCount.toString();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(6)),
        border: Border.all(color: colors.line),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text('写真添付（$max枚まで）'),
          ),
          const Gap(8),
          Divider(
            height: 1,
            thickness: 1,
            color: colors.line,
          ),
          const Gap(8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: GalleryPicker(
              max: maxImagesCount,
              onAdd: onAdd,
              onRemove: (index) {},
              isRegister: isRegister,
            ),
          ),
        ],
      ),
    );
  }
}
