// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_equipments_records_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckEquipmentsRecordsStateHash() => r'85aa4e44da38087257665aed3d33dd2532e65497';

/// 現在時間は期間以内、初期化設備情報State
///
/// Copied from [FreonCheckEquipmentsRecordsState].
@ProviderFor(FreonCheckEquipmentsRecordsState)
final freonCheckEquipmentsRecordsStateProvider = AutoDisposeAsyncNotifierProvider<FreonCheckEquipmentsRecordsState,
    ({bool isCurrentTimeInPeriod, List<LocationEquipment> locationEquipments})>.internal(
  FreonCheckEquipmentsRecordsState.new,
  name: r'freonCheckEquipmentsRecordsStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckEquipmentsRecordsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckEquipmentsRecordsState
    = AutoDisposeAsyncNotifier<({bool isCurrentTimeInPeriod, List<LocationEquipment> locationEquipments})>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
