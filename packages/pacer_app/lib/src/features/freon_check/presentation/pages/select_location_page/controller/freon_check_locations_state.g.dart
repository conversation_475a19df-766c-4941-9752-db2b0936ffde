// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_locations_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckLocationsStateHash() => r'a957e54a0fb61a0a0b652fc94bcf111df0847c9b';

/// 初期化場所情報を更新、画面に表示される情報を取得State
///
/// Copied from [FreonCheckLocationsState].
@ProviderFor(FreonCheckLocationsState)
final freonCheckLocationsStateProvider =
    AutoDisposeNotifierProvider<FreonCheckLocationsState, AsyncValue<List<LocationProcess>?>>.internal(
  FreonCheckLocationsState.new,
  name: r'freonCheckLocationsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckLocationsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckLocationsState = AutoDisposeNotifier<AsyncValue<List<LocationProcess>?>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
