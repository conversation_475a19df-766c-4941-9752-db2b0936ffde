// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_states_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$registerStatesStateHash() => r'50d11766360ed45a259389fe0309156046b67873';

/// 確認した登録状態リストstate
///
/// Copied from [RegisterStatesState].
@ProviderFor(RegisterStatesState)
final registerStatesStateProvider =
    AutoDisposeNotifierProvider<RegisterStatesState, List<FreonCheckRegisterStateDetail>>.internal(
  RegisterStatesState.new,
  name: r'registerStatesStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$registerStatesStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RegisterStatesState = AutoDisposeNotifier<List<FreonCheckRegisterStateDetail>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
