import 'dart:developer';
import 'dart:io';

import 'package:clock/clock.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../../../domain/equipment_register_job.dart';
import '../../select_location_page/controller/freon_check_current_location_state.dart';
import '../../select_location_page/controller/freon_check_period_state.dart';
import '../../step/controller/freon_check_select_equipment_state.dart';
import 'form_data_controller.dart';
import 'register_images_state.dart';
import 'register_states_state.dart';

part 'register_update_state.g.dart';

/// 問題を登録State
@riverpod
class RegisterUpdateState extends _$RegisterUpdateState {
  @override
  AsyncValue<bool> build() {
    ref.showGlobalLoading();
    return const AsyncData(false);
  }

  /// 問題を登録
  Future<void> register() async {
    state = const AsyncValue.loading();
    try {
      final periodCode = ref.read(freonCheckPeriodStateProvider)?.periodCode;
      if (periodCode == null) {
        throw UnknownException('期間情報の取得が失敗しました。');
      }

      final place = ref.read(freonCheckCurrentLocationStateProvider);
      if (place == null) {
        throw UnknownException('場所情報の取得が失敗しました。');
      }

      final equipment = ref.read(freonCheckSelectEquipmentStateProvider);
      if (equipment == null) {
        throw UnknownException('設備情報の取得が失敗しました。');
      }

      final freonCheckService = ref.read(freonCheckServiceProvider);

      /// imageをlocalに保存される。
      final registerImagesState = ref.read(registerImagesStateProvider);
      for (final registerImage in registerImagesState.registerImages) {
        final savedImage = File(registerImage.imagePath);
        if (savedImage.existsSync()) {
          final imageData = await savedImage.readAsBytes();
          await freonCheckService.savePhotoInSandbox(
            imageData: imageData,
            imageName: registerImage.imageName,
            periodCode: periodCode,
          );
        }
      }

      /// insertする時historyResultIdはtimestamp
      final historyResultId = clock.now().millisecondsSinceEpoch ~/ 1000;
      final states = ref.read(registerStatesStateProvider.notifier).getSelectStateCodesString();

      final inputEquipmentNumber = ref.read(inputEquipmentNumberStateProvider);
      final inputErrorCode = ref.read(inputErrorCodeStateProvider);
      final inputRemark = ref.read(inputRemarkStateProvider);
      final job = EquipmentRegisterJob(
        historyResultId: historyResultId,
        periodCode: periodCode,
        placeCode: place.placeCode,
        equipmentCode: equipment.equipmentCode,
        equipmentState: states,
        equipmentNumber: inputEquipmentNumber,
        errorCode: inputErrorCode,
        comment: inputRemark,
        imageUrls: registerImagesState.registerImages.map((e) => e.imageName).toList(),
        isCanceled: false,
        isUploaded: false,
      );

      await freonCheckService.registerProblem(registerJob: job);

      state = const AsyncValue.data(true);
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }
}
