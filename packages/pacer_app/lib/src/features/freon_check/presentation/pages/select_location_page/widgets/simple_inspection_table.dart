import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../authentication/data/auth_repository.dart';
import '../../../config/strings.dart';

/// table
class SimpleInspectionTable extends ConsumerWidget {
  /// constructor
  const SimpleInspectionTable({
    super.key,
    required this.freonPeriodName,
  });

  /// 期間名（例　2023年2回目 (7/1~9/30)）
  final String freonPeriodName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.25), // 1列目は全体の50%
        1: FractionColumnWidth(0.75), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(4),
        ),
      ),
      children: [
        TableRow(
          children: [
            /// 期間
            const _ItemNameText(kTime),
            _ValueText(freonPeriodName),
          ],
        ),
        TableRow(
          children: [
            /// 店舗
            const _ItemNameText(kShop),
            _ValueText(
              ref.read(authRepositoryProvider).currentUser?.clockInStore.name ?? '',
            ),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
        child: Text(
          text,
          style: texts.bodyLarge,
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.bodyLarge,
        ),
      ),
    );
  }
}
