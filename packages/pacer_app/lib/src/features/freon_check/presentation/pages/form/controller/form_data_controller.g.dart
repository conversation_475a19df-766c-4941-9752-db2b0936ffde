// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'form_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectStatusStateHash() => r'66f22b3728606a48486a489ce07807da0eec339b';

/// a
///
/// Copied from [SelectStatusState].
@ProviderFor(SelectStatusState)
final selectStatusStateProvider = AutoDisposeNotifierProvider<SelectStatusState, String>.internal(
  SelectStatusState.new,
  name: r'selectStatusStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectStatusStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectStatusState = AutoDisposeNotifier<String>;
String _$inputEquipmentNumberStateHash() => r'47d0c4ad97ee005216d215373e0be084e3482f1a';

/// input equipment number
///
/// Copied from [InputEquipmentNumberState].
@ProviderFor(InputEquipmentNumberState)
final inputEquipmentNumberStateProvider = AutoDisposeNotifierProvider<InputEquipmentNumberState, String>.internal(
  InputEquipmentNumberState.new,
  name: r'inputEquipmentNumberStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputEquipmentNumberStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputEquipmentNumberState = AutoDisposeNotifier<String>;
String _$inputErrorCodeStateHash() => r'37ddd4e781f410348a6edfe7a5fda0a1824f8c05';

/// input error code
///
/// Copied from [InputErrorCodeState].
@ProviderFor(InputErrorCodeState)
final inputErrorCodeStateProvider = AutoDisposeNotifierProvider<InputErrorCodeState, String>.internal(
  InputErrorCodeState.new,
  name: r'inputErrorCodeStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputErrorCodeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputErrorCodeState = AutoDisposeNotifier<String>;
String _$inputRemarkStateHash() => r'ebd8b1b2e72d6293ec928365f187abf8dbdd6639';

/// input remark
///
/// Copied from [InputRemarkState].
@ProviderFor(InputRemarkState)
final inputRemarkStateProvider = AutoDisposeNotifierProvider<InputRemarkState, String>.internal(
  InputRemarkState.new,
  name: r'inputRemarkStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputRemarkStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputRemarkState = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
