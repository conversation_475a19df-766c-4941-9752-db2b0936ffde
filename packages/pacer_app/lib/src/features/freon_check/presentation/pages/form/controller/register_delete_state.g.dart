// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_delete_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$registerDeleteStateHash() => r'5eac618191345d4a83dabff6ec527ec5df23791f';

/// 登録してる設備問題を削除State
///
/// Copied from [RegisterDeleteState].
@ProviderFor(RegisterDeleteState)
final registerDeleteStateProvider = AutoDisposeNotifierProvider<RegisterDeleteState, AsyncValue<bool>>.internal(
  RegisterDeleteState.new,
  name: r'registerDeleteStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$registerDeleteStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RegisterDeleteState = AutoDisposeNotifier<AsyncValue<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
