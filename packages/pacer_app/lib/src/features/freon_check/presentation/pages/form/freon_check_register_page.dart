import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../exceptions/app_exception.dart';
import '../../../../../routing/app_router.dart';
import '../../../../../utils/keep_alive.dart';
import '../../../../acceptance_inspection/presentation/widgets/bottom_tab_bar.dart';
import '../../../../device/data/available_cameras.dart';
import '../../config/strings.dart';
import '../../routing/freon_route.dart';
import '../../widgets/out_line_round_text_button.dart';
import '../location/controller/current_equipment_detail.dart';
import '../take_photo_page/freon_check_take_photo.dart';
import 'controller/form_data_controller.dart';
import 'controller/freon_check_register_state.dart';
import 'controller/register_delete_state.dart';
import 'controller/register_images_state.dart';
import 'controller/register_states_state.dart';
import 'controller/register_update_state.dart';
import 'widgets/freon_check_form_table.dart';
import 'widgets/select_image_item.dart';

/// 点検問題を登録する画面
class FreonCheckRegisterPage extends HookConsumerWidget {
  /// init
  const FreonCheckRegisterPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final camera = ref.watch(camerasProvider).requireValue.first;

    final registerState = ref.watch(freonCheckRegisterStateProvider);

    ref
      ..keepOnly(registerImagesStateProvider)
      ..keepOnly(selectStatusStateProvider)
      ..keepOnly(inputEquipmentNumberStateProvider)
      ..keepOnly(inputErrorCodeStateProvider)
      ..keepOnly(inputRemarkStateProvider)
      ..keepOnly(registerStatesStateProvider)
      ..listen(registerDeleteStateProvider, (previous, state) {
        switch (state) {
          case AsyncData(:final value) when value:
            {
              _showMessageBySnackBar('問題を削除しました。', ref);
              context.pop();
            }
          case AsyncLoading():
            {
              log('globalLoadingServiceProvider is loading');
            }
          case AsyncError(:final error):
            {
              final errorMessage = (error is AppException) ? error.message : '失敗しました';
              _showErrorMessageBySnackBar(errorMessage, ref);
            }
          case _:
            {}
        }
      })
      ..listen(registerUpdateStateProvider, (previous, state) {
        switch (state) {
          case AsyncData(:final value) when value:
            {
              _showMessageBySnackBar('問題を登録しました。', ref);
              const FreonCheckLocationEquipmentsRoute().go(context);
            }
          case AsyncLoading():
            {
              log('globalLoadingServiceProvider is loading');
            }
          case AsyncError(:final error):
            {
              final errorMessage = (error is AppException) ? error.message : '失敗しました';
              _showErrorMessageBySnackBar(errorMessage, ref);
            }
          case _:
            {}
        }
      });

    final isRegister = ref.read(currentEquipmentDetailProvider) == null;

    return Scaffold(
      appBar: PacerAppBar(
        title: const Text(kSimpleInspectionTitle),
        context: context,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: _lineHeight,
          horizontal: _edge,
        ),
        child: switch (registerState) {
          AsyncLoading() => const Center(child: CircularProgressIndicator()),
          AsyncData(value: (final register?)) => ListView(
              children: [
                FreonCheckFormTable(
                  freonCheckRegister: register,
                  isRegister: isRegister,
                ),
                const Gap(6),
                SelectImageItem(
                  isRegister: isRegister,
                  maxImagesCount: register.maxImagesCount,
                  onAdd: () {
                    log('camera: $camera');
                    Navigator.of(context).push<void>(
                      MaterialPageRoute(
                        builder: (context) => FreonCheckCameraApp(camera: camera),
                      ),
                    );
                  },
                ),
              ],
            ),
          _ => const SizedBox.shrink(),
        },
      ),
      bottomNavigationBar: BottomTabBar(
        leading: PacerBackButton(
          onPressed: () => context.pop(),
        ),
        actions: [
          OutlineRoundTextButton(
            isRegister ? kLogin : kDelete,
            onPressed: () async {
              if (isRegister) {
                await registerProblem(ref);
              } else {
                await deleteProblem(ref);
              }
            },
          ),
        ],
      ),
    );
  }

  /// 画面共通SnackBar、エラーなど表示するため
  void _showErrorMessageBySnackBar(String text, WidgetRef ref) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.error,
        content: Text(text),
      ),
    );
  }

  /// 画面共通SnackBar、表示するため
  void _showMessageBySnackBar(String text, WidgetRef ref) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.secondary,
        content: Text(text),
      ),
    );
  }

  /// 設備問題の登録を行う
  Future<void> registerProblem(WidgetRef ref) async {
    final stateString = ref.read(selectStatusStateProvider);
    if (stateString.isEmpty) {
      await showAlertDialog(
        context: ref.context,
        title: '状態を選択してください。',
      );
      return;
    }

    final registerImagesState = ref.read(registerImagesStateProvider);
    if (registerImagesState.registerImages.isEmpty) {
      await showAlertDialog(
        context: ref.context,
        title: '不良箇所を撮影してください。',
      );
      return;
    }

    log('message');
    final isShouldRegister = await showAlertDialog(
          context: ref.context,
          title: '登録します、よろしいですか？',
          defaultActionText: 'はい',
          cancelActionText: 'いいえ',
        ) ??
        false;
    if (isShouldRegister) {
      await ref.read(registerUpdateStateProvider.notifier).register();
    }
  }

  /// 削除ロジックを行う
  Future<void> deleteProblem(WidgetRef ref) async {
    final isUploaded = ref.read(freonCheckRegisterStateProvider).value?.isUploaded ?? false;

    if (isUploaded) {
      _showErrorMessageBySnackBar('報告済みのデータは削除できない。', ref);
      return;
    }
    final isShouldDelete = await showAlertDialog(
          context: ref.context,
          title: '削除します。よろしいですか？',
          defaultActionText: 'はい',
          cancelActionText: 'いいえ',
        ) ??
        false;
    if (isShouldDelete) {
      final detail = ref.read(currentEquipmentDetailProvider);
      await ref.read(registerDeleteStateProvider.notifier).delete(historyResultId: detail?.historyResultId ?? 0);
    }
  }
}
