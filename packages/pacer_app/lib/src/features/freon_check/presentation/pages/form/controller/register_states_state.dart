import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../domain/freon_check_register_state_detail.dart';

part 'register_states_state.g.dart';

/// 確認した登録状態リストstate
@riverpod
class RegisterStatesState extends _$RegisterStatesState {
  /// init
  @override
  List<FreonCheckRegisterStateDetail> build() {
    return [];
  }

  /// 状態情報を更新
  void updateList(List<FreonCheckRegisterStateDetail> statusList) {
    log('updateList');
    state = statusList;
  }

  /// get select name string
  String getSelectNameString() {
    if (state.isEmpty) return '';
    return state.where((e) => e.isSelected).map((e) => e.statusName).join(',');
  }

  /// get select code string
  String getSelectStateCodesString() {
    if (state.isEmpty) return '';
    return state.where((e) => e.isSelected).map((e) => e.statusCode).join(',');
  }
}
