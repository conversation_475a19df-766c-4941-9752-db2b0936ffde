// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_register_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckRegisterStateHash() => r'822d6def04eb365226fa939ad56de891889d4e92';

/// 初期化設備登録情報取得State
///
/// Copied from [FreonCheckRegisterState].
@ProviderFor(FreonCheckRegisterState)
final freonCheckRegisterStateProvider =
    AutoDisposeAsyncNotifierProvider<FreonCheckRegisterState, FreonCheckRegister?>.internal(
  FreonCheckRegisterState.new,
  name: r'freonCheckRegisterStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckRegisterStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckRegisterState = AutoDisposeAsyncNotifier<FreonCheckRegister?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
