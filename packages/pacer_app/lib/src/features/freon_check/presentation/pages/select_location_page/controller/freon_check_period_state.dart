import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../domain/freon_period.dart';

part 'freon_check_period_state.g.dart';

///  該当期間
@riverpod
class FreonCheckPeriodState extends _$FreonCheckPeriodState {
  @override
  FreonPeriod? build() {
    return null;
  }

  /// 該当期間を更新
  void update(FreonPeriod? freonPeriod) {
    if (freonPeriod != null) {
      state = freonPeriod;
    }
  }
}
