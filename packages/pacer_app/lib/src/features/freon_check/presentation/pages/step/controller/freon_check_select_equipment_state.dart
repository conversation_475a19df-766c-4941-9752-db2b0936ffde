import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../domain/location_equipment.dart';

part 'freon_check_select_equipment_state.g.dart';

///  選択してる設備
@riverpod
class FreonCheckSelectEquipmentState extends _$FreonCheckSelectEquipmentState {
  @override
  LocationEquipment? build() {
    return null;
  }

  /// 選択してる設備情報を更新
  void update(LocationEquipment? equipment) {
    if (equipment != null) {
      state = equipment;
    }
  }
}
