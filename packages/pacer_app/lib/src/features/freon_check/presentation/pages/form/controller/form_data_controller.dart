import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'form_data_controller.g.dart';

/// a
@riverpod
class SelectStatusState extends _$SelectStatusState {
  /// init
  @override
  String build() => '';

  /// update select status
  void updateSelectStatus(String? selectString) {
    if (selectString != null) {
      state = selectString;
    }
  }

  /// update select status by name list
  void updateSelectStatusByNameList(List<String>? nameList) {
    if (nameList != null && nameList.isNotEmpty) {
      state = nameList.join(',');
    }
  }

  /// clear
  void clear() {
    state = '';
  }
}

/// input equipment number
@riverpod
class InputEquipmentNumberState extends _$InputEquipmentNumberState {
  /// init
  @override
  String build() => '';

  /// update number
  void updateNumber(String number) {
    log('input equipment number: $number');
    state = number;
  }

  /// clear
  void clear() {
    state = '';
  }
}

/// input error code
@riverpod
class InputErrorCodeState extends _$InputErrorCodeState {
  /// init
  @override
  String build() => '';

  /// update number
  void updateNumber(String number) {
    log('input error code: $number');
    state = number;
  }

  /// clear
  void clear() {
    state = '';
  }
}

/// input remark
@riverpod
class InputRemarkState extends _$InputRemarkState {
  /// init
  @override
  String build() => '';

  /// update number
  void updateNumber(String number) {
    log('input remark: $number');
    state = number;
  }

  /// clear
  void clear() {
    state = '';
  }
}
