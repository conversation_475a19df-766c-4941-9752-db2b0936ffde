import 'dart:developer';

import 'package:clock/clock.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/freon_check_service.dart';
import '../../../../domain/location_process.dart';
import 'freon_check_period_state.dart';

part 'freon_check_locations_state.g.dart';

/// 初期化場所情報を更新、画面に表示される情報を取得State
@riverpod
class FreonCheckLocationsState extends _$FreonCheckLocationsState {
  @override
  AsyncValue<List<LocationProcess>?> build() {
    ref.showGlobalLoading();
    return const AsyncData(null);
  }

  /// 画面に表示される情報を取得
  Future<void> update() async {
    state = const AsyncValue.loading();
    try {
      state = await AsyncValue.guard(() async {
        final freonCheckService = ref.read(freonCheckServiceProvider);

        /// current dateで期間情報を検索します。
        final period = await freonCheckService.getCurrentPeriod(clock.now());
        if (period == null) {
          log('error ,画面に何も表示されない');
          throw UnknownException('失敗しました。');
        }

        ref.read(freonCheckPeriodStateProvider.notifier).update(period);

        /// local DBのtable場所進捗情報を更新し、該当期間（period）の場所リストを取得
        await freonCheckService.updateWorkProgress(period.periodCode);

        final locationList = await freonCheckService.listWorkProgress(period.periodCode);
        if (locationList.isEmpty) {
          log('error ,画面に何も表示されない');
          throw UnknownException('フロン点検対象がありません。');
        }

        return locationList;
      });
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// 画面に戻るとき進捗tableと場所状態table情報を更新
  Future<void> refreshWorkProgressAndePlaceState() async {
    state = const AsyncValue.loading();
    try {
      state = await AsyncValue.guard(() async {
        final freonCheckService = ref.read(freonCheckServiceProvider);

        /// current dateで期間情報を検索します。
        final period = await freonCheckService.getCurrentPeriod(clock.now());
        if (period == null) {
          log('error ,画面に何も表示されない');
          throw UnknownException('失敗しました。');
        }

        ref.read(freonCheckPeriodStateProvider.notifier).update(period);

        /// 場所状態tableを更新
        await freonCheckService.updatePlaceState();

        /// local DBのtable場所進捗情報を更新し、該当期間（period）の場所リストを取得
        await freonCheckService.updateWorkProgress(period.periodCode);

        final locationList = await freonCheckService.listWorkProgress(period.periodCode);
        if (locationList.isEmpty) {
          log('error ,画面に何も表示されない');
          throw UnknownException('フロン点検対象がありません。');
        }

        return locationList;
      });
    } on Exception catch (e) {
      log(e.toString());
      state = AsyncError(e, StackTrace.current);
    }
  }
}
