// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'equipment_register_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$equipmentRegisterStateHash() => r'8a606f9386109a010a83d62695363fce5135feaa';

/// 問題なしを登録State
///
/// Copied from [EquipmentRegisterState].
@ProviderFor(EquipmentRegisterState)
final equipmentRegisterStateProvider = AutoDisposeNotifierProvider<EquipmentRegisterState, AsyncValue<bool>>.internal(
  EquipmentRegisterState.new,
  name: r'equipmentRegisterStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$equipmentRegisterStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EquipmentRegisterState = AutoDisposeNotifier<AsyncValue<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
