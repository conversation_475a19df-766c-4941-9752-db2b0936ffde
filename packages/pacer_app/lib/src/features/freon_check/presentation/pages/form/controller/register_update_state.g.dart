// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_update_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$registerUpdateStateHash() => r'7aa1357561e1e8b6f5596b961ba6bfce8791483c';

/// 問題を登録State
///
/// Copied from [RegisterUpdateState].
@ProviderFor(RegisterUpdateState)
final registerUpdateStateProvider = AutoDisposeNotifierProvider<RegisterUpdateState, AsyncValue<bool>>.internal(
  RegisterUpdateState.new,
  name: r'registerUpdateStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$registerUpdateStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RegisterUpdateState = AutoDisposeNotifier<AsyncValue<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
