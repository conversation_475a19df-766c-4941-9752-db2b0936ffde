// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_has_unregistered_data_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckDeleteStateHash() => r'a2e078002aa40d9a70d6b22f4c3f38aa5becb52d';

///  未登録データのフラグ
///
/// Copied from [FreonCheckDeleteState].
@ProviderFor(FreonCheckDeleteState)
final freonCheckDeleteStateProvider = AutoDisposeNotifierProvider<FreonCheckDeleteState, bool>.internal(
  FreonCheckDeleteState.new,
  name: r'freonCheckDeleteStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckDeleteStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreonCheckDeleteState = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
