// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckServiceHash() => r'20eb0bf129830d857ef4aea9067544dd754f39c1';

///  フロン点検service
///
/// Copied from [freonCheckService].
@ProviderFor(freonCheckService)
final freonCheckServiceProvider = Provider<FreonCheckService>.internal(
  freonCheckService,
  name: r'freonCheckServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FreonCheckServiceRef = ProviderRef<FreonCheckService>;
String _$getHasUnregisteredDataHash() => r'a2a9eb438275f54407b10990347080071a0c9358';

/// 未登録データフラグを取得
///
/// Copied from [getHasUnregisteredData].
@ProviderFor(getHasUnregisteredData)
final getHasUnregisteredDataProvider = AutoDisposeFutureProvider<bool>.internal(
  getHasUnregisteredData,
  name: r'getHasUnregisteredDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getHasUnregisteredDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef GetHasUnregisteredDataRef = AutoDisposeFutureProviderRef<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
