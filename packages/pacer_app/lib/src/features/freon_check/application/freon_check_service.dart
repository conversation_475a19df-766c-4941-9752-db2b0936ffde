import 'dart:developer';
import 'dart:typed_data';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/freon_check_image_file_repository.dart';
import '../data/freon_client.dart';
import '../data/sqlite/sqlite_command_repository.dart';
import '../data/sqlite/sqlite_query_repository.dart';
import '../data/sqlite/sqlite_sync_repository.dart';
import '../domain/equipment_detail.dart';
import '../domain/equipment_image.dart';
import '../domain/equipment_image_detail.dart';
import '../domain/equipment_place_state_enum.dart';
import '../domain/equipment_register_job.dart';
import '../domain/equipment_registered_detail.dart';
import '../domain/equipment_report_type_enum.dart';
import '../domain/freon_check_register_state_detail.dart';
import '../domain/freon_period.dart';
import '../domain/location_equipment.dart';
import '../domain/location_process.dart';

part 'freon_check_service.g.dart';

///  フロン点検service
@Riverpod(keepAlive: true)
FreonCheckService freonCheckService(FreonCheckServiceRef ref) {
  return FreonCheckService(ref);
}

///  フロン点検service
class FreonCheckService {
  /// init
  FreonCheckService(this.ref);

  /// hooks_riverpod ref
  final Ref ref;

  /// 初期化local DBに情報を更新
  Future<void> updateMasterTables() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    try {
      /// 1.APIでマスタtable情報を取得
      final freonClient = ref.read(freonClientProvider(caller: caller));

      /// TODO oukaku 性能確認
      ///　https://github.com/retail-ai-inc/pacer/pull/1782#discussion_r1628784353
      final (
        equipments,
        equipmentStates,
        equipmentStores,
        periods,
        placeSubs,
        placeStates,
      ) = await (
        freonClient.listEquipments(store.code),
        freonClient.listEquipmentStates(store.code),
        freonClient.listEquipmentStores(store.code),
        freonClient.listPeriods(store.code),
        freonClient.listPlaceSubstitutes(store.code),
        freonClient.listPlaceStates(store.code),
      ).wait;

      final sqliteSyncRepository = ref.read(sqliteSyncRepositoryProvider);

      /// 2.local DBに最新情報を保存されます。
      /// TODO oukaku 同じ情報をinsertする前にdeleteしないように確認
      /// ：https://github.com/retail-ai-inc/pacer/pull/1782#discussion_r1628925371
      await (
        sqliteSyncRepository.insertEquipment(equipments),
        sqliteSyncRepository.insertEquipmentState(equipmentStates),
        sqliteSyncRepository.insertEquipmentSub(equipmentStores),
        sqliteSyncRepository.insertPeriod(periods),
        sqliteSyncRepository.insertPlaceSub(placeSubs),
        sqliteSyncRepository.insertPlaceState(placeStates),
      ).wait;
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          _ => UnknownException('データベースの更新に失敗しました'),
        },
        StackTrace.current,
      );
    }
  }

  /// 作業進捗tableの更新
  Future<void> updateWorkProgress(int periodCode) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    try {
      /// APIから作業進捗情報の取得
      final workProgresses = await ref.read(freonClientProvider(caller: caller)).listWorkProgress(
            storeCode: store.code,
            periodCode: periodCode,
          );

      /// local tableの作業進捗情報の更新
      await ref.read(sqliteSyncRepositoryProvider).insertProgressSub(workProgresses);
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          _ => UnknownException('データベースの更新に失敗しました'),
        },
        StackTrace.current,
      );
    }
  }

  /// 場所状態tableの更新
  Future<void> updatePlaceState() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    try {
      /// APIから作業進捗情報の取得
      final placeStates = await ref.read(freonClientProvider(caller: caller)).listPlaceStates(store.code);

      /// local tableの作業進捗情報の更新
      await ref.read(sqliteSyncRepositoryProvider).insertPlaceState(placeStates);
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          _ => UnknownException('データベースの更新に失敗しました'),
        },
        StackTrace.current,
      );
    }
  }

  /// 該当期間の場所進捗リストを取得
  Future<List<LocationProcess>> listWorkProgress(int periodCode) {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;

    /// 場所list情報を取得
    return ref.read(sqliteQueryRepositoryProvider).listCurrentPeriod(storeCode: store.code, periodCode: periodCode);
  }

  /// 期間を検索
  Future<FreonPeriod?> getCurrentPeriod(
    DateTime currentDateTime,
  ) {
    return ref.read(sqliteQueryRepositoryProvider).getCurrentPeriod(currentDateTime);
  }

  /// 期間数を取得
  Future<int> countOfPeriod({
    required int periodCode,
    required DateTime currentDateTime,
  }) {
    return ref.read(sqliteQueryRepositoryProvider).countOfPeriod(
          periodCode: periodCode,
          currentDateTime: currentDateTime,
        );
  }

  /// 場所の設備リストを取得
  Future<List<LocationEquipment>?> listLocationEquipments({
    required int placeCode,
    required int periodCode,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    return ref.read(sqliteQueryRepositoryProvider).listLocationEquipments(
          storeCode: store.code,
          placeCode: placeCode,
          periodCode: periodCode,
        );
  }

  /// 最新情報を取得（写真履歴tableと結果tableを更新し、写真情報をプリロード）
  Future<bool> refreshCheckResult({
    required int placeCode,
    required int periodCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    try {
      ///点検結果tableから履歴番号リストを取得
      final historyResultIDs = await ref.read(sqliteQueryRepositoryProvider).listResultIdOfEquipment(
            storeCode: store.code,
            placeCode: placeCode,
            periodCode: periodCode,
          );

      final freonClient = ref.read(freonClientProvider(caller: caller));
      final refreshResult = await freonClient.getRefreshResult(
        periodCode: periodCode,
        storeCode: store.code,
        placeCode: placeCode.toString(),
        historyResultIDs: historyResultIDs,
      );

      if (refreshResult.sqlList.isEmpty) {
        return false;
      }

      /// 写真履歴tableと点検結果tableをSQL文で更新
      await ref.read(sqliteCommandRepositoryProvider).executeFreonCheckDbBySqlStrings(
            refreshResult.sqlList,
            continueOnError: true,
          );

      /// preload写真がない
      if (refreshResult.freonCheckImageList.isEmpty) {
        return true;
      }

      /// 写真をpreload
      for (final freonCheckImage in refreshResult.freonCheckImageList) {
        if (freonCheckImage.imagePath.isEmpty || freonCheckImage.imageName.isEmpty) {
          continue;
        }

        /// download
        final imageData = await freonClient.getPhoto(
          storeCode: store.code,
          fileUrl: freonCheckImage.imagePath,
        );
        if (imageData.isEmpty) {
          continue;
        }

        /// cache fileに保存
        await ref.read(freonCheckImageFileRepositoryProvider).saveImageInLocalSandbox(
              imageBytes: imageData,
              imageName: freonCheckImage.imageName,
              foldName: periodCode.toString(),
            );
      }

      return true;
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          _ => UnknownException('データベースの更新に失敗しました'),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備点検報告（途中報告）
  Future<void> intermediateReportEquipment({
    required int periodCode,
    required String placeCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    try {
      final sqliteQueryRepository = ref.read(sqliteQueryRepositoryProvider);
      final freonClient = ref.read(freonClientProvider(caller: caller));
      final freonImageFileRepository = ref.read(freonCheckImageFileRepositoryProvider);

      final placeCodeInt = int.tryParse(placeCode);
      if (placeCodeInt == null) {
        throw UnknownException('placeCodeが存在しません');
      }

      /// 写真リストを取得（image path and result Id）
      final imageList = await sqliteQueryRepository.listImageResult(
        periodCode: periodCode,
        storeCode: store.code,
        placeCode: placeCodeInt,
      );

      if (imageList != null) {
        for (final imageJob in imageList) {
          /// localに保存してる写真dataを取得
          final imageData = await freonImageFileRepository.getImageFromSandbox(
            imageName: imageJob.imageName,
            foldName: periodCode.toString(),
          );
          if (imageData == null) {
            continue;
          }

          /// APIで写真をupdate、image urlを取得する
          final equipmentImage = await freonClient.uploadProblemImage(
            storeCode: store.code,
            imageData: imageData,
          );

          /// image urlをresult tableに更新
          await ref.read(sqliteCommandRepositoryProvider).updateResultAndImageHistory(
                historyResultId: imageJob.historyResultId,
                imagePath: imageJob.imagePath,
                newImageUrl: equipmentImage.imageUrl,
              );
        }
      }

      final jobList = await sqliteQueryRepository.listEquipmentUpdateJobs(
        periodCode: periodCode,
        storeCode: store.code,
        placeCode: placeCodeInt,
      );

      final _ = await freonClient.reportEquipment(
        periodCode: periodCode,
        storeCode: store.code,
        placeCode: placeCode,
        userCode: caller.userCode,
        equipmentReportType: EquipmentReportTypeEnum.isIntermediateReport,
        equipmentUpdateJobs: jobList,
      );
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          _ => UnknownException('データベースの更新に失敗しました'),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備点検報告（完了報告）
  Future<void> finishReportEquipment({
    required int periodCode,
    required String placeCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    try {
      final sqliteQueryRepository = ref.read(sqliteQueryRepositoryProvider);
      final freonClient = ref.read(freonClientProvider(caller: caller));
      final freonImageFileRepository = ref.read(freonCheckImageFileRepositoryProvider);

      final placeCodeInt = int.tryParse(placeCode);
      if (placeCodeInt == null) {
        throw UnknownException('placeCodeが存在しません');
      }

      /// 写真のimage　pathとresult idを取得する
      final imageList = await sqliteQueryRepository.listImageResult(
        periodCode: periodCode,
        storeCode: store.code,
        placeCode: placeCodeInt,
      );

      if (imageList != null) {
        for (final imageJob in imageList) {
          /// localに保存してる写真dataを取得
          final imageData = await freonImageFileRepository.getImageFromSandbox(
            imageName: imageJob.imageName,
            foldName: periodCode.toString(),
          );
          if (imageData == null) {
            continue;
          }

          /// APIで写真をupdate、image urlを取得する
          final equipmentImage = await freonClient.uploadProblemImage(
            storeCode: store.code,
            imageData: imageData,
          );

          /// image urlをresult tableに更新
          await ref.read(sqliteCommandRepositoryProvider).updateResultAndImageHistory(
                historyResultId: imageJob.historyResultId,
                imagePath: imageJob.imagePath,
                newImageUrl: equipmentImage.imageUrl,
              );
        }
      }

      final jobList = await sqliteQueryRepository.listEquipmentUpdateJobs(
        periodCode: periodCode,
        storeCode: store.code,
        placeCode: placeCodeInt,
      );

      /// tableからAPI用更新情報を取得
      final reportResult = await freonClient.reportEquipment(
        periodCode: periodCode,
        storeCode: store.code,
        placeCode: placeCode,
        userCode: caller.userCode,
        equipmentReportType: EquipmentReportTypeEnum.isFinishReport,
        equipmentUpdateJobs: jobList,
      );

      log(reportResult);
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          _ => UnknownException('データベースの更新に失敗しました'),
        },
        StackTrace.current,
      );
    }
  }

  /// CheckResultとPlace tableを更新
  Future<void> updateCheckResultPlace({
    required int periodCode,
    required String placeCode,
    required EquipmentReportTypeEnum equipmentReportType,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    try {
      final sqliteCommandRepository = ref.read(sqliteCommandRepositoryProvider);

      await sqliteCommandRepository.updateCheckResult(
        periodCode: periodCode,
        storeCode: store.code,
        placeCode: placeCode,
      );
      await sqliteCommandRepository.updatePlaceState(
        periodCode: periodCode,
        storeCode: store.code,
        placeCode: placeCode,
        placeState: equipmentReportType.intValue,
      );
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          _ => UnknownException('データベースの更新に失敗しました'),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備関する期間を取得
  Future<EquipmentPlaceStateEnum> getPeriodOfEquipment({
    required int placeCode,
    required int periodCode,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    return ref.read(sqliteQueryRepositoryProvider).getPeriodOfEquipment(
          storeCode: store.code,
          placeCode: placeCode,
          periodCode: periodCode,
        );
  }

  /// 設備の選択Image情報を取得
  Future<EquipmentImageDetail?> getEquipmentImageDetail({
    required int equipmentCode,
  }) {
    return ref.read(sqliteQueryRepositoryProvider).getEquipmentImageDetail(equipmentCode: equipmentCode);
  }

  /// 設備情報を取得
  Future<List<EquipmentDetail>?> listEquipmentDetail({
    required int placeCode,
    required int periodCode,
    required int equipmentCode,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    return ref.read(sqliteQueryRepositoryProvider).listEquipmentDetail(
          storeCode: store.code,
          placeCode: placeCode,
          periodCode: periodCode,
          equipmentCode: equipmentCode,
        );
  }

  /// 問題なしを登録
  Future<void> registerNoProblem({
    required int periodCode,
    required String placeCode,
    required int equipmentCode,
    required EquipmentRegisterJob equipmentRegisterJob,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    final job = equipmentRegisterJob.copyWith(
      storeCode: store.code,
      createEmployee: caller.userCode,
      modifyEmployee: caller.userCode,
    );
    return ref.read(sqliteCommandRepositoryProvider).registerNoProblem(
          storeCode: store.code,
          placeCode: placeCode,
          periodCode: periodCode,
          equipmentCode: equipmentCode,
          equipmentRegisterJob: job,
        );
  }

  /// 履歴設備情報リストを取得
  Future<List<EquipmentDetail>> listEquipmentDetails({
    required int placeCode,
    required int periodCode,
    required int equipmentCode,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    return ref.read(freonClientProvider(caller: caller)).listEquipmentDetails(
          storeCode: store.code,
          placeCode: placeCode,
          periodCode: periodCode,
          equipmentCode: equipmentCode,
        );
  }

  /// 登録の状態リストを取得
  Future<List<FreonCheckRegisterStateDetail>?> listRegisterStates({
    required int equipmentCode,
  }) {
    return ref.read(sqliteQueryRepositoryProvider).listRegisterStates(
          equipmentCode: equipmentCode,
        );
  }

  /// isUploadedを取得:報告済み(1) =>true 未報告(0)=>false
  Future<bool?> getIsUploaded({required int historyResultId}) {
    return ref.read(sqliteQueryRepositoryProvider).getIsUploaded(historyResultId: historyResultId);
  }

  /// 登録してる問題を削除
  Future<void> deleteRegisteredProblem({required int historyResultId}) {
    return ref.read(sqliteCommandRepositoryProvider).deleteRegisteredProblem(historyResultId: historyResultId);
  }

  /// 登録画面：問題を登録
  Future<void> registerProblem({
    required EquipmentRegisterJob registerJob,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    final job = registerJob.copyWith(
      storeCode: store.code,
      createEmployee: caller.userCode,
      modifyEmployee: caller.userCode,
    );
    return ref.read(sqliteCommandRepositoryProvider).registerProblem(registerJob: job);
  }

  /// url or imageNameで写真情報を取得
  Future<Uint8List?> getImageByUrl({
    /// "/202308/30/202308300912318946880554.jpg"
    required String imageUrlPath,
    required String imageName,
    required int periodCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw UnknownException('ユーザーが存在しません');
    }
    final store = caller.clockInStore;
    try {
      final freonImageFile = ref.read(freonCheckImageFileRepositoryProvider);

      final imageData = await freonImageFile.getImageFromSandbox(
        imageName: imageName,
        foldName: periodCode.toString(),
      );
      if (imageData != null) {
        return imageData;
      }

      final imageUrlData = await ref.read(freonClientProvider(caller: caller)).getPhoto(
            storeCode: store.code,
            fileUrl: imageUrlPath,
          );

      if (imageUrlData.isEmpty) {
        return null;
      }

      final successSave = await freonImageFile.saveImageInLocalSandbox(
        imageName: imageName,
        imageBytes: imageUrlData,
        foldName: periodCode.toString(),
      );

      if (!successSave) {
        log('写真情報の保存が失敗しました。');
      }

      return imageUrlData;
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          _ => UnknownException('写真のダウンロードに失敗しました。'),
        },
        StackTrace.current,
      );
    }
  }

  /// 撮影写真をsandboxに保存される
  Future<void> savePhotoInSandbox({
    required Uint8List imageData,
    required String imageName,
    required int periodCode,
  }) {
    return ref.read(freonCheckImageFileRepositoryProvider).saveImageInLocalSandbox(
          imageBytes: imageData,
          imageName: imageName,
          foldName: periodCode.toString(),
        );
  }

  /// 登録してる問題詳細情報を取得
  Future<EquipmentRegisteredDetail?> getEquipmentRegisteredDetail(
    int historyResultId,
  ) {
    return ref.read(sqliteQueryRepositoryProvider).getEquipmentRegisteredDetail(historyResultId);
  }

  /// sandboxに保存してる写真を削除
  Future<void> deleteImageFromLocalSandbox({
    required String imageName,
    required int periodCode,
  }) {
    return ref.read(freonCheckImageFileRepositoryProvider).deleteImageFromLocalSandbox(
          imageName: imageName,
          foldName: periodCode.toString(),
        );
  }

  /// 設備問題の写真をアップロードする
  Future<EquipmentImage> uploadProblemImage(Uint8List imageBitMap) async {
    final user = ref.read(authRepositoryProvider).currentUser;
    if (user == null) throw UnknownException('ユーザーが存在しません');
    final storeCode = user.clockInStore.code;

    try {
      return ref.read(freonClientProvider(caller: user)).uploadProblemImage(
            storeCode: storeCode,
            imageData: imageBitMap,
          );
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          _ => UnknownException(e.toString()),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備の最大写真数を取得
  Future<int> getMaxPhoto(int equipmentCode) {
    return ref.read(sqliteQueryRepositoryProvider).getMaxPhoto(equipmentCode);
  }

  /// 写真情報を削除
  Future<void> deleteFolderFromLocalSandbox() {
    final freonImageFileRepository = ref.read(freonCheckImageFileRepositoryProvider);
    return freonImageFileRepository.deleteFolderFromLocalSandbox();
  }

  /// master table以外の情報を消します
  Future<void> deleteNonMasterTableData() {
    return ref.read(sqliteCommandRepositoryProvider).deleteNonMasterTableData();
  }
}

/// 未登録データフラグを取得
@Riverpod()
Future<bool> getHasUnregisteredData(GetHasUnregisteredDataRef ref) {
  return ref.read(sqliteQueryRepositoryProvider).getHasUnregisteredData();
}
