/// WebAPIから情報を取得し、masterテーブルと同期
library;

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../exceptions/app_exception.dart';
import '../../domain/freon_equipment.dart';
import '../../domain/freon_equipment_state.dart';
import '../../domain/freon_equipment_substitute.dart';
import '../../domain/freon_period.dart';
import '../../domain/freon_place_state.dart';
import '../../domain/freon_place_substitute.dart';
import '../../domain/freon_work_progress.dart';
import 'freon_check_sqlite_config.dart';
import 'sqlite_repository.dart';

part 'sqlite_sync_repository.g.dart';

/// フロン簡易点検SQLiteリポジトリ_データの同期
@Riverpod(keepAlive: true)
SqliteSyncRepository sqliteSyncRepository(SqliteSyncRepositoryRef ref) => SqliteSyncRepository(ref);

/// 点検SQLiteリポジトリ
class SqliteSyncRepository {
  /// init
  SqliteSyncRepository(this.ref);

  /// ref
  final Ref ref;

  /// 設備マスタに情報をinsert
  Future<void> insertEquipment(List<FreonEquipment> equipmentList) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();
      final _ = batch.delete(FreonCheckMasterTable.equipment.name);
      for (final tableData in equipmentList) {
        batch.insert(FreonCheckMasterTable.equipment.name, tableData.toMap());
      }

      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備状態マスタに情報をinsert
  Future<void> insertEquipmentState(List<FreonEquipmentState> equipmentStateList) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();
      final _ = batch.delete(FreonCheckMasterTable.equipmentState.name);
      for (final tableData in equipmentStateList) {
        batch.insert(FreonCheckMasterTable.equipmentState.name, tableData.toMap());
      }

      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備所属マスタに情報をinsert
  Future<void> insertEquipmentSub(List<FreonEquipmentSubstitute> equipmentSubList) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();
      final _ = batch.delete(FreonCheckMasterTable.equipmentSub.name);
      for (final tableData in equipmentSubList) {
        batch.insert(FreonCheckMasterTable.equipmentSub.name, tableData.toMap());
      }

      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 期間マスタに情報をinsert
  Future<void> insertPeriod(List<FreonPeriod> periodList) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();
      final _ = batch.delete(FreonCheckMasterTable.period.name);
      for (final tableData in periodList) {
        batch.insert(FreonCheckMasterTable.period.name, tableData.toMap());
      }

      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 場所マスタに情報をinsert
  Future<void> insertPlaceSub(List<FreonPlaceSubstitute> placeSubList) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();
      final _ = batch.delete(FreonCheckMasterTable.placeSub.name);
      for (final tableData in placeSubList) {
        batch.insert(FreonCheckMasterTable.placeSub.name, tableData.toMap());
      }

      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 場所状態 マスタに情報をinsert（初期化すると、更新します）
  Future<void> insertPlaceState(List<FreonPlaceState> placeStateList) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();
      final _ = batch.delete(FreonCheckDataTable.placeState.name);
      for (final tableData in placeStateList) {
        batch.insert(FreonCheckDataTable.placeState.name, tableData.toMap());
      }

      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 作業進捗情報に情報をinsert
  Future<void> insertProgressSub(List<FreonWorkProgress> workProgressList) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();
      final _ = batch.delete(FreonCheckDataTable.progressSub.name);
      for (final tableData in workProgressList) {
        batch.insert(FreonCheckDataTable.progressSub.name, tableData.toMap());
      }
      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }
}
