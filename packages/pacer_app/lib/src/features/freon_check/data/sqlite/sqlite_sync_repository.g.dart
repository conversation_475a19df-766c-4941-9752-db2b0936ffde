// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sqlite_sync_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sqliteSyncRepositoryHash() => r'7b00945d4175e76d896f8b2788a70ad03ff4dae1';

/// フロン簡易点検SQLiteリポジトリ_データの同期
///
/// Copied from [sqliteSyncRepository].
@ProviderFor(sqliteSyncRepository)
final sqliteSyncRepositoryProvider = Provider<SqliteSyncRepository>.internal(
  sqliteSyncRepository,
  name: r'sqliteSyncRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$sqliteSyncRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SqliteSyncRepositoryRef = ProviderRef<SqliteSyncRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
