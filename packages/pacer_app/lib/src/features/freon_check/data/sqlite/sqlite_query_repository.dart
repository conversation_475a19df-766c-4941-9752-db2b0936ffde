/// masterとdata テーブルから情報を取得
library;

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sqflite/sqflite.dart';

import '../../../../exceptions/app_exception.dart';
import '../../domain/equipment_detail.dart';
import '../../domain/equipment_image_detail.dart';
import '../../domain/equipment_place_state_enum.dart';
import '../../domain/equipment_registered_detail.dart';
import '../../domain/freon_check_register_state_detail.dart';
import '../../domain/freon_period.dart';
import '../../domain/location_equipment.dart';
import '../../domain/location_equipment_image_job.dart';
import '../../domain/location_equipment_update_job.dart';
import '../../domain/location_process.dart';
import 'freon_check_sqlite_config.dart';
import 'sqlite_repository.dart';

part 'sqlite_query_repository.g.dart';

/// フロン簡易点検SQLiteリポジトリ_データの検索
@Riverpod(keepAlive: true)
SqliteQueryRepository sqliteQueryRepository(SqliteQueryRepositoryRef ref) => SqliteQueryRepository(ref);

/// 点検SQLiteリポジトリ
class SqliteQueryRepository {
  /// init
  SqliteQueryRepository(this.ref);

  /// ref
  final Ref ref;

  /// 期間を検索
  Future<FreonPeriod?> getCurrentPeriod(DateTime currentDateTime) async {
    final db = await getFreonCheckDatabase();
    final currentDateString = currentDateTime.toIso8601String().split('T').first;
    try {
      final result = await db.rawQuery('''
SELECT T.periodcd AS periodcd , T.periodname as periodname
FROM (
    SELECT
        p1.periodcd,
        p1.periodname,
        p1.startdate AS pstartdate,
        CASE
            WHEN p2.startdate IS NULL THEN '2500-01-01'
            ELSE p2.startdate
        END AS penddate
    FROM ${FreonCheckMasterTable.period.name} p1
    LEFT JOIN ${FreonCheckMasterTable.period.name} p2 ON p1.periodcd + 1 = p2.periodcd
    WHERE p1.hitflag = 0
) AS T
WHERE date(T.pstartdate) <= date('$currentDateString')
  AND date(T.penddate) > date('$currentDateString');
          ''');
      if (result.isEmpty) {
        return null;
      }
      return FreonPeriod.fromMap(result.first);
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 期間内のフロン点検の点検場所の進捗一覧を取得
  Future<List<LocationProcess>> listCurrentPeriod({
    required String storeCode,
    required int periodCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final result = await db.rawQuery('''
SELECT 
    mp.placecd, 
    mp.placename, 
    dpr.progress,
    CASE dps.placestate
        WHEN 1 THEN '作業中'
        WHEN 2 THEN '済'
        ELSE '未着手'
    END AS placestate
FROM ${FreonCheckMasterTable.placeSub.name} mp
JOIN ${FreonCheckDataTable.progressSub.name} dpr ON dpr.placecd = mp.placecd
LEFT JOIN (
    SELECT * 
    FROM ${FreonCheckDataTable.placeState.name} dp
    WHERE dp.branchcd = $storeCode
      AND dp.periodcd = $periodCode
      AND dp.hitflag = 0
) dps ON dps.placecd = mp.placecd
WHERE mp.branchcd = $storeCode
  AND mp.hitflag = 0;
          ''');
      return result.map(LocationProcess.fromMap).toList();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 期間数を取得
  Future<int> countOfPeriod({
    required int periodCode,
    required DateTime currentDateTime,
  }) async {
    final db = await getFreonCheckDatabase();
    final currentDateString = currentDateTime.toIso8601String().split('T').first;
    try {
      return Sqflite.firstIntValue(
            await db.rawQuery('''
SELECT count(periodcd)
FROM ${FreonCheckMasterTable.period.name}
WHERE date(startdate) <= date('$currentDateString')
  AND date(enddate) >= date('$currentDateString')
  AND periodcd = $periodCode;
'''),
          ) ??
          0;
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 場所の設備リストを取得
  Future<List<LocationEquipment>?> listLocationEquipments({
    required String storeCode,
    required int placeCode,
    required int periodCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final result = await db.rawQuery('''
SELECT
    eq.equiimg, 
    sub.equicd,
    eq.equiname,
    COUNT(re.resultid) AS checkCount,
    COUNT(re.equistate) AS questionCount
FROM
    ${FreonCheckMasterTable.equipmentSub.name} sub
JOIN
    ${FreonCheckMasterTable.equipment.name} eq ON sub.equicd = eq.equicd
LEFT JOIN ${FreonCheckDataTable.checkResult.name} re 
    ON sub.branchcd = re.branchcd
    AND sub.placecd = re.placecd
    AND sub.equicd = re.equicd
    AND re.periodcd = $periodCode
WHERE
    sub.hitflag != 1
    AND eq.hitflag != 1
    AND (re.hitflag != 1 OR re.hitflag IS NULL)
    AND sub.branchcd = $storeCode
    AND sub.placecd = $placeCode
GROUP BY
    sub.equicd,
    eq.equiimg,
    eq.equiname;
          ''');
      if (result.isEmpty) {
        final backupResult = await db.rawQuery('''
WITH equidata AS ( -- 場所code、設備写真、設備code、設備ナーム情報を検索
   SELECT 
       sub.placecd,
       eq.equiimg, 
       sub.equicd, 
       eq.equiname  
   FROM ${FreonCheckMasterTable.equipmentSub.name} sub 
   JOIN ${FreonCheckMasterTable.equipment.name} eq ON sub.equicd = eq.equicd 
   WHERE sub.hitflag != 1 
     AND sub.branchcd = $storeCode
     AND eq.hitflag != 1 
     AND sub.placecd = $placeCode
),
resultequidata AS ( -- 点検数、問題あり点検数を検索
   SELECT 
       equidata.*,
       CASE 
           WHEN re.resultid IS NULL THEN 0 
           ELSE 1 
       END AS resall,
       CASE 
           WHEN re.resultid IS NULL OR re.equistate IS NULL THEN 0 
           ELSE 1 
       END AS res 
   FROM equidata
   LEFT JOIN ${FreonCheckDataTable.checkResult.name} re ON equidata.placecd = re.placecd 
                                       AND equidata.equicd = re.equicd 
                                       AND re.branchcd = $storeCode
                                       AND re.periodcd = $periodCode
)
SELECT 
   resultequidata.equiimg AS equiimg, 
   resultequidata.equicd AS equicd, 
   resultequidata.equiname AS equiname, 
   SUM(resultequidata.resall) AS checkCount, 
   SUM(resultequidata.res) AS questionCount 
FROM resultequidata
GROUP BY 
   resultequidata.equiimg, 
   resultequidata.equicd, 
   resultequidata.equiname;

    ''');
        return backupResult.map(LocationEquipment.fromMap).toList();
      }
      return result.map(LocationEquipment.fromMap).toList();
    } catch (e) {
      rethrow;
    }
  }

  /// 点検結果tableから履歴番号リストを取得
  Future<List<String>> listResultIdOfEquipment({
    required String storeCode,
    required int placeCode,
    required int periodCode,
  }) async {
    final db = await getFreonCheckDatabase();

    try {
      final result = await db.query(
        FreonCheckDataTable.checkResult.name,
        columns: ['resultid'],
        where: '''
periodcd = ?
and branchcd = ? 
and placecd = ?
''',
        whereArgs: [periodCode, storeCode, placeCode],
      );

      return result.expand((map) => map.values).map((value) => value.toString()).toList();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// tableからimageとresultIdを取得する
  Future<List<LocationEquipmentImageJob>?> listImageResult({
    required String storeCode,
    required int placeCode,
    required int periodCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final imageResults = await db.rawQuery('''
SELECT 
    t_img.resultid,
    t_img.img 
FROM 
    ${FreonCheckDataTable.checkResult.name} ch 
JOIN 
    ${FreonCheckDataTable.image.name} t_img 
ON 
    ch.resultid = t_img.resultid 
WHERE 
    isuploaded = 0 
    AND branchcd = $storeCode
    AND placecd = $placeCode
    AND periodcd = $periodCode
          ''');

      if (imageResults.isEmpty) return null;
      return imageResults.map(LocationEquipmentImageJob.fromMap).toList();
    } catch (e) {
      rethrow;
    }
  }

  /// 点検結果tableから履歴番号リストを取得
  Future<List<LocationEquipmentUpdateJob>> listEquipmentUpdateJobs({
    required String storeCode,
    required int placeCode,
    required int periodCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final checkResultList = await db.query(
        FreonCheckDataTable.checkResult.name,
        columns: [
          'resultid',
          'periodcd',
          'branchcd',
          'placecd',
          'equicd',
          'equistate',
          'equino',
          'errorcode',
          'comment',
          'imgs',
          'hitflag',
          'createtime',
          'createemoployee',
          'modifytime',
          'modifyemployee',
        ],
        whereArgs: [storeCode, placeCode, periodCode],
        where: '''
isuploaded = 0 
AND branchcd = ? 
AND placecd = ?
AND periodcd = ?
      ''',
      );

      return checkResultList.map(LocationEquipmentUpdateJob.fromMap).toList();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備詳細の期間状況を取得
  Future<EquipmentPlaceStateEnum> getPeriodOfEquipment({
    required String storeCode,
    required int placeCode,
    required int periodCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final result = await db.rawQuery('''
SELECT 
    periodcd AS 'datacount' 
FROM 
    ${FreonCheckMasterTable.period.name}
WHERE 
    startdate <= date('now', 'localtime') 
    AND enddate >= date('now', 'localtime') 
    AND periodcd = $periodCode
    ''');
      if (result.isEmpty) {
        return EquipmentPlaceStateEnum.isNotExist;
      }
      final backupResult = await db.query(
        FreonCheckDataTable.placeState.name,
        columns: ['placestate'],
        where: '''
    periodcd = ?
    AND branchcd = ?
    AND placecd = ?
    AND hitflag = 0;
''',
        whereArgs: [periodCode, storeCode, placeCode],
      );

      if (backupResult.isEmpty) {
        return EquipmentPlaceStateEnum.isInProgress;
      }
      final placeStateMap = backupResult.first;
      final placeState = placeStateMap['placestate'];
      if (placeState == null || placeState is! int) {
        return EquipmentPlaceStateEnum.isInProgress;
      }
      return switch (placeState) {
        2 => EquipmentPlaceStateEnum.isCompleted,
        _ => EquipmentPlaceStateEnum.isInProgress,
      };
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備の選択Image情報を取得
  Future<EquipmentImageDetail?> getEquipmentImageDetail({
    required int equipmentCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final imageResult = await db.query(
        FreonCheckMasterTable.equipment.name,
        columns: [
          'equiimgo',
          'equiimgx',
          'remark',
        ],
        where: ' hitflag != 1 AND equicd = ?',
        whereArgs: [equipmentCode],
      );

      if (imageResult.isEmpty) {
        return null;
      }

      return EquipmentImageDetail.fromMap(imageResult.first);
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備情報を取得
  Future<List<EquipmentDetail>?> listEquipmentDetail({
    required String storeCode,
    required int placeCode,
    required int periodCode,
    required int equipmentCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final result = await db.rawQuery('''
SELECT 
    re.resultid,
    group_concat(his.img, ',') AS img,
    re.equistate,
    re.equino,
    date(re.createtime) AS createtime
FROM 
    ${FreonCheckDataTable.checkResult.name} re
JOIN 
    (SELECT * FROM ${FreonCheckDataTable.image.name} UNION SELECT * FROM ${FreonCheckDataTable.imageHistory.name}) his 
ON 
    re.resultid = his.resultid
WHERE 
    re.hitflag = 0 
    AND re.periodcd = $periodCode
    AND re.branchcd = $storeCode
    AND re.placecd = $placeCode
    AND re.equicd = $equipmentCode
GROUP BY 
    re.resultid,
    re.equistate,
    re.equino
ORDER BY 
    re.createtime DESC
    ''');

      if (result.isEmpty) {
        return null;
      }

      final stateList = await listRegisterStates(equipmentCode: equipmentCode);

      return result
          .map(
            (value) => EquipmentDetail.fromMap(
              map: value,
              stateList: stateList,
            ),
          )
          .toList();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 問題登録の状態リストを取得
  Future<List<FreonCheckRegisterStateDetail>?> listRegisterStates({
    required int equipmentCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final result = await db.query(
        FreonCheckMasterTable.equipmentState.name,
        columns: ['statecd', 'statename'],
        where: 'equicd = ? AND hitflag = 0',
        whereArgs: [equipmentCode],
      );

      return result.map(FreonCheckRegisterStateDetail.fromMap).toList();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// isUploadedを取得:報告済み(1) =>true 未報告(0)=>false
  Future<bool?> getIsUploaded({required int historyResultId}) async {
    final db = await getFreonCheckDatabase();
    try {
      final result = await db.query(
        FreonCheckDataTable.checkResult.name,
        columns: ['isuploaded'],
        where: 'resultid=?',
        whereArgs: [historyResultId],
      );

      if (result.isEmpty) {
        return null;
      }
      final map = result.first;
      final isUploaded = map['isuploaded'];
      if (isUploaded == null || isUploaded is! int) {
        return null;
      }
      return isUploaded == 1;
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 登録してる問題詳細情報を取得
  Future<EquipmentRegisteredDetail?> getEquipmentRegisteredDetail(int historyResultId) async {
    final db = await getFreonCheckDatabase();
    try {
      final result = await db.rawQuery('''
SELECT
    re.equistate AS equistate, 
    group_concat(his.img, ',') AS imgs, 
    re.equino AS equino, 
    re.errorcode AS errorcode, 
    re.comment AS comment 
FROM 
    ${FreonCheckDataTable.checkResult.name} re 
LEFT JOIN 
    (
        SELECT * FROM ${FreonCheckDataTable.image.name} 
        UNION 
        SELECT * FROM ${FreonCheckDataTable.imageHistory.name}
    ) his 
ON 
    re.resultid = his.resultid  
WHERE 
    re.hitflag = 0 
    AND re.resultid = $historyResultId
GROUP BY 
    re.resultid;
      ''');
      if (result.isEmpty) {
        return null;
      }
      return EquipmentRegisteredDetail.fromMap(result.first);
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 設備の最大写真数を取得
  Future<int> getMaxPhoto(int equipmentCode) async {
    final db = await getFreonCheckDatabase();
    try {
      final result = await db.query(
        FreonCheckMasterTable.equipment.name,
        columns: ['maxpicnum'],
        where: 'equicd = ?',
        whereArgs: [equipmentCode],
      );

      if (result.isEmpty) {
        return 0;
      }
      final maxPhoto = result.first['maxpicnum'];
      if (maxPhoto is! int) {
        return 0;
      }
      return maxPhoto;
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 未登録情報数を取得
  Future<bool> getHasUnregisteredData() async {
    final db = await getFreonCheckDatabase();
    try {
      final result = await db.query(
        FreonCheckDataTable.checkResult.name,
        columns: ['resultid'],
        where: 'isuploaded = 0',
      );

      return result.isNotEmpty;
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }
}
