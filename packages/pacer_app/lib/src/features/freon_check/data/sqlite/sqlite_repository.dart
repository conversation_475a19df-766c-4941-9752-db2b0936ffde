import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

import 'freon_check_sqlite_config.dart';

/// TABLEの生成、Databaseのインステンスを取得
Future<Database> getFreonCheckDatabase() async {
  final databasesPath = await getDatabasesPath();
  final path = join(databasesPath, freonCheckDbName);

  return openDatabase(
    path,
    version: 1,
    onCreate: (Database db, int version) async {
      await db.execute('''
CREATE TABLE ${FreonCheckMasterTable.equipment.name} (
equicd int4 NOT NULL,
equiname varchar(100) NULL,
equiimg text NULL,
equiimgo text NULL,
equiimgx text NULL,
remark varchar(50) NULL,
hitflag int4 NULL,
createtime timestamp NULL,
createemoployee int4 NULL,
modifytime timestamp NULL,
modifyemployee int4 NULL,
checkmemo text NULL,
maxpicnum int4 NULL,
PRIMARY KEY (equicd))
        ''');
      await db.execute('''
CREATE TABLE  ${FreonCheckMasterTable.equipmentState.name}  (
equicd int4 NOT NULL,
statecd int4 NOT NULL,
statename varchar(100) NULL,
hitflag int4 NULL,
createtime timestamp NULL,
createemoployee int4 NULL,
modifytime timestamp NULL,
modifyemployee int4 NULL,
PRIMARY KEY (equicd,statecd))
        ''');
      await db.execute('''
CREATE TABLE ${FreonCheckMasterTable.equipmentSub.name} (
branchcd int4 NOT NULL,
placecd int4 NOT NULL,
equicd int4 NOT NULL,
hitflag int4 NULL,
createtime timestamp NULL,
createemoployee int4 NULL,
modifytime timestamp NULL,
modifyemployee int4 NULL,
PRIMARY KEY (branchcd,placecd,equicd))
        ''');
      await db.execute('''
CREATE TABLE ${FreonCheckMasterTable.period.name} (
periodcd int4 NOT NULL,
periodname varchar(100) NULL,
hitflag int4 NULL,
startdate date NULL,
enddate date NULL,
createtime timestamp NULL,
createemoployee int4 NULL,
modifytime timestamp NULL,
modifyemployee int4 NULL,
PRIMARY KEY (periodcd))
        ''');
      await db.execute('''
CREATE TABLE ${FreonCheckMasterTable.placeSub.name} (
placecd int4 NOT NULL,
branchcd int4 NOT NULL,
placename varchar(100) NULL,
hitflag int4 NULL,
createtime timestamp NULL,
createemoployee int4 NULL,
modifytime timestamp NULL,
modifyemployee int4 NULL,
PRIMARY KEY (placecd,branchcd))
        ''');
      await db.execute('''
CREATE TABLE ${FreonCheckDataTable.placeState.name} (
periodcd int4 NOT NULL,
branchcd int4 NOT NULL,
placecd int4 NOT NULL,
placestate int4 NULL,
hitflag int4 NULL,
createtime timestamp NULL,
createemoployee int4 NULL,
modifytime timestamp NULL,
modifyemployee int4 NULL,
PRIMARY KEY (periodcd,branchcd,placecd))
        ''');
      await db.execute('''
CREATE TABLE ${FreonCheckDataTable.checkResult.name} (
resultid int8 NOT NULL,
periodcd int4 NOT NULL,
branchcd int4 NOT NULL,
placecd int4 NOT NULL,
equicd int4 NOT NULL,
equistate varchar(100) NULL,
equino varchar(100) NULL,
errorcode varchar(100) NULL,
"comment" text NULL,
imgs text NULL,
isuploaded int4 NULL,
hitflag int4 NULL,
createtime timestamp NULL,
createemoployee int4 NULL,
modifytime timestamp NULL,
modifyemployee int4 NULL,
PRIMARY KEY (resultid))
        ''');
      await db.execute('''
CREATE TABLE ${FreonCheckDataTable.progressSub.name} (
periodcd int4 NOT NULL,
branchcd int4 NOT NULL,
placecd int4 NOT NULL,
progress text NULL,
PRIMARY KEY (periodcd,branchcd,placecd))
        ''');
      await db.execute('''
CREATE TABLE ${FreonCheckDataTable.imageHistory.name} (
resultid int8 NOT NULL,
img text NULL,
createtime timestamp NULL,
createemoployee int4 NULL,
modifytime timestamp NULL,
modifyemployee int4 NULL,
PRIMARY KEY (resultid,img))
        ''');

      await db.execute('''
CREATE TABLE ${FreonCheckDataTable.image.name} (
resultid int8 NOT NULL,
img text NULL,
createtime timestamp NULL,
createemoployee int4 NULL,
modifytime timestamp NULL,
modifyemployee int4 NULL,
PRIMARY KEY (resultid,img))
        ''');
    },
  );
}
