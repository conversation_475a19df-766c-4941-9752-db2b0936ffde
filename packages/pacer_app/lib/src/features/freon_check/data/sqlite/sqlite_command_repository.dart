/// data テーブルを更新
library;

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../exceptions/app_exception.dart';
import '../../domain/equipment_register_job.dart';
import 'freon_check_sqlite_config.dart';
import 'sqlite_repository.dart';

part 'sqlite_command_repository.g.dart';

/// フロン簡易点検SQLiteリポジトリ_データの変更（delete,update,insert）
@Riverpod(keepAlive: true)
SqliteCommandRepository sqliteCommandRepository(SqliteCommandRepositoryRef ref) => SqliteCommandRepository(ref);

/// 点検SQLiteリポジトリ
class SqliteCommandRepository {
  /// init
  SqliteCommandRepository(this.ref);

  /// ref
  final Ref ref;

  /// 複数SQL文で結果と写真tableを更新。
  Future<void> executeFreonCheckDbBySqlStrings(List<String> sqlList, {bool? continueOnError = false}) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();
      for (final sqlString in sqlList) {
        if (sqlString.isNotEmpty) batch.execute(sqlString);
      }

      await batch.commit(continueOnError: continueOnError);
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 結果情報tableの情報を更新
  Future<void> updateCheckResult({
    required int periodCode,
    required String storeCode,
    required String placeCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      await db.update(
        FreonCheckDataTable.checkResult.name,
        {'isuploaded': 1},
        where: '''
isuploaded = 0
AND periodcd = ?
AND branchcd = ?
AND placecd = ?
      ''',
        whereArgs: [periodCode, storeCode, placeCode],
      );
    } catch (e) {
      rethrow;
    }
  }

  /// 場所tableの情報を更新
  Future<void> updatePlaceState({
    required int placeState,
    required int periodCode,
    required String storeCode,
    required String placeCode,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      await db.update(
        FreonCheckDataTable.placeState.name,
        {'placestate': placeState},
        whereArgs: [periodCode, storeCode, placeCode],
        where: '''
periodcd = ?
and branchcd = ?
and placecd = ?
      ''',
      );
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 結果table、image tableを更新
  /// 1.image履歴tableを更新
  /// 2.imageの写真情報を削除
  /// 3.result tableにupdateした写真を更新
  Future<void> updateResultAndImageHistory({
    required int historyResultId,
    required String imagePath,

    /// 最新uploadした写真アドレス
    required String newImageUrl,
  }) async {
    final db = await getFreonCheckDatabase();
    final batch = db.batch();
    try {
      final _ = batch
        ..rawInsert('''
INSERT INTO ${FreonCheckDataTable.imageHistory.name} 
    (
    resultid,
    img,
    createtime,
    createemoployee,
    modifytime,
    modifyemployee
    ) 
SELECT 
    resultid, 
    img, 
    createtime, 
    createemoployee, 
    modifytime, 
    modifyemployee 
FROM 
    ${FreonCheckDataTable.image.name} 
WHERE 
    resultid = $historyResultId
    AND img = $imagePath
 ''')
        ..delete(
          FreonCheckDataTable.image.name,
          where: 'resultid = ? and img = ?',
          whereArgs: [historyResultId, imagePath],
        )

        /// TODO: 単純化する
        ..rawUpdate('''
          UPDATE ${FreonCheckDataTable.checkResult.name}
           SET imgs= replace(imgs,'$imagePath','$newImageUrl')
            WHERE resultid=$historyResultId
          ''');
      await batch.commit(continueOnError: false);
    } catch (e) {
      rethrow;
    }
  }

  /// DBに問題なしを登録
  Future<void> registerNoProblem({
    required int periodCode,
    required String storeCode,
    required String placeCode,
    required int equipmentCode,
    required EquipmentRegisterJob equipmentRegisterJob,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch()
        ..delete(
          FreonCheckDataTable.checkResult.name,
          where: '''
periodcd = ?
AND branchcd = ?
AND placecd = ?
AND equicd = ?
AND equistate IS NULL;
''',
          whereArgs: [periodCode, storeCode, placeCode, equipmentCode],
        )

        /// INSERT INTO $_freonCheckCheckResult
        ///   (resultid, periodcd, branchcd, placecd,
        ///   equicd, equistate, equino, errorcode,
        ///   "comment", imgs, isuploaded, hitflag,
        ///   createtime, createemoployee, modifytime, modifyemployee)
        /// VALUES
        ///   (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ///   datetime('now', 'localtime'), ?,
        ///   datetime('now', 'localtime'), ?);
        ..insert(
          FreonCheckDataTable.checkResult.name,
          equipmentRegisterJob.toRegisterResultMap(),
        );
      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// localの登録してる問題を削除
  Future<void> deleteRegisteredProblem({required int historyResultId}) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch()
        ..delete(
          FreonCheckDataTable.checkResult.name,
          where: '''
resultid=?
and isuploaded=0
''',
          whereArgs: [historyResultId],
        )
        ..delete(FreonCheckDataTable.image.name, where: 'resultid=?', whereArgs: [historyResultId]);
      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 登録画面：問題を登録
  Future<void> registerProblem({
    required EquipmentRegisterJob registerJob,
  }) async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();
      final resultMap = registerJob.toRegisterResultMap();

      /// INSERT_INTO t_d_checkresult
      /// (resultid, periodcd, branchcd, placecd,
      /// equicd, equistate, equino, errorcode, 'comment',
      /// imgs, isuploaded, hitflag, createtime, createemoployee,
      /// modifytime, modifyemployee)
      /// VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
      /// datetime('now','localtime'), ?, datetime('now','localtime'), ?)
      batch.insert(FreonCheckDataTable.checkResult.name, resultMap);

      final imageMap = registerJob.toRegisterImageMapList();
      if (imageMap != null) {
        /// INSERT_INTO t_d_img
        /// (resultid, img, createtime, createemoployee,
        /// modifytime, modifyemployee)
        /// VALUES(?, ?,  datetime('now','localtime'), ?,
        /// datetime('now','localtime'), ?);
        for (final map in imageMap) {
          batch.insert(FreonCheckDataTable.image.name, map);
        }
      }
      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// master以外tableのデータを消します
  Future<void> deleteNonMasterTableData() async {
    final db = await getFreonCheckDatabase();
    try {
      final batch = db.batch();

      final _ = batch
        ..delete(FreonCheckDataTable.checkResult.name)
        ..delete(FreonCheckDataTable.imageHistory.name)
        ..delete(FreonCheckDataTable.image.name);

      await batch.commit();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(freonCheckSqlErrorMessage),
        },
        StackTrace.current,
      );
    }
  }
}
