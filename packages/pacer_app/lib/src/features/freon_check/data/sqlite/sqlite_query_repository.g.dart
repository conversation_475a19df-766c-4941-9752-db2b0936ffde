// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sqlite_query_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sqliteQueryRepositoryHash() => r'b3b589c761080c82c5381548d6b237c97292a09e';

/// フロン簡易点検SQLiteリポジトリ_データの検索
///
/// Copied from [sqliteQueryRepository].
@ProviderFor(sqliteQueryRepository)
final sqliteQueryRepositoryProvider = Provider<SqliteQueryRepository>.internal(
  sqliteQueryRepository,
  name: r'sqliteQueryRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$sqliteQueryRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SqliteQueryRepositoryRef = ProviderRef<SqliteQueryRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
