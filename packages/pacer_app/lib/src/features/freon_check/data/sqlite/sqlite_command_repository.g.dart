// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sqlite_command_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sqliteCommandRepositoryHash() => r'b68ef7915f630ae94b1d09bf3221dea80f6c31c2';

/// フロン簡易点検SQLiteリポジトリ_データの変更（delete,update,insert）
///
/// Copied from [sqliteCommandRepository].
@ProviderFor(sqliteCommandRepository)
final sqliteCommandRepositoryProvider = Provider<SqliteCommandRepository>.internal(
  sqliteCommandRepository,
  name: r'sqliteCommandRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$sqliteCommandRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SqliteCommandRepositoryRef = ProviderRef<SqliteCommandRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
