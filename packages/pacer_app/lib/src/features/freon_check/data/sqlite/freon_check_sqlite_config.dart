/// フロン点検SQLのconfig
/// SQL error message
const String freonCheckSqlErrorMessage = 'SQLiteのエラーです。再度お試しください';

/// db
const String freonCheckDbName = 'FreonCheckSQLite.db3';

/// フロン点検のmaster tableのname type
enum FreonCheckMasterTable {
  /// 設備マスタ t_m_equi Master
  equipment(name: 't_m_equi'),

  /// 設備状態マスタ t_m_equistate Master
  equipmentState(name: 't_m_equistate'),

  /// 設備所属 t_m_equisub Master
  equipmentSub(name: 't_m_equisub'),

  /// 期間マスタ t_m_period Master
  period(name: 't_m_period'),

  /// 場所マスタ t_m_placesub Master
  placeSub(name: 't_m_placesub');

  /// init
  const FreonCheckMasterTable({
    required this.name,
  });

  /// table name
  final String name;
}

/// フロン点検のdata tableのname type
enum FreonCheckDataTable {
  /// 場所状態 t_d_placestate Data
  placeState(name: 't_d_placestate'),

  /// 点検結果 t_d_checkresult Data
  checkResult(name: 't_d_checkresult'),

  /// 進捗情報　t_d_progress_sub
  progressSub(name: 't_d_progress_sub'),

  /// 写真履歴 t_d_img_history
  imageHistory(name: 't_d_img_history'),

  /// 写真 t_d_img
  image(name: 't_d_img');

  /// init
  const FreonCheckDataTable({
    required this.name,
  });

  /// table name
  final String name;
}
