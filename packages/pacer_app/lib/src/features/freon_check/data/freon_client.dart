import 'dart:typed_data';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/cloud_files/v1/cloud_files.pbgrpc.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../acceptance_inspection/presentation/utils/grpc_error_extension.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/equipment_detail.dart';
import '../domain/equipment_image.dart';
import '../domain/equipment_report_type_enum.dart';
import '../domain/freon_equipment.dart';
import '../domain/freon_equipment_state.dart';
import '../domain/freon_equipment_substitute.dart';
import '../domain/freon_period.dart';
import '../domain/freon_place_state.dart';
import '../domain/freon_place_substitute.dart';
import '../domain/freon_work_progress.dart';
import '../domain/history.dart';
import '../domain/location_equipment_update_job.dart';
import '../domain/refresh_result.dart';

part 'freon_client.g.dart';

/// フロン点検
@Riverpod(keepAlive: true)
FreonClient freonClient(
  FreonClientRef ref, {
  required AppUser? caller,
}) =>
    FreonClient(caller: caller);

/// フロン点検
class FreonClient {
  /// ペアリング　init
  FreonClient({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  final _uri = Env.getApiBaseUrl();

  final _options = CallOptions(timeout: const Duration(seconds: 60));

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));

  /// 設備マスタデータ情報を取得
  Future<List<FreonEquipment>> listEquipments(
    String? storeCode,
  ) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getTableEquipment(
        GetTableEquipmentRequest(storeCode: storeCode),
      );

      return switch (resp) {
        GetTableEquipmentResponse(code: == '000') => resp.tableEquipmentInfo.map(FreonEquipment.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 設備状態マスタデータ取得
  Future<List<FreonEquipmentState>> listEquipmentStates(
    String? storeCode,
  ) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.getTableEquipmentState(
        GetTableEquipmentStateRequest(storeCode: storeCode),
      );

      return switch (resp) {
        GetTableEquipmentStateResponse(code: == '000') =>
          resp.tableEquipmentStateInfo.map(FreonEquipmentState.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 店舗マスタデータ(設備所属table)取得
  Future<List<FreonEquipmentSubstitute>> listEquipmentStores(
    String? storeCode,
  ) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getTableEquipmentSub(
        GetTableEquipmentSubRequest(storeCode: storeCode),
      );

      return switch (resp) {
        GetTableEquipmentSubResponse(code: == '000') =>
          resp.tableEquipmentSub.map(FreonEquipmentSubstitute.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 期間マスタデータ取得
  Future<List<FreonPeriod>> listPeriods(
    String? storeCode,
  ) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getTableEquipmentPeriod(
        GetTableEquipmentPeriodRequest(storeCode: storeCode),
      );

      return switch (resp) {
        GetTableEquipmentPeriodResponse(code: == '000') => resp.tableEquipmentPeriod.map(FreonPeriod.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 場所マスタデータ取得
  Future<List<FreonPlaceSubstitute>> listPlaceSubstitutes(
    String? storeCode,
  ) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getTablePlaceSub(
        GetTablePlaceSubRequest(storeCode: storeCode),
      );

      return switch (resp) {
        GetTablePlaceSubResponse(code: == '000') => resp.tablePlaceSub.map(FreonPlaceSubstitute.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 場所状態データ取得
  Future<List<FreonPlaceState>> listPlaceStates(
    String? storeCode,
  ) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getTablePlaceState(
        GetTablePlaceStateRequest(storeCode: storeCode),
      );

      return switch (resp) {
        GetTablePlaceStateResponse(code: == '000') => resp.tablePlaceState.map(FreonPlaceState.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 作業進捗情報の取得
  Future<List<FreonWorkProgress>> listWorkProgress({
    required String storeCode,
    required int periodCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getProgress(
        GetProgressRequest(
          storeCode: storeCode,
          periodCode: periodCode,
        ),
      );

      return switch (resp) {
        GetProgressResponse(code: == '000') => resp.progressResponse
            .map(
              (e) => FreonWorkProgress.fromGrpc(
                e,
                periodCode: periodCode,
                storeCode: storeCode,
              ),
            )
            .toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 過去履歴情報取得
  Future<List<History>> getHistory({
    required String? storeCode,
    required int periodCode,
    required String placeCode,
    required int equipmentCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getCheckHistory(
        GetCheckHistoryRequest(
          storeCode: storeCode,
          periodCode: periodCode,
          placeCode: placeCode,
          equipmentCode: equipmentCode,
        ),
      );

      return switch (resp) {
        GetCheckHistoryResponse(code: == '000') => resp.checkHistory.map(History.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  ///　最新点検結果リストを取得
  Future<RefreshResult> getRefreshResult({
    required int periodCode,
    required String storeCode,
    required String placeCode,

    /// 点検結果tableのPK　履歴番号
    required List<String> historyResultIDs,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getEquilist(
        GetEquilistRequest(
          periodCode: periodCode,
          storeCode: storeCode,
          placeCode: placeCode,
          resultId: historyResultIDs,
        ),
      );
      return RefreshResult.fromString(resp.data);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  ///　設備情報を報告（途中報告・完了報告）
  Future<String> reportEquipment({
    required int periodCode,
    required String storeCode,
    required String placeCode,
    required String userCode,
    required EquipmentReportTypeEnum equipmentReportType,

    /// 点検結果リスト SetEquilistRequest_Equilist
    required List<LocationEquipmentUpdateJob> equipmentUpdateJobs,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    final request = SetEquilistRequest(
      periodCode: periodCode,
      storeCode: storeCode,
      placeCode: placeCode,
      editor: userCode,
      flag: equipmentReportType.boolValue,
      jsonData: equipmentUpdateJobs.map((value) => value.toEquipment()).toList(),
    );

    try {
      final resp = await stub.setEquilist(request);

      return switch (resp) {
        SetEquilistResponse(code: == '000') => resp.procSetPlaceState,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 履歴設備情報リストを取得
  Future<List<EquipmentDetail>> listEquipmentDetails({
    required String storeCode,
    required int placeCode,
    required int periodCode,
    required int equipmentCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = FreonCheckServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getCheckHistory(
        GetCheckHistoryRequest(
          periodCode: periodCode,
          storeCode: storeCode,
          placeCode: placeCode.toString(),
          equipmentCode: equipmentCode,
        ),
      );

      return switch (resp) {
        GetCheckHistoryResponse(code: == '000') => resp.checkHistory.map(EquipmentDetail.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 設備問題写真取得
  Future<Uint8List> getPhoto({
    required String storeCode,

    /// "/202308/30/202308300912318946880554.jpg"
    required String fileUrl,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = CloudFilesServiceClient(
      channel,
      options: _options,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final request = GetFileRequest(path: fileUrl, storeCode: storeCode);
    final response = await stub.getFile(request);
    try {
      if (response.data.isEmpty) {
        throw UnknownException('写真が取得できませんでした');
      }
      return Uint8List.fromList(response.data);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 設備問題の写真をアップロードする
  /// ファイル名は自動生成したIDにする
  Future<EquipmentImage> uploadProblemImage({
    required String storeCode,
    required Uint8List imageData,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = CloudFilesServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final fileRequest = File(
      /// "iVBORw0KGgoAAAAN...
      content: imageData,

      /// 意味がないフィールドなので空文字にしておく
      /// .jpgじゃないとエラーになる
      fileName: 'pacer4.jpg',
    );
    final request = UploadFileCommonRequest(
      storeCode: storeCode,
      employeeCode: caller?.userCode,
      userUploadFile: fileRequest,
    );
    try {
      final response = await stub.uploadFileCommon(request);

      return EquipmentImage(
        id: PhotoID(response.fileId),
        imageUrl: response.path,
      );
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
