// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_client.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonClientHash() => r'0e1df15b0e7fafdb804c08a63d569b363cf7a3c0';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// フロン点検
///
/// Copied from [freonClient].
@ProviderFor(freonClient)
const freonClientProvider = FreonClientFamily();

/// フロン点検
///
/// Copied from [freonClient].
class FreonClientFamily extends Family {
  /// フロン点検
  ///
  /// Copied from [freonClient].
  const FreonClientFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'freonClientProvider';

  /// フロン点検
  ///
  /// Copied from [freonClient].
  FreonClientProvider call({
    required AppUser? caller,
  }) {
    return FreonClientProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  FreonClientProvider getProviderOverride(
    covariant FreonClientProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(FreonClient Function(FreonClientRef ref) create) {
    return _$FreonClientFamilyOverride(this, create);
  }
}

class _$FreonClientFamilyOverride implements FamilyOverride {
  _$FreonClientFamilyOverride(this.overriddenFamily, this.create);

  final FreonClient Function(FreonClientRef ref) create;

  @override
  final FreonClientFamily overriddenFamily;

  @override
  FreonClientProvider getProviderOverride(
    covariant FreonClientProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// フロン点検
///
/// Copied from [freonClient].
class FreonClientProvider extends Provider<FreonClient> {
  /// フロン点検
  ///
  /// Copied from [freonClient].
  FreonClientProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => freonClient(
            ref as FreonClientRef,
            caller: caller,
          ),
          from: freonClientProvider,
          name: r'freonClientProvider',
          debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonClientHash,
          dependencies: FreonClientFamily._dependencies,
          allTransitiveDependencies: FreonClientFamily._allTransitiveDependencies,
          caller: caller,
        );

  FreonClientProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    FreonClient Function(FreonClientRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FreonClientProvider._internal(
        (ref) => create(ref as FreonClientRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  ProviderElement<FreonClient> createElement() {
    return _FreonClientProviderElement(this);
  }

  FreonClientProvider _copyWith(
    FreonClient Function(FreonClientRef ref) create,
  ) {
    return FreonClientProvider._internal(
      (ref) => create(ref as FreonClientRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is FreonClientProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FreonClientRef on ProviderRef<FreonClient> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _FreonClientProviderElement extends ProviderElement<FreonClient> with FreonClientRef {
  _FreonClientProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as FreonClientProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
