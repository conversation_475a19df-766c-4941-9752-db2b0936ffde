import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';

part 'freon_check_image_file_repository.g.dart';

/// 写真保存機能
@Riverpod(keepAlive: true)
FreonCheckImageFileRepository freonCheckImageFileRepository(
  FreonCheckImageFileRepositoryRef ref,
) =>
    FreonCheckImageFileRepository(ref);

/// 点検写真ファイル保存リポジトリ
class FreonCheckImageFileRepository {
  /// init
  FreonCheckImageFileRepository(this.ref);

  /// ref
  final Ref ref;

  /// db
  static const _freonCheckFile = 'FreonCheckImageFile';

  /// File error message
  static const _errorMessage = 'Fileのエラーです。再度お試しください';

  /// Sandboxのフロン点検フィルのPathを取得（path/FreonCheckImageFile）
  Future<String> _getFreonCheckFilePath() async {
    /// appのbase path
    final directory = await getApplicationDocumentsDirectory();

    /// fileのPathを生成
    return '${directory.path}/$_freonCheckFile';
  }

  ///　Imageをlocalのsandboxに保存される
  /// true：保存が成功、false：失敗
  /// foldName:子フォルダ名（path/FreonCheckImageFile/foldName）
  Future<bool> saveImageInLocalSandbox({
    required Uint8List imageBytes,
    required String imageName,
    required String foldName,
  }) async {
    try {
      final freonCheckFilePath = await _getFreonCheckFilePath();
      final freonCheckDirectory = Directory(freonCheckFilePath);
      if (!freonCheckDirectory.existsSync()) {
        await freonCheckDirectory.create(recursive: true);
      }

      final subFreonCheckDirectory = Directory('${freonCheckDirectory.path}/$foldName');
      if (!subFreonCheckDirectory.existsSync()) {
        await subFreonCheckDirectory.create(recursive: true);
      }

      final fullFilePath = '${subFreonCheckDirectory.path}/$imageName';

      /// 写真を保存
      final file = File(fullFilePath);
      await file.writeAsBytes(imageBytes);
      return true;
    } catch (e) {
      log(e.toString());

      /// 保存が失敗しでも、エラーを表示されない
      return false;
    }
  }

  /// Imageをlocalのsandboxから取得する
  /// foldName:子フォルダ名（path/FreonCheckImageFile/foldName）
  Future<Uint8List?> getImageFromSandbox({
    required String imageName,
    required String foldName,
  }) async {
    try {
      final freonCheckFilePath = await _getFreonCheckFilePath();

      /// fileのPathを生成
      final fullFilePath = '$freonCheckFilePath/$foldName/$imageName';
      final imageFile = File(fullFilePath);

      if (imageFile.existsSync()) {
        /// 写真情報を取得
        return await File(fullFilePath).readAsBytes();
      }

      return null;
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(_errorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// sandboxに保存してる写真を削除
  /// foldName:子フォルダ名（path/FreonCheckImageFile/foldName）
  Future<void> deleteImageFromLocalSandbox({
    required String imageName,
    required String foldName,
  }) async {
    try {
      final freonCheckFilePath = await _getFreonCheckFilePath();
      final freonCheckDirectory = Directory(freonCheckFilePath);
      final fullFilePath = '${freonCheckDirectory.path}/$foldName/$imageName';

      /// 写真を保存
      final file = File(fullFilePath);

      if (file.existsSync()) {
        await file.delete();
      }
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(_errorMessage),
        },
        StackTrace.current,
      );
    }
  }

  /// 写真情報を消します。
  Future<void> deleteFolderFromLocalSandbox() async {
    try {
      final freonCheckFilePath = await _getFreonCheckFilePath();
      final freonCheckDirectory = Directory(freonCheckFilePath);

      final folds = freonCheckDirectory.listSync();

      for (final entity in folds) {
        if (entity is Directory) {
          await entity.delete(recursive: true);
        }
      }
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException(_errorMessage),
        },
        StackTrace.current,
      );
    }
  }
}
