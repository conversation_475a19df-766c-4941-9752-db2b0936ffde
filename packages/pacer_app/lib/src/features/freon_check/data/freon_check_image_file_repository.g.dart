// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freon_check_image_file_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freonCheckImageFileRepositoryHash() => r'83abae0c42ee9d15d94da8e81edbf5a9295c4ad2';

/// 写真保存機能
///
/// Copied from [freonCheckImageFileRepository].
@ProviderFor(freonCheckImageFileRepository)
final freonCheckImageFileRepositoryProvider = Provider<FreonCheckImageFileRepository>.internal(
  freonCheckImageFileRepository,
  name: r'freonCheckImageFileRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freonCheckImageFileRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FreonCheckImageFileRepositoryRef = ProviderRef<FreonCheckImageFileRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
