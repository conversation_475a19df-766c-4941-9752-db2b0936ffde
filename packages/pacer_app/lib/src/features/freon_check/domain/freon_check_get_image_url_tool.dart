/// tableの写真項目からimage情報（name and path）を取得する
/// 例：equi11.jpg,http://172.21.17.10/TcloudFiles/Files/common/UserFreonCheckImages/equi11.jpg
(String?, String?) getImageNamePathFromDBTableData({
  required String dbImageUrlString,
}) {
  if (dbImageUrlString.isEmpty) {
    return (null, null);
  }

  final parts = dbImageUrlString.split(',');

  if (parts.length < 2) {
    return (null, null);
  }

  final imageName = parts[0];

  final imageUrl = parts[1];

  if (imageUrl.isEmpty) {
    return (imageName, imageName);
  }

  final urlParts = imageUrl.split('Files/');
  if (urlParts.length < 2) {
    return (imageName, imageName);
  }
  return (imageName, urlParts.last);
}
