import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

import '../../../exceptions/app_exception.dart';

/// 作業進捗情報
class FreonWorkProgress extends Equatable {
  /// 標準コンストラクタ
  const FreonWorkProgress({
    required this.periodCode,
    required this.storeCode,
    required this.progress,
    required this.placeCode,
  });

  /// Grpcの結果を構築します
  factory FreonWorkProgress.fromGrpc(
    GetProgressResponse_Progress info, {
    required int periodCode,
    required String storeCode,
  }) {
    final storeCodeInt = int.tryParse(storeCode);
    if (storeCodeInt == null) {
      throw UnknownException('店舗が存在しません');
    }

    return FreonWorkProgress(
      periodCode: periodCode,
      storeCode: storeCodeInt,
      placeCode: info.placeCode,
      progress: info.progress,
    );
  }

  /// 期間番号
  final int periodCode;

  /// 店舗番号
  final int storeCode;

  /// 場所番号
  final String placeCode;

  /// 作業進捗
  final String progress;

  @override
  List<Object?> get props => [
        placeCode,
        progress,
      ];

  /// DBにInsertするために、Mapに変換する関数
  Map<String, dynamic> toMap() {
    return {
      'periodcd': periodCode,
      'branchcd': storeCode,
      'placecd': placeCode,
      'progress': progress,
    };
  }
}
