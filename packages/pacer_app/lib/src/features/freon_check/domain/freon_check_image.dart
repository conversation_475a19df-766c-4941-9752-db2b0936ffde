import 'package:equatable/equatable.dart';

/// フロン点検写真
class FreonCheckImage extends Equatable {
  /// 標準コンストラクタ
  const FreonCheckImage({
    required this.imageName,
    required this.imagePath,
  });

  /// DBから検索結果で構築します
  /// image url stringでimagePath、imageNameを取得
  /// imageStringは
  /// http:10.100.2.118/TCloudFiles/Files/202307/19/202307190951125306880383.jpg
  /// あるいは　202307/19/202307190951125306880383.jpg
  factory FreonCheckImage.fromMap(String imageString) {
    final imageList = imageString.split('/');

    final urlParts = imageString.split('Files/');

    final imagePath = urlParts.length > 1 ? urlParts.last : imageString;

    return FreonCheckImage(
      imageName: switch (imageList.isEmpty) {
        true => imageString,
        false => imageList.last,
      },
      imagePath: imagePath,
    );
  }

  /// image url stringでimagePath、imageNameを取得
  /// http: //10.100.2.118/TCloudFiles/Files/202307/19/202307190951125306880383.jpg
  factory FreonCheckImage.fromImageUrl(String imageUrlString) {
    final parts = imageUrlString.split('/');
    if (parts.length < 2) {
      return const FreonCheckImage(imageName: '', imagePath: '');
    }
    final imageName = parts.last;

    final urlParts = imageUrlString.split('Files/');
    if (urlParts.length < 2) {
      return const FreonCheckImage(imageName: '', imagePath: '');
    }
    final imagePath = urlParts.last;

    return FreonCheckImage(imageName: imageName, imagePath: imagePath);
  }

  /// 写真名
  final String imageName;

  /// 写真Path
  final String imagePath;

  @override
  List<Object?> get props => [
        imageName,
        imagePath,
      ];

  /// copyWith
  FreonCheckImage copyWith({
    String? imageName,
    String? imagePath,
  }) {
    return FreonCheckImage(
      imageName: imageName ?? this.imageName,
      imagePath: imagePath ?? this.imagePath,
    );
  }
}
