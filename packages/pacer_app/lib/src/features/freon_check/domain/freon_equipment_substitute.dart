import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

import '../../../exceptions/app_exception.dart';

/// 店舗マスタデータ情報
class FreonEquipmentSubstitute extends Equatable {
  /// 標準コンストラクタ
  const FreonEquipmentSubstitute({
    required this.storeCode,
    required this.placeCode,
    required this.equipmentCode,
    this.isCanceled,
    this.createTime,
    this.createdBy,
    this.updateTime,
    this.updatedBy,
  });

  /// Grpcの結果を構築します
  factory FreonEquipmentSubstitute.fromGrpc(
    GetTableEquipmentSubResponse_TableEquipmentSub info,
  ) {
    final storeCodeInt = int.tryParse(info.storeCode);
    if (storeCodeInt == null) {
      throw UnknownException('店舗が存在しません');
    }
    return FreonEquipmentSubstitute(
      storeCode: storeCodeInt,
      placeCode: info.placeCode,
      equipmentCode: info.equipmentCode,
      isCanceled: info.isCanceled,
      createTime: info.createTime,
      createdBy: int.tryParse(info.creator),
      updateTime: info.modifyTime,
      updatedBy: int.tryParse(info.editor),
    );
  }

  /// 店舗番号
  final int storeCode;

  /// 場所番号
  final int placeCode;

  /// 設備番号
  final int equipmentCode;

  /// 取消フラグ
  final bool? isCanceled;

  /// 作成日時
  final String? createTime;

  /// 作成者
  final int? createdBy;

  /// 編集日時
  final String? updateTime;

  /// 編集者
  final int? updatedBy;

  @override
  List<Object?> get props => [
        storeCode,
        placeCode,
        equipmentCode,
        isCanceled,
        createTime,
        createdBy,
        updateTime,
        updatedBy,
      ];

  /// DBにInsertするために、Mapに変換する関数
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    map['branchcd'] = storeCode;
    map['placecd'] = placeCode;
    map['equicd'] = equipmentCode;

    map['hitflag'] = switch (isCanceled) {
      null => null,
      true => 1,
      false => 0,
    };

    map['createtime'] = createTime;
    map['createemoployee'] = createdBy;
    map['modifytime'] = updateTime;
    map['modifyemployee'] = updatedBy;

    return map;
  }
}
