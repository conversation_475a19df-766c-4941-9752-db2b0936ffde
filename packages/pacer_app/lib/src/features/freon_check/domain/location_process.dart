import 'package:equatable/equatable.dart';

/// フロン点検ホーム画面の場所情報(進捗、場所名、状態)
class LocationProcess extends Equatable {
  /// 標準コンストラクタ
  const LocationProcess({
    required this.placeCode,
    this.placeName,
    this.progress,
    this.placeState,
  });

  /// Grpcの結果を構築します
  factory LocationProcess.fromMap(
    Map<String, Object?> map,
  ) {
    final placeCd = map['placecd'];

    final name = map['placename'];

    final progress = map['progress'];

    final placeState = map['placestate'];
    return LocationProcess(
      placeCode: placeCd is int ? placeCd : 0,
      placeName: name is String ? name : '',
      progress: progress is String ? progress : '',
      placeState: placeState is String ? placeState : '',
    );
  }

  /// 場所code
  final int placeCode;

  /// 場所名
  final String? placeName;

  ///　進捗
  final String? progress;

  /// 状態
  final String? placeState;

  @override
  List<Object?> get props => [
        placeCode,
        placeName,
        progress,
        placeState,
      ];
}
