import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

import 'freon_check_register_state_detail.dart';

/// フロン点検設備詳細画面の設備情報
class EquipmentDetail extends Equatable {
  /// 標準コンストラクタ
  const EquipmentDetail({
    this.historyResultId,
    this.imagePath,
    this.imageName,
    this.equipmentState,
    this.equipmentNumber,
    this.createTimeString,
  });

  /// Grpcの結果を構築します
  factory EquipmentDetail.fromGrpc(
    GetCheckHistoryResponse_CheckHistory result,
  ) {
    final firstUrl = result.image.isEmpty ? null : result.image.first;

    /// 写真がない場合、[]のような文字列を返します。ゴミデータを削除する必要があります。
    final firstImageUrl = firstUrl?.replaceAll('[', '').replaceAll(']', '');

    final nameParts = firstImageUrl?.split('/');
    final imageName = switch (nameParts?.isNotEmpty) {
      true => nameParts?.last,
      _ => firstImageUrl,
    };
    final urlParts = firstImageUrl?.split('Files/');
    final imagePath = switch (urlParts?.isNotEmpty) {
      true => urlParts?.last,
      _ => firstImageUrl,
    };

    return EquipmentDetail(
      historyResultId: int.tryParse(result.resultId),
      imagePath: imagePath,
      imageName: imageName,
      equipmentNumber: result.equipNumber,
      createTimeString: result.createTime,
      equipmentState: result.stateName,
    );
  }

  /// DBから検索の結果を構築します
  factory EquipmentDetail.fromMap({
    Map<String, Object?>? map,
    List<FreonCheckRegisterStateDetail>? stateList,
  }) {
    final resultId = map?['resultid'];

    /// img:例 1719281738,1719281757,1719281767
    final img = map?['img'];
    final imageListString = img is String ? img : '';
    final imageList = imageListString.split(',');

    final firstImageUrl = imageList.isEmpty ? null : imageList.first;

    final nameParts = firstImageUrl?.split('/');
    final imageName = switch (nameParts?.isNotEmpty) {
      true => nameParts?.last,
      _ => firstImageUrl,
    };
    final urlParts = firstImageUrl?.split('Files/');
    final imagePath = switch (urlParts?.isNotEmpty) {
      true => urlParts?.last,
      _ => firstImageUrl,
    };

    final equipmentCode = map?['equistate'];
    final equipmentCodeString = equipmentCode is String ? equipmentCode : '';
    final stateCodeList = equipmentCodeString.split(',');

    final equipmentName = stateList
        ?.where(
          (element) => stateCodeList.contains(element.statusCode.toString()),
        )
        .map((element) => element.statusName)
        .toList()
        .join(',');

    final equipmentNumber = map?['equino'];
    final createTime = map?['createtime'];

    return EquipmentDetail(
      historyResultId: resultId is int ? resultId : null,
      imagePath: imagePath,
      imageName: imageName,
      equipmentState: equipmentName,
      equipmentNumber: equipmentNumber is String ? equipmentNumber : null,
      createTimeString: createTime is String ? createTime : null,
    );
  }

  /// 履歴結果ID
  final int? historyResultId;

  /// 写真Path
  final String? imagePath;

  /// 写真名
  final String? imageName;

  ///　設備state(name1,name2)
  final String? equipmentState;

  /// 設備Number
  final String? equipmentNumber;

  /// 追加日付け（登録日つけ）
  final String? createTimeString;

  @override
  List<Object?> get props => [
        historyResultId,
        imagePath,
        imageName,
        equipmentState,
        equipmentNumber,
        createTimeString,
      ];
}
