import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

/// 設備マスタdata
class FreonEquipment extends Equatable {
  /// 標準コンストラクタ
  const FreonEquipment({
    required this.equipmentCode,
    this.equipmentName,
    this.equipmentImageUrl,
    this.acceptableImageUrl,
    this.unacceptableImageUrl,
    this.remark,
    this.isCanceled,
    this.createTime,
    this.createdBy,
    this.updateTime,
    this.updatedBy,
    this.checkMemo,
    this.maxPhotoCount,
  });

  /// Grpcの結果を構築します
  factory FreonEquipment.fromGrpc(
    GetTableEquipmentResponse_TableEquipment info,
  ) =>
      FreonEquipment(
        equipmentCode: info.equipmentCode,
        equipmentName: info.equipmentName,
        equipmentImageUrl: '${info.equipmentImage.name},${info.equipmentImage.url}',
        acceptableImageUrl: '${info.equipmentImageO.name},${info.equipmentImageO.url}',
        unacceptableImageUrl: '${info.equipmentImageX.name},${info.equipmentImageX.url}',
        remark: info.remark,
        isCanceled: info.isCanceled,
        createTime: info.createTime,
        createdBy: int.tryParse(info.creator),
        updateTime: info.modifyTime,
        updatedBy: int.tryParse(info.editor),
        checkMemo: info.checkMemo,
        maxPhotoCount: info.maxPictureNumber,
      );

  /// 設備番号
  final int equipmentCode;

  /// 設備名
  final String? equipmentName;

  /// 設備画像のURL
  final String? equipmentImageUrl;

  /// 設備点検時、許容できる状態の写真URL（○写真）
  final String? acceptableImageUrl;

  /// 設備点検時、許容できない状態の写真URL（☓写真）
  final String? unacceptableImageUrl;

  /// 備考
  final String? remark;

  /// 取消フラグ
  final bool? isCanceled;

  /// 作成日時
  final String? createTime;

  /// 作成者
  final int? createdBy;

  /// 編集日時
  final String? updateTime;

  /// 編集者
  final int? updatedBy;

  /// 点検メモ
  final String? checkMemo;

  /// 最大写真枚数
  final int? maxPhotoCount;

  @override
  List<Object?> get props => [
        equipmentCode,
        equipmentName,
        equipmentImageUrl,
        acceptableImageUrl,
        unacceptableImageUrl,
        remark,
        isCanceled,
        createTime,
        createdBy,
        updateTime,
        updatedBy,
        checkMemo,
        maxPhotoCount,
      ];

  /// DBにInsertするために、Mapに変換する関数
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    map['equicd'] = equipmentCode;

    map['equiname'] = equipmentName;

    map['equiimg'] = equipmentImageUrl;

    map['equiimgo'] = acceptableImageUrl;

    map['equiimgx'] = unacceptableImageUrl;

    map['remark'] = remark;

    map['hitflag'] = switch (isCanceled) {
      null => null,
      true => 1,
      false => 0,
    };
    map['createtime'] = createTime;

    map['createemoployee'] = createdBy;

    map['modifytime'] = updateTime;

    map['modifyemployee'] = updatedBy;

    map['checkmemo'] = checkMemo;

    map['maxpicnum'] = maxPhotoCount;
    return map;
  }
}
