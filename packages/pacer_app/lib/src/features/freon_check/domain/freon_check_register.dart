import 'package:equatable/equatable.dart';

import 'freon_check_register_state_detail.dart';
import 'location_equipment.dart';
import 'location_process.dart';

/// フロン点検設備登録画面初期化情報
class FreonCheckRegister extends Equatable {
  /// 標準コンストラクタ
  const FreonCheckRegister({
    this.placeCode,
    this.placeName,
    this.equipmentCode,
    this.equipmentName,
    this.registerStates,
    this.isUploaded,
    this.maxImagesCount = 4,
  });

  /// 登録state、場所、設備でFreonCheckRegisterを構築します
  factory FreonCheckRegister.fromMap({
    required LocationProcess locationProcess,
    required LocationEquipment locationEquipment,
    required List<FreonCheckRegisterStateDetail>? registerStates,
    bool? isUploaded,
    required int maxImagesCount,
  }) {
    return FreonCheckRegister(
      placeCode: locationProcess.placeCode,
      placeName: locationProcess.placeName,
      equipmentCode: locationEquipment.equipmentCode,
      equipmentName: locationEquipment.equipmentName,
      registerStates: registerStates,
      isUploaded: isUploaded,
      maxImagesCount: maxImagesCount,
    );
  }

  /// 場所コード
  final int? placeCode;

  /// 場所名
  final String? placeName;

  /// 設備コード
  final int? equipmentCode;

  /// 設備名
  final String? equipmentName;

  /// 状態リスト
  /// 例：
  /// code　Name
  /// 1	氷着
  /// 2	エラー
  /// 3	破損
  /// 4	異音
  final List<FreonCheckRegisterStateDetail>? registerStates;

  /// Uploadedフラグ：1：報告済み　0：未報告
  final bool? isUploaded;

  /// 写真最大数
  final int maxImagesCount;

  /// code listでname listを取得
  List<String>? listStatusName(List<String> statusCodes) {
    if (registerStates == null) return [];
    return registerStates
        ?.where(
          (element) => statusCodes.contains(element.statusCode.toString()),
        )
        .map((element) => element.statusName)
        .toList();
  }

  @override
  List<Object?> get props => [
        placeCode,
        placeName,
        equipmentCode,
        equipmentName,
        registerStates,
        isUploaded,
        maxImagesCount,
      ];
}
