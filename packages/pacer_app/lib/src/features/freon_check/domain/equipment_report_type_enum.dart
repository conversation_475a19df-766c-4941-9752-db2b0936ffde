///  設備場所レポートのEnum
enum EquipmentReportTypeEnum {
  /// 設備場所レポートは完了報告Type
  isFinishReport(
    boolValue: true,
    intValue: 2,
    message: '設備場所レポートは完了報告です。',
  ),

  /// 設備場所レポートは途中報告Type
  isIntermediateReport(
    boolValue: false,
    intValue: 1,
    message: '設備場所レポートは途中報告です。',
  );

  const EquipmentReportTypeEnum({
    required this.intValue,
    required this.boolValue,
    required this.message,
  });

  /// API向け、true:完了報告Type false:途中報告Type
  final bool boolValue;

  /// SQL向け、2:完了報告Type 1:途中報告Type
  final int intValue;

  /// 説明情報
  final String message;
}
