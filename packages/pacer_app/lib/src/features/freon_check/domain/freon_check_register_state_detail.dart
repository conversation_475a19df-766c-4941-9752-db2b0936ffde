import 'package:equatable/equatable.dart';

/// 登録状態
/// 例
/// 1	氷着
/// 2	エラー
/// 3	破損
/// 4	異音
class FreonCheckRegisterStateDetail extends Equatable {
  /// 標準コンストラクタ
  const FreonCheckRegisterStateDetail({
    required this.statusName,
    required this.statusCode,
    this.isSelected = false,
  });

  /// DBからMapを構築します
  factory FreonCheckRegisterStateDetail.fromMap(
    Map<String, Object?>? map,
  ) {
    final stateCode = map?['statecd'];

    final stateName = map?['statename'];

    return FreonCheckRegisterStateDetail(
      statusCode: stateCode is int ? stateCode : 0,
      statusName: stateName is String ? stateName : '',
    );
  }

  /// copy
  FreonCheckRegisterStateDetail copyWith({
    bool? isSelected,
  }) {
    return FreonCheckRegisterStateDetail(
      statusName: statusName,
      statusCode: statusCode,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  /// status name
  final String statusName;

  /// status code
  final int statusCode;

  /// is selected
  final bool isSelected;

  @override
  List<Object?> get props => [
        statusName,
        statusCode,
        isSelected,
      ];
}
