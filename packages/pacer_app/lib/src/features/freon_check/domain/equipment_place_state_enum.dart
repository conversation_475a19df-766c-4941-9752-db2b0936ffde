///  設備場所状況Enum
enum EquipmentPlaceStateEnum {
  /// 0：設備の場所が（済みではない）作業中まだ未着手
  isInProgress(value: 0, message: '設備の場所が（済みではない）作業中まだ未着手'),

  /// 1：設備の場所状態が済み
  isCompleted(value: 1, message: '設備の場所状態が済み'),

  /// 2：状態情報がない
  isNotExist(value: 2, message: '状態情報がない');

  const EquipmentPlaceStateEnum({
    required this.value,
    required this.message,
  });

  /// touch value
  final int value;

  /// 説明情報
  final String message;
}
