import 'package:equatable/equatable.dart';

import 'freon_check_image.dart';

/// 設備情報問題の写真を登録Job
class EquipmentRegisterImageJob extends Equatable {
  /// 標準コンストラクタ
  const EquipmentRegisterImageJob({
    required this.readOnly,
    required this.registerImages,
    this.isUploadedImage = false,
  });

  /// 編集できるフラグ
  final bool readOnly;

  /// アップロードした写真のフラグ
  final bool isUploadedImage;

  /// 写真リスト
  final List<FreonCheckImage> registerImages;

  @override
  List<Object?> get props => [
        readOnly,
        isUploadedImage,
        registerImages,
      ];

  /// copyWith
  EquipmentRegisterImageJob copyWith({
    bool? readOnly,
    bool? isUploadedImage,
    List<FreonCheckImage>? imagePaths,
  }) {
    return EquipmentRegisterImageJob(
      readOnly: readOnly ?? this.readOnly,
      isUploadedImage: isUploadedImage ?? this.isUploadedImage,
      registerImages: imagePaths ?? registerImages,
    );
  }
}
