import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

import '../../../exceptions/app_exception.dart';

/// 場所情報
class FreonPlaceSubstitute extends Equatable {
  /// 標準コンストラクタ
  const FreonPlaceSubstitute({
    required this.storeCode,
    required this.placeCode,
    this.placeName,
    this.isCanceled,
    this.createTime,
    this.createdBy,
    this.updateTime,
    this.updatedBy,
  });

  /// Grpcの結果を構築します
  factory FreonPlaceSubstitute.fromGrpc(
    GetTablePlaceSubResponse_TablePlaceSub info,
  ) {
    final storeCodeInt = int.tryParse(info.storeCode);
    if (storeCodeInt == null) {
      throw UnknownException('店舗が存在しません');
    }

    return FreonPlaceSubstitute(
      storeCode: storeCodeInt,
      placeCode: info.placeCode,
      placeName: info.placeName,
      isCanceled: info.isCanceled,
      createTime: info.createTime,
      createdBy: int.tryParse(info.creator),
      updateTime: info.modifyTime,
      updatedBy: int.tryParse(info.editor),
    );
  }

  /// 店舗番号
  final int storeCode;

  /// 場所番号
  final int placeCode;

  /// 場所名
  final String? placeName;

  /// 取消フラグ
  final bool? isCanceled;

  /// 作成日時
  final String? createTime;

  /// 作成者
  final int? createdBy;

  /// 編集日時
  final String? updateTime;

  /// 編集者
  final int? updatedBy;
  @override
  List<Object?> get props => [
        storeCode,
        placeCode,
        placeName,
        isCanceled,
        createTime,
        createdBy,
        updateTime,
        updatedBy,
      ];

  /// DBにInsertするために、Mapに変換する関数
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    map['branchcd'] = storeCode;
    map['placecd'] = placeCode;
    map['placename'] = placeName;

    map['hitflag'] = switch (isCanceled) {
      null => null,
      true => 1,
      false => 0,
    };

    map['createtime'] = createTime;
    map['createemoployee'] = createdBy;
    map['modifytime'] = updateTime;
    map['modifyemployee'] = updatedBy;

    return map;
  }
}
