import 'package:equatable/equatable.dart';

import 'freon_check_get_image_url_tool.dart';

///  設備点検状態（問題あり　or　問題なし）状況Enum
enum EquipmentCheckStatus {
  /// 0：未着手
  notStarted(value: 0, message: '未着手'),

  /// 1：問題あり
  problem(value: 1, message: '問題あり'),

  /// 2：問題なし
  noProblem(value: 2, message: '問題なし');

  const EquipmentCheckStatus({
    required this.value,
    required this.message,
  });

  /// touch value
  final int value;

  /// 説明情報
  final String message;
}

/// 場所の設備情報
class LocationEquipment extends Equatable {
  /// 標準コンストラクタ
  const LocationEquipment({
    required this.equipmentCode,
    required this.equipmentImagePath,
    required this.equipmentImageName,
    this.equipmentName,
    this.questionCount,
    this.equipmentCheckStatus = EquipmentCheckStatus.notStarted,
  });

  /// Grpcの結果を構築します
  factory LocationEquipment.fromMap(
    Map<String, Object?> map,
  ) {
    final equipmentCode = map['equicd'];

    final equipmentImageString = map['equiimg'];
    final dbUrlString = equipmentImageString is String ? equipmentImageString : '';

    final (equipmentImageName, equipmentImagePath) = getImageNamePathFromDBTableData(dbImageUrlString: dbUrlString);

    final equipmentName = map['equiname'];

    final checkCount = map['checkCount'];
    final checkCountInt = checkCount is int ? checkCount : null;

    final questionCount = map['questionCount'];
    final questionCountInt = questionCount is int ? questionCount : null;

    final equipmentProblematicEnum = switch (checkCountInt) {
      0 => EquipmentCheckStatus.notStarted,
      _ when questionCountInt == 0 => EquipmentCheckStatus.noProblem,
      _ => EquipmentCheckStatus.problem,
    };

    return LocationEquipment(
      equipmentCode: equipmentCode is int ? equipmentCode : 0,
      equipmentImagePath: equipmentImagePath ?? '',
      equipmentImageName: equipmentImageName ?? '',
      equipmentName: equipmentName is String ? equipmentName : '',
      questionCount: questionCountInt,
      equipmentCheckStatus: equipmentProblematicEnum,
    );
  }

  /// 設備code
  final int equipmentCode;

  /// 設備イメージのurlのPath
  final String equipmentImagePath;

  /// 設備イメージの名
  final String equipmentImageName;

  ///　設備名
  final String? equipmentName;

  /// 点検問題件数（問題ありの点検）
  final int? questionCount;

  /// 点検状態（問題あり　or　問題なし）
  final EquipmentCheckStatus equipmentCheckStatus;

  String get questionCountString => questionCount != null ? questionCount.toString() : '';
  @override
  List<Object?> get props => [
        equipmentCode,
        equipmentImagePath,
        equipmentImageName,
        equipmentName,
        questionCount,
        equipmentCheckStatus,
      ];
}
