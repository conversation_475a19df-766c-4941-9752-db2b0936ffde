import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

/// 設備状態マスタ情報
class FreonEquipmentState extends Equatable {
  /// 標準コンストラクタ
  const FreonEquipmentState({
    required this.equipmentCode,
    required this.stateCode,
    this.stateName,
    this.isCanceled,
    this.createTime,
    this.createdBy,
    this.updateTime,
    this.updatedBy,
  });

  /// Grpcの結果を構築します
  factory FreonEquipmentState.fromGrpc(
    GetTableEquipmentStateResponse_TableEquipmentState info,
  ) =>
      FreonEquipmentState(
        equipmentCode: info.equipmentCode,
        stateCode: info.stateCode,
        stateName: info.stateName,
        isCanceled: info.isCanceled,
        createTime: info.createTime,
        createdBy: int.tryParse(info.creator),
        updateTime: info.modifyTime,
        updatedBy: int.tryParse(info.editor),
      );

  /// 設備番号
  final int equipmentCode;

  /// 状態番号
  final int stateCode;

  /// 状態名
  final String? stateName;

  /// 取消フラグ
  final bool? isCanceled;

  /// 作成日時
  final String? createTime;

  /// 作成者
  final int? createdBy;

  /// 編集日時
  final String? updateTime;

  /// 編集者
  final int? updatedBy;

  @override
  List<Object?> get props => [
        equipmentCode,
        stateCode,
        stateName,
        isCanceled,
        createTime,
        createdBy,
        updateTime,
        updatedBy,
      ];

  /// DBにInsertするために、Mapに変換する関数
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    map['equicd'] = equipmentCode;

    map['statecd'] = stateCode;

    map['statename'] = stateName;

    map['hitflag'] = switch (isCanceled) {
      null => null,
      true => 1,
      false => 0,
    };

    map['createtime'] = createTime;

    map['createemoployee'] = createdBy;

    map['modifytime'] = updateTime;

    map['modifyemployee'] = updatedBy;

    return map;
  }
}
