import 'package:equatable/equatable.dart';

import 'freon_check_get_image_url_tool.dart';

/// フロン点検設備詳細画面の設備問題選択情報
class EquipmentImageDetail extends Equatable {
  /// 標準コンストラクタ
  const EquipmentImageDetail({
    this.acceptableImagePath,
    this.acceptableImageName,
    this.unacceptableImagePath,
    this.unacceptableImageName,
    this.remark,
  });

  /// Grpcの結果を構築します
  factory EquipmentImageDetail.fromMap(Map<String, Object?> imageMap) {
    final acceptableImage = imageMap['equiimgo'];

    final unacceptableImage = imageMap['equiimgx'];

    final remark = imageMap['remark'];

    final (acceptableImageName, acceptableImagePath) = getImageNamePathFromDBTableData(
      dbImageUrlString: acceptableImage is String ? acceptableImage : '',
    );

    final (unAcceptableImageName, unAcceptableImagePath) = getImageNamePathFromDBTableData(
      dbImageUrlString: unacceptableImage is String ? unacceptableImage : '',
    );
    return EquipmentImageDetail(
      acceptableImagePath: acceptableImagePath,
      acceptableImageName: acceptableImageName,
      unacceptableImageName: unAcceptableImageName,
      unacceptableImagePath: unAcceptableImagePath,
      remark: remark is String ? remark : null,
    );
  }

  /// 設備点検時、許容できる状態の写真URL（○写真）
  final String? acceptableImagePath;

  /// 設備点検時、許容できる状態の写真名　(○写真）
  final String? acceptableImageName;

  /// 設備点検時、許容できない状態の写真URL（☓写真）
  final String? unacceptableImagePath;

  /// 設備点検時、許容できない状態の写真U名（☓写真）
  final String? unacceptableImageName;

  /// 備考
  final String? remark;
  @override
  List<Object?> get props => [
        acceptableImagePath,
        acceptableImageName,
        unacceptableImagePath,
        unacceptableImageName,
        remark,
      ];
}
