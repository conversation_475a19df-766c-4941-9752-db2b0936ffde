import 'package:clock/clock.dart';
import 'package:equatable/equatable.dart';

/// 設備情報問題を登録Job
class EquipmentRegisterJob extends Equatable {
  /// 標準コンストラクタ
  const EquipmentRegisterJob({
    required this.historyResultId,
    required this.periodCode,
    this.storeCode,
    required this.placeCode,
    required this.equipmentCode,
    this.equipmentState,
    this.equipmentNumber,
    this.errorCode,
    this.comment,
    this.imageUrls,
    this.isCanceled,
    this.isUploaded,
    this.createEmployee,
    this.modifyEmployee,
  });

  /// result tableにInsertするために、問題を登録のMapに変換する関数
  Map<String, dynamic> toRegisterResultMap() {
    final map = <String, dynamic>{};
    final currentTime = clock.now().toIso8601String();
    map['resultid'] = historyResultId;
    map['periodcd'] = periodCode;
    map['branchcd'] = storeCode;
    map['placecd'] = placeCode;
    map['equicd'] = equipmentCode;
    map['equistate'] = equipmentState;
    map['equino'] = equipmentNumber;
    map['errorcode'] = errorCode;
    map['comment'] = comment;

    map['imgs'] = imageUrls?.join(',');
    map['isuploaded'] = switch (isUploaded) {
      null => null,
      true => 1,
      false => 0,
    };
    map['hitflag'] = switch (isCanceled) {
      null => null,
      true => 1,
      false => 0,
    };
    map['createtime'] = currentTime;
    map['createemoployee'] = createEmployee;
    map['modifytime'] = currentTime;
    map['modifyemployee'] = modifyEmployee;
    return map;
  }

  /// image tableにInsertするために、問題を登録のMapに変換する関数
  List<Map<String, dynamic>>? toRegisterImageMapList() {
    return imageUrls
        ?.map((imageUrl) {
          final map = <String, dynamic>{};
          final currentTime = clock.now().toIso8601String();
          map['resultid'] = historyResultId;
          map['img'] = imageUrl;
          map['createtime'] = currentTime;
          map['createemoployee'] = createEmployee;
          map['modifytime'] = currentTime;
          map['modifyemployee'] = modifyEmployee;
          return map;
        })
        .nonNulls
        .toList();
  }

  /// 履歴番号
  final int historyResultId;

  /// 期間番号
  final int periodCode;

  /// 店舗番号
  final String? storeCode;

  /// 場所番号
  final int placeCode;

  /// 設備番号
  final int equipmentCode;

  /// 設備状態
  final String? equipmentState;

  /// 設備No
  final String? equipmentNumber;

  /// エラーコード
  final String? errorCode;

  /// 備考
  final String? comment;

  /// 添付写真
  final List<String>? imageUrls;

  ///取消フラグ 0:取消なし 1：取消
  final bool? isCanceled;

  ///Uploadedフラグ insertするときは0　else　１
  final bool? isUploaded;

  /// 作成者
  final String? createEmployee;

  ///編集者
  final String? modifyEmployee;
  @override
  List<Object?> get props => [
        historyResultId,
        periodCode,
        storeCode,
        placeCode,
        equipmentCode,
        equipmentState,
        equipmentNumber,
        errorCode,
        comment,
        imageUrls,
        isCanceled,
        isUploaded,
        createEmployee,
        modifyEmployee,
      ];

  /// copyWith
  EquipmentRegisterJob copyWith({
    int? historyResultId,
    int? periodCode,
    String? storeCode,
    int? placeCode,
    int? equipmentCode,
    String? equipmentState,
    String? equipmentNumber,
    String? errorCode,
    String? comment,
    List<String>? imageUrls,
    bool? isCanceled,
    bool? isUploaded,
    String? createEmployee,
    String? modifyEmployee,
  }) {
    return EquipmentRegisterJob(
      historyResultId: historyResultId ?? this.historyResultId,
      periodCode: periodCode ?? this.periodCode,
      storeCode: storeCode ?? this.storeCode,
      placeCode: placeCode ?? this.placeCode,
      equipmentCode: equipmentCode ?? this.equipmentCode,
      equipmentState: equipmentState ?? this.equipmentState,
      equipmentNumber: equipmentNumber ?? this.equipmentNumber,
      errorCode: errorCode ?? this.errorCode,
      comment: comment ?? this.comment,
      imageUrls: imageUrls ?? this.imageUrls,
      isCanceled: isCanceled ?? this.isCanceled,
      isUploaded: isUploaded ?? this.isUploaded,
      createEmployee: createEmployee ?? this.createEmployee,
      modifyEmployee: modifyEmployee ?? this.modifyEmployee,
    );
  }
}
