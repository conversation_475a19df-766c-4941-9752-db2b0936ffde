import 'package:equatable/equatable.dart';

/// image info
class LocationEquipmentImageJob extends Equatable {
  /// 標準コンストラクタ
  const LocationEquipmentImageJob({
    required this.historyResultId,
    required this.imagePath,
    required this.imageName,
  });

  /// Mapの結果を構築します
  factory LocationEquipmentImageJob.fromMap(
    Map<String, Object?> map,
  ) {
    final historyResultId = map['resultid'];
    final imageString = map['img'];

    final imagePath = switch (imageString) {
      final String value => value,
      _ => '',
    };

    final imageList = imagePath.split('/');
    final imageName = switch (imageList.isEmpty) {
      true => imagePath,
      false => imageList.last,
    };

    return LocationEquipmentImageJob(
      historyResultId: historyResultId is int ? historyResultId : 0,
      imagePath: imageString is String ? imageString : '',
      imageName: imageName,
    );
  }

  /// 結果Id
  final int historyResultId;

  /// imageのPath：path/CAP992792305063841037.jpg
  final String imagePath;

  /// imageのname：CAP992792305063841037.jpg
  final String imageName;
  @override
  List<Object?> get props => [
        historyResultId,
        imagePath,
        imageName,
      ];
}
