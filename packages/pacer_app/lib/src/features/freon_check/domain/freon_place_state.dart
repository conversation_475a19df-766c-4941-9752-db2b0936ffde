import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

import '../../../exceptions/app_exception.dart';
import 'location_status.dart';

/// 場所状態マスタdata
class FreonPlaceState extends Equatable {
  /// 標準コンストラクタ
  const FreonPlaceState({
    required this.periodCode,
    required this.storeCode,
    required this.locationCode,
    this.locationStatus,
    this.isCanceled,
    this.createTime,
    this.createdBy,
    this.updateTime,
    this.updatedBy,
  });

  /// Grpcの結果を構築します
  factory FreonPlaceState.fromGrpc(
    GetTablePlaceStateResponse_TablePlaceState info,
  ) {
    final storeCodeInt = int.tryParse(info.storeCode);
    if (storeCodeInt == null) {
      throw UnknownException('店舗が存在しません');
    }
    return FreonPlaceState(
      periodCode: info.periodCode,
      storeCode: storeCodeInt,
      locationCode: info.placeCode,
      locationStatus: LocationStatus.fromValue(info.placeState.value),
      isCanceled: info.isCanceled,
      createTime: info.createTime,
      createdBy: int.tryParse(info.creator),
      updateTime: info.modifyTime,
      updatedBy: int.tryParse(info.editor),
    );
  }

  /// 期間番号
  final int periodCode;

  /// 店舗番号
  final int storeCode;

  /// 場所番号
  final int locationCode;

  /// 場所状態
  final LocationStatus? locationStatus;

  /// 取消フラグ
  final bool? isCanceled;

  /// 作成日時
  final String? createTime;

  /// 作成者
  final int? createdBy;

  /// 編集日時
  final String? updateTime;

  /// 編集者
  final int? updatedBy;
  @override
  List<Object?> get props => [
        periodCode,
        storeCode,
        locationCode,
        locationStatus,
        isCanceled,
        createTime,
        createdBy,
        updateTime,
        updatedBy,
      ];

  /// DBにInsertするために、Mapに変換する関数
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    map['periodcd'] = periodCode;
    map['branchcd'] = storeCode;
    map['placecd'] = locationCode;
    map['placestate'] = locationStatus?.value;

    map['hitflag'] = switch (isCanceled) {
      null => null,
      true => 1,
      false => 0,
    };

    map['createtime'] = createTime;

    map['createemoployee'] = createdBy;

    map['modifytime'] = updateTime;

    map['modifyemployee'] = updatedBy;

    return map;
  }
}
