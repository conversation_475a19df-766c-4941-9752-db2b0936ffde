import 'package:equatable/equatable.dart';

import 'freon_check_image.dart';

/// 最新取得Model
class RefreshResult extends Equatable {
  /// 標準コンストラクタ
  const RefreshResult({
    required this.sqlList,
    required this.freonCheckImageList,
  });

  /// Grpcの結果(String)から最新取得Modelを構築します
  factory RefreshResult.fromString(
    /// SQL文１(空可能)　＋　；　＋　SQL文２(空可能)　＋　；　＋　複数Imageの情報(空可能)
    /// 写真情報Stringフォマット:文字列にはある文字列とある結合された配列文字列があります
    /// image+,+[image1,image2]+,image3+,+[image4,image5]
    /// 例：http:path/image1.jpg,[http:path/image2.jpg,http:path/image3.jpg]
    /// ,[http:path/image4.jpg,http:path/image5.jpg],http:path/image6.jpg
    String dataString,
  ) {
    final list = dataString.split(';');
    if (list.isEmpty) {
      return const RefreshResult(
        sqlList: [],
        freonCheckImageList: [],
      );
    }

    final imageString = list.last;
    final urlList =
        imageString.replaceAll('[', '').replaceAll(']', '').split(',').where((item) => item.isNotEmpty).toList();

    list.removeLast();
    final sqlList = list.where((item) => item.isNotEmpty).toList();

    return RefreshResult(
      sqlList: sqlList,
      freonCheckImageList: urlList.map(FreonCheckImage.fromImageUrl).toList(),
    );
  }

  /// SQL文のStringリスト
  final List<String> sqlList;

  /// 添付写真URLのリスト
  final List<FreonCheckImage> freonCheckImageList;

  @override
  List<Object?> get props => [
        sqlList,
        freonCheckImageList,
      ];
}
