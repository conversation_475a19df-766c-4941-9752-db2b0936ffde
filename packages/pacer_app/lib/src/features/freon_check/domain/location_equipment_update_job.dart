import 'package:clock/clock.dart';
import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

/// 場所の設備情報:(途中報告、完了報告)Job
class LocationEquipmentUpdateJob extends Equatable {
  /// 標準コンストラクタ

  const LocationEquipmentUpdateJob({
    required this.historyResultId,
    required this.periodCode,
    required this.storeCode,
    required this.placeCode,
    required this.equipmentCode,
    required this.equipmentState,
    required this.equipmentNumber,
    required this.errorCode,
    required this.comment,
    required this.imageUrls,
    required this.isCanceled,
    required this.createTime,
    required this.createemoployee,
    required this.modifyTime,
    required this.modifyEmployee,
  });

  /// Grpcの結果を構築します
  factory LocationEquipmentUpdateJob.fromMap(
    Map<String, Object?> map,
  ) {
    final historyResultId = map['resultid'];
    final periodcd = map['periodcd'];
    final branchcd = map['branchcd'];
    final placecd = map['placecd'];
    final equicd = map['equicd'];
    final equistate = map['equistate'];
    final equino = map['equino'];
    final errorcode = map['errorcode'];
    final comment = map['comment'];
    final imagesString = map['imgs'];
    final images = imagesString is String ? imagesString.split(',') : const <String>[];
    final hitflag = map['hitflag'];
    final createtime = map['createtime'];
    final createemoployee = map['createemoployee'];
    final modifytime = map['modifytime'];
    final modifyemployee = map['modifyemployee'];
    return LocationEquipmentUpdateJob(
      historyResultId: historyResultId is int ? historyResultId.toString() : '',
      periodCode: periodcd is int ? periodcd : 0,
      storeCode: branchcd is int ? branchcd.toString() : '',
      placeCode: placecd is int ? placecd : 0,
      equipmentCode: equicd is int ? equicd : 0,
      equipmentState: equistate is String ? equistate : '',
      equipmentNumber: equino is String ? equino : '',
      errorCode: errorcode is String ? errorcode : '',
      comment: comment is String ? comment : '',
      imageUrls: images,
      isCanceled: hitflag is bool && hitflag,
      createTime: createtime is String ? createtime : '',
      createemoployee: createemoployee is int ? createemoployee.toString() : '',
      modifyTime: modifytime is String ? modifytime : '',
      modifyEmployee: modifyemployee is int ? modifyemployee.toString() : '',
    );
  }

  /// SetEquilistRequest_Equilistに
  SetEquilistRequest_Equilist toEquipment() {
    final currentDate = clock.now();
    return SetEquilistRequest_Equilist(
      resultId: historyResultId,
      periodCode: periodCode,
      storeCode: storeCode,
      placeCode: placeCode,
      equipmentCode: equipmentCode,
      equipmentState: equipmentState,
      equipmentNumber: equipmentNumber,
      errorCode: errorCode,
      comment: comment,
      images: imageUrls,
      isCanceled: isCanceled,
      createTime: DateTime(
        year: currentDate.year,
        month: currentDate.month,
        day: currentDate.day,
        hour: currentDate.hour,
        minute: currentDate.minute,
        second: currentDate.second,
      ),
      creator: createemoployee,
      modifyTime: DateTime(
        year: currentDate.year,
        month: currentDate.month,
        day: currentDate.day,
        hour: currentDate.hour,
        minute: currentDate.minute,
        second: currentDate.second,
      ),
      editor: modifyEmployee,
    );
  }

  /// 履歴番号
  final String historyResultId;

  /// 期間番号
  final int periodCode;

  /// 店舗番号
  final String storeCode;

  /// 場所番号
  final int placeCode;

  /// 設備番号
  final int equipmentCode;

  /// 設備状態
  final String equipmentState;

  /// 設備No
  final String equipmentNumber;

  /// エラーコード
  final String errorCode;

  /// 備考
  final String comment;

  /// 添付写真:table のimgsの保存例：1718866750,1718866757
  final List<String> imageUrls;

  ///取消フラグ 0:取消なし 1：取消
  final bool isCanceled;

  /// 作成日時
  final String createTime;

  /// 作成者
  final String createemoployee;

  /// 編集日時
  final String modifyTime;

  ///編集者
  final String modifyEmployee;
  @override
  List<Object?> get props => [
        historyResultId,
        periodCode,
        storeCode,
        placeCode,
        equipmentCode,
        equipmentState,
        equipmentNumber,
        errorCode,
        comment,
        imageUrls,
        isCanceled,
        createTime,
        createemoployee,
        modifyTime,
        modifyEmployee,
      ];
}
