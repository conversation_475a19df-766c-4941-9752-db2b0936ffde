import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

/// 過去の履歴
class History extends Equatable {
  /// 標準コンストラクタ
  const History({
    required this.resultId,
    required this.imageList,
    required this.stateName,
    required this.equipNumber,
    required this.createTime,
  });

  /// Grpcの結果を構築します
  factory History.fromGrpc(
    GetCheckHistoryResponse_CheckHistory info,
  ) =>
      History(
        resultId: info.resultId,
        imageList: info.image,
        stateName: info.stateName,
        equipNumber: info.equipNumber,
        createTime: info.createTime,
      );

  /// 履歴番号
  final String resultId;

  /// 添付写真
  final List<String> imageList;

  /// 状態名称
  final String stateName;

  /// 設備No
  final String equipNumber;

  /// 作成日時
  final String createTime;

  @override
  List<Object?> get props => [
        resultId,
        imageList,
        stateName,
        createTime,
        equipNumber,
      ];
}
