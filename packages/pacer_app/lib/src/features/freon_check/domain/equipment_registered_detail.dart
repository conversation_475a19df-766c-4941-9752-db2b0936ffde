import 'package:equatable/equatable.dart';

import 'freon_check_image.dart';

/// 登録してる設備問題情報
class EquipmentRegisteredDetail extends Equatable {
  /// 標準コンストラクタ
  const EquipmentRegisteredDetail({
    this.equipmentState = '',
    this.registerImages,
    this.equipmentNumber = '',
    this.errorCode = '',
    this.comment = '',
  });

  /// 登録state、場所、設備でEquipmentRegisteredDetailを構築します
  factory EquipmentRegisteredDetail.fromMap(Map<String, Object?> map) {
    final equipmentState = map['equistate'];
    final imagesString = switch (map['imgs']) { final String value => value, _ => '' };

    final registerList = imagesString.split(',').map(FreonCheckImage.fromMap).toList();

    final equipmentNumber = map['equino'];
    final errorCode = map['errorcode'];
    final comment = map['comment'];

    return EquipmentRegisteredDetail(
      equipmentState: equipmentState is String ? equipmentState : '',
      registerImages: registerList,
      equipmentNumber: equipmentNumber is String ? equipmentNumber : '',
      errorCode: errorCode is String ? errorCode : '',
      comment: comment is String ? comment : '',
    );
  }

  /// 設備状態code情報：フォマットは(1,2)
  final String equipmentState;

  /// 写真リスト
  final List<FreonCheckImage>? registerImages;

  /// 設備No
  final String equipmentNumber;

  /// エラーコード
  final String errorCode;

  /// 備考
  final String comment;
  @override
  List<Object?> get props => [
        equipmentState,
        registerImages,
        equipmentNumber,
        errorCode,
        comment,
      ];

  /// 設備の状態コードリストを取得
  List<String> listEquipmentStateCodes() {
    return equipmentState.split(',');
  }
}
