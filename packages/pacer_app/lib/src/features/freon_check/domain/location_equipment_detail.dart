import 'package:equatable/equatable.dart';

import 'equipment_detail.dart';
import 'equipment_detail_state_enum.dart';
import 'equipment_image_detail.dart';
import 'equipment_place_state_enum.dart';

/// フロン点検設備詳細画面の情報
class LocationEquipmentDetail extends Equatable {
  /// 標準コンストラクタ
  const LocationEquipmentDetail({
    this.equipmentPlaceStateEnum = EquipmentPlaceStateEnum.isNotExist,
    this.equipmentDetailStateEnum = EquipmentDetailStateEnum.isNotExist,
    this.equipmentImageDetail,
    this.equipmentDetailList,
  });

  /// 該当期間情報
  final EquipmentPlaceStateEnum equipmentPlaceStateEnum;

  /// 画面詳細
  final EquipmentDetailStateEnum equipmentDetailStateEnum;

  /// 設備の写真例情報
  final EquipmentImageDetail? equipmentImageDetail;

  ///　設備詳細情報リスト
  final List<EquipmentDetail>? equipmentDetailList;

  @override
  List<Object?> get props => [
        equipmentPlaceStateEnum,
        equipmentDetailStateEnum,
        equipmentImageDetail,
        equipmentDetailList,
      ];
}
