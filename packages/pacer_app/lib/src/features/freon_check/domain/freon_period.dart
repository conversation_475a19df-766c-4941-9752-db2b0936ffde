import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/freon_check/v1/freon_check.pb.dart';

import '../../../exceptions/app_exception.dart';

/// 期間マスタdata
class FreonPeriod extends Equatable {
  /// 標準コンストラクタ
  const FreonPeriod({
    required this.periodCode,
    this.periodName,
    this.isCanceled,
    this.startDate,
    this.endDate,
    this.createTime,
    this.createdBy,
    this.updateTime,
    this.updatedBy,
  });

  /// Grpcの結果を構築します
  factory FreonPeriod.fromGrpc(
    GetTableEquipmentPeriodResponse_TablePeriod info,
  ) =>
      FreonPeriod(
        periodCode: info.periodCode,
        periodName: info.periodName,
        isCanceled: info.isCanceled,
        startDate: info.startDate,
        endDate: info.endDate,
        createTime: info.createTime,
        createdBy: int.tryParse(info.creator),
        updateTime: info.modifyTime,
        updatedBy: int.tryParse(info.editor),
      );

  /// Grpcの結果を構築します
  factory FreonPeriod.fromMap(
    Map<String, dynamic> freonPeriodMap,
  ) {
    final periodCode = freonPeriodMap['periodcd'];
    final periodName = freonPeriodMap['periodname'];
    if (periodCode is int && periodName is String) {
      return FreonPeriod(periodCode: periodCode, periodName: periodName);
    } else {
      throw UnknownException('期間情報が存在しません');
    }
  }

  /// 期間番号
  final int periodCode;

  /// 期間名
  final String? periodName;

  /// 取消フラグ
  final bool? isCanceled;

  /// 開始日
  final String? startDate;

  /// 終了日
  final String? endDate;

  /// 作成日時
  final String? createTime;

  /// 作成者
  final int? createdBy;

  /// 編集日時
  final String? updateTime;

  /// 編集者
  final int? updatedBy;
  @override
  List<Object?> get props => [
        periodCode,
        periodName,
        isCanceled,
        startDate,
        endDate,
        createTime,
        createdBy,
        updateTime,
        updatedBy,
      ];

  /// DBにInsertするために、Mapに変換する関数
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    map['periodcd'] = periodCode;
    map['periodname'] = periodName;

    map['hitflag'] = switch (isCanceled) {
      null => null,
      true => 1,
      false => 0,
    };

    map['startdate'] = startDate;
    map['enddate'] = endDate;
    map['createtime'] = createTime;
    map['createemoployee'] = createdBy;
    map['modifytime'] = updateTime;
    map['modifyemployee'] = updatedBy;

    return map;
  }
}
