// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:clock/clock.dart';
import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/authentication/v1/authentication.pb.dart';

import '../../../exceptions/app_exception.dart';
import '../../branch/domain/branch.dart';

/// 認証情報 アクセストークン
class Certificate with EquatableMixin {
  const Certificate(this.certificate, this.certificateKey);

  final String certificate;
  final String certificateKey;

  @override
  List<Object?> get props => [certificate, certificateKey];
}

class AppUser with EquatableMixin {
  const AppUser({
    required this.userCode,
    required this.userID,
    required this.name,
    required this.certificate,
    required this.privileges,
    required this.clockOutTime,
    required this.clockInStore,
    required this.storeInCharge,
  });

  /// 社員コード
  final String userCode;

  /// 社員ID
  final String userID;

  /// 社員名
  final String name;

  /// 認証情報
  final Certificate? certificate;

  /// 権限
  final List<String>? privileges;

  /// 自動ログアウト時間
  final DateTime clockOutTime;

  /// 入店した店舗
  final Branch clockInStore;

  /// 担当店舗
  final Branch storeInCharge;

  /// 退勤時間
  /// 自動ログアウトの時間の5分前
  DateTime get workOutTime => clockOutTime.subtract(const Duration(minutes: 5));

  /// 退勤までの時間
  Duration get timeToWorkOut => workOutTime.difference(clock.now());

  /// ログイン中の店舗が担当店舗と同じか
  bool get loggedInChargeStore => clockInStore.code == storeInCharge.code;

  @override
  List<Object?> get props => [
        userCode,
        name,
        certificate,
        privileges,
        userID,
        clockOutTime,
        clockInStore,
      ];

  factory AppUser.fromGrpc(
    LoginResponse res,
    String userCode,

    /// 稼働計画APIのレスポンスから計算した退勤時間
    DateTime clockOutTime,

    /// ログイン画面で選択中の店舗
    /// Store IDの情報は持っていない
    Branch clockInStore,
  ) {
    return switch (res) {
      LoginResponse(
        code: '000',
        sessionId: != '' && final certificate,
        key: != '' && final certificateKey,
        userId: != '' && final userID,
        storeId: != '' && final storeID,
        shopVisit: [
          LoginResponse_ShopVisit(
            userName: != '' && final name,
            :final shopCode,
            :final shopName,
          ),
          ...
        ]
      ) =>

        /// 成功
        AppUser(
          userCode: userCode,
          certificate: Certificate(certificate, certificateKey),
          privileges: null,
          userID: userID,
          name: name,
          clockOutTime: clockOutTime,

          /// APIから取得したStore IDで上書きする
          clockInStore: clockInStore.setId(storeID),
          storeInCharge: Branch.build(code: shopCode, name: shopName),
        ),

      /// 失敗
      LoginResponse(
        code: != '000',
        message: != '' && final message,
      ) =>
        throw ParseAuthFailure('ログインに失敗しました $message'),
      _ => throw ParseAuthFailure('ログインに失敗しました'),
    };
  }

  /// fakeのAppUser。ユーザーコードとストア情報を渡して初期化。
  /// ユーザー名、認証情報は固定値。
  /// 自動ログアウトは1日後。
  /// 権限、userIDは持っていない。
  factory AppUser.fake({required String userCode, required Branch store}) => AppUser(
        userCode: userCode,
        userID: '9999',
        privileges: null,
        name: 'stgユーザ',
        clockOutTime: clock.now().add(const Duration(days: 1)),
        clockInStore: store,
        certificate: const Certificate('n', 'n'),
        storeInCharge: store.copyWith(code: '担当店舗'),
      );

  AppUser updateClockOutTime(DateTime newClockOutTime) {
    return AppUser(
      userCode: userCode,
      certificate: certificate,
      privileges: privileges,
      userID: userID,
      name: name,
      clockOutTime: newClockOutTime,
      clockInStore: clockInStore,
      storeInCharge: storeInCharge,
    );
  }

  @override
  String toString() {
    return '''
ユーザー情報
userCode: $userCode, 
userID: $userID, 
name: $name,
clockOutTime: $clockOutTime,
clockInStore: $clockInStore, 
storeInCharge: $storeInCharge,
''';
  }
}
