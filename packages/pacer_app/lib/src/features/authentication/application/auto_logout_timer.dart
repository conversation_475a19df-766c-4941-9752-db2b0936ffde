import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../app_foundation/application/timer.dart';
import '../data/auth_repository.dart';

part 'auto_logout_timer.g.dart';

@riverpod
class TimeState extends _$TimeState {
  @override
  TimerStateEnum build() {
    final currentTime = ref.watch(everyOneMinutesTimerProvider);

    /// 1分ごとにログイン済みユーザーのログアウト時間と現在時刻を比較
    final logoutTime = ref.read(authRepositoryProvider).currentUser?.clockOutTime;
    if (logoutTime == null) return TimerStateEnum.other;

    if (currentTime.isAfter(logoutTime) || currentTime.isAtSameMomentAs(logoutTime)) {
      return TimerStateEnum.timeUp;
    }

    /// ログアウト時間と現在時刻の差分を計算
    final diffMinutes = logoutTime.difference(currentTime).inMinutes;

    return TimerStateEnum.fromInt(diffMinutes);
  }
}

enum TimerStateEnum {
  /// 15分前通知
  fifteenMinutesBefore,

  /// 3分前通知
  threeMinutesBefore,

  /// タイムアップ
  timeUp,

  /// どれでもない
  other;

  /// 数値からTimerStateを計算
  static TimerStateEnum fromInt(int remainMinute) {
    return switch (remainMinute) {
      15 => TimerStateEnum.fifteenMinutesBefore,
      3 => TimerStateEnum.threeMinutesBefore,
      0 => TimerStateEnum.timeUp,
      _ => TimerStateEnum.other,
    };
  }
}
