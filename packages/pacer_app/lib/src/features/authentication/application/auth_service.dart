// ignore_for_file: public_member_api_docs

import 'dart:async';
import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../branch/domain/branch.dart';
import '../../device/data/pacer_repository.dart';
import '../../pop/presentation/bundle_pop/bundle_pops_state.dart';
import '../../pop/presentation/fresh_pop/fresh_pops_state.dart';
import '../../pop/presentation/normal_pop/normal_pops_state.dart';
import '../../print_shelf_label/presentation/label_state.dart';
import '../data/auth_repository.dart';
import 'fake_workplan.dart';

part 'auth_service.g.dart';

@Riverpod(keepAlive: true)
AuthService authService(AuthServiceRef ref) {
  return AuthService(ref);
}

class AuthService {
  AuthService(this.ref);
  final Ref ref;

  final String _fakeIp = '*********';

  /// パスワードによるログイン
  Future<void> logInByPass({
    required String userCode,
    required String pass,
    required Branch? store,
  }) async {
    if (store == null) return;

    try {
      final ip = await NetworkInfo().getWifiIP();

      final device = ref.read(deviceInfoProvider).requireValue;

      final logOffTime = await _checkWorkPlan(userCode);

      await ref.read(authRepositoryProvider).logInByPass(
            password: pass,
            userCode: userCode,
            store: store,
            ip: ip ?? _fakeIp,
            device: device,
            logOffTime: logOffTime,
          );
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(e, stack);
    }
  }

  /// スキャンによるログイン
  Future<void> logInByScan(String userCode, Branch? store) async {
    try {
      if (store == null) return;
      final ip = await NetworkInfo().getWifiIP();

      final device = ref.read(deviceInfoProvider).requireValue;

      final logOffTime = await _checkWorkPlan(userCode);

      log('ユーザー $userCode スキャンログイン開始');

      await ref.read(authRepositoryProvider).logInByScan(
            userCode: userCode,
            store: store,
            ip: ip ?? _fakeIp,
            device: device,
            logOffTime: logOffTime,
          );
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(e, stack);
    }
  }

  /// ログアウト時に、ユーザーの一時保存データをクリアする
  Future<void> logOut() async {
    final storeCode = ref.read(authRepositoryProvider).currentUser?.clockInStore.code;
    if (storeCode == null) return;

    final device = ref.read(deviceInfoProvider).requireValue;

    final ip = await NetworkInfo().getWifiIP();

    /// POP保存データ
    ref.read(myLabelsProvider.notifier).clear();
    ref.read(myNormalJobsProvider.notifier).clear();
    ref.read(myBundleJobsProvider.notifier).clear();
    ref.read(myFreshJobsProvider.notifier).clear();
    ref.read(fakeWorkplanProvider.notifier).clear();

    await ref.read(authRepositoryProvider).logOut(
          storeCode: storeCode,
          device: device,
          ip: ip ?? '',
        );
  }

  /// 自動ログアウト時間をサーバーから取得する
  /// fakeが設定されている場合はサーバーに問い合わせずにそちらの時間を返す
  Future<DateTime> _checkWorkPlan(String userCode) async {
    /// デバッグ用の退勤時間がセットされていればそれを返す
    final debugWorkplan = ref.read(fakeWorkplanProvider);
    if (debugWorkplan case final plan?) {
      return plan;
    }

    final workplan = await ref.read(authRepositoryProvider).checkWorkPlan(userCode: userCode);

    return workplan;
  }

  /// 稼働時間が変更されているかを確認し、変更されていたらユーザーの稼働時間を更新する
  Future<void> checkWorkPlanUpdated(VoidCallback onUnUpdated) async {
    final caller = ref.watch(authRepositoryProvider).currentUser;
    if (caller == null) return;
    final currentWorkplan = caller.clockOutTime;

    final workplan = await _checkWorkPlan(caller.userCode);

    final diff = workplan.difference(currentWorkplan).inMinutes;

    if (diff > 1) {
      /// 1分以上離れていたら、[currentUser]のclockOutTimeを上書き
      ref.read(authRepositoryProvider).updateClockOutTime(workplan);
      return;
    }
    onUnUpdated();
  }
}
