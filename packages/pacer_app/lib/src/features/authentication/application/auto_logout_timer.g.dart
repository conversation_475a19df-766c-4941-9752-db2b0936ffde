// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auto_logout_timer.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$timeStateHash() => r'e59e7d5e68d67a6fbd4ab26be4c2d3fa0bcf92a5';

/// See also [TimeState].
@ProviderFor(TimeState)
final timeStateProvider = AutoDisposeNotifierProvider<TimeState, TimerStateEnum>.internal(
  TimeState.new,
  name: r'timeStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$timeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TimeState = AutoDisposeNotifier<TimerStateEnum>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
