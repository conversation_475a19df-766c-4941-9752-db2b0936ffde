import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'fake_workplan.g.dart';

/// テスト用。サーバー側の稼働計画を再現するためのクラス
/// ユーザーが持つclockOutTimeとは別
@Riverpod(keepAlive: true)
class FakeWorkplan extends _$FakeWorkplan {
  @override
  DateTime? build() {
    return null;
  }

  /// デバッグ用の退勤時間をセット
  void setWorkplanForDebug(DateTime workplan) {
    log('DebugWorkplan  $workplan');

    state = workplan;
  }

  void clear() {
    state = null;
  }
}
