// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fake_workplan.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fakeWorkplanHash() => r'2cb61994d316472b53aac81466268cff15796af1';

/// テスト用。サーバー側の稼働計画を再現するためのクラス
/// ユーザーが持つclockOutTimeとは別
///
/// Copied from [FakeWorkplan].
@ProviderFor(FakeWorkplan)
final fakeWorkplanProvider = NotifierProvider<FakeWorkplan, DateTime?>.internal(
  FakeWorkplan.new,
  name: r'fakeWorkplanProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fakeWorkplanHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FakeWorkplan = Notifier<DateTime?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
