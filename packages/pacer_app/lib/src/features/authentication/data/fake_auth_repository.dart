// ignore_for_file: public_member_api_docs

import 'package:device_info_plus/device_info_plus.dart';

import '../../../utils/in_memory_store.dart';
import '../../branch/domain/branch.dart';
import '../domain/app_user.dart';
import 'auth_repository.dart';

class FakeAuthRepository implements AuthRepository {
  final _authState = InMemoryStore<AppUser?>(null);

  @override
  Stream<AppUser?> authStateChanges() => _authState.stream;

  @override
  AppUser? get currentUser => _authState.value;

  void dispose() => _authState.close();

  @override
  Future<DateTime> checkWorkPlan({required String userCode}) {
    return Future.value(
      DateTime.now().add(const Duration(hours: 2)),
    );
  }

  @override
  void updateClockOutTime(DateTime time) {
    // TODO: implement updateClockOutTime
  }

  @override
  Future<void> logInByPass({
    required String userCode,
    required String password,
    required Branch store,
    required String ip,
    required BaseDeviceInfo device,
    required DateTime logOffTime,
  }) {
    // TODO: implement logInByPass
    throw UnimplementedError();
  }

  @override
  Future<void> logInByScan({
    required String userCode,
    required Branch store,
    required String ip,
    required BaseDeviceInfo device,
    required DateTime logOffTime,
  }) {
    // TODO: implement logInByScan
    throw UnimplementedError();
  }

  @override
  Future<void> logOut({required String storeCode, required BaseDeviceInfo device, required String ip}) {
    // TODO: implement logOut
    throw UnimplementedError();
  }
}
