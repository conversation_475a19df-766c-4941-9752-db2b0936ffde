/// Fake user class used to simulate a user account on the backend
/// * This class is implementation-specific and should only be used by the
/// * FakeAuthRepository, so it should not belong to the domain layer
// ignore_for_file: public_member_api_docs

library;

import 'package:clock/clock.dart';

import '../../branch/domain/branch.dart';
import '../domain/app_user.dart';

class FakeAppUser extends AppUser {
  const FakeAppUser({
    required this.password,
    required super.userCode,
    required super.userID,
    required super.name,
    required super.certificate,
    required super.privileges,
    required super.clockOutTime,
    required super.clockInStore,
    required super.storeInCharge,
  });

  factory FakeAppUser.build({
    String password = '',
    required String userCode,
    String? userID,
    String? name,
    Certificate? certificate,
    List<String>? privileges,
    DateTime? clockOutTime,
    Branch? clockInStore,
  }) {
    return FakeAppUser(
      password: password,
      userCode: userCode,
      userID: userID ?? '',
      name: name ?? '名前がないよう',
      certificate: certificate,
      privileges: privileges,
      clockOutTime: clockOutTime ?? clock.now(),
      clockInStore: clockInStore ?? Branch.fake(),
      storeInCharge: Branch.fake(),
    );
  }
  final String password;
}
