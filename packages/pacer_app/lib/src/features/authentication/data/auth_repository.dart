import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:clock/clock.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:get_phone_number/get_phone_number.dart';
import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/authentication/v1/v1.dart';
import 'package:shinise_core_client/clock/v1/v1.dart';
import 'package:shinise_core_client/device_management/v1/device_management.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../../utils/device_ip_interceptor.dart';
import '../../../utils/in_memory_store.dart';
import '../../branch/domain/branch.dart';
import '../../device/domain/extension.dart';
import '../domain/app_user.dart';

part 'auth_repository.g.dart';

/// autoDisposeProvider
@Riverpod(keepAlive: true)
AuthRepository authRepository(AuthRepositoryRef ref) => AuthRepository();

/// 認証関連のリポジトリ
class AuthRepository {
  /// init
  AuthRepository();

  final _macAddress = '00:00:00:00:00:00';

  final _shiniseUri = Env.getApiBaseUrl();
  final _callOptions = CallOptions(
    timeout: const Duration(seconds: 90),
    metadata: {'certificate': 'n', 'certificate_key': 'n'},
  );

  final _authState = InMemoryStore<AppUser?>(null);

  /// 認証状態の変更を監視する
  Stream<AppUser?> authStateChanges() => _authState.stream;

  /// 現在の認証状態を返す
  AppUser? get currentUser => _authState.value;

  void updateClockOutTime(DateTime time) {
    final user = _authState.value;
    if (user != null) {
      _authState.value = user.updateClockOutTime(time);
    }
  }

  /// パスワードでのログイン
  Future<void> logInByPass({
    required String userCode,
    required String password,
    required Branch store,
    required String ip,
    required BaseDeviceInfo device,
    required DateTime logOffTime,
  }) async {
    final channel = ClientChannel(_shiniseUri.host, port: _shiniseUri.port);

    final option = _callOptions.mergedWith(
      CallOptions(
        metadata: {'ip': ip, 'user_code': userCode, 'mac_address': _macAddress},
      ),
    );

    final stub = AuthenticationServiceClient(channel, options: option);

    final req = LoginRequest(password: password, storeCode: store.code);

    try {
      log('login storeCode ${store.code}');

      final user = AppUser.fromGrpc(await stub.login(req), userCode, logOffTime, store);

      _authState.value = user;

      /// CrashlyticsにユーザーIDをセット
      await FirebaseCrashlytics.instance.setUserIdentifier('');

      _writeLog(device, user, ip, 'in', store);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          DioException() => ParseAuthFailure(e.message.toString()),
          _ => ParseAuthFailure(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// スキャンでのログイン
  Future<void> logInByScan({
    required String userCode,
    required Branch store,
    required String ip,
    required BaseDeviceInfo device,
    required DateTime logOffTime,
  }) async {
    final channel = ClientChannel(_shiniseUri.host, port: _shiniseUri.port);

    final option = _callOptions.mergedWith(
      CallOptions(
        metadata: {
          'ip': ip,
          'user_code': userCode,
          'mac_address': _macAddress,
          'X-SCAN-LOGIN': 'true',
        },
      ),
    );

    final stub = AuthenticationServiceClient(channel, options: option);

    final req = LoginRequest(password: '', storeCode: store.code);

    try {
      final user = AppUser.fromGrpc(await stub.login(req), userCode, logOffTime, store);

      _authState.value = user;

      /// CrashlyticsにユーザーIDをセット
      await FirebaseCrashlytics.instance.setUserIdentifier('');
      _writeLog(device, user, ip, 'in', store);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          DioException() => ParseAuthFailure(e.message.toString()),
          _ => ParseAuthFailure(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ログアウトし、店舗からも退店する(clockout)
  Future<void> logOut({
    required String storeCode,
    required BaseDeviceInfo device,
    required String ip,
  }) async {
    final channel = ClientChannel(_shiniseUri.host, port: _shiniseUri.port);

    final stub = AuthenticationServiceClient(channel, options: _callOptions);
    final req = LogoutRequest(storeCode: storeCode);
    log('logout storeCode $storeCode');

    try {
      /// ITG400ではplayer側で処理するので、退店処理をスキップする
      if (!device.isITG400) await stub.logout(req);

      final currentUser = _authState.value;
      _authState.value = null;

      await FirebaseCrashlytics.instance.setUserIdentifier('');

      _writeLog(device, currentUser, ip, 'out', null);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          DioException() => ParseAuthFailure(e.message.toString()),
          _ => ParseAuthFailure(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 稼働計画があるなら、稼働計画上の退勤時刻を返す
  Future<DateTime> checkWorkPlan({required String userCode}) async {
    const canLogin = '1';

    final channel = ClientChannel(_shiniseUri.host, port: _shiniseUri.port);
    final stub = ClockServiceClient(channel, options: _callOptions);
    final now = clock.now();
    final req = GetPersonalClockTimeRequest(employeeCode: userCode);

    /// 退勤時間が設定されていない場合、８時間後に自動ログオフ
    final defaultTime = clock.now().add(const Duration(hours: 8));

    try {
      final res = await stub.getPersonalClockTime(req);
      log('checkWorkPlan response $res');

      return switch (res) {
        /// ログイン可能かつ退勤時間が設定されている場合は、退勤時間を計算して返す
        GetPersonalClockTimeResponse(
          registration: [
            GetPersonalClockTimeResponse_Registration(
              flagCount: canLogin,
              logoutTime: final logoutTime,
            ),
            ...
          ]
        )
            when logoutTime.isNotEmpty && (int.tryParse(logoutTime) != null) =>
          now.add(Duration(milliseconds: int.parse(logoutTime))),

        /// ログイン可能かつ退勤時間がない場合は、[defaultTime]を返す
        GetPersonalClockTimeResponse(
          registration: [
            GetPersonalClockTimeResponse_Registration(
              flagCount: canLogin,
            ),
            ...
          ]
        ) =>
          defaultTime,

        /// ログイン不能の場合は、ログイン不可能エラーをthrow
        GetPersonalClockTimeResponse(
          registration: [
            GetPersonalClockTimeResponse_Registration(
              flagCount: != canLogin,
            ),
            ...
          ]
        ) =>
          throw OutofWorkPlanException(),
        _ => throw OutofWorkPlanException(),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) { AppException() => e, _ => UnknownException(e.toString()) },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  void _writeLog(
    BaseDeviceInfo device,
    AppUser? user,
    String ip,
    String method,
    Branch? store,
  ) {
    if (!Platform.isAndroid) return;

    final uri = Env.getApiBaseUrl();

    final callOptions =
        CallOptions(timeout: const Duration(seconds: 20), metadata: {'certificate': 'temp', 'certificate_key': 'temp'});
    final channel = ClientChannel(uri.host, port: uri.port);

    final stub = DeviceManagementServiceClient(
      channel,
      options: callOptions,
      interceptors: [
        ShiniseInterceptor(caller: user),
        const DeviceIPInterceptor(),
      ],
    );
    if (device.isITG) {
      final req = RegisterLoginAndLogoutHistoryRequest(
        actionType: switch (method) {
          'in' => ActionType.ACTION_TYPE_LOGIN,
          'out' => ActionType.ACTION_TYPE_LOGOUT,
          _ => ActionType.ACTION_TYPE_UNSPECIFIED,
        },
        coordinate: RegisterLoginAndLogoutHistoryRequest_Coordinate(
          latitude: store?.positionAtLogin?.latitude,
          longitude: store?.positionAtLogin?.longitude,
        ),
        storeCode: user?.clockInStore.code,
      );
      stub.registerLoginAndLogoutHistory(req).ignore();
    } else {
      GetPhoneNumber().hasPermission().then((ok) {
        if (ok) {
          GetPhoneNumber().get().then((phoneNumber) {
            log('phone number: $phoneNumber');
            stub.getDevice(GetDeviceRequest(phoneNumber: phoneNumber)).then(
              (resp) {
                log('device name: ${resp.deviceName}');
                final req = RegisterLoginAndLogoutHistoryRequest(
                  phoneNumber: phoneNumber,
                  deviceName: resp.deviceName,
                  actionType: switch (method) {
                    'in' => ActionType.ACTION_TYPE_LOGIN,
                    'out' => ActionType.ACTION_TYPE_LOGOUT,
                    _ => ActionType.ACTION_TYPE_UNSPECIFIED,
                  },
                  coordinate: RegisterLoginAndLogoutHistoryRequest_Coordinate(
                    latitude: store?.positionAtLogin?.latitude,
                    longitude: store?.positionAtLogin?.longitude,
                  ),
                  storeCode: user?.clockInStore.code,
                );
                stub.registerLoginAndLogoutHistory(req).ignore();
              },
            );
          });
        }
      });
    }
  }
}
