part of '../login_page.dart';

final _formKey = GlobalKey<FormState>();

/// ログイン画面のボディ
class LoginPageBody extends HookConsumerWidget {
  /// init
  const LoginPageBody({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    final useIDController = useTextEditingController();
    final passwordController = useTextEditingController();

    final selectingStore = ref.watch(preLoginSelectStoreProvider);
    final storeNotifier = ref.watch(preLoginSelectStoreProvider.notifier);

    final loginState = ref.watch(loginStateProvider);
    final position = ref.watch(getCurrentPositionProvider);
    final enableScandit = ref.watch(scanditEnableProvider);

    final width = MediaQuery.sizeOf(context).width;

    if (Platform.isAndroid && !ref.watch(deviceInfoProvider).requireValue.isITG400) {
      /// アップデートがあれば、ダイアログを表示
      ref.listen(inappUpdateProvider, (_, next) {
        final inappInfo = next.asData?.valueOrNull;
        switch (inappInfo) {
          case AppUpdateInfo(updateAvailability: UpdateAvailability.updateAvailable):
            InAppUpdate.performImmediateUpdate();
        }
      });
    }

    ref
      ..listen(
        loginStateProvider,
        (_, state) => state.showSnackBarOnError(context),
      )
      ..listen(
        scanCodeProvider,
        (_, state) {
          log('update scanCodeProvider');

          final code = state.asData?.valueOrNull;
          log('scanCodeProvider: $code');
          if (code == null) return;

          _onScan(code, selectingStore, ref);
        },
      )

      /// 現在の対象支店が変更されたら、選択支店を変更
      ..listen(
        currentSiteProvider,
        (_, next) async {
          next.showSnackBarOnError(context);
          switch (next) {
            case AsyncData(:final value):
              storeNotifier.store = value;

            case _:
          }
        },
      );

    return Align(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Flexible(
                flex: 30,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Image.asset(
                    Assets.featureGraphic.featureGraphic.path,
                    fit: BoxFit.scaleDown,

                    /// border
                  ),
                ),
              ),
              const Spacer(),

              if (enableScandit.isEnable)
                Flexible(
                  flex: 10,
                  child: OutlinedButton.icon(
                    style: OutlinedButton.styleFrom(
                      foregroundColor: colors.onTertiaryContainer,
                      backgroundColor: colors.tertiaryContainer,
                      textStyle: Theme.of(context).textTheme.headlineSmall,
                    ),
                    onPressed: () {},
                    label: const Text('スキャン強化版'),
                    icon: const Icon(Icons.flutter_dash),
                  ),
                ),
              const Spacer(),
              Flexible(
                flex: 10,
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 600),
                  child: TextFormField(
                    onTapOutside: (_) => FocusScope.of(context).unfocus(),
                    textAlign: TextAlign.center,
                    controller: useIDController,
                    decoration: InputDecoration(
                      border: const OutlineInputBorder(),
                      suffixIcon: JustScanButtonIcon(
                        /// ITGにはフロントカメラがないので、バックカメラを使用する
                        /// 本来なら、フロントカメラの存在を確認してフロントにするべき
                        facing:
                            ref.read(deviceInfoProvider).requireValue.isITG ? CameraFacing.back : CameraFacing.front,
                        onScan: (code) {
                          if (code.isNotEmpty) {
                            ref.read(scanCodeProvider.notifier).updateWith(code);
                          }
                        },
                      ),
                      labelText: '社員番号'.hardcoded,
                      enabled: !loginState.isLoading,
                    ),
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.visiblePassword,
                    validator: (value) {
                      if (value == null || value.isEmpty) return '何か値を入力してください';

                      return null;
                    },
                  ),
                ),
              ),

              const Spacer(),

              // パスワード
              Flexible(
                flex: 10,
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 600),
                  child: TextFormField(
                    onTapOutside: (_) => FocusScope.of(context).unfocus(),
                    textAlign: TextAlign.center,
                    controller: passwordController,
                    decoration: InputDecoration(
                      border: const OutlineInputBorder(),
                      labelText: 'パスワード'.hardcoded,
                      enabled: !loginState.isLoading,
                    ),
                    obscureText: true,
                    autocorrect: false,
                    textInputAction: TextInputAction.done,
                    keyboardAppearance: Brightness.light,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '何か値を入力してください';
                      }

                      return null;
                    },
                  ),
                ),
              ),
              const Spacer(flex: 5),

              Flexible(
                flex: 10,
                child: ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: width),
                  child: Tooltip(
                    message: ' 選択店舗 ${selectingStore?.name ?? 'なし'}',
                    child: Semantics(
                      identifier: 'login_button',
                      child: ElevatedButton.icon(
                        icon: Icon(Icons.login, color: colors.onPrimary),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colors.primary,
                        ),
                        onPressed: (loginState.isLoading || (selectingStore == null))
                            ? null
                            : () async {
                                if (_formKey.currentState case final form? when form.validate()) {
                                  return ref
                                      .read(loginStateProvider.notifier)
                                      .login(useIDController.text, passwordController.text, selectingStore, () {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          '${selectingStore.name} にログインしました',
                                          style: TextStyle(
                                            color: colors.onPrimary,
                                          ),
                                        ),
                                      ),
                                    );
                                  });
                                }
                              },
                        label: loginState.isLoading
                            ? const CircularProgressIndicator()
                            : Text(
                                '${selectingStore?.name ?? '選択店舗なし'} ログイン',
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: colors.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ),
                ),
              ),

              Flexible(
                flex: 10,
                child: Tooltip(
                  message: position.valueOrNull.toString(),
                  child: const _ReloadStoreButton(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _onScan(String value, Branch? store, WidgetRef ref) async {
    final colors = Theme.of(ref.context).colorScheme;

    try {
      if (await Permission.location.request().isGranted) {
        final info = await InAppUpdate.checkForUpdate();
        if (info.updateAvailability == UpdateAvailability.updateAvailable) {
          await InAppUpdate.performImmediateUpdate();
          log('update available');

          return;
        }
      }
    } catch (e) {
      log('バージョン更新なし');
    }

    log('init _onScan: value is $value');
    if (value.isEmpty && value.length != 13) return;

    /// 社員番号を抜き出し、ゼロ抑制する
    final userID = int.parse(value.substring(4, 12)).toString();

    await ref.read(loginStateProvider.notifier).loginWithScan(
      userID,
      store,
      () {
        ScaffoldMessenger.of(ref.context).showSnackBar(
          SnackBar(
            content: Text(
              '${store?.name} にログインしました',
              style: TextStyle(color: colors.onPrimary),
            ),
          ),
        );
      },
    );
  }
}
