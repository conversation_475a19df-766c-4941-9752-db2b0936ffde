// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../branch/application/branch_service.dart';
import '../../../../branch/domain/branch.dart';

part 'sites_selector.g.dart';

/// ログイン前に店舗を選択する
@riverpod
class PreLoginSelectStore extends _$PreLoginSelectStore {
  @override
  Branch? build() {
    return null;
  }

// ignore: avoid_public_notifier_properties
  Branch? get store => state;
  set store(Branch? branch) => state = branch;
}

class SiteSelector extends ConsumerWidget {
  const SiteSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sites = ref.watch(listSitesProvider);
    final siteNotifier = ref.watch(preLoginSelectStoreProvider.notifier);

    return switch (sites) {
      AsyncLoading() => const Center(child: CircularProgressIndicator()),
      AsyncError() || AsyncData(value: []) => const Text('データの取得に失敗しました。再度お試しするか、サポートにお問い合わせください。'),
      AsyncData(:final value) => FittedBox(
          child: DropdownMenu(
            onSelected: (value) => siteNotifier.store = value,
            label: const Text('店舗手動選択', textAlign: TextAlign.center),
            dropdownMenuEntries: [
              ...value.map(
                (e) => DropdownMenuEntry(
                  value: e,
                  label: e.name,
                  trailingIcon: Text(e.code),
                ),
              ),
            ],
          ),
        ),
    };
  }
}
