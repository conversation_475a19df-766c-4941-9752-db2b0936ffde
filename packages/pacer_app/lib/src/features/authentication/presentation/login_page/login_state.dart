import 'dart:async';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../branch/domain/branch.dart';
import '../../application/auth_service.dart';

part 'login_state.g.dart';

/// ログイン状態を管理するNotifier
@riverpod
class LoginState extends _$LoginState {
  @override
  AsyncValue<void> build() {
    return const AsyncData(null);
  }

  ///　パスワードログイン
  Future<void> login(
    String userID,
    String password,
    Branch? store,
    VoidCallback onLoginSuccess,
  ) async {
    if (store == null) return;

    log('ユーザー $userID パスワードログイン開始');
    state = const AsyncLoading();

    state = await AsyncValue.guard(
      () => ref.read(authServiceProvider).logInByPass(userCode: userID, pass: password, store: store),
    );
    if (state case AsyncData()) {
      onLoginSuccess();
    }
  }

  /// スキャンログイン
  /// パスワード不要
  Future<void> loginWithScan(
    String userID,
    Branch? store,
    VoidCallback onLoginSuccess,
  ) async {
    if (store == null) return;

    log('ユーザー $userID スキャンログイン開始');
    state = const AsyncLoading();

    state = await AsyncValue.guard(
      () => ref.read(authServiceProvider).logInByScan(userID, store),
    );
    if (state case AsyncData()) {
      onLoginSuccess();
    }
  }

  /// ログアウト
  Future<void> logout() async {
    state = const AsyncLoading();
    await ref.read(authServiceProvider).logOut();
  }
}
