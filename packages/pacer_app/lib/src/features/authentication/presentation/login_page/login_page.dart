import 'dart:developer';
import 'dart:io';

import 'package:clock/clock.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scandit_flutter_datacapture_core/scandit_flutter_datacapture_core.dart';

import '../../../../../gen/assets.gen.dart';
import '../../../../localization/string_hardcoded.dart';
import '../../../../scandit_enable.dart';
import '../../../../utils/async_value_ui.dart';
import '../../../../utils/date_time.dart';
import '../../../app/data/inapp_update_provider.dart';
import '../../../branch/application/branch_service.dart';
import '../../../branch/domain/branch.dart';
import '../../../device/application/pacer_service.dart';
import '../../../device/data/pacer_repository.dart';
import '../../../device/domain/extension.dart';
import '../../../device/presentation/scan_window.dart';
import '../../application/fake_workplan.dart';
import 'components/sites_selector.dart';
import 'login_state.dart';

part 'components/login_page_body.dart';
part 'components/reload_store_button.dart';
part 'components/bottom_bar_with_secret.dart';

/// ログイン画面
class LoginPage extends ConsumerWidget {
  /// init
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    /// ログイン画面が最前面の時のみ、sparkscanを有効化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ModalRoute.of(context)?.isCurrent ?? false) {
        ref.read(scanditEnableProvider.notifier).enable();
      }
    });
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: const SafeArea(child: LoginPageBody()),
      bottomNavigationBar: _BottomBarWithSecret(),
    );
  }
}
