// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sites_selector.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$preLoginSelectStoreHash() => r'8bd02ac307b0a95e057faeebe11913323219516c';

/// ログイン前に店舗を選択する
///
/// Copied from [PreLoginSelectStore].
@ProviderFor(PreLoginSelectStore)
final preLoginSelectStoreProvider = AutoDisposeNotifierProvider<PreLoginSelectStore, Branch?>.internal(
  PreLoginSelectStore.new,
  name: r'preLoginSelectStoreProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$preLoginSelectStoreHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PreLoginSelectStore = AutoDisposeNotifier<Branch?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
