part of '../login_page.dart';

class _ReloadStoreButton extends ConsumerWidget {
  const _ReloadStoreButton();

  void Function()? _onPressed(WidgetRef ref) {
    return () async {
      final permission = await Permission.location.status;
      log(permission.toString());

      await Permission.location.request();

      ref.invalidate(currentSiteProvider);
    };
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentSite = ref.watch(currentSiteProvider);

    return Padding(
      padding: const EdgeInsets.all(8),
      child: switch (currentSite.isLoading) {
        true => const CircularProgressIndicator(),
        false => ElevatedButton.icon(
            onPressed: _onPressed(ref),
            icon: const Icon(Icons.refresh),
            label: const Text('選択店舗 自動更新'),
          ),
      },
    );
  }
}
