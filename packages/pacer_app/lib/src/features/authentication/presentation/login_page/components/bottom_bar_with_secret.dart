part of '../login_page.dart';

/// 隠しコマンド付きボトムバー
/// バージョン表示を３回タップで店舗選択ダイアログ出現
class _BottomBarWithSecret extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final package = ref.watch(packageInfoProvider).requireValue;
    final versionText = '${package.appName} version ${package.version}';

    return RawGestureDetector(
      gestures: {
        SerialTapGestureRecognizer: GestureRecognizerFactoryWithHandlers<SerialTapGestureRecognizer>(
          SerialTapGestureRecognizer.new,
          (instance) => instance.onSerialTapDown = _showDialogOnTap(ref),
        ),
      },
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              versionText,
              textAlign: TextAlign.right,
              style: Theme.of(context).textTheme.labelMedium,
            ),
            Text(
              DataCaptureContext.deviceId,
              textAlign: TextAlign.right,
              style: Theme.of(context).textTheme.labelSmall,
            ),
          ],
        ),
      ),
    );
  }
}

void Function(SerialTapDownDetails) _showDialogOnTap(WidgetRef ref) {
  /// ３回タップで障害時用の店舗選択ダイアログ出現
  const isFaultConfirmation = 3;
  const alertText = '障害調査用のボタンです。\n管理者以外が利用してはいけません';

  return (details) {
    switch (details) {
      case SerialTapDownDetails(count: isFaultConfirmation):
        showDialog<void>(
          context: ref.context,
          builder: (_) => Dialog(
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.warning),
                  const Text(alertText),
                  const Gap(8),
                  const SiteSelector(),
                  const Gap(8),
                  ElevatedButton(
                    onPressed: () async {
                      final logoutTime = await showTimePicker(
                        context: ref.context,
                        initialTime: TimeOfDay.now(),
                        initialEntryMode: TimePickerEntryMode.input,
                      );
                      if (logoutTime != null) {
                        final now = clock.now();
                        ref.read(fakeWorkplanProvider.notifier).setWorkplanForDebug(now.applied(logoutTime));
                      }
                    },
                    child: const Text('自動ログアウト時間を変更'),
                  ),
                  const Gap(8),
                  const WorkplanUpdateText(),
                ],
              ),
            ),
          ),
        );
    }
  };
}

class WorkplanUpdateText extends ConsumerWidget {
  const WorkplanUpdateText({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final debugWorkplan = ref.watch(fakeWorkplanProvider);
    if (debugWorkplan == null) return const SizedBox();
    return Text('現在の自動ログアウト時間: ${debugWorkplan.hour}時:'
        '${debugWorkplan.minute}分');
  }
}

class WorkplanUpdateButton extends ConsumerWidget {
  const WorkplanUpdateButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ElevatedButton(
      onPressed: () async {
        final logoutTime = await showTimePicker(
          context: ref.context,
          initialTime: TimeOfDay.now(),
        );
        if (logoutTime != null) {
          final now = clock.now();
          ref.read(fakeWorkplanProvider.notifier).setWorkplanForDebug(now.applied(logoutTime));
        }
      },
      child: const Text('自動ログアウト時間を変更'),
    );
  }
}
