import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:two_dimensional_scrollables/two_dimensional_scrollables.dart';
import 'package:url_launcher/url_launcher_string.dart';
import '../../../../exceptions/app_exception.dart';
import '../../../authentication/data/auth_repository.dart';
import '../../application/log_service.dart';
import '../../domain/usage_log.dart';

part 'printer_table.dart';

part 'auth_table.dart';

enum _TabType {
  mobile(label: 'モバイル'),
  printer(label: 'ラベルプリンタ');

  const _TabType({required this.label});

  final String label;
}

/// 店舗での端末情報を表示する
class DeviceUsagePage extends ConsumerWidget {
  /// init
  const DeviceUsagePage({super.key});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final store = ref.watch(authRepositoryProvider).currentUser?.clockInStore;

    return DefaultTabController(
      length: _TabType.values.length,
      child: Scaffold(
        appBar: AppBar(
          title: Text('${store?.name} 端末利用履歴'),
          bottom: TabBar(
            tabs: [
              Tab(text: _TabType.mobile.label),
              Tab(text: _TabType.printer.label),
            ],
          ),
        ),
        body: const SafeArea(child: TabBarView(children: [_AuthStateTable(), _PrinterLogTable()])),
      ),
    );
  }
}
