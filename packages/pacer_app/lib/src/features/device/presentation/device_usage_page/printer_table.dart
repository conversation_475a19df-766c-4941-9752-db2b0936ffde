part of 'device_usage_page.dart';

class _PrinterLogTable extends HookConsumerWidget {
  const _PrinterLogTable();
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final printerLogs = ref.watch(listLabelPrinterLogsProvider);

    final verticalController = useScrollController();
    return switch (printerLogs) {
      AsyncError(:final error) => Center(
          child:
              Text(switch (error) { final AppException appException => appException.message, _ => error.toString() }),
        ),
      AsyncLoading() => const Center(child: CircularProgressIndicator()),
      AsyncData(:final value) => TableView.builder(
          pinnedRowCount: 1,
          pinnedColumnCount: 1,
          verticalDetails: ScrollableDetails.vertical(
            controller: verticalController,
          ),
          cellBuilder: (context, vicinity) => _buildPrinterLogCell(context, vicinity, value),
          columnCount: 4,
          columnBuilder: _buildColumnSpan,
          rowCount: value.length + 1,
          rowBuilder: (index) => _buildRowSpan(context, index),
        ),
    };
  }

  /// セルの中身を作成
  TableViewCell _buildPrinterLogCell(
    BuildContext context,
    TableVicinity vicinity,
    List<PrinterUsageLog> logs,
  ) {
    return TableViewCell(
      child: switch ((vicinity.row, vicinity.column)) {
        /// ヘッダー
        (0, 0) => const _LargeValueText('MACアドレス'),
        (0, 1) => const _LargeValueText('ユーザー'),
        (0, 2) => const _LargeValueText('操作時間'),
        (0, 3) => const _LargeValueText('社員番号'),
        (final row, 0) => _LargeValueText(logs[row - 1].deviceCode),
        (final row, 1) => _LargeValueText(logs[row - 1].userName),
        (final row, 2) => _LargeValueText(
            DateFormat('MM/dd HH:mm').format(logs[row - 1].createdAt),
          ),
        (final row, 3) => _LargeValueText(logs[row - 1].userCode),
        _ => _LargeValueText('Tile c: ${vicinity.column}, r: ${vicinity.row}'),
      },
    );
  }

  /// カラムを作成
  TableSpan _buildColumnSpan(int index) {
    const decoration = TableSpanDecoration(border: TableSpanBorder(trailing: BorderSide()));

    return switch (index) {
      0 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.3),
        ),
      1 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.2),
        ),
      2 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.3),
        ),
      3 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.2),
        ),
      4 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.2),
        ),
      _ => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.2),
        ),
    };
  }

  /// 行を作成
  TableSpan _buildRowSpan(BuildContext context, int index) {
    const decoration = TableSpanDecoration(
      // color: index.isEven ? Colors.purple[100] : null,
      border: TableSpanBorder(trailing: BorderSide()),
    );
    if (index == 0) {
      return const TableSpan(
        backgroundDecoration: decoration,
        extent: FractionalTableSpanExtent(0.07),
      );
    }

    return const TableSpan(
      backgroundDecoration: decoration,
      extent: FractionalTableSpanExtent(0.07),
    );
  }
}
