part of 'device_usage_page.dart';

class _AuthStateTable extends HookConsumerWidget {
  const _AuthStateTable();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final verticalController = useScrollController();

    final authState = ref.watch(listAuthLogsProvider);

    return switch (authState) {
      AsyncData(:final value) => TableView.builder(
          pinnedRowCount: 1,
          pinnedColumnCount: 1,
          verticalDetails: ScrollableDetails.vertical(
            controller: verticalController,
          ),
          cellBuilder: (context, vicinity) => _buildMobileCell(context, vicinity, value),
          columnCount: 7,
          columnBuilder: _buildMobileColumnSpan,
          rowCount: value.length + 1,
          rowBuilder: (index) => _buildRowSpan(context, index),
        ),
      AsyncError(:final error) => Center(
          child:
              Text(switch (error) { final AppException appException => appException.message, _ => error.toString() }),
        ),
      AsyncLoading() => const Center(child: CircularProgressIndicator()),
    };
  }

  TableSpan _buildMobileColumnSpan(int index) {
    const decoration = TableSpanDecoration(border: TableSpanBorder(trailing: BorderSide()));

    return switch (index) {
      0 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.3),
        ),
      1 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.25),
        ),
      2 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.2),
        ),
      3 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.25),
        ),
      4 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.2),
        ),
      5 => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.4),
        ),
      _ => const TableSpan(
          foregroundDecoration: decoration,
          extent: FractionalTableSpanExtent(0.3),
        ),
    };
  }

  /// セルの中身を作成
  TableViewCell _buildMobileCell(
    BuildContext context,
    TableVicinity vicinity,
    List<AuthActionLog> logs,
  ) {
    return TableViewCell(
      child: switch ((vicinity.row, vicinity.column)) {
        /// ヘッダー
        (0, 0) => const _LargeValueText('端末名'),
        (0, 1) => const _LargeValueText('利用状況'),
        (0, 2) => const _LargeValueText('ユーザー'),
        (0, 3) => const _LargeValueText('操作時間'),
        (0, 4) => const _LargeValueText('社員番号'),
        (0, 5) => const _LargeValueText('TEL'),
        (0, 6) => const _LargeValueText('ip'),
        (final row, 0) => _LargeValueText(
            switch (logs[row - 1].deviceCode) {
              '' => logs[row - 1].ip,
              final code => code,
            },
          ),
        (final row, 1) => _LargeValueText(logs[row - 1].method),
        (final row, 2) => _LargeValueText(logs[row - 1].userName),
        (final row, 3) => _LargeValueText(
            DateFormat('MM/dd HH:mm').format(logs[row - 1].createdAt),
          ),
        (final row, 4) => _LargeValueText(logs[row - 1].userCode),
        (final row, 5) => Center(
            child: InkWell(
              onTap: () => launchUrlString('tel://${logs[row - 1].phoneNumber}'),
              child: FittedBox(
                child: Text(
                  _formatPhoneNumber(logs[row - 1].phoneNumber),
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                ),
              ),
            ),
          ),
        (final row, 6) => _LargeValueText(logs[row - 1].ip),
        _ => const _LargeValueText('***********'),
      },
    );
  }

  /// 行を作成
  TableSpan _buildRowSpan(BuildContext context, int index) {
    const decoration = TableSpanDecoration(
      // color: index.isEven ? Colors.purple[100] : null,
      border: TableSpanBorder(trailing: BorderSide()),
    );
    if (index == 0) {
      return const TableSpan(
        backgroundDecoration: decoration,
        extent: FractionalTableSpanExtent(0.07),
      );
    }

    return const TableSpan(
      backgroundDecoration: decoration,
      extent: FractionalTableSpanExtent(0.07),
    );
  }
}

class _LargeValueText extends StatelessWidget {
  const _LargeValueText(this.value);
  final String value;

  @override
  Widget build(BuildContext context) {
    final bodyLarge = Theme.of(context).textTheme.bodyLarge;
    final colors = Theme.of(context).colorScheme;

    return switch (value) {
      'ログイン' => ColoredBox(
          color: colors.primary,
          child: Center(
            child: FittedBox(
              fit: BoxFit.fitWidth,
              child: Text(
                value,
                style: bodyLarge?.copyWith(color: colors.onPrimary),
              ),
            ),
          ),
        ),
      'ログアウト' => ColoredBox(
          color: colors.surfaceContainerHighest,
          child: Center(
            child: FittedBox(
              fit: BoxFit.fitWidth,
              child: Text(
                value,
                style: bodyLarge?.copyWith(color: colors.onSurfaceVariant),
              ),
            ),
          ),
        ),
      _ => Center(
          child: FittedBox(
            fit: BoxFit.fitWidth,
            child: Text(value),
          ),
        ),
    };
  }
}

/// 09099999999 -> 090-9999-9999
String _formatPhoneNumber(String phneNumber) {
  if (phneNumber.length != 11) {
    return phneNumber;
  }
  return '${phneNumber.substring(0, 3)}-${phneNumber.substring(3, 7)}-${phneNumber.substring(7)}';
}
