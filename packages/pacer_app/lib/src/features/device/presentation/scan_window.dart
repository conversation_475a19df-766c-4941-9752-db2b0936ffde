import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:media_kit/media_kit.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:vibration/vibration.dart';

import '../../../../../gen/assets.gen.dart';
import '../../../scandit_enable.dart';
import '../../../utils/use_audio/use_audio.dart';
import '../../app/application/global_loading_service.dart';
import '../application/pacer_service.dart';
import 'scan_window/scan_window_overlay.dart';
import 'scan_window/switch_camera_button.dart';
import 'scan_window/toggle_flash_button.dart';

part 'scan_window/legacy_camera_screen.dart';

/// BottomAppBar埋め込み用のFIB
/// スキャン時にscanCodeProviderを更新しない
/// onScanで指定された処理だけをする
class ScanFloatingIconButton extends ConsumerWidget {
  /// init
  const ScanFloatingIconButton({
    super.key,
    this.onScan,
    this.formats,
  });

  /// スキャンボタン押下時のコールバック
  final void Function(String code)? onScan;

  /// スキャン可能なバーコードの種類
  final List<BarcodeFormat>? formats;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isEnableScandit = ref.watch(scanditEnableProvider).isEnable;

    return switch (isEnableScandit) {
      /// sparkScanが有効ならスキャンボタンは表示しない
      true => const SizedBox.shrink(),
      false => IconButton.filled(
          iconSize: 36,
          onPressed: () => _onScanButtonPressed(ref),
          icon: ImageIcon(AssetImage(Assets.commonIcon.iconBarcodeScanner.path)),
        ),
    };
  }

  Future<void> _onScanButtonPressed(WidgetRef ref) async {
    final code = await Navigator.of(ref.context)
        .push<String>(MaterialPageRoute(builder: (context) => const _BarcodeScanner(null)));
    if (code != null && code.isNotEmpty) {
      if (onScan != null) {
        onScan?.call(code);
      } else {
        ref.read(scanCodeProvider.notifier).updateWith(code);
      }
    }
  }
}

/// カメラスキャン画面を表示するアイコン
/// スキャン時にscanCodeProviderを更新しない
/// onScanで指定された処理だけをする
class JustScanButtonIcon extends ConsumerWidget {
  /// init
  const JustScanButtonIcon({
    super.key,
    this.onScan,
    this.facing,
    this.formats,
  });

  /// スキャンボタン押下時のコールバック
  final void Function(String code)? onScan;

  /// カメラの向き
  final CameraFacing? facing;

  /// スキャン可能なバーコードの種類
  final List<BarcodeFormat>? formats;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final isEnableScandit = ref.watch(scanditEnableProvider).isEnable;

    return switch (isEnableScandit) {
      /// sparkScanが有効ならスキャンボタンは表示しない
      true => const SizedBox.shrink(),
      false => IconButton.filled(
          onPressed: () => _onScanButtonPressed(ref),
          icon: ImageIcon(color: colors.onPrimary, AssetImage(Assets.commonIcon.iconBarcodeScanner.path)),
        ),
    };
  }

  Future<void> _onScanButtonPressed(WidgetRef ref) async {
    final isLoading = ref.read(globalLoadingServiceProvider);
    if (isLoading) return;

    final code = await Navigator.of(ref.context).push<String>(
      MaterialPageRoute(
        builder: (context) => _BarcodeScanner(facing),
      ),
    );
    if (code != null && code.isNotEmpty) {
      if (onScan != null) {
        onScan?.call(code);
      } else {
        ref.read(scanCodeProvider.notifier).updateWith(code);
      }
    }
  }
}
