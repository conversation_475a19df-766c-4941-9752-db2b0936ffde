import 'package:flutter/material.dart';

/// スキャン範囲のオーバーレイ
class ScannerOverlay extends CustomPainter {
  /// init
  const ScannerOverlay(this.scanWindow);

  /// スキャン窓の範囲
  final Rect scanWindow;

  /// 窓の丸み
  static const double borderRadius = 12;

  @override
  void paint(Canvas canvas, Size size) {
    final backgroundPath = Path()..addRect(Rect.largest);
    final cutoutPath = Path()
      ..addRRect(
        RRect.fromRectAndCorners(
          scanWindow,
          topLeft: const Radius.circular(borderRadius),
          topRight: const Radius.circular(borderRadius),
          bottomLeft: const Radius.circular(borderRadius),
          bottomRight: const Radius.circular(borderRadius),
        ),
      );

    final backgroundPaint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.dstOut;

    final backgroundWithCutout = Path.combine(
      PathOperation.difference,
      backgroundPath,
      cutoutPath,
    );

    // Create a Paint object for the white border
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0; // Adjust the border width as needed

    // Calculate the border rectangle with rounded corners
    // Adjust the radius as needed
    final borderRect = RRect.fromRectAndCorners(
      scanWindow,
      topLeft: const Radius.circular(borderRadius),
      topRight: const Radius.circular(borderRadius),
      bottomLeft: const Radius.circular(borderRadius),
      bottomRight: const Radius.circular(borderRadius),
    );

    /// 中心に引く横線
    final centerLinePaint = Paint()
      ..color = Colors.blueGrey // 線の色
      ..strokeWidth = 1.0; // 線の太さ

    final centerY = scanWindow.top + scanWindow.height / 2;
    final centerX = scanWindow.left + scanWindow.width / 2;

    // Draw the white border
    canvas

      /// 横線を引く
      ..drawLine(
        Offset(scanWindow.left, centerY),
        Offset(scanWindow.right, centerY),
        centerLinePaint,
      )

      /// 縦線をひく
      ..drawLine(
        Offset(centerX, scanWindow.top),
        Offset(centerX, scanWindow.bottom),
        centerLinePaint,
      )
      ..drawPath(backgroundWithCutout, backgroundPaint)
      ..drawRRect(borderRect, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
