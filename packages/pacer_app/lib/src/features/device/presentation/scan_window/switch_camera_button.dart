import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

/// カメラ切り替えボタン
class SwitchCameraButton extends StatelessWidget {
  /// init
  const SwitchCameraButton({required this.controller, super.key});

  /// MobileScannerController
  final MobileScannerController controller;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: controller,
      builder: (context, state, child) {
        if (!state.isInitialized || !state.isRunning) {
          return const SizedBox.shrink();
        }

        final availableCameras = state.availableCameras;

        if (availableCameras != null && availableCameras < 2) {
          return const SizedBox.shrink();
        }

        final icon = switch (state.cameraDirection) {
          CameraFacing.front => const Icon(Icons.camera_front),
          CameraFacing.back => const Icon(Icons.camera_rear),
        };

        return IconButton(
          color: Colors.white,
          iconSize: 32,
          icon: icon,
          onPressed: () async {
            await controller.switchCamera();
          },
        );
      },
    );
  }
}
