part of '../scan_window.dart';

class _BarcodeScanner extends StatefulHookWidget {
  const _BarcodeScanner(this.facing);

  final CameraFacing? facing;

  @override
  State<_BarcodeScanner> createState() => _BarcodeScannerState();
}

class _BarcodeScannerState extends State<_BarcodeScanner> {
  late MobileScannerController controller;
  late StreamSubscription<BarcodeCapture>? subs;

  void _startCamera(MobileScannerController controller, double zoom) {
    controller.stop().whenComplete(
          () => controller
              .start(cameraDirection: widget.facing)
              .whenComplete(() => controller.setZoomScale(zoom))
              .whenComplete(() => controller.toggleTorch()),
        );
  }

  // カメラを停止してリソースを解放する
  // disposeメソッドは以下の公式ドキュメントにある通り、awaitを用いて呼ぶ
  // https://pub.dev/packages/mobile_scanner
  Future<void> _stopCamera(MobileScannerController controller) async {
    try {
      await controller.stop();
    } finally {
      await controller.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final scanWindow = Rect.fromCenter(
      center: size.center(Offset(0, -size.height * 0.2)),
      width: size.width,
      height: size.height / 2.5,
    );

    const iconSize = 32.0;

    final zoom = useState(0.5);
    final barcode = useState<Barcode?>(null);
    final capture = useState<BarcodeCapture?>(null);
    final scanned = useState(false);

    final player = useAudio(Media('asset:///${Assets.audio.scan}'));

    useEffect(
      () {
        controller = MobileScannerController();

        _startCamera(controller, zoom.value);
        return () => _stopCamera(controller);
      },
      [],
    );

    subs = useOnStreamChange(
      controller.barcodes.skip(1),
      onData: (barcodeCapture) async {
        //// すでにスキャン済みなら何もしない
        if (scanned.value) return;

        scanned.value = true;
        capture.value = barcodeCapture;
        barcode.value = barcodeCapture.barcodes.first;
        log(barcodeCapture.barcodes.first.displayValue.toString());

        Navigator.of(context).pop<String>(barcode.value?.displayValue);

        if (await Vibration.hasVibrator() ?? false) {
          await Vibration.vibrate();
        }
        await player.play();
      },
    );

    useEffect(
      () => () {
        subs?.cancel();
        subs = null;
      },
      [],
    );

    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Builder(
          builder: (context) {
            return Stack(
              fit: StackFit.expand,
              children: [
                MobileScanner(
                  scanWindow: scanWindow,
                  controller: controller,
                  errorBuilder: (context, error, child) {
                    _startCamera(controller, zoom.value);

                    return _ScannerErrorWidget(error: error);
                  },
                ),
                CustomPaint(painter: ScannerOverlay(scanWindow)),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    alignment: Alignment.bottomCenter,
                    height: MediaQuery.sizeOf(context).height * 0.15,
                    color: Colors.black.withOpacity(0.4),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Slider(
                          value: zoom.value,
                          onChanged: (value) {
                            zoom.value = value;
                            controller.setZoomScale(value);
                          },
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            /// バックボタン
                            Expanded(
                              child: IconButton(
                                color: Colors.white,
                                icon: const Icon(Icons.arrow_back),
                                iconSize: iconSize,
                                onPressed: () => Navigator.of(context).pop(),
                              ),
                            ),
                            Expanded(
                              child: ToggleFlashlightButton(
                                controller: controller,
                              ),
                            ),
                            Expanded(
                              child: SwitchCameraButton(controller: controller),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

class _ScannerErrorWidget extends StatelessWidget {
  const _ScannerErrorWidget({required this.error});

  final MobileScannerException error;

  @override
  Widget build(BuildContext context) {
    return const ColoredBox(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Gap(32),
            Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: Icon(Icons.error, color: Colors.white),
            ),
            Text(
              'カメラ起動中です',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
