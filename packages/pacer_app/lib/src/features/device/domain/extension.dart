// ignore_for_file: public_member_api_docs

import 'package:device_info_plus/device_info_plus.dart';

extension IsITG on BaseDeviceInfo {
  static const itg400DeviceName = 'itg400';
  static const itg650DeviceName = 'itg650';

  /// ITG400またはITG600でアプリが動作しているか
  bool get isITG {
    // if (!Platform.isAndroid) return false;

    if (this case final AndroidDeviceInfo info
        when info.device == itg400DeviceName || info.device == itg650DeviceName) {
      return true;
    }

    return false;
  }

  /// ITG400でアプリが動作しているか
  bool get isITG400 {
    // if (!Platform.isAndroid) return false;

    if (this case final AndroidDeviceInfo info when info.device == itg400DeviceName) {
      return true;
    }

    return false;
  }

  /// ITG650でアプリが動作しているか
  bool get isITG650 {
    // if (!Platform.isAndroid) return false;

    if (this case final AndroidDeviceInfo info when info.device == itg650DeviceName) {
      return true;
    }

    return false;
  }
}
