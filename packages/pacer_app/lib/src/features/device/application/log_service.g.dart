// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$listLabelPrinterLogsHash() => r'7f3ada7a40f4cc9ae7d77266b059026980eaf946';

/// ラベルプリンター利用履歴を取得
///
/// Copied from [listLabelPrinterLogs].
@ProviderFor(listLabelPrinterLogs)
final listLabelPrinterLogsProvider = AutoDisposeFutureProvider<List<PrinterUsageLog>>.internal(
  listLabelPrinterLogs,
  name: r'listLabelPrinterLogsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$listLabelPrinterLogsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ListLabelPrinterLogsRef = AutoDisposeFutureProviderRef<List<PrinterUsageLog>>;
String _$listAuthLogsHash() => r'6868d5e35b22eedd64bb2753fe92e8d5e075e650';

/// モバイル端末利用履歴を取得
///
/// Copied from [listAuthLogs].
@ProviderFor(listAuthLogs)
final listAuthLogsProvider = AutoDisposeFutureProvider<List<AuthActionLog>>.internal(
  listAuthLogs,
  name: r'listAuthLogsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$listAuthLogsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ListAuthLogsRef = AutoDisposeFutureProviderRef<List<AuthActionLog>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
