import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:trial_item_code/trial_item_code.dart';

import '../../app/application/global_loading_service.dart';
import '../data/pacer_repository.dart';
import '../domain/extension.dart';

part 'pacer_service.g.dart';

/// カメラやバーコードスキャナでスキャンしたコードの状態
@Riverpod()
class ScanCode extends _$ScanCode {
  @override
  Stream<String> build() {
    final device = ref.watch(deviceInfoProvider).requireValue;
    final isLoading = ref.watch(globalLoadingServiceProvider);

    /// ITGの場合は、ITG SDKからのスキャンコードのストリームを返す
    if (device.isITG) {
      final itgManager = ref.watch(itgManagerProvider);

      if (isLoading) {
        return const Stream.empty();
      }

      return itgManager.scanBroadcast().map((event) => event.jan);
    }

    return const Stream.empty();
  }

  /// スキャンコードの手動更新
  void updateWith(String code) {
    if (code.isEmpty) return;
    log('スキャンコード更新: $code');
    final jan = TrialItemCode.fromString(code).jan;
    state = AsyncData(jan);
  }
}
