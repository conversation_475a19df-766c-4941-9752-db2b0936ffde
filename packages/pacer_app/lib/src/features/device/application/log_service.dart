import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../authentication/data/auth_repository.dart';
import '../data/label_printer_log_repository.dart';
import '../domain/usage_log.dart';

part 'log_service.g.dart';

/// ラベルプリンター利用履歴を取得
@riverpod
Future<List<PrinterUsageLog>> listLabelPrinterLogs(
  ListLabelPrinterLogsRef ref,
) {
  final store = ref.watch(authRepositoryProvider).currentUser?.clockInStore;
  final logs = ref.watch(labelPrinterLogRepositoryProvider).listPrinterUsageLogs(store?.code ?? '');
  return logs;
}

/// モバイル端末利用履歴を取得
@riverpod
Future<List<AuthActionLog>> listAuthLogs(ListAuthLogsRef ref) {
  final store = ref.watch(authRepositoryProvider).currentUser?.clockInStore;
  final logs = ref.watch(labelPrinterLogRepositoryProvider).listAuthActionLogs(store?.code ?? '');
  return logs;
}
