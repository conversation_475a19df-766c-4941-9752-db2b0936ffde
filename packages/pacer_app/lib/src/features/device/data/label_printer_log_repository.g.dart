// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'label_printer_log_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$labelPrinterLogRepositoryHash() => r'fdaac63e7ef83091223bed2f7c9e4f6a503c7866';

/// autoDisposeProvider
///
/// Copied from [labelPrinterLogRepository].
@ProviderFor(labelPrinterLogRepository)
final labelPrinterLogRepositoryProvider = Provider<LabelPrinterLogRepository>.internal(
  labelPrinterLogRepository,
  name: r'labelPrinterLogRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$labelPrinterLogRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef LabelPrinterLogRepositoryRef = ProviderRef<LabelPrinterLogRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
