import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/device_management/v1/device_management.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../../utils/device_ip_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/usage_log.dart';

part 'label_printer_log_repository.g.dart';

/// autoDisposeProvider
@Riverpod(keepAlive: true)
LabelPrinterLogRepository labelPrinterLogRepository(
  LabelPrinterLogRepositoryRef ref,
) =>
    LabelPrinterLogRepository();

/// ラベルプリンター利用履歴を保存する
class LabelPrinterLogRepository {
  /// init
  LabelPrinterLogRepository();

  final _shiniseUri = Env.getApiBaseUrl();
  final _callOptions = CallOptions(
    timeout: const Duration(seconds: 90),
    metadata: {'certificate': 'n', 'certificate_key': 'n'},
  );

  /// ラベルプリンター利用履歴を保存する
  void writeLog(AppUser? user, String ip, String deviceMacAddress) {
    final channel = ClientChannel(_shiniseUri.host, port: _shiniseUri.port);
    final stub = DeviceManagementServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: user),
        const DeviceIPInterceptor(),
      ],
    );
    final req = RegisterDiscountLabelPrinterHistoryRequest(
      macAddress: deviceMacAddress,
      storeCode: user?.clockInStore.code,
    );

    stub.registerDiscountLabelPrinterHistory(req).ignore();
  }

  /// ラベルプリンター利用履歴を取得
  /// deviceCodeごとに最新の1件を取得
  Future<List<PrinterUsageLog>> listPrinterUsageLogs(String storeCode) async {
    final channel = ClientChannel(_shiniseUri.host, port: _shiniseUri.port);
    final stub = DeviceManagementServiceClient(channel, options: _callOptions);
    final req = ListDiscountLabelPrinterHistoryRequest(storeCode: storeCode);
    try {
      final logs = await stub.listDiscountLabelPrinterHistory(req);

      return [
        ...logs.printerHistories.map(
          (e) {
            return (
              userCode: e.userCode,
              userName: e.userName,
              storeCode: storeCode,
              storeName: '',
              deviceCode: e.macAddress,
              ip: '',
              createdAt: DateTime(
                e.usageTime.year,
                e.usageTime.month,
                e.usageTime.day,
                e.usageTime.hour,
                e.usageTime.minute,
                e.usageTime.second,
              ),
            );
          },
        ),
      ];
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          final GrpcError e => e.handleGrpcError(),
          _ => InternalException(),
        },
        StackTrace.current,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ログイン状態を取得
  /// 店舗一致、デバイスコードが空でない、最新1000件を取得
  Future<List<AuthActionLog>> listAuthActionLogs(String storeCode) async {
    final channel = ClientChannel(_shiniseUri.host, port: _shiniseUri.port);
    final stub = DeviceManagementServiceClient(channel, options: _callOptions);
    final req = ListLoginAndLogoutHistoryRequest(storeCode: storeCode);
    try {
      final logs = await stub.listLoginAndLogoutHistory(req);

      return [
        ...logs.pacerHistories.map(
          (e) {
            return (
              phoneNumber: e.phoneNumber,
              userCode: e.userCode,
              userName: e.userName,
              storeCode: storeCode,
              storeName: '',
              deviceCode: e.deviceName,
              ip: e.ip,
              method: switch (e.actionType) {
                ActionType.ACTION_TYPE_LOGIN => 'ログイン',
                ActionType.ACTION_TYPE_LOGOUT => 'ログアウト',
                ActionType() => '',
              },
              createdAt: DateTime(
                e.actionTime.year,
                e.actionTime.month,
                e.actionTime.day,
                e.actionTime.hour,
                e.actionTime.minute,
                e.actionTime.second,
              ),
            );
          },
        ),
      ];
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          final GrpcError e => e.handleGrpcError(),
          _ => InternalException(),
        },
        StackTrace.current,
      );
    } finally {
      await channel.shutdown();
    }
  }
}

extension GrpcErrorX on GrpcError {
  AppException handleGrpcError() {
    log('GrpcError: $codeName');

    switch (code) {
      case StatusCode.notFound:
        return ProductNotFoundException();
      case StatusCode.deadlineExceeded:
        return TimeOutException();
      case StatusCode.invalidArgument:
        return WrongProductCodeException();
      case StatusCode.unknown:
        return ProductNotFoundException();
      case StatusCode.internal:
        return InternalException();
      case _:
        return AppCustomException(codeName, '端末管理の処理が失敗しました');
    }
  }
}
