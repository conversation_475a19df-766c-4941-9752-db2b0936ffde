import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:itg_plugin/itg_plugin.dart';
import 'package:itg_plugin/type.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:register_broadcast_receiver/intent_filter.dart';
import 'package:register_broadcast_receiver/register_broadcast_receiver.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:trial_item_code/trial_item_code.dart';

import '../domain/extension.dart';

part 'pacer_repository.g.dart';

/// デバイス情報を提供する
@Riverpod(keepAlive: true)
Future<BaseDeviceInfo> deviceInfo(DeviceInfoRef ref) {
  return DeviceInfoPlugin().deviceInfo;
}

/// パッケージ情報を提供する
@Riverpod(keepAlive: true)
Future<PackageInfo> packageInfo(PackageInfoRef ref) {
  /// パッケージ情報取得
  return PackageInfo.fromPlatform();
}

/// ディレクトリ情報を提供する
@Riverpod(keepAlive: true)
Future<Directory> directoryInfo(DirectoryInfoRef ref) {
  return getApplicationDocumentsDirectory();
}

/// ITGのデバイス設定管理を提供
@Riverpod(keepAlive: true)
ItgManager itgManager(ItgManagerRef ref) => ItgManager();

/// ITGデバイスの設定管理
class ItgManager {
  static const _scanAction = 'device.common.USERMSG';

  final _plugin = ItgPlugin();

  /// 組み込みスキャナーを起動する
  Future<void> openITGScanner(BaseDeviceInfo device) async {
    log('スキャナオープン');
    await _plugin.openScanner();

    log('中心読みに変更');
    await setCenteringWindow(CenteringWindowMode.enable);

    log('ライトとエイマーをオンにする');
    await setLightMode(LightMode.allOn);

    log('トリガーキーをオンにする');
    await setTriggerKeyEnable(TriggerKeyMode.enable);

    log('ユーザーメッセージ出力に変更');
    await setOutputType(OutputType.user);

    /// ITG650の場合のみ、ナビゲーションバーを非表示
    /// 先に物理ボタンにナビゲーションを割り当てておくこと
    if (device.isITG650) {
      /// ナビゲーションバーが有効な場合のみ、無効にする
      /// 無効の時にfalseにしようとするとハングアップするため
      final state = await getNavigationBarState();
      if (state ?? false) {
        await setNavigationBarState(showNavigationBar: false);
      }
    }
  }

  /// システムナビゲーションバーが有効かどうかを取得する
  Future<bool?> getNavigationBarState() async {
    return _plugin.getNavigationBarState();
  }

  /// システムナビゲーションバーの有効無効を設定する
  Future<void> setNavigationBarState({required bool showNavigationBar}) async {
    await _plugin.setNavigationBarState(showNavigationBar);
  }

  /// スキャナーの出力タイプを設定する
  Future<void> setOutputType(OutputType type) async {
    await _plugin.setOutputType(type);
  }

  /// スキャナーの中心読みを設定する
  Future<void> setCenteringWindow(CenteringWindowMode mode) async {
    await _plugin.setCenteringWindow(mode);
  }

  /// スキャナーのライトモードを設定する
  Future<void> setLightMode(LightMode mode) async {
    await _plugin.setLightMode(mode);
  }

  /// スキャナーのトリガーキーを設定する
  Future<void> setTriggerKeyEnable(TriggerKeyMode mode) async {
    await _plugin.setTriggerKeyEnable(mode);
  }

  /// スキャナーのサフィックスを設定する
  Future<void> setSuffix(SuffixType type) async {
    await _plugin.setSuffix(type);
  }

  /// スキャン結果を取得する
  Future<String> _getScanResult() async {
    final bytes = await _plugin.getScanResult();
    if (bytes == null) return '';

    /// 先頭と末尾の空白を削除します
    return utf8.decode(bytes, allowMalformed: true).trim();
  }

  /// スキャン結果を取得する
  Future<ScanResult?> getScanResultAsCllass() async {
    return _plugin.getScanResultAsClass();
  }

  /// スキャンを検知し、結果をストリームで返す
  Stream<TrialItemCode> scanBroadcast() => BroadcastReceiver.registerBroadcast(IntentFilter(actions: [_scanAction]))
      .asyncMap<String>((dataReceiver) async => _getScanResult())
      .where((e) => e.isNotEmpty)
      .map(TrialItemCode.fromString);
}
