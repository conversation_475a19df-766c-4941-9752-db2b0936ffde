// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pacer_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deviceInfoHash() => r'fbb2c5e05b84f8508a8789abe350845f3f595aac';

/// デバイス情報を提供する
///
/// Copied from [deviceInfo].
@ProviderFor(deviceInfo)
final deviceInfoProvider = FutureProvider<BaseDeviceInfo>.internal(
  deviceInfo,
  name: r'deviceInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deviceInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DeviceInfoRef = FutureProviderRef<BaseDeviceInfo>;
String _$packageInfoHash() => r'f7bd90882137aac7a9f28e9d10eae6efeee8c9d0';

/// パッケージ情報を提供する
///
/// Copied from [packageInfo].
@ProviderFor(packageInfo)
final packageInfoProvider = FutureProvider<PackageInfo>.internal(
  packageInfo,
  name: r'packageInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$packageInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PackageInfoRef = FutureProviderRef<PackageInfo>;
String _$directoryInfoHash() => r'07718a2cf5286880fc553890c2c9f727f637e473';

/// ディレクトリ情報を提供する
///
/// Copied from [directoryInfo].
@ProviderFor(directoryInfo)
final directoryInfoProvider = FutureProvider<Directory>.internal(
  directoryInfo,
  name: r'directoryInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$directoryInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DirectoryInfoRef = FutureProviderRef<Directory>;
String _$itgManagerHash() => r'e0f9a354eca17fdad97e9e036bf35bbbc37c2a52';

/// ITGのデバイス設定管理を提供
///
/// Copied from [itgManager].
@ProviderFor(itgManager)
final itgManagerProvider = Provider<ItgManager>.internal(
  itgManager,
  name: r'itgManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$itgManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ItgManagerRef = ProviderRef<ItgManager>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
