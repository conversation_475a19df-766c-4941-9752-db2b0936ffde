// todo: utamori UUIDでの検索も行う
import 'package:grpc/grpc.dart';
import 'package:shinise_core_client/device_management/v1/device_management.pbgrpc.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../../../constants/environment.dart';

Future<bool> scanditLicense(String phoneNumber) async {
  final uri = Env.getApiBaseUrl();

  final callOptions =
      CallOptions(timeout: const Duration(seconds: 20), metadata: {'certificate': 'temp', 'certificate_key': 'temp'});
  final channel = ClientChannel(uri.host, port: uri.port);

  final stub = DeviceManagementServiceClient(channel, options: callOptions);
  final req = GetDeviceRequest(phoneNumber: phoneNumber);
  Talker().info('scandit request: ${req.phoneNumber}');

  try {
    final resp = await stub.getDevice(req);
    Talker().info('scandit response: $resp');
    if (resp.hasEnableScanLicence() && resp.enableScanLicence) {
      return true;
    }
    return false;
  } catch (e) {
    return false;
  }
}
