// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'available_cameras.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$camerasHash() => r'9bce867df7ea362f2db2605e668acc3d1d5617db';

/// 利用可能なカメラのリスト
///
/// Copied from [cameras].
@ProviderFor(cameras)
final camerasProvider = FutureProvider<List<CameraDescription>>.internal(
  cameras,
  name: r'camerasProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$camerasHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CamerasRef = FutureProviderRef<List<CameraDescription>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
