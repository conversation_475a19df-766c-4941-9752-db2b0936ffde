// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favorite_app_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$favoriteAppServiceHash() => r'4593c9d3e737b8949af2f58d88a62bc8931be922';

/// お気に入りアプリサービス
///
/// Copied from [favoriteAppService].
@ProviderFor(favoriteAppService)
final favoriteAppServiceProvider = Provider<FavoriteAppService>.internal(
  favoriteAppService,
  name: r'favoriteAppServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$favoriteAppServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FavoriteAppServiceRef = ProviderRef<FavoriteAppService>;
String _$favoriteAppsListStreamHash() => r'220574c34e546bb035fdecc902495c77c5abf327';

/// お気に入りアプリの一覧ストリーム
///
/// Copied from [favoriteAppsListStream].
@ProviderFor(favoriteAppsListStream)
final favoriteAppsListStreamProvider = AutoDisposeStreamProvider<List<InApp>>.internal(
  favoriteAppsListStream,
  name: r'favoriteAppsListStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$favoriteAppsListStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FavoriteAppsListStreamRef = AutoDisposeStreamProviderRef<List<InApp>>;
String _$validInAppsHash() => r'abdcc62af8548696ff56cc518f8b1000719e5bf5';

/// 有効なミニアプリ一覧
/// - 客注を停止している店舗では、客注を除外する
/// - 処分方法調査はすべての店舗で利用可能
///
/// Copied from [validInApps].
@ProviderFor(validInApps)
final validInAppsProvider = AutoDisposeProvider<List<InApp>>.internal(
  validInApps,
  name: r'validInAppsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$validInAppsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ValidInAppsRef = AutoDisposeProviderRef<List<InApp>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
