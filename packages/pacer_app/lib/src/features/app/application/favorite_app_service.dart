import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../authentication/data/auth_repository.dart';
import '../data/favorite_app_repository.dart';
import '../domain/miniapp.dart';

part 'favorite_app_service.g.dart';

/// お気に入りアプリサービス
@Riverpod(keepAlive: true)
FavoriteAppService favoriteAppService(FavoriteAppServiceRef ref) {
  return FavoriteAppService(ref);
}

/// お気に入りアプリサービス
class FavoriteAppService {
  //// init
  FavoriteAppService(this.ref);

  /// ref
  final Ref ref;

  /// お気に入りアプリ追加
  Future<void> add({required MiniAppID id}) async {
    log('アプリ追加開始');

    final user = ref.read(authRepositoryProvider).currentUser;
    if (user == null) return;

    log('アプリ追加開始');

    await ref.read(favoriteAppRepositoryProvider).create(userCode: user.userCode, id: id);
  }

  /// お気に入りアプリ削除
  Future<void> delete({required MiniAppID id}) async {
    final user = ref.read(authRepositoryProvider).currentUser;
    if (user == null) return;

    await ref.read(favoriteAppRepositoryProvider).delete(userCode: user.userCode, id: id);
  }
}

/// お気に入りアプリの一覧ストリーム
@Riverpod()
Stream<List<InApp>> favoriteAppsListStream(FavoriteAppsListStreamRef ref) {
  final caller = ref.read(authRepositoryProvider).currentUser;
  if (caller == null) return const Stream.empty();

  final favoriteAppRepository = ref.watch(favoriteAppRepositoryProvider);

  return favoriteAppRepository.watchList(userCode: caller.userCode);
}

/// 有効なミニアプリ一覧
/// - 客注を停止している店舗では、客注を除外する
/// - 処分方法調査はすべての店舗で利用可能
@riverpod
List<InApp> validInApps(ValidInAppsRef ref) {
  // TODO: 未ログイン状態ではアプリを使用させない。if(currentUser == null) return [];

  final storeCode = ref.read(authRepositoryProvider).currentUser?.clockInStore.code;

  // 特定の店舗は発注アプリだけを有効化する。
  const orderOnlyStoreCodes = {'960'};
  if (orderOnlyStoreCodes.contains(storeCode)) {
    return [InApp.order];
  }

  // 以下の展開店舗はSetで定義する。
  // Setは順序保証されないが、ここでは順序は重要ではない。
  // 重複の排除と検索速度を重視して、ここではSetを使用する。

  /// 客注発注を利用する店舗
  const useCustomerOrderStoreCodes = {
    '5',
    '6',
    '10',
    '44',
    '50',
    '57',
    '62',
    '67',
    '78',
    '135',
    '178',
    '269',
  };

  const app = InApp.values;

  /// 客注を利用する店舗だけ、有効化
  final apps = switch (useCustomerOrderStoreCodes.contains(storeCode)) {
    false => app.whereNot((e) => e == InApp.customerOrder).toList(),
    true => app,
  };

  return apps;
}
