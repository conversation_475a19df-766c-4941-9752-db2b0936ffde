// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'global_loading_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$globalLoadingServiceHash() => r'53dc5d3df47dd5a93891ea174e2d55c94692ec03';

/// アプリ全体でのローディング状態を管理します
/// これがtrueの間、グローバルローディングインジケーターが表示されます
///
/// Copied from [GlobalLoadingService].
@ProviderFor(GlobalLoadingService)
final globalLoadingServiceProvider = AutoDisposeNotifierProvider<GlobalLoadingService, bool>.internal(
  GlobalLoadingService.new,
  name: r'globalLoadingServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$globalLoadingServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GlobalLoadingService = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
