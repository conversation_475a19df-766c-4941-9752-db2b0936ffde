import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../routing/app_router.dart';
import '../../../scandit_enable.dart';
import '../../acceptance_inspection/presentation/routing/acceptance_inspection_route.dart';
import '../../adjust_inventory/presentation/routing/adjust_inventory_route.dart';
import '../../check_expiry/presentation/routing/expiry_route.dart';
import '../../check_locality/presentation/routing/check_locality_route.dart';
import '../../consumable_adjust/presentation/routing/consumable_adjust_route.dart';
import '../../delivery_slip/presentation/routing/delivery_slip_route.dart';
import '../../device/presentation/routing/device_routing.dart';
import '../../face_photo/employee_photo/presentation/routing/employee_route.dart';
import '../../fixed_assets/presentation/routing/fixed_asset_route.dart';
import '../../freon_check/presentation/routing/freon_route.dart';
import '../../fresh_order/presentation/routing/fresh_order_route.dart';
import '../../fresh_sale/presentation/routing/fresh_sale_route.dart';
import '../../heap_order/presentation/routing/heap_order_route.dart';
import '../../inspection_scan/presentation/inspection_scan_route.dart';
import '../../instruction_single/presentation/routing/instruction_single_route.dart';
import '../../lost_item/routing.dart';
import '../../material_transfer/presentation/routing/material_transfer_route.dart';
import '../../missing_item/presentation/routing/missing_item_route.dart';
import '../../operate_instruction/presentation/operate_instruction_route.dart';
import '../../order/presentation/routing/order_route.dart';
import '../../out_of_stock/presentation/routing/out_of_stock_routing.dart';
import '../../pairing/presentation/route/pairing_route.dart';
import '../../pop/presentation/routing/pop_route.dart';
import '../../pos_distribution/presentation/routing/pos_distribution_routing.dart';
import '../../price_alter/presentation/routing/price_alter_routing.dart';
import '../../print_discount/presentation/routing/discount_route.dart';
import '../../print_shelf_label/presentation/routing/routing.dart';
import '../../rescue_robot/presentation/routing/rescue_robot_route.dart';
import '../../reverse/presentation/routing/reverse_route.dart';
import '../../scrap/routing/scrap_route.dart';
import '../../search_product/routing/search_product_route.dart';
import '../../searching_disporsal_method/presentation/routing/searching_disporsal_method_route.dart';
import '../../shelf_scan/presentation/routing/shelf_scan_route.dart';
import '../../stocktake/top/ui/widgets/routing/top_routing.dart';
import '../../stocktake_fresh/presentation/routing/fresh_stocktake_routing.dart';
import '../../withdrawal/presentation/routing/withdrawal_route.dart';

/// ミニアプリの名前
typedef MiniAppID = String;

/// 発注、棚卸などのミニアプリ
class MiniApp extends Equatable {
  /// init
  const MiniApp({
    required this.id,
  });

  /// JSONからミニアプリを生成
  factory MiniApp.fromMap(Map<String, dynamic> map) {
    return MiniApp(
      id: map['id'] as String,
    );
  }

  /// ミニアプリID
  final MiniAppID id;

  /// Mapに変換
  Map<String, dynamic> toMap() => {'id': id};

  /// ミニアプリのコピーを生成
  MiniApp copyWith({
    MiniAppID? id,
    String? title,
    String? description,
  }) {
    return MiniApp(id: id ?? this.id);
  }

  @override
  List<Object?> get props => [id];

  @override
  bool? get stringify => true;
}

/// ミニアプリのカテゴリー一覧
enum InAppCategory {
  /// お気に入り
  fav(name: 'お気に入り'),

  /// 発注カテゴリ
  order(name: '発注'),

  /// 検収カテゴリ
  check(name: '検収'),

  /// 商品管理カテゴリ
  item(name: '商品管理'),

  /// 検索カテゴリ
  search(name: '検索'),

  /// 棚卸カテゴリ
  inventory(name: '棚卸'),

  /// デジタル棚札カテゴリ
  digitalPriceTag(name: '電子棚札'),

  /// マネジメントカテゴリ
  management(name: 'マネジメント'),

  /// メンテナンスカテゴリ
  maintenance(name: 'メンテナンス');

  const InAppCategory({required this.name});

  /// カテゴリー名
  final String name;
}

/// ミニアプリの一覧
/// 遷移メソッドも持った方がいいか
enum InApp {
  /// 店舗端末利用履歴
  device(
    title: '店舗端末利用履歴',
    description: '',
    iconPath: 'assets/feature_icon/device_management.png',
    category: InAppCategory.management,
  ),

  /// 忘れ物
  lostItem(
    title: '忘れ物管理',
    description: '',
    iconPath: 'assets/feature_icon/lost_finder.png',
    category: InAppCategory.management,
  ),

  /// 棚卸
  stocktake(
    title: '棚卸',
    description: '',
    iconPath: 'assets/feature_icon/take_inventory.png',
    category: InAppCategory.inventory,
  ),

  /// 生鮮棚卸
  freshStocktake(
    title: '生鮮棚卸',
    description: '',
    iconPath: 'assets/feature_icon/fresh_take_inventory.png',
    category: InAppCategory.inventory,
  ),

  /// 生鮮棚卸 監査用
  freshStocktakeByAudit(
    title: '生鮮棚卸 監査',
    description: '',
    iconPath: 'assets/feature_icon/fresh_take_inventory.png',
    category: InAppCategory.inventory,
  ),

  /// 検品
  inspection(
    title: '検品',
    description: '',
    iconPath: 'assets/feature_icon/inspection.png',
    category: InAppCategory.check,
  ),

  /// 出庫
  withdrawal(
    title: '出庫',
    description: '',
    iconPath: 'assets/feature_icon/withdrawal.png',
    category: InAppCategory.check,
  ),

  /// 棚取
  shelfScan(
    title: '棚取',
    description: '',
    iconPath: 'assets/feature_icon/scan_shelf.png',
    category: InAppCategory.item,
  ),

  /// レスキューロボ
  rescueRobot(
    title: 'レスキューロボ',
    description: 'レスキューロボ',
    iconPath: 'assets/feature_icon/rescue_robo.png',
    category: InAppCategory.item,
  ),

  /// 廃棄
  scrap(
    title: '廃棄',
    description: '',
    iconPath: 'assets/feature_icon/waste.png',
    category: InAppCategory.check,
  ),

  /// 発注
  order(
    title: '商品発注',
    description: '',
    iconPath: 'assets/feature_icon/order.png',
    category: InAppCategory.order,
  ),

  /// お客様発注
  customerOrder(
    title: '客注発注',
    description: 'お客様の注文に基づいて発注を行います',
    iconPath: 'assets/feature_icon/order.png',
    category: InAppCategory.order,
  ),

  /// 生鮮発注
  freshOrder(
    title: '生鮮発注',
    description: '',
    iconPath: 'assets/feature_icon/fresh_order.png',
    category: InAppCategory.order,
  ),

  /// プロモ山積設定
  heapOrder(
    title: 'プロモ山積設定',
    description: 'エンド・ミッドの商品を登録し（プロモーション商品の登録）、自動発注の計算に利用されます',
    iconPath: 'assets/feature_icon/pileup.png',
    category: InAppCategory.order,
  ),

  /// 棚ラベル印刷
  shelfLabelPrint(
    title: '棚ラベル印刷',
    description: '',
    iconPath: 'assets/feature_icon/tana.png',
    category: InAppCategory.item,
  ),

  /// POP印刷
  popPrint(
    title: 'POP印刷',
    description: '',
    iconPath: 'assets/feature_icon/pop.png',
    category: InAppCategory.item,
  ),

  /// 売価変更
  priceAlter(
    title: '売価変更',
    description: '',
    iconPath: 'assets/feature_icon/price_alter.png',
    category: InAppCategory.item,
  ),

  /// 値下ラベル
  discountLabel(
    title: '値下ラベル',
    description: '',
    iconPath: 'assets/feature_icon/discount_label.png',
    category: InAppCategory.item,
  ),

  /// 賞味期限チェック
  checkExpiry(
    title: '賞味期限チェック',
    description: '',
    iconPath: 'assets/feature_icon/expiry.png',
    category: InAppCategory.item,
  ),

  /// 納品伝票
  deliverySlip(
    title: '納品伝票',
    description: '納品伝票の登録、印刷を行う。納品する商品情報の入力や修正、過去に登録した納品伝票の再発行もこちらから可能。',
    iconPath: 'assets/feature_icon/delivery_slip.png',
    category: InAppCategory.check,
  ),

  /// 返品
  reverse(
    title: '返品',
    description: '返品データ登録作業、発行された返品伝票がなくなった場合、再発行を行うことです',
    iconPath: 'assets/feature_icon/returns.png',
    category: InAppCategory.check,
  ),

  /// 在庫調整
  adjustInventory(
    title: '在庫調整',
    description: '',
    iconPath: 'assets/feature_icon/adjust_inventory.png',
    category: InAppCategory.order,
  ),

  /// 単品指示
  instructionSingle(
    title: '単品指示',
    description: '',
    iconPath: 'assets/feature_icon/instruction_single.png',
    category: InAppCategory.search,
  ),

  /// 消耗品振替
  consumableAdjust(
    title: '消耗品振替',
    description: '',
    iconPath: 'assets/feature_icon/transfer_expendables.png',
    category: InAppCategory.order,
  ),

  /// 商品検索
  searchProduct(
    title: '商品検索',
    description: '',
    iconPath: 'assets/feature_icon/search.png',
    category: InAppCategory.search,
  ),

  /// 顔写真
  employee(
    title: '顔写真',
    description: '',
    iconPath: 'assets/feature_icon/face_photo.png',
    category: InAppCategory.management,
  ),

  /// 原材料振替
  materialTransfer(
    title: '原材料振替',
    description: '',
    iconPath: 'assets/feature_icon/transfer_raw_materials.png',
    category: InAppCategory.order,
  ),

  /// 固定資産実査
  fixedAsset(
    title: '固定資産実査',
    description: '',
    iconPath: 'assets/feature_icon/fixed_assets.png',
    category: InAppCategory.inventory,
  ),

  /// ペアリング
  pairing(
    title: 'ペアリング',
    description: '',
    iconPath: 'assets/feature_icon/pairing.png',
    category: InAppCategory.digitalPriceTag,
  ),

  /// 産地チェック
  checkLocality(
    title: '産地チェック',
    description: '',
    iconPath: 'assets/feature_icon/checkLocation.png',
    category: InAppCategory.digitalPriceTag,
  ),

  /// 新店欠品調査
  outOfStockInvestigation(
    title: '新店欠品調査',
    description: '',
    iconPath: 'assets/feature_icon/inveset_stockout.png',
    category: InAppCategory.item,
  ),

  /// 生鮮売変
  freshSale(
    title: '生鮮売価変更',
    description: '',
    iconPath: 'assets/feature_icon/price_alter.png',
    category: InAppCategory.item,
  ),

  /// 緊急POS配信
  posDistribution(
    title: '緊急POS配信',
    description: '',
    iconPath: 'assets/feature_icon/pos.png',
    category: InAppCategory.maintenance,
  ),

  /// フロン簡易点検
  simpleInspection(
    title: 'フロン簡易点検',
    description: '',
    iconPath: 'assets/feature_icon/photo_report.png',
    category: InAppCategory.management,
  ),

  /// 欠品スキャンAPP
  missingItem(
    title: '欠品スキャン',
    description: '',
    iconPath: 'assets/feature_icon/inveset_stockout.png',
    category: InAppCategory.item,
    // 開発完了しているが運用中止のため非表示。
    isHidden: true,
  ),

  /// 処分方法調査APP
  searchingDisposalMethod(
    title: '処分方法調査',
    description: '',
    iconPath: 'assets/feature_icon/inveset_stockout.png',
    category: InAppCategory.search,
  ),

  /// 作業完了報告APP
  completionReport(
    title: '作業完了報告',
    description: '',
    iconPath: 'assets/feature_icon/task_complete_report.png',
    category: InAppCategory.management,
  ),

  /// 検品タッチスキャンAPP
  inspectionScan(
    title: '到着確認',
    description: '',
    iconPath: 'assets/feature_icon/ship_decision.png',
    category: InAppCategory.check,
    isHidden: false,
  );

  const InApp({
    required this.iconPath,
    required this.title,
    required this.description,
    required this.category,
    this.isHidden = false,
  });

  /// アイコン画像へのパス
  final String iconPath;

  /// 名前
  final String title;

  /// 説明
  final String description;

  /// カテゴリー
  final InAppCategory category;

  /// 本番環境では隠したい場合はtrueにする
  /// 開発中、バグ修正中など
  final bool isHidden;

  /// ミニアプリごとに遷移メソッドを返却
  void Function()? onTap(BuildContext context) => switch (this) {
        /// スキャンなし
        /// sparkScan無効化
        InApp.lostItem => () {
            ProviderScope.containerOf(context).read(scanditEnableProvider.notifier).disable();
            const LostItemRoute().go(context);
          },
        InApp.employee => () {
            ProviderScope.containerOf(context).read(scanditEnableProvider.notifier).disable();
            const EmployeeRoute().go(context);
          },
        InApp.simpleInspection => () {
            ProviderScope.containerOf(context).read(scanditEnableProvider.notifier).disable();
            const FreonSelectLocationRoute().go(context);
          },
        InApp.device => () {
            ProviderScope.containerOf(context).read(scanditEnableProvider.notifier).disable();
            const DeviceUsageRoute().go(context);
          },

        /// スキャンあり
        /// sparkScan有効化
        InApp.stocktake => () => const StocktakeTopRoute().go(context),
        InApp.freshStocktake => () => const FreshStocktakeMenuRoute().go(context),
        InApp.freshStocktakeByAudit => () => const FreshStocktakeMenuRoute(isAudit: true).go(context),
        InApp.inspection => () => const InspectionMenuRoute().go(context),
        InApp.order => () => const OrderRoute().go(context),
        InApp.freshOrder => () => const FreshOrderRoute().go(context),
        InApp.customerOrder => () => const AheadOrderRoute().go(context),
        InApp.shelfLabelPrint => () => const NewLabelJobRoute().go(context),
        InApp.popPrint => () => const PopMenuRoute().go(context),
        InApp.priceAlter => () => const PriceAlterLinesRoute().go(context),
        InApp.scrap => () => const ScrapHomeRoute().go(context),
        InApp.shelfScan => () => const ShelfScanSettingRoute().go(context),
        InApp.discountLabel => () => const SetPrinterRoute().go(context),
        InApp.checkExpiry => () => const ExpiryTopRoute().go(context),
        InApp.deliverySlip => () => DeliverySlipRoute().go(context),
        InApp.heapOrder => () => const HeapOrderRoute().go(context),
        InApp.reverse => () => const ReverseMenuRoute().go(context),
        InApp.withdrawal => () => const WithdrawalMenuRoute().go(context),
        InApp.adjustInventory => () => const AdjustInventoryRoute().go(context),
        InApp.instructionSingle => () => const InstructionSingleTopRoute().go(context),
        InApp.consumableAdjust => () => const ConsumableAdjustRoute().go(context),
        InApp.searchProduct => () => const SearchProductRoute().go(context),
        InApp.fixedAsset => () => const FixedAssetsInspectionRoute().go(context),
        InApp.materialTransfer => () => const MaterialTransferDestinationRoute().go(context),
        InApp.rescueRobot => () => const RescueRobotRoute().go(context),
        InApp.checkLocality => () => const CheckLocalityTopPageRoute().go(context),
        InApp.outOfStockInvestigation => () => const OutOfStockInvestigationRoute().go(context),
        InApp.posDistribution => () => const PosDistributionRoute().go(context),
        InApp.freshSale => () => const FreshSaleMenuRoute().go(context),
        InApp.pairing => () => const PairingMenuRoute().go(context),

        /// 欠品
        InApp.missingItem => () => const MissingItemMenuRoute().go(context),
        InApp.searchingDisposalMethod => () => const SearchingDisposalMethodRoute().go(context),
        InApp.completionReport => () => const OperateInstructionRoute().go(context),
        InApp.inspectionScan => () => const InspectionScanRoute().go(context),
      };
}
