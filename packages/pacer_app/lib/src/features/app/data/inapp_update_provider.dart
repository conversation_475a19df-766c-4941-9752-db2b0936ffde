/// アプリ内アップデートを通知するProvider
library;

import 'dart:async';

import 'package:in_app_update/in_app_update.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'inapp_update_provider.g.dart';

/// アップデート可能かチェックするプロバイダー
/// 1時間ごとに再チェックする
@riverpod
FutureOr<AppUpdateInfo> inappUpdate(InappUpdateRef ref) {
  const duration = Duration(hours: 1);

  final timer = Timer(duration, () => ref.invalidateSelf());

  ref.onDispose(timer.cancel);

  return InAppUpdate.checkForUpdate();
}
