// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favorite_app_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$favoriteAppRepositoryHash() => r'47b93df9d07168a5588f9526f3f81aa65767e0b0';

/// See also [favoriteAppRepository].
@ProviderFor(favoriteAppRepository)
final favoriteAppRepositoryProvider = Provider<FavoriteAppRepository>.internal(
  favoriteAppRepository,
  name: r'favoriteAppRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$favoriteAppRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FavoriteAppRepositoryRef = ProviderRef<FavoriteAppRepository>;
String _$favoriteAppsListStreamHash() => r'46bdd8c7d1a7265b7c458ea393ef34ae6e0d25c0';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [favoriteAppsListStream].
@ProviderFor(favoriteAppsListStream)
const favoriteAppsListStreamProvider = FavoriteAppsListStreamFamily();

/// See also [favoriteAppsListStream].
class FavoriteAppsListStreamFamily extends Family {
  /// See also [favoriteAppsListStream].
  const FavoriteAppsListStreamFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'favoriteAppsListStreamProvider';

  /// See also [favoriteAppsListStream].
  FavoriteAppsListStreamProvider call({
    required String userCode,
  }) {
    return FavoriteAppsListStreamProvider(
      userCode: userCode,
    );
  }

  @visibleForOverriding
  @override
  FavoriteAppsListStreamProvider getProviderOverride(
    covariant FavoriteAppsListStreamProvider provider,
  ) {
    return call(
      userCode: provider.userCode,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(Stream<List<InApp>> Function(FavoriteAppsListStreamRef ref) create) {
    return _$FavoriteAppsListStreamFamilyOverride(this, create);
  }
}

class _$FavoriteAppsListStreamFamilyOverride implements FamilyOverride {
  _$FavoriteAppsListStreamFamilyOverride(this.overriddenFamily, this.create);

  final Stream<List<InApp>> Function(FavoriteAppsListStreamRef ref) create;

  @override
  final FavoriteAppsListStreamFamily overriddenFamily;

  @override
  FavoriteAppsListStreamProvider getProviderOverride(
    covariant FavoriteAppsListStreamProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// See also [favoriteAppsListStream].
class FavoriteAppsListStreamProvider extends AutoDisposeStreamProvider<List<InApp>> {
  /// See also [favoriteAppsListStream].
  FavoriteAppsListStreamProvider({
    required String userCode,
  }) : this._internal(
          (ref) => favoriteAppsListStream(
            ref as FavoriteAppsListStreamRef,
            userCode: userCode,
          ),
          from: favoriteAppsListStreamProvider,
          name: r'favoriteAppsListStreamProvider',
          debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$favoriteAppsListStreamHash,
          dependencies: FavoriteAppsListStreamFamily._dependencies,
          allTransitiveDependencies: FavoriteAppsListStreamFamily._allTransitiveDependencies,
          userCode: userCode,
        );

  FavoriteAppsListStreamProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userCode,
  }) : super.internal();

  final String userCode;

  @override
  Override overrideWith(
    Stream<List<InApp>> Function(FavoriteAppsListStreamRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FavoriteAppsListStreamProvider._internal(
        (ref) => create(ref as FavoriteAppsListStreamRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userCode: userCode,
      ),
    );
  }

  @override
  ({
    String userCode,
  }) get argument {
    return (userCode: userCode,);
  }

  @override
  AutoDisposeStreamProviderElement<List<InApp>> createElement() {
    return _FavoriteAppsListStreamProviderElement(this);
  }

  FavoriteAppsListStreamProvider _copyWith(
    Stream<List<InApp>> Function(FavoriteAppsListStreamRef ref) create,
  ) {
    return FavoriteAppsListStreamProvider._internal(
      (ref) => create(ref as FavoriteAppsListStreamRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      userCode: userCode,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is FavoriteAppsListStreamProvider && other.userCode == userCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FavoriteAppsListStreamRef on AutoDisposeStreamProviderRef<List<InApp>> {
  /// The parameter `userCode` of this provider.
  String get userCode;
}

class _FavoriteAppsListStreamProviderElement extends AutoDisposeStreamProviderElement<List<InApp>>
    with FavoriteAppsListStreamRef {
  _FavoriteAppsListStreamProviderElement(super.provider);

  @override
  String get userCode => (origin as FavoriteAppsListStreamProvider).userCode;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
