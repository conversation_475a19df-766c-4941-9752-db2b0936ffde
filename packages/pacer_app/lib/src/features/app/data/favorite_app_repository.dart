// ignore_for_file: public_member_api_docs

import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../domain/miniapp.dart';

part 'favorite_app_repository.g.dart';

/// お気に入りミニアプリをFirestoreで管理する
class FavoriteAppRepository {
  const FavoriteAppRepository(this._firestore);
  final FirebaseFirestore _firestore;

  static String favoriteAppsPath({
    required String userCode,
  }) =>
      'users/$userCode/favoriteApps';

  static String favoriteAppPath({
    required String userCode,
    required MiniAppID id,
  }) =>
      '${favoriteAppsPath(userCode: userCode)}/$id';

  Stream<List<InApp>> watchList({required String userCode}) {
    final ref = _favoriteAppsRef(userCode: userCode);

    return ref.snapshots().map(
          (snapshot) => snapshot.docs
              .map((docSnapshot) => docSnapshot.data())

              /// InApp Enumに含まれていないアプリは無視する
              .map((app) {
                try {
                  return InApp.values.byName(app.id);
                } catch (e) {
                  return null;
                }
              })
              .nonNulls
              .toList(),
        );
  }

  Future<void> create({
    required String userCode,
    required MiniAppID id,
  }) {
    return _firestore.doc(favoriteAppPath(userCode: userCode, id: id)).set(
      {
        'id': id,
        'createdAt': FieldValue.serverTimestamp(),
      },
      // use merge: true to keep old fields (if any)
      SetOptions(merge: true),
    );
  }

  Future<void> delete({
    required String userCode,
    required MiniAppID id,
  }) {
    return _firestore.doc(favoriteAppPath(userCode: userCode, id: id)).delete();
  }

  Query<MiniApp> _favoriteAppsRef({required String userCode}) => _firestore
      .collection(favoriteAppsPath(userCode: userCode))
      .withConverter(
        fromFirestore: (doc, _) {
          final data = doc.data();
          if (data != null) {
            return MiniApp.fromMap(data);
          } else {
            throw Exception('Document data is null');
          }
        },
        toFirestore: (MiniApp app, options) => app.toMap(),
      )
      .orderBy('createdAt', descending: false);
}

@Riverpod(keepAlive: true)
FavoriteAppRepository favoriteAppRepository(FavoriteAppRepositoryRef ref) {
  return FavoriteAppRepository(FirebaseFirestore.instance);
}

@riverpod
Stream<List<InApp>> favoriteAppsListStream(
  FavoriteAppsListStreamRef ref, {
  required String userCode,
}) {
  final favoriteAppRepository = ref.watch(favoriteAppRepositoryProvider);

  return favoriteAppRepository.watchList(userCode: userCode);
}
