// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inapp_update_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$inappUpdateHash() => r'b62342bfc91c42e65bd3e8e1f19f5a6e4c888db0';

/// アップデート可能かチェックするプロバイダー
/// 1時間ごとに再チェックする
///
/// Copied from [inappUpdate].
@ProviderFor(inappUpdate)
final inappUpdateProvider = AutoDisposeFutureProvider<AppUpdateInfo>.internal(
  inappUpdate,
  name: r'inappUpdateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inappUpdateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef InappUpdateRef = AutoDisposeFutureProviderRef<AppUpdateInfo>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
