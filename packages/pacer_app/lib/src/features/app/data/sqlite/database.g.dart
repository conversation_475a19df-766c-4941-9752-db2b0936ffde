// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $FreshStocktakeLinesTable extends FreshStocktakeLines
    with TableInfo<$FreshStocktakeLinesTable, FreshStocktakeLine> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $FreshStocktakeLinesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _codeMeta = const VerificationMeta('code');
  @override
  late final GeneratedColumn<int> code =
      GeneratedColumn<int>('code', aliasedName, false, type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name =
      GeneratedColumn<String>('name', aliasedName, false, type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [code, name];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'fresh_stocktake_lines';
  @override
  VerificationContext validateIntegrity(Insertable<FreshStocktakeLine> instance, {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('code')) {
      context.handle(_codeMeta, code.isAcceptableOrUnknown(data['code']!, _codeMeta));
    } else if (isInserting) {
      context.missing(_codeMeta);
    }
    if (data.containsKey('name')) {
      context.handle(_nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => const {};
  @override
  FreshStocktakeLine map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return FreshStocktakeLine(
      code: attachedDatabase.typeMapping.read(DriftSqlType.int, data['${effectivePrefix}code'])!,
      name: attachedDatabase.typeMapping.read(DriftSqlType.string, data['${effectivePrefix}name'])!,
    );
  }

  @override
  $FreshStocktakeLinesTable createAlias(String alias) {
    return $FreshStocktakeLinesTable(attachedDatabase, alias);
  }
}

class FreshStocktakeLine extends DataClass implements Insertable<FreshStocktakeLine> {
  /// Line code
  final int code;

  /// Line name
  final String name;
  const FreshStocktakeLine({required this.code, required this.name});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['code'] = Variable<int>(code);
    map['name'] = Variable<String>(name);
    return map;
  }

  FreshStocktakeLinesCompanion toCompanion(bool nullToAbsent) {
    return FreshStocktakeLinesCompanion(
      code: Value(code),
      name: Value(name),
    );
  }

  factory FreshStocktakeLine.fromJson(Map<String, dynamic> json, {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return FreshStocktakeLine(
      code: serializer.fromJson<int>(json['code']),
      name: serializer.fromJson<String>(json['name']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'code': serializer.toJson<int>(code),
      'name': serializer.toJson<String>(name),
    };
  }

  FreshStocktakeLine copyWith({int? code, String? name}) => FreshStocktakeLine(
        code: code ?? this.code,
        name: name ?? this.name,
      );
  FreshStocktakeLine copyWithCompanion(FreshStocktakeLinesCompanion data) {
    return FreshStocktakeLine(
      code: data.code.present ? data.code.value : this.code,
      name: data.name.present ? data.name.value : this.name,
    );
  }

  @override
  String toString() {
    return (StringBuffer('FreshStocktakeLine(')
          ..write('code: $code, ')
          ..write('name: $name')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(code, name);
  @override
  bool operator ==(Object other) =>
      identical(this, other) || (other is FreshStocktakeLine && other.code == this.code && other.name == this.name);
}

class FreshStocktakeLinesCompanion extends UpdateCompanion<FreshStocktakeLine> {
  final Value<int> code;
  final Value<String> name;
  final Value<int> rowid;
  const FreshStocktakeLinesCompanion({
    this.code = const Value.absent(),
    this.name = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  FreshStocktakeLinesCompanion.insert({
    required int code,
    required String name,
    this.rowid = const Value.absent(),
  })  : code = Value(code),
        name = Value(name);
  static Insertable<FreshStocktakeLine> custom({
    Expression<int>? code,
    Expression<String>? name,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (code != null) 'code': code,
      if (name != null) 'name': name,
      if (rowid != null) 'rowid': rowid,
    });
  }

  FreshStocktakeLinesCompanion copyWith({Value<int>? code, Value<String>? name, Value<int>? rowid}) {
    return FreshStocktakeLinesCompanion(
      code: code ?? this.code,
      name: name ?? this.name,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (code.present) {
      map['code'] = Variable<int>(code.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('FreshStocktakeLinesCompanion(')
          ..write('code: $code, ')
          ..write('name: $name, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $FreshStocktakeLinesTable freshStocktakeLines = $FreshStocktakeLinesTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables => allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [freshStocktakeLines];
  @override
  DriftDatabaseOptions get options => const DriftDatabaseOptions(storeDateTimeAsText: true);
}

typedef $$FreshStocktakeLinesTableCreateCompanionBuilder = FreshStocktakeLinesCompanion Function({
  required int code,
  required String name,
  Value<int> rowid,
});
typedef $$FreshStocktakeLinesTableUpdateCompanionBuilder = FreshStocktakeLinesCompanion Function({
  Value<int> code,
  Value<String> name,
  Value<int> rowid,
});

class $$FreshStocktakeLinesTableFilterComposer extends Composer<_$AppDatabase, $FreshStocktakeLinesTable> {
  $$FreshStocktakeLinesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get code => $composableBuilder(column: $table.code, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(column: $table.name, builder: (column) => ColumnFilters(column));
}

class $$FreshStocktakeLinesTableOrderingComposer extends Composer<_$AppDatabase, $FreshStocktakeLinesTable> {
  $$FreshStocktakeLinesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get code =>
      $composableBuilder(column: $table.code, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => ColumnOrderings(column));
}

class $$FreshStocktakeLinesTableAnnotationComposer extends Composer<_$AppDatabase, $FreshStocktakeLinesTable> {
  $$FreshStocktakeLinesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get code => $composableBuilder(column: $table.code, builder: (column) => column);

  GeneratedColumn<String> get name => $composableBuilder(column: $table.name, builder: (column) => column);
}

class $$FreshStocktakeLinesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $FreshStocktakeLinesTable,
    FreshStocktakeLine,
    $$FreshStocktakeLinesTableFilterComposer,
    $$FreshStocktakeLinesTableOrderingComposer,
    $$FreshStocktakeLinesTableAnnotationComposer,
    $$FreshStocktakeLinesTableCreateCompanionBuilder,
    $$FreshStocktakeLinesTableUpdateCompanionBuilder,
    (FreshStocktakeLine, BaseReferences<_$AppDatabase, $FreshStocktakeLinesTable, FreshStocktakeLine>),
    FreshStocktakeLine,
    PrefetchHooks Function()> {
  $$FreshStocktakeLinesTableTableManager(_$AppDatabase db, $FreshStocktakeLinesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () => $$FreshStocktakeLinesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () => $$FreshStocktakeLinesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () => $$FreshStocktakeLinesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> code = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              FreshStocktakeLinesCompanion(
            code: code,
            name: name,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required int code,
            required String name,
            Value<int> rowid = const Value.absent(),
          }) =>
              FreshStocktakeLinesCompanion.insert(
            code: code,
            name: name,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0.map((e) => (e.readTable(table), BaseReferences(db, table, e))).toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$FreshStocktakeLinesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $FreshStocktakeLinesTable,
    FreshStocktakeLine,
    $$FreshStocktakeLinesTableFilterComposer,
    $$FreshStocktakeLinesTableOrderingComposer,
    $$FreshStocktakeLinesTableAnnotationComposer,
    $$FreshStocktakeLinesTableCreateCompanionBuilder,
    $$FreshStocktakeLinesTableUpdateCompanionBuilder,
    (FreshStocktakeLine, BaseReferences<_$AppDatabase, $FreshStocktakeLinesTable, FreshStocktakeLine>),
    FreshStocktakeLine,
    PrefetchHooks Function()>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$FreshStocktakeLinesTableTableManager get freshStocktakeLines =>
      $$FreshStocktakeLinesTableTableManager(_db, _db.freshStocktakeLines);
}
