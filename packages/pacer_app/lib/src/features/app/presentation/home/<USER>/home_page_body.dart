import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_adaptive_scaffold/flutter_adaptive_scaffold.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../../../constants/environment.dart';
import '../../../application/favorite_app_service.dart';
import '../../../domain/miniapp.dart';

/// ミニアプリ一覧画面のbody
/// カテゴリ一覧タブ、
/// お気に入り部分と一覧部分に別れている
/// 一覧部分で追加アクションを行うと、お気に入り部分にミニアプリが固定される
class HomePageBody extends ConsumerStatefulWidget {
  /// init
  const HomePageBody({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomePageBodyState();
}

class _HomePageBodyState extends ConsumerState<HomePageBody> with TickerProviderStateMixin {
  /// カテゴリタブを操作するコントローラー
  late final _tabController = TabController(length: InAppCategory.values.length, vsync: this);

  /// スクロールを司るコントローラ
  late final _itemScrollController = ItemScrollController();

  /// スクロール中のインデックスを司るリスナー
  late final _itemPositionsListener = ItemPositionsListener.create()..itemPositions.addListener(_itemPositionsCallback);

  /// スクロールが発生したら、それに応じてタブを切り替える
  void _itemPositionsCallback() {
    final firstIndex = _itemPositionsListener.itemPositions.value.first.index;
    _tabController.animateTo(firstIndex);
  }

  /// カテゴリタブをタップしたら、そのカテゴリの位置にスクロールする
  void _onTabTap(int index) {
    _itemScrollController.scrollTo(
      index: index,
      duration: const Duration(milliseconds: 500),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final titleMedium = Theme.of(context).textTheme.titleMedium;

    final favoriteAppsStream = ref.watch(favoriteAppsListStreamProvider);
    final inApps = ref.watch(validInAppsProvider);

    final texts = Theme.of(context).textTheme;

    final iconGrid = Breakpoints.large.isActive(context) ? 3 : 1;

    const childAspectRatio = 7 / 1;

    return Column(
      children: [
        Flexible(
          flex: 10,
          child: TabBar(
            tabAlignment: TabAlignment.start,
            labelStyle: texts.bodyLarge,
            controller: _tabController,
            isScrollable: true,
            tabs: [...InAppCategory.values.map((e) => Tab(text: e.name))],
            onTap: _onTabTap,
          ),
        ),
        Expanded(
          flex: 90,
          child: ScrollablePositionedList.separated(
            itemScrollController: _itemScrollController,
            itemPositionsListener: _itemPositionsListener,
            itemCount: InAppCategory.values.length,
            itemBuilder: (context, index) {
              final category = InAppCategory.values[index];

              return switch (category) {
                /// お気に入りタブはストリームでFirestoreから取得する
                InAppCategory.fav => switch (favoriteAppsStream) {
                    AsyncError(:final error) => Text(error.toString()),
                    AsyncLoading() => const Center(child: CircularProgressIndicator()),
                    AsyncData(:final value) => Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ListTile(
                            title: Text(
                              category.name,
                              style: titleMedium,
                              textAlign: TextAlign.center,
                            ),
                          ),
                          GridView.count(
                            physics: const NeverScrollableScrollPhysics(),
                            childAspectRatio: childAspectRatio,
                            shrinkWrap: true,
                            crossAxisCount: iconGrid,
                            children: [
                              ...value.whereNot((e) {
                                if (Env.inProduction) {
                                  return e.isHidden;
                                } else {
                                  return false;
                                }
                              }).map(
                                (app) => _MiniAppTile(
                                  app: app,
                                  trailing: IconButton(
                                    icon: const Icon(Icons.remove),
                                    onPressed: () async => ref.read(favoriteAppServiceProvider).delete(id: app.name),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                  },

                /// お気に入り以外のカテゴリは非同期処理は必要ない
                final category => Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ListTile(
                        title: Text(
                          category.name,
                          style: titleMedium,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      GridView.count(
                        physics: const NeverScrollableScrollPhysics(),
                        childAspectRatio: childAspectRatio,
                        shrinkWrap: true,
                        crossAxisCount: iconGrid,
                        children: [
                          ...inApps

                              /// 本番アプリでは、開発中の隠しアプリは表示しない
                              .whereNot((e) {
                                if (Env.inProduction) {
                                  return e.isHidden;
                                } else {
                                  return false;
                                }
                              })
                              .where((e) => e.category == category)
                              .map(
                                (app) => _MiniAppTile(
                                  app: app,
                                  trailing: IconButton(
                                    icon: const Icon(Icons.add),
                                    onPressed: () async => ref.read(favoriteAppServiceProvider).add(id: app.name),
                                  ),
                                ),
                              ),
                        ],
                      ),
                    ],
                  ),
              };
            },
            separatorBuilder: (context, index) => const Divider(height: 0.1, thickness: 0.5),
          ),
        ),
      ],
    );
  }
}

/// ミニアプリを表示するタイル
/// タップでミニアプリに登録された遷移メソッドを呼び出す
/// addボタンを押すと、お気に入りに追加される
class _MiniAppTile extends ConsumerWidget {
  const _MiniAppTile({required this.app, required this.trailing});
  final InApp app;
  final Widget trailing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.all(4),
      child: InkWell(
        onTap: app.onTap(context),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Image.asset(
                app.iconPath,
                fit: BoxFit.fitWidth,
              ),
            ),
            Expanded(
              flex: 4,
              child: FittedBox(
                alignment: Alignment.centerRight,
                fit: BoxFit.scaleDown,
                child: Text(app.title, style: texts.titleLarge),
              ),
            ),
            Flexible(child: trailing),
          ],
        ),
      ),
    );
  }
}
