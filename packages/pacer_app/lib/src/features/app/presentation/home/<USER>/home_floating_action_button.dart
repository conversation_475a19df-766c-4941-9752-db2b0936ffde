import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../authentication/presentation/login_page/login_state.dart';
import '../../../../device/data/pacer_repository.dart';
import '../../../../device/domain/extension.dart';

/// ログオフボタン
/// ITG400で動作時のみ、Pacer Playerに遷移する
class HomeFloatingActionButton extends ConsumerWidget {
  /// init
  const HomeFloatingActionButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        foregroundColor: colors.onPrimary,
        backgroundColor: colors.primary,
      ),
      onPressed: () => _onLogout(ref),
      icon: const Icon(Icons.logout),
      label: const Text('ログオフ'),
    );
  }

  Future<void> _onLogout(WidgetRef ref) async {
    await ref.read(loginStateProvider.notifier).logout();

    /// ITG400の時だけ、アプリをpopする（pacer_playerに戻るため）
    if (ref.read(deviceInfoProvider).requireValue.isITG400) {
      await SystemNavigator.pop();
    }
  }
}
