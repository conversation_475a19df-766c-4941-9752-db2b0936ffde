import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_adaptive_scaffold/flutter_adaptive_scaffold.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../scandit_enable.dart';
import '../../../../utils/user_asynceffect/use_asynceffect.dart';
import '../../../authentication/data/auth_repository.dart';
import '../../../authentication/presentation/login_page/login_state.dart';
import 'components/home_app_bar.dart';
import 'components/home_floating_action_button.dart';
import 'components/home_page_body.dart';

/// アプリ一覧ページ
class HomePage extends HookConsumerWidget {
  /// init
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    /// ホーム画面が最前面の時のみ、sparkscanを有効化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ModalRoute.of(context)?.isCurrent ?? false) {
        ref.read(scanditEnableProvider.notifier).enable();
      }
    });

    /// ログイン時のロジック
    useAsyncEffect(
      () async {
        final user = ref.read(authRepositoryProvider).currentUser;
        if (user?.loggedInChargeStore != true) {
          log('ログインしている店舗が担当店舗ではありません');
          final ok = await showDialog<bool>(
            barrierDismissible: false,
            context: ref.context,
            builder: (context) => AlertDialog(
              icon: const Icon(Icons.warning),
              iconColor: Theme.of(context).colorScheme.error,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Padding(
                    padding: EdgeInsets.all(8),
                    child: Text('以下の店舗に \nログインしました'),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: FittedBox(
                      child: Text(
                        '${user?.clockInStore.name}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Theme.of(context).colorScheme.error,
                            ),
                      ),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.all(8),
                    child: Text('作業を続けますか'),
                  ),
                ],
              ),
              titleTextStyle: Theme.of(context).textTheme.titleLarge,
              contentTextStyle: Theme.of(context).textTheme.titleLarge,
              actions: <Widget>[
                OutlinedButton(
                  child: const Text('作業を続ける'),
                  onPressed: () => Navigator.of(context).pop(false),
                ),
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                  child: const Text('ログオフ'),
                  onPressed: () => Navigator.of(context).pop(true),
                ),
              ],
            ),
          );

          if (ok ?? false) {
            await ref.read(loginStateProvider.notifier).logout();
          }
        }
        return Future.value();
      },
      [],
    );
    useAsyncEffect(
      () async {
        final timeToClockOut = ref.read(authRepositoryProvider).currentUser?.timeToWorkOut;
        if (timeToClockOut == null) return Future.value();

        if (timeToClockOut.inMinutes <= 10) {
          final ok = await showDialog<bool>(
            barrierDismissible: false,
            context: ref.context,
            builder: (context) => AlertDialog(
              title: Text(
                switch (timeToClockOut.inMinutes) {
                  > 0 => '退勤時間 ${timeToClockOut.inMinutes} 分前です',
                  _ => '退勤時間を過ぎています',
                },
              ),
              content: Text(
                '退勤時間を過ぎると\n'
                'PACERは使えません。\n'
                '引き続き利用する場合\n'
                '所属長の残業命令が必要です。',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              actions: [
                OutlinedButton(
                  child: const Text('確認して閉じる'),
                  onPressed: () => Navigator.of(context).pop(false),
                ),
              ],
            ),
          );

          if (ok ?? false) {
            await ref.read(loginStateProvider.notifier).logout();
          }
        }
        return Future.value();
      },
      [],
    );

    return Scaffold(
      floatingActionButton: const HomeFloatingActionButton(),
      floatingActionButtonLocation:
          Breakpoints.large.isActive(context) ? null : FloatingActionButtonLocation.endContained,
      appBar: const HomeAppBar(),
      body: const SafeArea(child: HomePageBody()),
      bottomNavigationBar: Breakpoints.large.isActive(context) ? null : const BottomAppBar(height: 60),
    );
  }
}
