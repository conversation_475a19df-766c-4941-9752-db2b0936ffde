import 'package:clock/clock.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../constants/environment.dart';
import '../../../../../utils/date_time.dart';
import '../../../../authentication/application/fake_workplan.dart';
import '../../../../authentication/data/auth_repository.dart';

/// ユーザー名と店舗の名前を表示するAppBar
class HomeAppBar extends ConsumerWidget implements PreferredSizeWidget {
  /// init
  const HomeAppBar({super.key});

  @override
  Size get preferredSize => const Size(double.infinity, kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final user = ref.watch(authRepositoryProvider).currentUser;

    return AppBar(
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      elevation: 0,
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: InkWell(
              onDoubleTap: Env.inNotProduction
                  ? () async {
                      final logoutTime = await showTimePicker(
                        context: ref.context,
                        initialTime: TimeOfDay.now(),
                        initialEntryMode: TimePickerEntryMode.input,
                      );
                      if (logoutTime != null) {
                        final now = clock.now();
                        ref.read(fakeWorkplanProvider.notifier).setWorkplanForDebug(now.applied(logoutTime));
                      }
                    }
                  : null,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(user?.name ?? ' '),
              ),
            ),
          ),
          Flexible(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(user?.clockInStore.name ?? ' '),
            ),
          ),
        ],
      ),
    );
  }
}
