import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../fixed_assets_inspection/fixed_assets_inspection_page.dart';
import '../fixed_assets_list/fixed_assets_list_page.dart';
import '../fixed_assets_registration/fixed_assets_registration_page.dart';

/// 固定資産実査route
class FixedAssetsInspectionRoute extends GoRouteData {
  /// コンストラクタ関数
  const FixedAssetsInspectionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const FixedAssetsInspectionPage();
}

/// 固定資産一覧route
class FixedAssetsListRoute extends GoRouteData {
  /// コンストラクタ関数
  const FixedAssetsListRoute(this.id, this.placeName);

  /// 設置場所コード
  final String id;

  /// 設置場所名
  final String placeName;

  @override
  Widget build(BuildContext context, GoRouterState state) => FixedAssetsListPage(id, placeName);
}

/// 固定資産登録route
class FixedAssetsRegistrationRoute extends GoRouteData {
  /// コンストラクタ関数
  const FixedAssetsRegistrationRoute(
    this.id,
    this.placeName, {
    required this.isEnabledMoveToPlace,
  });

  /// 設置場所コード
  final String id;

  /// 設置場所名
  final String placeName;

  /// 移動可能かの判定
  final bool isEnabledMoveToPlace;

  @override
  Widget build(BuildContext context, GoRouterState state) => FixedAssetsRegistrationPage(
        id,
        placeName,
        isEnabledMoveToPlace: isEnabledMoveToPlace,
      );
}
