// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fixed_assets_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getPlaceDetailControllerHash() => r'4f17d67fea7bd53163d22032b0475da1b1136ae6';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// 設置場所詳細を取得するコントローラー
///
/// Copied from [getPlaceDetailController].
@ProviderFor(getPlaceDetailController)
const getPlaceDetailControllerProvider = GetPlaceDetailControllerFamily();

/// 設置場所詳細を取得するコントローラー
///
/// Copied from [getPlaceDetailController].
class GetPlaceDetailControllerFamily extends Family {
  /// 設置場所詳細を取得するコントローラー
  ///
  /// Copied from [getPlaceDetailController].
  const GetPlaceDetailControllerFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'getPlaceDetailControllerProvider';

  /// 設置場所詳細を取得するコントローラー
  ///
  /// Copied from [getPlaceDetailController].
  GetPlaceDetailControllerProvider call(
    String placeCode,
  ) {
    return GetPlaceDetailControllerProvider(
      placeCode,
    );
  }

  @visibleForOverriding
  @override
  GetPlaceDetailControllerProvider getProviderOverride(
    covariant GetPlaceDetailControllerProvider provider,
  ) {
    return call(
      provider.placeCode,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(FutureOr<PlaceDetail?> Function(GetPlaceDetailControllerRef ref) create) {
    return _$GetPlaceDetailControllerFamilyOverride(this, create);
  }
}

class _$GetPlaceDetailControllerFamilyOverride implements FamilyOverride {
  _$GetPlaceDetailControllerFamilyOverride(this.overriddenFamily, this.create);

  final FutureOr<PlaceDetail?> Function(GetPlaceDetailControllerRef ref) create;

  @override
  final GetPlaceDetailControllerFamily overriddenFamily;

  @override
  GetPlaceDetailControllerProvider getProviderOverride(
    covariant GetPlaceDetailControllerProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// 設置場所詳細を取得するコントローラー
///
/// Copied from [getPlaceDetailController].
class GetPlaceDetailControllerProvider extends AutoDisposeFutureProvider<PlaceDetail?> {
  /// 設置場所詳細を取得するコントローラー
  ///
  /// Copied from [getPlaceDetailController].
  GetPlaceDetailControllerProvider(
    String placeCode,
  ) : this._internal(
          (ref) => getPlaceDetailController(
            ref as GetPlaceDetailControllerRef,
            placeCode,
          ),
          from: getPlaceDetailControllerProvider,
          name: r'getPlaceDetailControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$getPlaceDetailControllerHash,
          dependencies: GetPlaceDetailControllerFamily._dependencies,
          allTransitiveDependencies: GetPlaceDetailControllerFamily._allTransitiveDependencies,
          placeCode: placeCode,
        );

  GetPlaceDetailControllerProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.placeCode,
  }) : super.internal();

  final String placeCode;

  @override
  Override overrideWith(
    FutureOr<PlaceDetail?> Function(GetPlaceDetailControllerRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetPlaceDetailControllerProvider._internal(
        (ref) => create(ref as GetPlaceDetailControllerRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        placeCode: placeCode,
      ),
    );
  }

  @override
  (String,) get argument {
    return (placeCode,);
  }

  @override
  AutoDisposeFutureProviderElement<PlaceDetail?> createElement() {
    return _GetPlaceDetailControllerProviderElement(this);
  }

  GetPlaceDetailControllerProvider _copyWith(
    FutureOr<PlaceDetail?> Function(GetPlaceDetailControllerRef ref) create,
  ) {
    return GetPlaceDetailControllerProvider._internal(
      (ref) => create(ref as GetPlaceDetailControllerRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      placeCode: placeCode,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is GetPlaceDetailControllerProvider && other.placeCode == placeCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, placeCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin GetPlaceDetailControllerRef on AutoDisposeFutureProviderRef<PlaceDetail?> {
  /// The parameter `placeCode` of this provider.
  String get placeCode;
}

class _GetPlaceDetailControllerProviderElement extends AutoDisposeFutureProviderElement<PlaceDetail?>
    with GetPlaceDetailControllerRef {
  _GetPlaceDetailControllerProviderElement(super.provider);

  @override
  String get placeCode => (origin as GetPlaceDetailControllerProvider).placeCode;
}

String _$fixedAssetsListControllerHash() => r'b8194a4c51c40f88bc7699b46be350446597fdf5';

abstract class _$FixedAssetsListController extends BuildlessAutoDisposeAsyncNotifier<List<FixedAsset>> {
  late final String placeCode;

  FutureOr<List<FixedAsset>> build(
    String placeCode,
  );
}

/// 実査システム＿固定資産一覧
///
/// Copied from [FixedAssetsListController].
@ProviderFor(FixedAssetsListController)
const fixedAssetsListControllerProvider = FixedAssetsListControllerFamily();

/// 実査システム＿固定資産一覧
///
/// Copied from [FixedAssetsListController].
class FixedAssetsListControllerFamily extends Family {
  /// 実査システム＿固定資産一覧
  ///
  /// Copied from [FixedAssetsListController].
  const FixedAssetsListControllerFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'fixedAssetsListControllerProvider';

  /// 実査システム＿固定資産一覧
  ///
  /// Copied from [FixedAssetsListController].
  FixedAssetsListControllerProvider call(
    String placeCode,
  ) {
    return FixedAssetsListControllerProvider(
      placeCode,
    );
  }

  @visibleForOverriding
  @override
  FixedAssetsListControllerProvider getProviderOverride(
    covariant FixedAssetsListControllerProvider provider,
  ) {
    return call(
      provider.placeCode,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(FixedAssetsListController Function() create) {
    return _$FixedAssetsListControllerFamilyOverride(this, create);
  }
}

class _$FixedAssetsListControllerFamilyOverride implements FamilyOverride {
  _$FixedAssetsListControllerFamilyOverride(this.overriddenFamily, this.create);

  final FixedAssetsListController Function() create;

  @override
  final FixedAssetsListControllerFamily overriddenFamily;

  @override
  FixedAssetsListControllerProvider getProviderOverride(
    covariant FixedAssetsListControllerProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// 実査システム＿固定資産一覧
///
/// Copied from [FixedAssetsListController].
class FixedAssetsListControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<FixedAssetsListController, List<FixedAsset>> {
  /// 実査システム＿固定資産一覧
  ///
  /// Copied from [FixedAssetsListController].
  FixedAssetsListControllerProvider(
    String placeCode,
  ) : this._internal(
          () => FixedAssetsListController()..placeCode = placeCode,
          from: fixedAssetsListControllerProvider,
          name: r'fixedAssetsListControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$fixedAssetsListControllerHash,
          dependencies: FixedAssetsListControllerFamily._dependencies,
          allTransitiveDependencies: FixedAssetsListControllerFamily._allTransitiveDependencies,
          placeCode: placeCode,
        );

  FixedAssetsListControllerProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.placeCode,
  }) : super.internal();

  final String placeCode;

  @override
  FutureOr<List<FixedAsset>> runNotifierBuild(
    covariant FixedAssetsListController notifier,
  ) {
    return notifier.build(
      placeCode,
    );
  }

  @override
  Override overrideWith(FixedAssetsListController Function() create) {
    return ProviderOverride(
      origin: this,
      override: FixedAssetsListControllerProvider._internal(
        () => create()..placeCode = placeCode,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        placeCode: placeCode,
      ),
    );
  }

  @override
  (String,) get argument {
    return (placeCode,);
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<FixedAssetsListController, List<FixedAsset>> createElement() {
    return _FixedAssetsListControllerProviderElement(this);
  }

  FixedAssetsListControllerProvider _copyWith(
    FixedAssetsListController Function() create,
  ) {
    return FixedAssetsListControllerProvider._internal(
      () => create()..placeCode = placeCode,
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      placeCode: placeCode,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is FixedAssetsListControllerProvider && other.placeCode == placeCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, placeCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FixedAssetsListControllerRef on AutoDisposeAsyncNotifierProviderRef<List<FixedAsset>> {
  /// The parameter `placeCode` of this provider.
  String get placeCode;
}

class _FixedAssetsListControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<FixedAssetsListController, List<FixedAsset>>
    with FixedAssetsListControllerRef {
  _FixedAssetsListControllerProviderElement(super.provider);

  @override
  String get placeCode => (origin as FixedAssetsListControllerProvider).placeCode;
}

String _$fixedAssetsProgressControllerHash() => r'c4e0e235d20e8608e935ec7a69a7b1c8c1cae290';

abstract class _$FixedAssetsProgressController extends BuildlessAutoDisposeAsyncNotifier<Progress> {
  late final String placeCode;

  FutureOr<Progress> build(
    String placeCode,
  );
}

/// 固定資産実査進行状況
///
/// Copied from [FixedAssetsProgressController].
@ProviderFor(FixedAssetsProgressController)
const fixedAssetsProgressControllerProvider = FixedAssetsProgressControllerFamily();

/// 固定資産実査進行状況
///
/// Copied from [FixedAssetsProgressController].
class FixedAssetsProgressControllerFamily extends Family {
  /// 固定資産実査進行状況
  ///
  /// Copied from [FixedAssetsProgressController].
  const FixedAssetsProgressControllerFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'fixedAssetsProgressControllerProvider';

  /// 固定資産実査進行状況
  ///
  /// Copied from [FixedAssetsProgressController].
  FixedAssetsProgressControllerProvider call(
    String placeCode,
  ) {
    return FixedAssetsProgressControllerProvider(
      placeCode,
    );
  }

  @visibleForOverriding
  @override
  FixedAssetsProgressControllerProvider getProviderOverride(
    covariant FixedAssetsProgressControllerProvider provider,
  ) {
    return call(
      provider.placeCode,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(FixedAssetsProgressController Function() create) {
    return _$FixedAssetsProgressControllerFamilyOverride(this, create);
  }
}

class _$FixedAssetsProgressControllerFamilyOverride implements FamilyOverride {
  _$FixedAssetsProgressControllerFamilyOverride(this.overriddenFamily, this.create);

  final FixedAssetsProgressController Function() create;

  @override
  final FixedAssetsProgressControllerFamily overriddenFamily;

  @override
  FixedAssetsProgressControllerProvider getProviderOverride(
    covariant FixedAssetsProgressControllerProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// 固定資産実査進行状況
///
/// Copied from [FixedAssetsProgressController].
class FixedAssetsProgressControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<FixedAssetsProgressController, Progress> {
  /// 固定資産実査進行状況
  ///
  /// Copied from [FixedAssetsProgressController].
  FixedAssetsProgressControllerProvider(
    String placeCode,
  ) : this._internal(
          () => FixedAssetsProgressController()..placeCode = placeCode,
          from: fixedAssetsProgressControllerProvider,
          name: r'fixedAssetsProgressControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$fixedAssetsProgressControllerHash,
          dependencies: FixedAssetsProgressControllerFamily._dependencies,
          allTransitiveDependencies: FixedAssetsProgressControllerFamily._allTransitiveDependencies,
          placeCode: placeCode,
        );

  FixedAssetsProgressControllerProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.placeCode,
  }) : super.internal();

  final String placeCode;

  @override
  FutureOr<Progress> runNotifierBuild(
    covariant FixedAssetsProgressController notifier,
  ) {
    return notifier.build(
      placeCode,
    );
  }

  @override
  Override overrideWith(FixedAssetsProgressController Function() create) {
    return ProviderOverride(
      origin: this,
      override: FixedAssetsProgressControllerProvider._internal(
        () => create()..placeCode = placeCode,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        placeCode: placeCode,
      ),
    );
  }

  @override
  (String,) get argument {
    return (placeCode,);
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<FixedAssetsProgressController, Progress> createElement() {
    return _FixedAssetsProgressControllerProviderElement(this);
  }

  FixedAssetsProgressControllerProvider _copyWith(
    FixedAssetsProgressController Function() create,
  ) {
    return FixedAssetsProgressControllerProvider._internal(
      () => create()..placeCode = placeCode,
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      placeCode: placeCode,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is FixedAssetsProgressControllerProvider && other.placeCode == placeCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, placeCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FixedAssetsProgressControllerRef on AutoDisposeAsyncNotifierProviderRef<Progress> {
  /// The parameter `placeCode` of this provider.
  String get placeCode;
}

class _FixedAssetsProgressControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<FixedAssetsProgressController, Progress>
    with FixedAssetsProgressControllerRef {
  _FixedAssetsProgressControllerProviderElement(super.provider);

  @override
  String get placeCode => (origin as FixedAssetsProgressControllerProvider).placeCode;
}

String _$fixedAssetsReasonControllerHash() => r'f41b853db3093c717387ba1a77a745608821655b';

/// 実査システム＿理由一覧
///
/// Copied from [FixedAssetsReasonController].
@ProviderFor(FixedAssetsReasonController)
final fixedAssetsReasonControllerProvider =
    AutoDisposeAsyncNotifierProvider<FixedAssetsReasonController, List<Reason>>.internal(
  FixedAssetsReasonController.new,
  name: r'fixedAssetsReasonControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fixedAssetsReasonControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FixedAssetsReasonController = AutoDisposeAsyncNotifier<List<Reason>>;
String _$updateFixedAssetStatusControllerHash() => r'd746c9ea8d9b83816e4e215f23672a34f7e67a61';

abstract class _$UpdateFixedAssetStatusController extends BuildlessAutoDisposeAsyncNotifier<void> {
  late final String assetCode;

  FutureOr<void> build(
    String assetCode,
  );
}

/// 実査システム＿固定資産一覧_更新
///
/// Copied from [UpdateFixedAssetStatusController].
@ProviderFor(UpdateFixedAssetStatusController)
const updateFixedAssetStatusControllerProvider = UpdateFixedAssetStatusControllerFamily();

/// 実査システム＿固定資産一覧_更新
///
/// Copied from [UpdateFixedAssetStatusController].
class UpdateFixedAssetStatusControllerFamily extends Family {
  /// 実査システム＿固定資産一覧_更新
  ///
  /// Copied from [UpdateFixedAssetStatusController].
  const UpdateFixedAssetStatusControllerFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'updateFixedAssetStatusControllerProvider';

  /// 実査システム＿固定資産一覧_更新
  ///
  /// Copied from [UpdateFixedAssetStatusController].
  UpdateFixedAssetStatusControllerProvider call(
    String assetCode,
  ) {
    return UpdateFixedAssetStatusControllerProvider(
      assetCode,
    );
  }

  @visibleForOverriding
  @override
  UpdateFixedAssetStatusControllerProvider getProviderOverride(
    covariant UpdateFixedAssetStatusControllerProvider provider,
  ) {
    return call(
      provider.assetCode,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(UpdateFixedAssetStatusController Function() create) {
    return _$UpdateFixedAssetStatusControllerFamilyOverride(this, create);
  }
}

class _$UpdateFixedAssetStatusControllerFamilyOverride implements FamilyOverride {
  _$UpdateFixedAssetStatusControllerFamilyOverride(this.overriddenFamily, this.create);

  final UpdateFixedAssetStatusController Function() create;

  @override
  final UpdateFixedAssetStatusControllerFamily overriddenFamily;

  @override
  UpdateFixedAssetStatusControllerProvider getProviderOverride(
    covariant UpdateFixedAssetStatusControllerProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// 実査システム＿固定資産一覧_更新
///
/// Copied from [UpdateFixedAssetStatusController].
class UpdateFixedAssetStatusControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<UpdateFixedAssetStatusController, void> {
  /// 実査システム＿固定資産一覧_更新
  ///
  /// Copied from [UpdateFixedAssetStatusController].
  UpdateFixedAssetStatusControllerProvider(
    String assetCode,
  ) : this._internal(
          () => UpdateFixedAssetStatusController()..assetCode = assetCode,
          from: updateFixedAssetStatusControllerProvider,
          name: r'updateFixedAssetStatusControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$updateFixedAssetStatusControllerHash,
          dependencies: UpdateFixedAssetStatusControllerFamily._dependencies,
          allTransitiveDependencies: UpdateFixedAssetStatusControllerFamily._allTransitiveDependencies,
          assetCode: assetCode,
        );

  UpdateFixedAssetStatusControllerProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.assetCode,
  }) : super.internal();

  final String assetCode;

  @override
  FutureOr<void> runNotifierBuild(
    covariant UpdateFixedAssetStatusController notifier,
  ) {
    return notifier.build(
      assetCode,
    );
  }

  @override
  Override overrideWith(UpdateFixedAssetStatusController Function() create) {
    return ProviderOverride(
      origin: this,
      override: UpdateFixedAssetStatusControllerProvider._internal(
        () => create()..assetCode = assetCode,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        assetCode: assetCode,
      ),
    );
  }

  @override
  (String,) get argument {
    return (assetCode,);
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<UpdateFixedAssetStatusController, void> createElement() {
    return _UpdateFixedAssetStatusControllerProviderElement(this);
  }

  UpdateFixedAssetStatusControllerProvider _copyWith(
    UpdateFixedAssetStatusController Function() create,
  ) {
    return UpdateFixedAssetStatusControllerProvider._internal(
      () => create()..assetCode = assetCode,
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      assetCode: assetCode,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is UpdateFixedAssetStatusControllerProvider && other.assetCode == assetCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, assetCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin UpdateFixedAssetStatusControllerRef on AutoDisposeAsyncNotifierProviderRef<void> {
  /// The parameter `assetCode` of this provider.
  String get assetCode;
}

class _UpdateFixedAssetStatusControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<UpdateFixedAssetStatusController, void>
    with UpdateFixedAssetStatusControllerRef {
  _UpdateFixedAssetStatusControllerProviderElement(super.provider);

  @override
  String get assetCode => (origin as UpdateFixedAssetStatusControllerProvider).assetCode;
}

String _$fixedAssetsAllBranchControllerHash() => r'a84b58d114e129aa6353e275b4e53aeb5d05a44d';

/// 実査システム＿場所一覧
///
/// Copied from [FixedAssetsAllBranchController].
@ProviderFor(FixedAssetsAllBranchController)
final fixedAssetsAllBranchControllerProvider =
    AutoDisposeAsyncNotifierProvider<FixedAssetsAllBranchController, List<Branch>>.internal(
  FixedAssetsAllBranchController.new,
  name: r'fixedAssetsAllBranchControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fixedAssetsAllBranchControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FixedAssetsAllBranchController = AutoDisposeAsyncNotifier<List<Branch>>;
String _$fixedAssetsAllPlacesControllerHash() => r'a46bbc58c81259bddd628f9f9a86d9d04ba203bc';

/// 実査システム＿場所一覧
///
/// Copied from [FixedAssetsAllPlacesController].
@ProviderFor(FixedAssetsAllPlacesController)
final fixedAssetsAllPlacesControllerProvider =
    AutoDisposeAsyncNotifierProvider<FixedAssetsAllPlacesController, List<Place>>.internal(
  FixedAssetsAllPlacesController.new,
  name: r'fixedAssetsAllPlacesControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fixedAssetsAllPlacesControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FixedAssetsAllPlacesController = AutoDisposeAsyncNotifier<List<Place>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
