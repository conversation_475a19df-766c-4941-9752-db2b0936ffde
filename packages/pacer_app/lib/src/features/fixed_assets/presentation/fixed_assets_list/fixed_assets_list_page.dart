// ignore_for_file: public_member_api_docs
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../common_widgets/pacer_back_button.dart';
import '../../../../localization/app_localizations_context.dart';
import '../../../../routing/app_router.dart';
import '../common_widgets/asset_list_tile.dart';
import '../common_widgets/dialog_helpers.dart';
import '../fixed_assets_inspection/fixed_assets_inspection_controller.dart';
import '../routing/fixed_asset_route.dart';
import 'fixed_assets_list_controller.dart';

class FixedAssetsListPage extends ConsumerWidget {
  const FixedAssetsListPage(this.placeCode, this.placeName, {super.key});

  final String placeCode;
  final String placeName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final progress = ref.watch(fixedAssetsProgressControllerProvider(placeCode));
    final placeDetail = ref.watch(getPlaceDetailControllerProvider(placeCode)).valueOrNull;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        _onBackPressed(ref);
      },
      child: Scaffold(
        appBar: PacerAppBar(
          context: context,
          title: Text(context.loc.fixedAssetsList),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                switch (progress) {
                  AsyncData(:final value) => Text(
                      context.loc.branchAssetsCount(
                        placeName,
                        value.fulfill,
                        value.total,
                      ),
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  _ => const SizedBox.shrink()
                },
                const SizedBox(height: 16),
                Expanded(
                  child: _ListContent(
                    placeCode,
                    isEnabledMove: placeDetail?.isEnabledMove ?? false,
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: BottomAppBar(
          child: Row(
            children: [
              PacerBackButton(onPressed: () => _onBackPressed(ref)),
              const Spacer(),
              TextButton(
                onPressed: () => FixedAssetsRegistrationRoute(
                  placeCode,
                  placeName,
                  isEnabledMoveToPlace: placeDetail?.isEnabledMove ?? false,
                ).go(context),
                child: Text(context.loc.start),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 戻るボタンの処理
  void _onBackPressed(WidgetRef ref) {
    ref.invalidate(fixedAssetsPlacesControllerProvider);
    ref.context.pop();
  }
}

class _ListContent extends ConsumerWidget {
  const _ListContent(this.placeCode, {required this.isEnabledMove});

  final String placeCode;
  final bool isEnabledMove;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final assets = ref.watch(fixedAssetsListControllerProvider(placeCode));
    final reasons = ref.watch(fixedAssetsReasonControllerProvider).valueOrNull;
    ref.listen(
      fixedAssetsListControllerProvider(placeCode),
      (_, state) => state.showAlertDialogOnError(context),
    );
    return switch (assets) {
      AsyncData(:final value, :final isLoading) when reasons != null =>
        // データ更新中です
        isLoading
            ? const Center(child: CircularProgressIndicator())
            : ListView.separated(
                itemBuilder: (_, index) => AssetListTile(
                  fixedAsset: value[index],
                  placeCode: placeCode,
                  isEnabledMove: isEnabledMove,
                  reasons: reasons,
                ),
                itemCount: value.length,
                separatorBuilder: (BuildContext context, int index) => const SizedBox(height: 8),
              ),
      AsyncError() => const SizedBox.shrink(),
      _ => const Center(child: CircularProgressIndicator()),
    };
  }
}
