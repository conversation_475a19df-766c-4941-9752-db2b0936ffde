import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../application/fixed_asset_service.dart';
import '../../domain/branch.dart';
import '../../domain/fixed_asset.dart';
import '../../domain/place.dart';
import '../../domain/place_detail.dart';
import '../../domain/progress.dart';
import '../../domain/reason.dart';
import '../fixed_assets_inspection/fixed_assets_inspection_controller.dart';

part 'fixed_assets_list_controller.g.dart';

/// 実査システム＿固定資産一覧
@riverpod
class FixedAssetsListController extends _$FixedAssetsListController {
  @override
  Future<List<FixedAsset>> build(String placeCode) {
    final service = ref.read(fixedAssetServiceProvider);
    return service.listFixedAssets(placeCode);
  }

  /// コードと新しい理由を使用してアセットを更新します
  void updateFixedAsset(String assetCode, int reasonCode) {
    if (state case AsyncData(:final value)) {
      state = AsyncData([
        ...value.map(
          (e) => e.code == assetCode ? e.copyWith(reasonCode: reasonCode) : e,
        ),
      ]);
    }
  }
}

/// 設置場所詳細を取得するコントローラー
@riverpod
FutureOr<PlaceDetail?> getPlaceDetailController(
  GetPlaceDetailControllerRef ref,
  String placeCode,
) async {
  return await ref.watch(fixedAssetServiceProvider).getPlaceDetail(placeCode);
}

/// 固定資産実査進行状況
@riverpod
class FixedAssetsProgressController extends _$FixedAssetsProgressController {
  @override
  Future<Progress> build(String placeCode) {
    final service = ref.read(fixedAssetServiceProvider);
    return service.getProgress(placeCode);
  }

  /// プロバイダーの状態を無効にして更新します
  void refresh() {
    ref
      ..invalidateSelf()
      ..invalidate(fixedAssetsListControllerProvider)
      ..invalidate(fixedAssetsPlacesControllerProvider);
  }
}

/// 実査システム＿理由一覧
@riverpod
class FixedAssetsReasonController extends _$FixedAssetsReasonController {
  @override
  Future<List<Reason>> build() {
    final service = ref.read(fixedAssetServiceProvider);
    return service.listReasons();
  }
}

/// 実査システム＿固定資産一覧_更新
@riverpod
class UpdateFixedAssetStatusController extends _$UpdateFixedAssetStatusController {
  @override
  FutureOr<void> build(String assetCode) {}

  /// ケースの移動を除く固定資産ステータスを更新する
  Future<void> updateAsset({
    required String code,
    required int reasonCode,
    required String placeCode,
    required bool hasTransferred,
    String? assetStoreCode,
  }) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(fixedAssetServiceProvider).updateAsset(
            code: code,
            reasonCode: reasonCode,
            hasTransferred: hasTransferred,
            assetStoreCode: assetStoreCode,
          ),
    );
    if (state is AsyncData) {
      ref.read(fixedAssetsListControllerProvider(placeCode).notifier).updateFixedAsset(code, reasonCode);
    }
    // 固定資産の現在進捗を再取得
    ref.invalidate(fixedAssetsProgressControllerProvider(placeCode));
  }

  /// ストア内のアセットを移動
  Future<void> moveAssetInsideStore({
    required String code,
    required String currentPlaceCode,
    required String destinationPlaceCode,
    required bool hasTransferred,
    String? assetStoreCode,
  }) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(fixedAssetServiceProvider).moveAssetInsideStore(
            assetCode: code,
            destinationPlaceCode: destinationPlaceCode,
            hasTransferred: hasTransferred,
            assetStoreCode: assetStoreCode,
          ),
    );
    if (state is AsyncData) {
      ref
          .read(fixedAssetsListControllerProvider(currentPlaceCode).notifier)
          .updateFixedAsset(code, ReasonType.move.index);
    }
    // 固定資産の現在進捗を再取得
    ref.invalidate(fixedAssetsProgressControllerProvider(currentPlaceCode));
  }

  /// アセットをストアの外に移動する
  Future<void> moveAssetOutsideStore({
    required String code,
    required String currentPlaceCode,
    required String destinationBranchCode,
    required bool hasTransferred,
    String? assetStoreCode,
  }) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(fixedAssetServiceProvider).moveAssetOutsideStore(
            assetCode: code,
            destinationBranchCode: destinationBranchCode,
            destinationPlaceCode: currentPlaceCode,
            hasTransferred: hasTransferred,
            assetStoreCode: assetStoreCode,
          ),
    );
    if (state is AsyncData) {
      ref
          .read(fixedAssetsListControllerProvider(currentPlaceCode).notifier)
          .updateFixedAsset(code, ReasonType.move.index);
    }
    // 固定資産の現在進捗を再取得
    ref.invalidate(fixedAssetsProgressControllerProvider(currentPlaceCode));
  }
}

/// 実査システム＿場所一覧
@riverpod
class FixedAssetsAllBranchController extends _$FixedAssetsAllBranchController {
  @override
  Future<List<Branch>> build() {
    final service = ref.read(fixedAssetServiceProvider);
    return service.listAllBranch();
  }
}

/// 実査システム＿場所一覧
@riverpod
class FixedAssetsAllPlacesController extends _$FixedAssetsAllPlacesController {
  @override
  Future<List<Place>> build() {
    final service = ref.read(fixedAssetServiceProvider);
    return service.listAllPlaces();
  }
}
