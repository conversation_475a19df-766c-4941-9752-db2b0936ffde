import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../fixed_assets_list/fixed_assets_list_controller.dart';

/// ダイアログにはすべての場所が含まれており、
/// ユーザーは場所間でアセットを移動することを選択できます
class PlaceListDialog extends ConsumerWidget {
  /// コンストラクタ
  const PlaceListDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final places = ref.watch(fixedAssetsAllPlacesControllerProvider);

    return SimpleDialog(
      insetPadding: const EdgeInsets.all(16),
      children: switch (places) {
        AsyncData(:final value) => value
            .map(
              (e) => SimpleDialogOption(
                child: Text(
                  e.placeName,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                onPressed: () => context.pop(e.placeCode),
              ),
            )
            .toList(),
        AsyncError(:final error) => [Center(child: Text(error.toString()))],
        _ => [const Center(child: CircularProgressIndicator())],
      },
    );
  }
}
