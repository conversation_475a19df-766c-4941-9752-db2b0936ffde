import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../fixed_assets_list/fixed_assets_list_controller.dart';

/// ダイアログにはすべてのストアが含まれており、
/// ユーザーはストア間でアセットを移動することを選択できます。
class BranchListDialog extends ConsumerWidget {
  /// コンストラクタ
  const BranchListDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final branches = ref.watch(fixedAssetsAllBranchControllerProvider);

    return SimpleDialog(
      insetPadding: const EdgeInsets.all(16),
      children: switch (branches) {
        AsyncData(:final value) => value
            .map(
              (e) => SimpleDialogOption(
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        e.name,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ),
                    Text(e.code),
                  ],
                ),
                onPressed: () => context.pop(e.code),
              ),
            )
            .toList(),
        AsyncError(:final error) => [Center(child: Text(error.toString()))],
        _ => [const Center(child: CircularProgressIndicator())],
      },
    );
  }
}
