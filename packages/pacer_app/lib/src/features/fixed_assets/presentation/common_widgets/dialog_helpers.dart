import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../exceptions/app_exception.dart';

/// ダイアログを表示する
Future<bool?> showAlertDialog({
  required BuildContext context,
  String title = 'エラー',
  String? content,
  String? cancelActionText,
  String defaultActionText = 'OK',
}) async {
  return showDialog(
    barrierDismissible: false,
    context: context,
    builder: (context) => AlertDialog(
      title: Text(title),
      content: content != null ? Text(content) : null,
      titleTextStyle: Theme.of(context).textTheme.titleLarge,
      contentTextStyle: Theme.of(context).textTheme.titleLarge,
      actions: <Widget>[
        if (cancelActionText != null)
          OutlinedButton(
            child: Text(cancelActionText),
            onPressed: () => Navigator.of(context).pop(false),
          ),
        OutlinedButton(
          child: Text(defaultActionText),
          onPressed: () => Navigator.of(context).pop(true),
        ),
      ],
    ),
  );
}

/// エラーの詳細を表示する
extension AsyncValueUI on AsyncValue<dynamic> {
  /// エラーの詳細を表示するダイアログ
  void showAlertDialogOnError(BuildContext context) {
    if (!isLoading && hasError) {
      final message = _errorMessage(error);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showAlertDialog(
          context: context,
          content: message,
        );
      });
    }
  }

  String _errorMessage(Object? error) => switch (error) {
        final AppException e => e.message,
        _ => error.toString(),
      };
}
