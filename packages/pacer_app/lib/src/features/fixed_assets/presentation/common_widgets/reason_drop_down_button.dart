// ignore_for_file: public_member_api_docs
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../localization/app_localizations_context.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../domain/fixed_asset.dart';
import '../../domain/reason.dart';
import '../fixed_assets_list/fixed_assets_list_controller.dart';
import 'branch_list_dialog.dart';
import 'dialog_helpers.dart';
import 'move_options.dart';
import 'place_list_dialog.dart';

class ReasonDropDownButton extends ConsumerWidget {
  const ReasonDropDownButton({
    super.key,
    required this.fixedAsset,
    required this.placeCode,
    required this.isEnabledMoveToPlace,
    required this.reasons,
  });

  final FixedAsset fixedAsset;
  final String placeCode;
  final bool isEnabledMoveToPlace;
  final List<Reason> reasons;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final updateStatus = ref.watch(updateFixedAssetStatusControllerProvider(fixedAsset.code));
    ref.listen(updateFixedAssetStatusControllerProvider(fixedAsset.code), (_, state) {
      state.showAlertDialogOnError(context);
    });

    final textStyle = Theme.of(context).textTheme.bodyLarge;

    return ColoredBox(
      color: Theme.of(context).colorScheme.button,
      child: switch (updateStatus) {
        AsyncLoading() => const _SmallCircularProgressIndicator(),
        _ => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: DropdownButton(
              style: textStyle,
              underline: const SizedBox.shrink(),
              isExpanded: true,
              items: reasons
                  .map(
                    (e) => DropdownMenuItem(
                      value: e.code,
                      child: Text(e.name, style: textStyle),
                    ),
                  )
                  .toList(),
              onChanged: (reason) async {
                // ユーザーがキャンセルしたかどうかを確認する
                if (reason == null) return;
                return updateAssetStatus(ref, reason, fixedAsset);
              },
              value: fixedAsset.reasonCode,
            ),
          ),
      },
    );
  }

  Future<void> updateAssetStatus(
    WidgetRef ref,
    int reason,
    FixedAsset asset,
  ) async {
    if (reason == ReasonType.move.index) {
      final option = await _showMoveDialog(ref.context);
      // ユーザーがキャンセルしたかどうかを確認する
      if (option == null || option == MoveOptions.cancel) return;
      return _handleMoveAsset(option, asset, ref);
    } else {
      return ref.read(updateFixedAssetStatusControllerProvider(asset.code).notifier).updateAsset(
            code: asset.code,
            reasonCode: reason,
            placeCode: placeCode,
            hasTransferred: asset.hasTransferred,
            assetStoreCode: asset.branchCode,
          );
    }
  }

  Future<MoveOptions?> _showMoveDialog(BuildContext context) {
    final textStyle = Theme.of(context).textTheme.titleLarge;
    return showDialog<MoveOptions>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.loc.message),
        content: Text(context.loc.moveConfirm),
        titleTextStyle: textStyle,
        contentTextStyle: textStyle,
        actions: MoveOptions.values
            .map(
              (e) => TextButton(
                onPressed: () => context.pop(e),
                child: Text(e.displayText(context)),
              ),
            )
            .toList(),
      ),
    );
  }

  Future<void> _handleMoveAsset(
    MoveOptions option,
    FixedAsset asset,
    WidgetRef ref,
  ) async {
    switch (option) {
      case MoveOptions.inside:
        final selectedPlaceCode = await showDialog<String>(
          context: ref.context,
          builder: (context) => const PlaceListDialog(),
        );
        if (selectedPlaceCode == null) return; // user cancelled
        return ref.read(updateFixedAssetStatusControllerProvider(asset.code).notifier).moveAssetInsideStore(
              code: asset.code,
              destinationPlaceCode: selectedPlaceCode,
              currentPlaceCode: placeCode,
              hasTransferred: asset.hasTransferred,
              assetStoreCode: asset.branchCode,
            );
      case MoveOptions.outside:
        final selectedBranchCode = await showDialog<String>(
          context: ref.context,
          builder: (context) => const BranchListDialog(),
        );
        if (selectedBranchCode == null) return; // user cancelled
        return ref.read(updateFixedAssetStatusControllerProvider(asset.code).notifier).moveAssetOutsideStore(
              code: asset.code,
              destinationBranchCode: selectedBranchCode,
              currentPlaceCode: placeCode,
              hasTransferred: asset.hasTransferred,
              assetStoreCode: asset.branchCode,
            );
      case MoveOptions.cancel:
        return;
    }
  }
}

class _SmallCircularProgressIndicator extends StatelessWidget {
  const _SmallCircularProgressIndicator();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.centerLeft,
      child: const SizedBox(
        height: 32,
        width: 32,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }
}
