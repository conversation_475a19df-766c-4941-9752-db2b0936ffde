import 'package:flutter/material.dart';

import '../../../../localization/app_localizations_context.dart';

/// 移動ダイアログの移動オプション
enum MoveOptions {
  /// 店内
  inside,

  /// 店外
  outside,

  /// キャンセル
  cancel;

  /// ボタンのテキストに使用します
  String displayText(BuildContext context) => switch (this) {
        cancel => context.loc.cancel,
        inside => context.loc.insideStore,
        outside => context.loc.outsideStore,
      };
}
