// ignore_for_file: public_member_api_docs
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../localization/app_localizations_context.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/adaptive_number_input_type.dart';
import '../fixed_assets_registration/fixed_assets_registration_controller.dart';

class RegisterTable extends ConsumerWidget {
  const RegisterTable({
    super.key,
    this.assetCode,
    this.assetName,
    this.productName,
  });

  final String? assetCode;
  final String? assetName;
  final String? productName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.25),
        1: FractionColumnWidth(0.75),
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        TableRow(
          children: [
            _ItemHeader(context.loc.assetNo),
            _ScanField(
              code: assetCode,
              onCodeChanged: (code) => ref.read(fixedAssetsRegistrationControllerProvider.notifier).scan(code),
            ),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.assetName),
            _ItemValue(assetName),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.productName),
            _ItemValue(productName),
          ],
        ),
      ],
    );
  }
}

class _ItemHeader extends StatelessWidget {
  const _ItemHeader(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleLarge,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ItemValue extends StatelessWidget {
  const _ItemValue(this.text);

  final String? text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text ?? '',
        style: texts.titleLarge,
      ),
    );
  }
}

class _ScanField extends HookWidget {
  const _ScanField({
    this.code,
    required this.onCodeChanged,
  });

  final String? code;

  final void Function(String)? onCodeChanged;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;
    final textEditingController = useTextEditingController();

    useEffect(
      () {
        if (code case final String notNullCode) {
          textEditingController.text = notNullCode;
        }
        return;
      },
    );

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: ColoredBox(
        color: colors.button,
        child: TextFormField(
          controller: textEditingController,
          keyboardType: TextInputType.number.withEnter(),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp('[0-9-]')),
          ],
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            filled: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 4),
            fillColor: colors.button,
            focusColor: colors.button,
            hintText: context.loc.assetNumberPlaceholder,
            hintStyle: texts.titleSmall?.copyWith(color: colors.subText),
            border: InputBorder.none,
          ),
          onFieldSubmitted: onCodeChanged,
        ),
      ),
    );
  }
}
