// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

import '../../../../localization/app_localizations_context.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../domain/place.dart';

class PlaceListTile extends StatelessWidget {
  const PlaceListTile({super.key, required this.place, this.onPressed});

  final Place place;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final textStyle = Theme.of(context).textTheme.bodyLarge;
    return ColoredBox(
      color:
          place.placeCount == place.placeAllCount ? Theme.of(context).colorScheme.successContainer : Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Row(
            children: [
              Expanded(
                child: Text(place.placeName, style: textStyle),
              ),
              Text(
                context.loc.placeAssetsCount(
                  place.placeCount,
                  place.placeAllCount,
                ),
                style: textStyle,
              ),
              SizedBox(
                width: 50,
                child: Text(place.status),
              ),
              const Icon(Icons.arrow_right, size: 24),
            ],
          ),
        ),
      ),
    );
  }
}
