// ignore_for_file: public_member_api_docs
import 'package:flutter/material.dart';

import '../../../../localization/app_localizations_context.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../domain/fixed_asset.dart';
import '../../domain/reason.dart';
import 'reason_drop_down_button.dart';

class AssetListTile extends StatelessWidget {
  const AssetListTile({
    super.key,
    required this.fixedAsset,
    required this.placeCode,
    required this.isEnabledMove,
    required this.reasons,
  });

  final FixedAsset fixedAsset;
  final String placeCode;
  final bool isEnabledMove;
  final List<Reason> reasons;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.25),
        1: FractionColumnWidth(0.75),
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        TableRow(
          children: [
            _ItemHeader(context.loc.assetNo),
            _ItemValue(fixedAsset.code),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.productName),
            _ItemValue(fixedAsset.productName),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.assetName),
            _ItemValue(fixedAsset.name),
          ],
        ),
        TableRow(
          children: [
            TableCell(
              verticalAlignment: TableCellVerticalAlignment.middle,
              child: _ItemHeader(context.loc.status),
            ),
            ReasonDropDownButton(
              fixedAsset: fixedAsset,
              placeCode: placeCode,
              isEnabledMoveToPlace: isEnabledMove,
              reasons: reasons,
            ),
          ],
        ),
      ],
    );
  }
}

class _ItemHeader extends StatelessWidget {
  const _ItemHeader(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.bodyLarge,
        textAlign: TextAlign.right,
      ),
    );
  }
}

class _ItemValue extends StatelessWidget {
  const _ItemValue(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.bodyLarge,
      ),
    );
  }
}
