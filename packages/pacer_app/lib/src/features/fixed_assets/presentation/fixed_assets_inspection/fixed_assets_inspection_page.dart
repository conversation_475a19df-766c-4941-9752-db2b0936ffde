// ignore_for_file: public_member_api_docs
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../localization/app_localizations_context.dart';
import '../../../../routing/app_router.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../domain/place.dart';
import '../common_widgets/dialog_helpers.dart';
import '../common_widgets/place_list_tile.dart';
import '../fixed_assets_list/fixed_assets_list_controller.dart';
import '../routing/fixed_asset_route.dart';
import 'fixed_assets_inspection_controller.dart';

class FixedAssetsInspectionPage extends ConsumerWidget {
  const FixedAssetsInspectionPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final placeListInfo = ref.watch(fixedAssetsPlacesControllerProvider);

    ref
      ..watch(fixedAssetsReasonControllerProvider)
      ..listen(
        fixedAssetsPlacesControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      );
    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: Text(context.loc.fixedAssetsInspection),
      ),
      body: SafeArea(
        child: switch (placeListInfo) {
          AsyncData(:final value) => Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.loc.branchAssetsCount(
                      value.progress.branchName,
                      value.progress.branchCount,
                      value.progress.branchAllCount,
                    ),
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 16),
                  const ListHeader(),
                  Expanded(
                    child: ListContent(value.places),
                  ),
                ],
              ),
            ),
          AsyncError() => const SizedBox.shrink(),
          _ => const Center(child: CircularProgressIndicator())
        },
      ),
      bottomNavigationBar: const BottomBar(),
    );
  }
}

class BottomBar extends StatelessWidget {
  const BottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;
    return BottomAppBar(
      child: Row(
        children: [
          TextButton(
            onPressed: () => context.pop(),
            child: Text(
              context.loc.end,
              style: texts.titleLarge?.copyWith(
                color: colors.primary,
              ),
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }
}

class ListHeader extends StatelessWidget {
  const ListHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final textStyle = Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: colors.secondary,
        );
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Text(context.loc.placeName, style: textStyle),
        Text(context.loc.number, style: textStyle),
        Text(context.loc.status, style: textStyle),
      ],
    );
  }
}

class ListContent extends StatelessWidget {
  const ListContent(this.places, {super.key});

  final List<Place> places;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    return ListView.separated(
      itemBuilder: (_, index) => PlaceListTile(
        place: places[index],
        onPressed: () => FixedAssetsListRoute(
          places[index].placeCode,
          places[index].placeName,
        ).go(context),
      ),
      itemCount: places.length,
      separatorBuilder: (BuildContext context, int index) => Divider(height: 1, color: colors.sub),
    );
  }
}
