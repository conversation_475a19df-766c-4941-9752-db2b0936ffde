import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../application/fixed_asset_service.dart';
import '../../domain/place_list.dart';

part 'fixed_assets_inspection_controller.g.dart';

/// 設定場所画面で場所情報取得
@riverpod
class FixedAssetsPlacesController extends _$FixedAssetsPlacesController {
  @override
  Future<PlaceList> build() {
    return ref.read(fixedAssetServiceProvider).listPlaces();
  }
}
