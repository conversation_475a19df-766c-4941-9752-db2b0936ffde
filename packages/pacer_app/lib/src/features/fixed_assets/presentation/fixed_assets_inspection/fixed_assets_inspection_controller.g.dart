// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fixed_assets_inspection_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fixedAssetsPlacesControllerHash() => r'efc0e52f212e0808431fe58965d47222b8eee379';

/// 設定場所画面で場所情報取得
///
/// Copied from [FixedAssetsPlacesController].
@ProviderFor(FixedAssetsPlacesController)
final fixedAssetsPlacesControllerProvider =
    AutoDisposeAsyncNotifierProvider<FixedAssetsPlacesController, PlaceList>.internal(
  FixedAssetsPlacesController.new,
  name: r'fixedAssetsPlacesControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fixedAssetsPlacesControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FixedAssetsPlacesController = AutoDisposeAsyncNotifier<PlaceList>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
