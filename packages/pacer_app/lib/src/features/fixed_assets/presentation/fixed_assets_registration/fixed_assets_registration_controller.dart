import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../application/fixed_asset_service.dart';
import '../../domain/fixed_asset.dart';
import '../fixed_assets_list/fixed_assets_list_controller.dart';

part 'fixed_assets_registration_controller.g.dart';

/// 固定資産登録
@riverpod
class FixedAssetsRegistrationController extends _$FixedAssetsRegistrationController {
  @override
  FutureOr<FixedAsset?> build() {
    return null;
  }

  /// アセットコードをスキャンする
  Future<void> scan(String? code) async {
    if (code == null) return;
    if (code.isEmpty) return;

    /// string can contain other infor
    /// each one is in a new line
    /// first line is the code we need
    final realCode = code.split('\n').first.trim();
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(fixedAssetServiceProvider).searchFixedAsset(realCode),
    );
  }

  /// アセットコードをスキャンする
  Future<void> receiveAsset({
    required String code,
    String? assetStoreCode,
    required String placeCode,
    required String placeName,
    required int reasonCode,
    required int receiveReasonCode,
    required bool hasTransferred,
  }) async {
    await ref.read(fixedAssetServiceProvider).receiveAsset(
          code: code,
          assetStoreCode: assetStoreCode,
          placeCode: placeCode,
          placeName: placeName,
          reasonCode: reasonCode,
          receiveReasonCode: receiveReasonCode,
          hasTransferred: hasTransferred,
        );
    ref.read(fixedAssetsProgressControllerProvider(placeCode).notifier).refresh();
  }

  /// 資産ステータスのみを更新する
  Future<void> updateAssetStatus({
    required String code,
    String? assetStoreCode,
    required String placeCode,
    required String placeName,
    required int reasonCode,
    int? receiveReasonCode,
  }) async {
    await ref.read(fixedAssetServiceProvider).updateAssetStatus(
          code: code,
          assetStoreCode: assetStoreCode,
          placeCode: placeCode,
          placeName: placeName,
          reasonCode: reasonCode,
          receiveReasonCode: receiveReasonCode,
        );
    ref.read(fixedAssetsProgressControllerProvider(placeCode).notifier).refresh();
  }
}
