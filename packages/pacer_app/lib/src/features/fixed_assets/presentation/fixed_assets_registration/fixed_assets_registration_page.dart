// ignore_for_file: public_member_api_docs
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../common_widgets/pacer_back_button.dart';
import '../../../../localization/app_localizations_context.dart';
import '../../../authentication/data/auth_repository.dart';
import '../../../device/application/pacer_service.dart';
import '../../../device/presentation/scan_window.dart';
import '../../domain/fixed_asset.dart';
import '../../domain/reason.dart';
import '../common_widgets/dialog_helpers.dart';
import '../common_widgets/register_table.dart';
import '../fixed_assets_list/fixed_assets_list_controller.dart';
import 'fixed_assets_registration_controller.dart';

class FixedAssetsRegistrationPage extends ConsumerWidget {
  const FixedAssetsRegistrationPage(
    this.placeCode,
    this.placeName, {
    super.key,
    required this.isEnabledMoveToPlace,
  });

  final String placeCode;
  final String placeName;
  final bool isEnabledMoveToPlace;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref
      ..listen(
        scanCodeProvider,
        (_, state) {
          ref.read(fixedAssetsRegistrationControllerProvider.notifier).scan(state.value);
        },
      )
      ..listen(
        fixedAssetsRegistrationControllerProvider,
        (_, state) {
          state.showAlertDialogOnError(context);
          final currentUser = ref.read(authRepositoryProvider).currentUser;

          switch (state) {
            case AsyncData(:final value?) when value.currentBranchCode != currentUser?.clockInStore.code:
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _showMoveDialog(
                  ref,
                  value,
                  context.loc.receiveConfirm(
                    value.currentBranchName ?? '',
                    currentUser?.clockInStore.name ?? '',
                  ),
                );
              });
            case AsyncData(:final value?)
                when value.branchCode != currentUser?.clockInStore.code && value.currentPlaceCode != placeCode:
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _showMoveDialog(
                  ref,
                  value,
                  context.loc.receiveConfirm(
                    value.currentPlaceName ?? '',
                    placeName,
                  ),
                );
              });
            case AsyncData(:final value?) when value.branchCode != currentUser?.clockInStore.code:
              ref.read(fixedAssetsRegistrationControllerProvider.notifier).updateAssetStatus(
                    code: value.code,
                    assetStoreCode: value.branchCode,
                    placeCode: placeCode,
                    placeName: placeName,
                    reasonCode: ReasonType.move.index,
                    receiveReasonCode: ReasonType.scanned.index,
                  );
            case AsyncData(:final value?) when value.isEnabledMove == false && isEnabledMoveToPlace == false:
              ref.read(fixedAssetsRegistrationControllerProvider.notifier).updateAssetStatus(
                    code: value.code,
                    assetStoreCode: value.branchCode,
                    placeCode: placeCode,
                    placeName: placeName,
                    reasonCode: ReasonType.scanned.index,
                  );
            case AsyncData(:final value?) when value.currentPlaceCode != placeCode:
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _showMoveDialog(
                  ref,
                  value,
                  context.loc.receiveConfirm(
                    value.currentPlaceName ?? '',
                    placeName,
                  ),
                );
              });
            case AsyncData(:final value?) when value.placeCode != placeCode:
              ref.read(fixedAssetsRegistrationControllerProvider.notifier).updateAssetStatus(
                    code: value.code,
                    assetStoreCode: value.branchCode,
                    placeCode: placeCode,
                    placeName: placeName,
                    reasonCode: ReasonType.move.index,
                    receiveReasonCode: ReasonType.scanned.index,
                  );
            case AsyncData(:final value?):
              ref.read(fixedAssetsRegistrationControllerProvider.notifier).updateAssetStatus(
                    code: value.code,
                    assetStoreCode: value.branchCode,
                    placeCode: placeCode,
                    placeName: placeName,
                    reasonCode: ReasonType.scanned.index,
                  );
            default:
              break;
          }
        },
      );
    final asset = ref.watch(fixedAssetsRegistrationControllerProvider);
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: PacerAppBar(
          context: context,
          title: Text(context.loc.fixedAssetsRegistration),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _ProgressDetail(placeCode, placeName),
                const SizedBox(height: 16),
                switch (asset) {
                  AsyncLoading() => const Center(child: CircularProgressIndicator()),
                  AsyncData(:final value) => RegisterTable(
                      assetCode: value?.code,
                      assetName: value?.name,
                      productName: value?.productName,
                    ),
                  AsyncError() => const RegisterTable(),
                },
              ],
            ),
          ),
        ),
        bottomNavigationBar: const BottomAppBar(
          child: Row(children: [PacerBackButton()]),
        ),
        floatingActionButton: switch (asset) {
          AsyncLoading() => null,
          AsyncError() || AsyncData() => ScanFloatingIconButton(
              onScan: (code) => ref.read(fixedAssetsRegistrationControllerProvider.notifier).scan(code),
            ),
        },
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        resizeToAvoidBottomInset: false,
      ),
    );
  }

  Future<void> _showMoveDialog(
    WidgetRef ref,
    FixedAsset asset,
    String message,
  ) async {
    final context = ref.context;
    await showAlertDialog(
      context: context,
      title: context.loc.message,
      content: context.loc.differentStoreMessage,
    );

    // コンテキストがまだ有効であることを確認する
    if (!context.mounted) return;

    final allowMove = await showAlertDialog(
          context: context,
          title: context.loc.message,
          content: message,
          cancelActionText: context.loc.no,
          defaultActionText: context.loc.yes,
        ) ??
        false;

    if (allowMove) {
      await ref.read(fixedAssetsRegistrationControllerProvider.notifier).receiveAsset(
            code: asset.code,
            assetStoreCode: asset.branchCode,
            placeCode: placeCode,
            placeName: placeName,
            reasonCode: ReasonType.move.index,
            receiveReasonCode: ReasonType.scanned.index,
            hasTransferred: asset.hasTransferred,
          );
    } else {
      // 移動しない場合、スキャン済みへの更新のみ実行する
      await ref.read(fixedAssetsRegistrationControllerProvider.notifier).updateAssetStatus(
            code: asset.code,
            assetStoreCode: asset.branchCode,
            placeCode: placeCode,
            placeName: placeName,
            reasonCode: ReasonType.scanned.index,
          );
    }
  }
}

class _ProgressDetail extends ConsumerWidget {
  const _ProgressDetail(this.placeCode, this.placeName);

  final String placeCode;
  final String placeName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textStyle = Theme.of(context).textTheme.bodyLarge;
    final progress = ref.watch(fixedAssetsProgressControllerProvider(placeCode));
    return switch (progress) {
      AsyncData(:final value) => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.loc.branchAssetsCount(
                value.branchName,
                value.branchCount,
                value.branchAllCount,
              ),
              style: textStyle,
            ),
            Text(
              context.loc.branchAssetsCount(
                placeName,
                value.fulfill,
                value.total,
              ),
              style: textStyle,
            ),
          ],
        ),
      _ => const SizedBox.shrink(),
    };
  }
}
