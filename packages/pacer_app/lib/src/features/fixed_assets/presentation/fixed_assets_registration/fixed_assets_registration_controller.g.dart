// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fixed_assets_registration_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fixedAssetsRegistrationControllerHash() => r'be8ba55af9a83ebd2b4a49a0d8c3a4434b15070c';

/// 固定資産登録
///
/// Copied from [FixedAssetsRegistrationController].
@ProviderFor(FixedAssetsRegistrationController)
final fixedAssetsRegistrationControllerProvider =
    AutoDisposeAsyncNotifierProvider<FixedAssetsRegistrationController, FixedAsset?>.internal(
  FixedAssetsRegistrationController.new,
  name: r'fixedAssetsRegistrationControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$fixedAssetsRegistrationControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FixedAssetsRegistrationController = AutoDisposeAsyncNotifier<FixedAsset?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
