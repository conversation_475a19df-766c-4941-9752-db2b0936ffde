Legend: {
  Type: {
    Widget.shape: circle
    Provider.shape: rectangle
  }
  Arrows: {
    "." -> "..": read: {style.stroke-dash: 4}
    "." -> "..": listen
    "." -> "..": watch: {style.stroke-width: 4}
  }
}

fixedAssetsReasonControllerProvider: "fixedAssetsReasonControllerProvider"
fixedAssetsReasonControllerProvider.shape: rectangle
fixedAssetsReasonControllerProvider.tooltip: "実査システム＿理由一覧"
fixedAssetServiceProvider: "fixedAssetServiceProvider"
fixedAssetServiceProvider.shape: rectangle
fixedAssetServiceProvider.tooltip: "provider生成コード"
fixedAssetsAllBranchControllerProvider: "fixedAssetsAllBranchControllerProvider"
fixedAssetsAllBranchControllerProvider.shape: rectangle
fixedAssetsAllBranchControllerProvider.tooltip: "実査システム＿場所一覧"
fixedAssetsAllPlacesControllerProvider: "fixedAssetsAllPlacesControllerProvider"
fixedAssetsAllPlacesControllerProvider.shape: rectangle
fixedAssetsAllPlacesControllerProvider.tooltip: "実査システム＿場所一覧"
fixedAssetsProgressControllerProvider: "fixedAssetsProgressControllerProvider"
fixedAssetsProgressControllerProvider.shape: rectangle
fixedAssetsProgressControllerProvider.tooltip: "固定資産実査進行状況"
fixedAssetsListControllerProvider: "fixedAssetsListControllerProvider"
fixedAssetsListControllerProvider.shape: rectangle
fixedAssetsListControllerProvider.tooltip: "実査システム＿固定資産一覧"
fixedAssetsPlacesControllerProvider: "fixedAssetsPlacesControllerProvider"
fixedAssetsPlacesControllerProvider.shape: rectangle
fixedAssetsPlacesControllerProvider.tooltip: "設定場所画面で場所情報取得"
updateFixedAssetStatusControllerProvider: "updateFixedAssetStatusControllerProvider"
updateFixedAssetStatusControllerProvider.shape: rectangle
updateFixedAssetStatusControllerProvider.tooltip: "実査システム＿固定資産一覧_更新"
fixedAssetsRegistrationControllerProvider: "fixedAssetsRegistrationControllerProvider"
fixedAssetsRegistrationControllerProvider.shape: rectangle
fixedAssetsRegistrationControllerProvider.tooltip: "固定資産登録"
scanCodeProvider: "scanCodeProvider"
scanCodeProvider.shape: rectangle
scanCodeProvider.tooltip: "カメラやバーコードスキャナでスキャンしたコードの状態"
FixedAssetsListPage.shape: circle
_ListContent.shape: circle
FixedAssetsInspectionPage.shape: circle
PlaceListDialog.shape: circle
PlaceListDialog.tooltip: "ダイアログにはすべての場所が含まれており、"
ReasonDropDownButton.shape: circle
RegisterTable.shape: circle
BranchListDialog.shape: circle
BranchListDialog.tooltip: "ダイアログにはすべてのストアが含まれており、"
FixedAssetsRegistrationPage.shape: circle
_ProgressDetail.shape: circle

fixedAssetsProgressControllerProvider -> FixedAssetsListPage: {style.stroke-width: 4}
fixedAssetsListControllerProvider -> _ListContent: {style.stroke-width: 4}
fixedAssetsListControllerProvider -> _ListContent
fixedAssetsPlacesControllerProvider -> FixedAssetsInspectionPage: {style.stroke-width: 4}
fixedAssetsPlacesControllerProvider -> FixedAssetsInspectionPage
fixedAssetsAllPlacesControllerProvider -> PlaceListDialog: {style.stroke-width: 4}
fixedAssetsReasonControllerProvider -> ReasonDropDownButton: {style.stroke-width: 4}
updateFixedAssetStatusControllerProvider -> ReasonDropDownButton: {style.stroke-width: 4}
updateFixedAssetStatusControllerProvider -> ReasonDropDownButton
updateFixedAssetStatusControllerProvider -> ReasonDropDownButton: {style.stroke-dash: 4}
updateFixedAssetStatusControllerProvider -> ReasonDropDownButton: {style.stroke-dash: 4}
updateFixedAssetStatusControllerProvider -> ReasonDropDownButton: {style.stroke-dash: 4}
fixedAssetsRegistrationControllerProvider -> RegisterTable: {style.stroke-dash: 4}
fixedAssetsAllBranchControllerProvider -> BranchListDialog: {style.stroke-width: 4}
fixedAssetsRegistrationControllerProvider -> FixedAssetsRegistrationPage: {style.stroke-width: 4}
scanCodeProvider -> FixedAssetsRegistrationPage
fixedAssetsRegistrationControllerProvider -> FixedAssetsRegistrationPage
fixedAssetsRegistrationControllerProvider -> FixedAssetsRegistrationPage: {style.stroke-dash: 4}
fixedAssetsRegistrationControllerProvider -> FixedAssetsRegistrationPage: {style.stroke-dash: 4}
fixedAssetsRegistrationControllerProvider -> FixedAssetsRegistrationPage: {style.stroke-dash: 4}
fixedAssetsProgressControllerProvider -> _ProgressDetail: {style.stroke-width: 4}
fixedAssetServiceProvider -> fixedAssetsReasonControllerProvider: {style.stroke-dash: 4}
fixedAssetServiceProvider -> fixedAssetsAllBranchControllerProvider: {style.stroke-dash: 4}
fixedAssetServiceProvider -> fixedAssetsAllPlacesControllerProvider: {style.stroke-dash: 4}
fixedAssetServiceProvider -> fixedAssetsPlacesControllerProvider: {style.stroke-dash: 4}
