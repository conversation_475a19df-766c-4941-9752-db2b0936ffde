import 'package:grpc/grpc.dart';

import '../../../exceptions/app_exception.dart';

/// Grpcエラーの拡張
extension GrpcErrorX on GrpcError {
  /// grpcエラーをハンドリングする
  AppException handleGrpcError() {
    return switch (this) {
      GrpcError(code: StatusCode.notFound) => AssetNotFoundException(),
      GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
      GrpcError(code: _) => UnknownException(message ?? code.toString()),
    };
  }
}
