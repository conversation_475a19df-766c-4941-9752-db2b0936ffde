import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/asset/v1/v1.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/branch.dart';
import '../domain/fixed_asset.dart';
import '../domain/place.dart';
import '../domain/place_detail.dart';
import '../domain/place_list.dart';
import '../domain/progress.dart';
import '../domain/reason.dart';
import 'handle_grpc_error.dart';

part 'fixed_asset_repository.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
FixedAssetRepository fixedAssetRepository(FixedAssetRepositoryRef ref) {
  return FixedAssetRepository();
}

/// 固定資産リポジトリ
class FixedAssetRepository {
  static const _timeout = Duration(seconds: 20);
  final _uri = Env.getApiBaseUrl();
  final _callOptions = CallOptions(timeout: _timeout);

  /// 設定場所画面で場所情報取得
  Future<PlaceList> listPlaces({
    required String storeCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetPlaceInfoRequest(
      storeCode: storeCode,
    );

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.getPlaceInfo(request);

      if (response.placeInfo.isEmpty) {
        throw UnknownException('現在の店舗では資産が登録されていません');
      }

      return PlaceList.fromGrpc(response);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 現在場所の詳細情報取得
  Future<PlaceDetail> getPlaceDetail({
    required String storeCode,
    required String placeCode,
    required AppUser caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetPlacesRequest(
      storeCode: storeCode,
      placeCode: placeCode,
    );

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.getPlaces(request);

      return PlaceDetail.fromGrpc(response.placesInfo);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// QRコードより 固定資産情報取得
  Future<FixedAsset> searchFixedAsset({
    required String storeCode,
    required String assetCode,
    required AppUser caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetFixedAssetsInfoRequest(
      storeCode: storeCode,
      qrcode: assetCode,
    );

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final response = await stub.getFixedAssetsInfo(request);

      /// アセットが見つからない場合、response.fixedAssetsInfo は null ではありません
      /// 代わりに、すべてのプロパティは空の文字列になります
      if (response.fixedAssetsInfo.fixedAssetQrcode.isEmpty) {
        throw const GrpcError.notFound();
      }
      return FixedAsset.fromScanGrpc(response.fixedAssetsInfo);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 資産ステータスのみを更新する
  Future<void> updateAssetStatus({
    required String scanStoreCode,
    required String scanStoreName,
    required String assetStoreCode,
    required String placeCode,
    required String placeName,
    required String assetCode,
    required int? reasonCode,
    required AppUser caller,
    int? receiveReasonCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = UpdateAssetStatusRequest(
      storeCode: assetStoreCode,
      receiveBranchCode: int.tryParse(scanStoreCode),
      scanBranchCode: int.tryParse(scanStoreCode),
      receiveBranchName: scanStoreName,
      receivePlaceCode: int.tryParse(placeCode),
      scanPlace: int.tryParse(placeCode),
      receivePlaceName: placeName,
      reasonCode: reasonCode,
      receiveReasonCode: receiveReasonCode,
      qrcode: assetCode,
    );

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      await stub.updateAssetStatus(request);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 固定資産実査進行状況
  Future<Progress> getProgress(
    String storeCode,
    String placeCode,
    AppUser appUser,
  ) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetProgressRequest(
      storeCode: storeCode,
      placeCode: placeCode,
    );

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: appUser)],
    );

    try {
      final response = await stub.getProgress(request);

      return Progress.fromProgressGrpc(response.progressInfo);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 実査システム＿固定資産一覧
  Future<List<FixedAsset>> listFixedAssets(
    String storeCode,
    String placeCode,
    AppUser appUser,
  ) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetFixedAssetsInfoListRequest(
      storeCode: storeCode,
      placeCode: placeCode,
    );

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: appUser)],
    );

    try {
      final response = await stub.getFixedAssetsInfoList(request);

      return response.fixedAssetsInfo.map(FixedAsset.fromGrpc).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 実査システム＿理由一覧
  Future<List<Reason>> listReasons(String storeCode, AppUser appUser) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetReasonInfoListRequest(storeCode: storeCode);

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: appUser)],
    );

    try {
      final response = await stub.getReasonInfoList(request);

      return response.reasonInfo.map(Reason.fromGrpc).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 実査システム＿固定資産一覧_更新
  Future<void> updateAsset({
    required String assetCode,
    required int reasonCode,
    required String storeCode,
    required bool hasTransferred,
    required AppUser appUser,
    int? destinationPlaceCode,
    int? destinationBranchCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = UpdateFixedAssetsInfoRequest(
      fixedAssetQrcode: assetCode,
      storeCode: storeCode,
      qrBranchCode: int.tryParse(storeCode),
      reasonCode: reasonCode,
      isReceive: hasTransferred,
      receiveBranchCode: destinationBranchCode,
      receivePlaceCode: destinationPlaceCode,
    );

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: appUser)],
    );

    try {
      await stub.updateFixedAssetsInfo(request);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 店舗間で資産を移動する
  Future<void> moveAssetOutsideStore({
    required String assetCode,
    required int reasonCode,
    required String storeCode,
    required bool hasTransferred,
    required AppUser appUser,
    required int destinationPlaceCode,
    required int destinationBranchCode,
  }) {
    return updateAsset(
      assetCode: assetCode,
      reasonCode: ReasonType.move.index,
      storeCode: storeCode,
      hasTransferred: hasTransferred,
      appUser: appUser,
      destinationPlaceCode: destinationPlaceCode,
      destinationBranchCode: destinationBranchCode,
    );
  }

  /// アセットを場所間で移動する
  Future<void> moveAssetInsideStore({
    required String assetCode,
    required int reasonCode,
    required String storeCode,
    required String assetStoreCode,
    required bool hasTransferred,
    required AppUser appUser,
    required int destinationPlaceCode,
  }) {
    return updateAsset(
      assetCode: assetCode,
      reasonCode: ReasonType.move.index,
      storeCode: assetStoreCode,
      hasTransferred: hasTransferred,
      appUser: appUser,
      destinationPlaceCode: destinationPlaceCode,
      destinationBranchCode: int.tryParse(storeCode),
    );
  }

  /// 実査システム＿店舗一覧
  Future<List<Branch>> listAllBranch(String storeCode, AppUser appUser) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetBranchListInfoRequest(
      storeCode: storeCode,
    );

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: appUser)],
    );

    try {
      final response = await stub.getBranchListInfo(request);

      return response.branchInfo.map(Branch.fromGrpc).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 実査システム＿場所一覧
  Future<List<Place>> listAllPlaces(String storeCode, AppUser appUser) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetPlaceListInfoRequest(
      storeCode: storeCode,
    );

    final stub = AssetServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: appUser)],
    );

    try {
      final response = await stub.getPlaceListInfo(request);

      return response.placeInfo.map(Place.fromListGrpc).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
