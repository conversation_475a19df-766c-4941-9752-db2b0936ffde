// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fixed_asset_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fixedAssetRepositoryHash() => r'9bbde9a7ab51bf5391f4e80a7888eb482180e993';

/// provider生成コード
///
/// Copied from [fixedAssetRepository].
@ProviderFor(fixedAssetRepository)
final fixedAssetRepositoryProvider = Provider<FixedAssetRepository>.internal(
  fixedAssetRepository,
  name: r'fixedAssetRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fixedAssetRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FixedAssetRepositoryRef = ProviderRef<FixedAssetRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
