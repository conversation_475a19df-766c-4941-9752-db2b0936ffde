import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/fixed_asset_repository.dart';
import '../domain/branch.dart';
import '../domain/fixed_asset.dart';
import '../domain/place.dart';
import '../domain/place_detail.dart';
import '../domain/place_list.dart';
import '../domain/progress.dart';
import '../domain/reason.dart';

part 'fixed_asset_service.g.dart';

/// provider生成コード
@Riverpod()
FixedAssetService fixedAssetService(FixedAssetServiceRef ref) {
  return FixedAssetService(ref);
}

/// 固定資産サービス
class FixedAssetService {
  /// コンストラクタ関数
  FixedAssetService(this.ref);

  /// Riverpod ref
  final Ref ref;

  FixedAssetRepository get _repository => ref.read(fixedAssetRepositoryProvider);

  /// 設定場所画面で場所情報取得
  Future<PlaceList> listPlaces() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.listPlaces(
      storeCode: store.code,
      caller: caller,
    );
  }

  /// 現在場所の詳細情報取得
  Future<PlaceDetail> getPlaceDetail(String placeCode) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.getPlaceDetail(
      storeCode: store.code,
      placeCode: placeCode,
      caller: caller,
    );
  }

  /// QRコードより 固定資産情報取得
  Future<FixedAsset> searchFixedAsset(String code) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.searchFixedAsset(
      storeCode: store.code,
      assetCode: code,
      caller: caller,
    );
  }

  /// 資産ステータスのみを更新する
  Future<void> updateAssetStatus({
    required String code,
    String? assetStoreCode,
    required String placeCode,
    required String placeName,
    required int reasonCode,
    int? receiveReasonCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.updateAssetStatus(
      assetStoreCode: assetStoreCode ?? store.code,
      scanStoreCode: store.code,
      scanStoreName: store.name,
      assetCode: code,
      reasonCode: reasonCode,
      placeCode: placeCode,
      placeName: placeName,
      caller: caller,
      receiveReasonCode: receiveReasonCode,
    );
  }

  /// アセットをここに移管する
  Future<void> receiveAsset({
    required String code,
    String? assetStoreCode,
    required String placeCode,
    required String placeName,
    required int reasonCode,
    required int receiveReasonCode,
    required bool hasTransferred,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.updateAssetStatus(
      assetStoreCode: assetStoreCode ?? store.code,
      scanStoreCode: store.code,
      scanStoreName: store.name,
      assetCode: code,
      reasonCode: reasonCode,
      receiveReasonCode: receiveReasonCode,
      placeCode: placeCode,
      placeName: placeName,
      caller: caller,
    );
  }

  /// 実査システム＿固定資産一覧
  Future<List<FixedAsset>> listFixedAssets(String placeCode) {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.listFixedAssets(
      store.code,
      placeCode,
      caller,
    );
  }

  /// 実査システム＿固定資産一覧_更新
  Future<void> updateAsset({
    required String code,
    required int reasonCode,
    required bool hasTransferred,
    String? assetStoreCode,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;
    return _repository.updateAsset(
      storeCode: assetStoreCode ?? store.code,
      hasTransferred: hasTransferred,
      assetCode: code,
      reasonCode: reasonCode,
      appUser: caller,
    );
  }

  /// 固定資産実査進行状況
  Future<Progress> getProgress(String placeCode) {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;
    return _repository.getProgress(store.code, placeCode, caller);
  }

  /// 実査システム＿理由一覧
  Future<List<Reason>> listReasons() {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;
    return _repository.listReasons(store.code, caller);
  }

  /// ブランチ間でアセットを移動する
  Future<void> moveAssetOutsideStore({
    required String assetCode,
    required String destinationBranchCode,
    required String destinationPlaceCode,
    required bool hasTransferred,
    String? assetStoreCode,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;
    return _repository.moveAssetOutsideStore(
      storeCode: assetStoreCode ?? store.code,
      hasTransferred: hasTransferred,
      assetCode: assetCode,
      reasonCode: ReasonType.move.index,
      destinationBranchCode: int.parse(destinationBranchCode),
      destinationPlaceCode: int.parse(destinationPlaceCode),
      appUser: caller,
    );
  }

  /// ブランチ間でアセットを移動する
  Future<void> moveAssetInsideStore({
    required String assetCode,
    required String destinationPlaceCode,
    required bool hasTransferred,
    String? assetStoreCode,
  }) {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;
    return _repository.moveAssetInsideStore(
      storeCode: store.code,
      assetStoreCode: assetStoreCode ?? store.code,
      hasTransferred: hasTransferred,
      assetCode: assetCode,
      reasonCode: ReasonType.move.index,
      destinationPlaceCode: int.parse(destinationPlaceCode),
      appUser: caller,
    );
  }

  /// 実査システム＿店舗一覧
  Future<List<Branch>> listAllBranch() {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;
    return _repository.listAllBranch(store.code, caller);
  }

  /// 実査システム＿店舗一覧
  Future<List<Place>> listAllPlaces() {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;
    return _repository.listAllPlaces(store.code, caller);
  }
}
