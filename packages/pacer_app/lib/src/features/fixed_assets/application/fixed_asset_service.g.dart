// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fixed_asset_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fixedAssetServiceHash() => r'ac754ba0dd35c8b0ef891ce58f9f3a3f5eb76b2a';

/// provider生成コード
///
/// Copied from [fixedAssetService].
@ProviderFor(fixedAssetService)
final fixedAssetServiceProvider = AutoDisposeProvider<FixedAssetService>.internal(
  fixedAssetService,
  name: r'fixedAssetServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fixedAssetServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FixedAssetServiceRef = AutoDisposeProviderRef<FixedAssetService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
