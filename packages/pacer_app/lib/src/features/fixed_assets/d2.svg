<?xml version="1.0" encoding="utf-8"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" d2Version="v0.6.3-HEAD" preserveAspectRatio="xMidYMid meet" viewBox="0 0 4656 1406"><svg id="d2-svg" class="d2-1694934913" width="4656" height="1406" viewBox="-101 -112 4656 1406"><rect x="-101.000000" y="-112.000000" width="4656.000000" height="1406.000000" rx="0.000000" class=" fill-N7" stroke-width="0" /><style type="text/css"><![CDATA[
.d2-1694934913 .text {
	font-family: "d2-1694934913-font-regular";
}
@font-face {
	font-family: d2-1694934913-font-regular;
	src: url("data:application/font-woff;base64,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");
}
.appendix-icon {
	filter: drop-shadow(0px 0px 32px rgba(31, 36, 58, 0.1));
}
.d2-1694934913 .text-bold {
	font-family: "d2-1694934913-font-bold";
}
@font-face {
	font-family: d2-1694934913-font-bold;
	src: url("data:application/font-woff;base64,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");
}
.d2-1694934913 .text-italic {
	font-family: "d2-1694934913-font-italic";
}
@font-face {
	font-family: d2-1694934913-font-italic;
	src: url("data:application/font-woff;base64,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");
}]]></style><style type="text/css"><![CDATA[.shape {
  shape-rendering: geometricPrecision;
  stroke-linejoin: round;
}
.connection {
  stroke-linecap: round;
  stroke-linejoin: round;
}
.blend {
  mix-blend-mode: multiply;
  opacity: 0.5;
}

		.d2-1694934913 .fill-N1{fill:#0A0F25;}
		.d2-1694934913 .fill-N2{fill:#676C7E;}
		.d2-1694934913 .fill-N3{fill:#9499AB;}
		.d2-1694934913 .fill-N4{fill:#CFD2DD;}
		.d2-1694934913 .fill-N5{fill:#DEE1EB;}
		.d2-1694934913 .fill-N6{fill:#EEF1F8;}
		.d2-1694934913 .fill-N7{fill:#FFFFFF;}
		.d2-1694934913 .fill-B1{fill:#0D32B2;}
		.d2-1694934913 .fill-B2{fill:#0D32B2;}
		.d2-1694934913 .fill-B3{fill:#E3E9FD;}
		.d2-1694934913 .fill-B4{fill:#E3E9FD;}
		.d2-1694934913 .fill-B5{fill:#EDF0FD;}
		.d2-1694934913 .fill-B6{fill:#F7F8FE;}
		.d2-1694934913 .fill-AA2{fill:#4A6FF3;}
		.d2-1694934913 .fill-AA4{fill:#EDF0FD;}
		.d2-1694934913 .fill-AA5{fill:#F7F8FE;}
		.d2-1694934913 .fill-AB4{fill:#EDF0FD;}
		.d2-1694934913 .fill-AB5{fill:#F7F8FE;}
		.d2-1694934913 .stroke-N1{stroke:#0A0F25;}
		.d2-1694934913 .stroke-N2{stroke:#676C7E;}
		.d2-1694934913 .stroke-N3{stroke:#9499AB;}
		.d2-1694934913 .stroke-N4{stroke:#CFD2DD;}
		.d2-1694934913 .stroke-N5{stroke:#DEE1EB;}
		.d2-1694934913 .stroke-N6{stroke:#EEF1F8;}
		.d2-1694934913 .stroke-N7{stroke:#FFFFFF;}
		.d2-1694934913 .stroke-B1{stroke:#0D32B2;}
		.d2-1694934913 .stroke-B2{stroke:#0D32B2;}
		.d2-1694934913 .stroke-B3{stroke:#E3E9FD;}
		.d2-1694934913 .stroke-B4{stroke:#E3E9FD;}
		.d2-1694934913 .stroke-B5{stroke:#EDF0FD;}
		.d2-1694934913 .stroke-B6{stroke:#F7F8FE;}
		.d2-1694934913 .stroke-AA2{stroke:#4A6FF3;}
		.d2-1694934913 .stroke-AA4{stroke:#EDF0FD;}
		.d2-1694934913 .stroke-AA5{stroke:#F7F8FE;}
		.d2-1694934913 .stroke-AB4{stroke:#EDF0FD;}
		.d2-1694934913 .stroke-AB5{stroke:#F7F8FE;}
		.d2-1694934913 .background-color-N1{background-color:#0A0F25;}
		.d2-1694934913 .background-color-N2{background-color:#676C7E;}
		.d2-1694934913 .background-color-N3{background-color:#9499AB;}
		.d2-1694934913 .background-color-N4{background-color:#CFD2DD;}
		.d2-1694934913 .background-color-N5{background-color:#DEE1EB;}
		.d2-1694934913 .background-color-N6{background-color:#EEF1F8;}
		.d2-1694934913 .background-color-N7{background-color:#FFFFFF;}
		.d2-1694934913 .background-color-B1{background-color:#0D32B2;}
		.d2-1694934913 .background-color-B2{background-color:#0D32B2;}
		.d2-1694934913 .background-color-B3{background-color:#E3E9FD;}
		.d2-1694934913 .background-color-B4{background-color:#E3E9FD;}
		.d2-1694934913 .background-color-B5{background-color:#EDF0FD;}
		.d2-1694934913 .background-color-B6{background-color:#F7F8FE;}
		.d2-1694934913 .background-color-AA2{background-color:#4A6FF3;}
		.d2-1694934913 .background-color-AA4{background-color:#EDF0FD;}
		.d2-1694934913 .background-color-AA5{background-color:#F7F8FE;}
		.d2-1694934913 .background-color-AB4{background-color:#EDF0FD;}
		.d2-1694934913 .background-color-AB5{background-color:#F7F8FE;}
		.d2-1694934913 .color-N1{color:#0A0F25;}
		.d2-1694934913 .color-N2{color:#676C7E;}
		.d2-1694934913 .color-N3{color:#9499AB;}
		.d2-1694934913 .color-N4{color:#CFD2DD;}
		.d2-1694934913 .color-N5{color:#DEE1EB;}
		.d2-1694934913 .color-N6{color:#EEF1F8;}
		.d2-1694934913 .color-N7{color:#FFFFFF;}
		.d2-1694934913 .color-B1{color:#0D32B2;}
		.d2-1694934913 .color-B2{color:#0D32B2;}
		.d2-1694934913 .color-B3{color:#E3E9FD;}
		.d2-1694934913 .color-B4{color:#E3E9FD;}
		.d2-1694934913 .color-B5{color:#EDF0FD;}
		.d2-1694934913 .color-B6{color:#F7F8FE;}
		.d2-1694934913 .color-AA2{color:#4A6FF3;}
		.d2-1694934913 .color-AA4{color:#EDF0FD;}
		.d2-1694934913 .color-AA5{color:#F7F8FE;}
		.d2-1694934913 .color-AB4{color:#EDF0FD;}
		.d2-1694934913 .color-AB5{color:#F7F8FE;}.appendix text.text{fill:#0A0F25}.md{--color-fg-default:#0A0F25;--color-fg-muted:#676C7E;--color-fg-subtle:#9499AB;--color-canvas-default:#FFFFFF;--color-canvas-subtle:#EEF1F8;--color-border-default:#0D32B2;--color-border-muted:#0D32B2;--color-neutral-muted:#EEF1F8;--color-accent-fg:#0D32B2;--color-accent-emphasis:#0D32B2;--color-attention-subtle:#676C7E;--color-danger-fg:red;}.sketch-overlay-B1{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-B2{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-B3{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B6{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AA2{fill:url(#streaks-dark);mix-blend-mode:overlay}.sketch-overlay-AA4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AA5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AB4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AB5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N1{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-N2{fill:url(#streaks-dark);mix-blend-mode:overlay}.sketch-overlay-N3{fill:url(#streaks-normal);mix-blend-mode:color-burn}.sketch-overlay-N4{fill:url(#streaks-normal);mix-blend-mode:color-burn}.sketch-overlay-N5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N6{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N7{fill:url(#streaks-bright);mix-blend-mode:darken}.light-code{display: block}.dark-code{display: none}]]></style><g id="Legend"><g class="shape" ><rect x="0.000000" y="29.000000" width="696.000000" height="596.000000" class=" stroke-B1 fill-B4" style="stroke-width:2;" /></g><text x="348.000000" y="16.000000" class="text fill-N1" style="text-anchor:middle;font-size:28px">Legend</text></g><g id="fixedAssetsReasonControllerProvider"><g class="shape" ><rect x="1180.000000" y="473.000000" width="344.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1352.000000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">fixedAssetsReasonControllerProvider</text><title>実査システム＿理由一覧</title></g><g id="fixedAssetServiceProvider"><g class="shape" ><rect x="1843.000000" y="100.000000" width="265.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1975.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">fixedAssetServiceProvider</text><title>provider生成コード</title></g><g id="fixedAssetsAllBranchControllerProvider"><g class="shape" ><rect x="1584.000000" y="473.000000" width="361.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1764.500000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">fixedAssetsAllBranchControllerProvider</text><title>実査システム＿場所一覧</title></g><g id="fixedAssetsAllPlacesControllerProvider"><g class="shape" ><rect x="2005.000000" y="473.000000" width="356.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2183.000000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">fixedAssetsAllPlacesControllerProvider</text><title>実査システム＿場所一覧</title></g><g id="fixedAssetsProgressControllerProvider"><g class="shape" ><rect x="2826.000000" y="100.000000" width="355.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="3003.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">fixedAssetsProgressControllerProvider</text><title>固定資産実査進行状況</title></g><g id="fixedAssetsListControllerProvider"><g class="shape" ><rect x="3385.000000" y="100.000000" width="318.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="3544.000000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">fixedAssetsListControllerProvider</text><title>実査システム＿固定資産一覧</title></g><g id="fixedAssetsPlacesControllerProvider"><g class="shape" ><rect x="2421.000000" y="473.000000" width="338.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2590.000000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">fixedAssetsPlacesControllerProvider</text><title>設定場所画面で場所情報取得</title></g><g id="updateFixedAssetStatusControllerProvider"><g class="shape" ><rect x="736.000000" y="473.000000" width="384.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="928.000000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">updateFixedAssetStatusControllerProvider</text><title>実査システム＿固定資産一覧_更新</title></g><g id="fixedAssetsRegistrationControllerProvider"><g class="shape" ><rect x="3790.000000" y="100.000000" width="380.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="3980.000000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">fixedAssetsRegistrationControllerProvider</text><title>固定資産登録</title></g><g id="scanCodeProvider"><g class="shape" ><rect x="4230.000000" y="100.000000" width="207.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="4333.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">scanCodeProvider</text><title>カメラやバーコードスキャナでスキャンしたコードの状態</title></g><g id="FixedAssetsListPage"><g class="shape" ><ellipse rx="125.000000" ry="125.000000" cx="2944.000000" cy="506.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2944.000000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">FixedAssetsListPage</text></g><g id="_ListContent"><g class="shape" ><ellipse rx="88.000000" ry="88.000000" cx="3544.000000" cy="506.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="3544.000000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">_ListContent</text></g><g id="FixedAssetsInspectionPage"><g class="shape" ><ellipse rx="159.500000" ry="159.500000" cx="2590.500000" cy="1033.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2590.500000" y="1039.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">FixedAssetsInspectionPage</text></g><g id="PlaceListDialog"><g class="shape" ><ellipse rx="123.500000" ry="123.500000" cx="2183.500000" cy="1033.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2183.500000" y="1039.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">PlaceListDialog</text><title>ダイアログにはすべての場所が含まれており、</title></g><g id="ReasonDropDownButton"><g class="shape" ><ellipse rx="147.500000" ry="147.500000" cx="1121.500000" cy="1033.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1121.500000" y="1039.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">ReasonDropDownButton</text></g><g id="RegisterTable"><g class="shape" ><ellipse rx="93.000000" ry="93.000000" cx="3799.000000" cy="506.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="3799.000000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">RegisterTable</text></g><g id="BranchListDialog"><g class="shape" ><ellipse rx="132.000000" ry="132.000000" cx="1765.000000" cy="1034.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1765.000000" y="1039.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">BranchListDialog</text><title>ダイアログにはすべてのストアが含まれており、</title></g><g id="FixedAssetsRegistrationPage"><g class="shape" ><ellipse rx="168.500000" ry="168.500000" cx="4190.500000" cy="505.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="4190.500000" y="511.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">FixedAssetsRegistrationPage</text></g><g id="_ProgressDetail"><g class="shape" ><ellipse rx="103.500000" ry="103.500000" cx="3232.500000" cy="505.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="3232.500000" y="511.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">_ProgressDetail</text></g><g id="Legend.Type"><g class="shape" ><rect x="30.000000" y="417.000000" width="345.000000" height="178.000000" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="202.500000" y="405.000000" class="text fill-N1" style="text-anchor:middle;font-size:24px">Type</text></g><g id="Legend.Arrows"><g class="shape" ><rect x="426.000000" y="70.000000" width="240.000000" height="499.000000" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="546.000000" y="58.000000" class="text fill-N1" style="text-anchor:middle;font-size:24px">Arrows</text></g><g id="Legend.Type.Widget"><g class="shape" ><ellipse rx="59.000000" ry="59.000000" cx="119.000000" cy="506.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="119.000000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Widget</text></g><g id="Legend.Type.Provider"><g class="shape" ><rect x="238.000000" y="473.000000" width="107.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="291.500000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Provider</text></g><g id="Legend.Arrows.&#34;.&#34;"><g class="shape" ><rect x="474.000000" y="100.000000" width="49.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="498.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">.</text></g><g id="Legend.Arrows.&#34;..&#34;"><g class="shape" ><rect x="513.000000" y="473.000000" width="54.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="540.000000" y="511.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">..</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[0]"><marker id="mk-2177206569" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" class="connection fill-B2" stroke-width="2" /> </marker><path d="M 484.267751 167.361132 C 465.799988 214.300003 474.412994 324.100006 526.707933 468.738320" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /><text x="474.500000" y="327.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">read:</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[1]"><marker id="mk-3488378134" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" class="connection fill-B1" stroke-width="2" /> </marker><path d="M 513.232249 167.361132 C 531.700012 214.300003 537.093994 324.100006 539.406946 468.500513" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-1694934913)" /><text x="537.000000" y="323.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">listen</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[2]"><marker id="mk-3519660172" markerWidth="16.000000" markerHeight="20.000000" refX="10.000000" refY="10.000000" viewBox="0.000000 0.000000 16.000000 20.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 16.000000,10.000000 0.000000,20.000000" class="connection fill-B1" stroke-width="4" /> </marker><path d="M 525.599412 154.319542 C 597.849976 211.690002 603.510010 324.100006 553.866107 465.893228" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /><text x="602.000000" y="304.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">watch:</text></g><g id="(fixedAssetsProgressControllerProvider -&gt; FixedAssetsListPage)[0]"><path d="M 2981.384017 168.027568 C 2951.800049 214.300003 2944.000000 305.799988 2944.000000 374.000000" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsListControllerProvider -&gt; _ListContent)[0]"><path d="M 3479.604871 166.915377 C 3391.050049 214.300003 3392.399902 317.000000 3484.610528 431.547245" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsListControllerProvider -&gt; _ListContent)[1]"><path d="M 3588.847630 166.703153 C 3652.050049 214.300003 3652.000000 315.399994 3588.986530 425.528156" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsPlacesControllerProvider -&gt; FixedAssetsInspectionPage)[0]"><path d="M 2558.779946 541.740569 C 2429.354980 687.099976 2396.500000 734.000000 2396.500000 749.000000 C 2396.500000 764.000000 2413.600098 842.599976 2477.262410 911.846823" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsPlacesControllerProvider -&gt; FixedAssetsInspectionPage)[1]"><path d="M 2591.601945 541.497886 C 2598.302002 687.099976 2600.000000 734.000000 2600.000000 749.000000 C 2600.000000 764.000000 2599.600098 834.000000 2598.159882 870.003197" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsAllPlacesControllerProvider -&gt; PlaceListDialog)[0]"><path d="M 2183.000000 542.500000 C 2183.000000 687.099976 2183.000000 734.000000 2183.000000 749.000000 C 2183.000000 764.000000 2183.000000 841.200012 2183.000000 903.000000" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsReasonControllerProvider -&gt; ReasonDropDownButton)[0]"><path d="M 1352.000000 542.500000 C 1352.000000 687.099976 1352.000000 734.000000 1352.000000 749.000000 C 1352.000000 764.000000 1327.599976 846.000000 1235.198819 929.312540" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /></g><g id="(updateFixedAssetStatusControllerProvider -&gt; ReasonDropDownButton)[0]"><path d="M 907.665465 542.101800 C 824.431030 687.099976 803.250000 734.000000 803.250000 749.000000 C 803.250000 764.000000 842.200012 849.599976 992.150358 948.155304" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /></g><g id="(updateFixedAssetStatusControllerProvider -&gt; ReasonDropDownButton)[1]"><path d="M 911.310618 541.301634 C 841.034973 687.099976 823.250000 734.000000 823.250000 749.000000 C 823.250000 764.000000 858.799988 849.000000 997.728052 946.699053" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-1694934913)" /></g><g id="(updateFixedAssetStatusControllerProvider -&gt; ReasonDropDownButton)[2]"><path d="M 928.199492 541.499967 C 929.036987 687.099976 929.250000 734.000000 929.250000 749.000000 C 929.250000 764.000000 947.799988 844.200012 1019.294460 922.053807" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(updateFixedAssetStatusControllerProvider -&gt; ReasonDropDownButton)[3]"><path d="M 959.869622 540.964914 C 1095.701050 687.099976 1130.000000 734.000000 1130.000000 749.000000 C 1130.000000 764.000000 1129.400024 836.400024 1127.193324 882.004675" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(updateFixedAssetStatusControllerProvider -&gt; ReasonDropDownButton)[4]"><path d="M 963.953480 540.902872 C 1112.505005 687.099976 1150.000000 734.000000 1150.000000 749.000000 C 1150.000000 764.000000 1148.199951 836.599976 1141.565681 883.040201" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsRegistrationControllerProvider -&gt; RegisterTable)[0]"><path d="M 3913.976560 166.424613 C 3822.149902 214.300003 3798.800049 312.200012 3798.992065 409.000008" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsAllBranchControllerProvider -&gt; BranchListDialog)[0]"><path d="M 1764.500000 542.500000 C 1764.500000 687.099976 1764.500000 734.000000 1764.500000 749.000000 C 1764.500000 764.000000 1764.599976 839.599976 1764.955126 895.000144" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsRegistrationControllerProvider -&gt; FixedAssetsRegistrationPage)[0]"><path d="M 3975.206332 168.485592 C 3970.699951 214.300003 3989.800049 307.000000 4066.013545 382.087234" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /></g><g id="(scanCodeProvider -&gt; FixedAssetsRegistrationPage)[0]"><path d="M 4333.000000 167.500000 C 4333.000000 214.300003 4322.799805 302.399994 4284.208789 360.665146" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsRegistrationControllerProvider -&gt; FixedAssetsRegistrationPage)[1]"><path d="M 3983.695779 167.490395 C 3988.300049 214.300003 4006.800049 305.799988 4073.291436 378.056587" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsRegistrationControllerProvider -&gt; FixedAssetsRegistrationPage)[2]"><path d="M 4032.932932 166.580620 C 4107.250000 214.300003 4129.600098 298.399994 4141.872254 340.162268" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsRegistrationControllerProvider -&gt; FixedAssetsRegistrationPage)[3]"><path d="M 4039.991421 166.483592 C 4124.648926 214.300003 4148.398926 297.600006 4156.204775 336.079845" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsRegistrationControllerProvider -&gt; FixedAssetsRegistrationPage)[4]"><path d="M 4047.035892 166.400328 C 4142.049805 214.300003 4167.200195 297.200012 4170.629075 334.017235" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetsProgressControllerProvider -&gt; _ProgressDetail)[0]"><path d="M 3087.273646 166.643193 C 3202.899902 214.300003 3232.600098 310.000000 3232.969573 395.000066" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetServiceProvider -&gt; fixedAssetsReasonControllerProvider)[0]"><path d="M 1840.522151 153.181833 C 1450.099976 211.776993 1352.000000 324.100006 1352.000000 468.500000" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetServiceProvider -&gt; fixedAssetsAllBranchControllerProvider)[0]"><path d="M 1898.675154 166.318497 C 1791.699951 214.300003 1764.500000 324.100006 1764.500000 468.500000" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetServiceProvider -&gt; fixedAssetsAllPlacesControllerProvider)[0]"><path d="M 2049.822578 166.323535 C 2156.000000 214.300003 2183.000000 324.100006 2183.000000 468.500000" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g id="(fixedAssetServiceProvider -&gt; fixedAssetsPlacesControllerProvider)[0]"><path d="M 2110.477187 153.445215 C 2493.699951 211.828003 2590.000000 324.100006 2590.000000 468.500000" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-1694934913)" /></g><g transform="translate(1508 457)" class="appendix-icon"><title>実査システム＿理由一覧</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2092 84)" class="appendix-icon"><title>provider生成コード</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1929 457)" class="appendix-icon"><title>実査システム＿場所一覧</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2345 457)" class="appendix-icon"><title>実査システム＿場所一覧</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(3165 84)" class="appendix-icon"><title>固定資産実査進行状況</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(3687 84)" class="appendix-icon"><title>実査システム＿固定資産一覧</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2743 457)" class="appendix-icon"><title>設定場所画面で場所情報取得</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1104 457)" class="appendix-icon"><title>実査システム＿固定資産一覧_更新</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(4154 84)" class="appendix-icon"><title>固定資産登録</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(4421 84)" class="appendix-icon"><title>カメラやバーコードスキャナでスキャンしたコードの状態</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2255 930)" class="appendix-icon"><title>ダイアログにはすべての場所が含まれており、</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1842 925)" class="appendix-icon"><title>ダイアログにはすべてのストアが含まれており、</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><mask id="d2-1694934913" maskUnits="userSpaceOnUse" x="-101" y="-112" width="4656" height="1406">
<rect x="-101" y="-112" width="4656" height="1406" fill="white"></rect>
<rect x="306.500000" y="-12.000000" width="83" height="36" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1218.500000" y="495.500000" width="267" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1881.500000" y="122.500000" width="188" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1622.500000" y="495.500000" width="284" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2043.500000" y="495.500000" width="279" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2864.500000" y="122.500000" width="278" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="3423.500000" y="122.500000" width="241" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2459.500000" y="495.500000" width="261" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="774.500000" y="495.500000" width="307" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="3828.500000" y="122.500000" width="303" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="4268.500000" y="122.500000" width="130" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2872.500000" y="495.500000" width="143" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="3498.500000" y="495.500000" width="91" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2494.500000" y="1023.000000" width="192" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2129.000000" y="1023.000000" width="109" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1034.000000" y="1023.000000" width="175" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="3750.000000" y="495.500000" width="98" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1704.500000" y="1023.500000" width="121" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="4088.000000" y="495.000000" width="205" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="3176.000000" y="495.000000" width="113" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="178.000000" y="381.000000" width="49" height="31" fill="rgba(0,0,0,0.75)"></rect>
<rect x="511.000000" y="34.000000" width="70" height="31" fill="rgba(0,0,0,0.75)"></rect>
<rect x="94.000000" y="495.500000" width="50" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="260.500000" y="495.500000" width="62" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="496.500000" y="122.500000" width="4" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="535.500000" y="495.500000" width="9" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="457.000000" y="311.000000" width="35" height="21" fill="black"></rect>
<rect x="519.000000" y="307.000000" width="36" height="21" fill="black"></rect>
<rect x="580.000000" y="288.000000" width="44" height="21" fill="black"></rect>
</mask></svg></svg>