import 'package:shinise_core_client/asset/v1/v1.dart';

/// 設置場所
class Place {
  /// コンストラクタ関数
  Place({
    required this.placeCode,
    required this.placeName,
    String? placeCount,
    String? placeAllCount,
    String? status,
  })  : placeCount = placeCount ?? '',
        placeAllCount = placeAllCount ?? '',
        status = status ?? '';

  /// grpc 応答からのコンストラクター関数
  factory Place.fromGrpc(GetPlaceInfoResponse_PlaceInfo placeGrpc) => Place(
        placeCode: placeGrpc.placeCode,
        placeName: placeGrpc.placeName,
        placeCount: placeGrpc.placeCount,
        placeAllCount: placeGrpc.placeAllCount,
        status: placeGrpc.status,
      );

  /// grpc 応答からのコンストラクター関数
  factory Place.fromListGrpc(GetPlaceListInfoResponse_PlaceInfo placeGrpc) => Place(
        placeCode: placeGrpc.placeCode.toString(),
        placeName: placeGrpc.placeName,
      );

  /// 設置場所コード
  final String placeCode;

  /// 設置場所名
  final String placeName;

  /// 設置場所別の処理件数
  final String placeCount;

  /// 設置場所別総件数
  final String placeAllCount;

  /// 状態
  final String status;
}
