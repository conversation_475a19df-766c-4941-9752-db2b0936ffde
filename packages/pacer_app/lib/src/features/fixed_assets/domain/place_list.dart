import 'package:shinise_core_client/asset/v1/v1.dart';

import 'place.dart';
import 'progress.dart';

/// 場所リストと分岐の進行状況
class PlaceList {
  /// コンストラクタ関数
  const PlaceList({
    required this.places,
    required this.progress,
  });

  /// grpc 応答からのコンストラクター関数
  factory PlaceList.fromGrpc(GetPlaceInfoResponse placeGrpc) => PlaceList(
        places: placeGrpc.placeInfo.map(Place.fromGrpc).toList(),
        progress: Progress.fromGrpc(placeGrpc.placeInfo.first),
      );

  /// 所在地リスト
  final List<Place> places;

  /// ブランチの進行状況
  final Progress progress;
}
