// ignore_for_file: public_member_api_docs
import 'package:shinise_core_client/asset/v1/v1.dart';

enum ReasonType { unprocessed, scanned, move, discard, repair, unclear, unseal }

/// 理由
class Reason {
  /// コンストラクタ関数
  Reason({
    required this.code,
    required this.name,
    required this.shortName,
  });

  /// grpc 応答からのコンストラクター関数
  factory Reason.fromGrpc(GetReasonInfoListResponse_ReasonInfo reasonGrpc) => Reason(
        code: int.tryParse(reasonGrpc.reasonCode) ?? 0,
        name: reasonGrpc.reasonName,
        shortName: reasonGrpc.reasonRead,
      );

  /// 理由コード
  final int code;

  /// 理由名
  final String name;

  /// 理由名(略称)
  final String shortName;
}
