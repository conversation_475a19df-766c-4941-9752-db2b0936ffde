import 'package:shinise_core_client/asset/v1/v1.dart';

/// 固定資産実査進行状況
class Progress {
  /// コンストラクタ関数
  Progress({
    required this.fulfill,
    required this.total,
    required this.branchCount,
    required this.branchAllCount,
    required this.branchName,
  });

  /// grpc 応答からのコンストラクター関数
  factory Progress.fromGrpc(GetPlaceInfoResponse_PlaceInfo placeGrpc) => Progress(
        fulfill: placeGrpc.placeCount,
        total: placeGrpc.placeAllCount,
        branchCount: placeGrpc.bhCount,
        branchAllCount: placeGrpc.bhAllCount,
        branchName: placeGrpc.branchName,
      );

  /// grpc 応答からのコンストラクター関数
  factory Progress.fromProgressGrpc(
    GetProgressResponse_ProgressInfo progressGrpc,
  ) =>
      Progress(
        fulfill: progressGrpc.fulfill,
        total: progressGrpc.total,
        branchCount: progressGrpc.bhCount,
        branchAllCount: progressGrpc.bhAllCount,
        branchName: progressGrpc.branchName,
      );

  /// 設置場所別の処理件数
  final String fulfill;

  /// 設置場所別総件数
  final String total;

  /// 実査件数
  final String branchCount;

  /// 総件数
  final String branchAllCount;

  /// 移動先店舗名
  final String branchName;
}
