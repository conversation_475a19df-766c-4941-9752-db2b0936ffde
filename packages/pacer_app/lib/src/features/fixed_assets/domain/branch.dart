import 'package:shinise_core_client/asset/v1/v1.dart';

/// 設置場所
class Branch {
  /// コンストラクタ関数
  Branch({
    required this.code,
    required this.name,
  });

  /// grpc 応答からのコンストラクター関数
  factory Branch.fromGrpc(
    GetBranchListInfoResponse_BranchInfo branchGrpc,
  ) =>
      Branch(code: branchGrpc.branchCode, name: branchGrpc.branchNameAbbr);

  /// 店舗コード
  final String code;

  /// 店舗名
  final String name;
}
