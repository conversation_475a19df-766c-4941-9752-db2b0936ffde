import 'package:shinise_core_client/asset/v1/v1.dart';

/// 固定資産
class FixedAsset {
  /// コンストラクタ関数
  FixedAsset({
    required this.code,
    required this.name,
    required this.productName,
    required this.reasonCode,
    this.placeCode,
    this.currentPlaceCode,
    this.currentPlaceName,
    this.branchCode,
    this.currentBranchCode,
    this.currentBranchName,
    required this.hasTransferred,
    this.isEnabledMove,
  });

  /// grpc 応答からのコンストラクター関数
  factory FixedAsset.fromGrpc(
    GetFixedAssetsInfoListResponse_FixedAssetsInfo assetGrpc,
  ) =>
      FixedAsset(
        code: assetGrpc.fixedAssetQrcode,
        name: assetGrpc.fixedAssetName,
        branchCode: assetGrpc.qrBranchCode,
        productName: assetGrpc.makerName,
        reasonCode: int.tryParse(assetGrpc.reasonCode) ?? 0,
        hasTransferred: assetGrpc.isReceive,
        isEnabledMove: assetGrpc.enableMove,
      );

  /// grpc 応答からのコンストラクター関数
  factory FixedAsset.fromScanGrpc(
    GetFixedAssetsInfoResponse_FixedAssetsInfo assetGrpc,
  ) =>
      FixedAsset(
        code: assetGrpc.fixedAssetQrcode,
        name: assetGrpc.fixedAssetName,
        productName: assetGrpc.makerName,
        reasonCode: int.tryParse(assetGrpc.reasonCode) ?? 0,
        placeCode: assetGrpc.placeCode,
        currentPlaceCode: assetGrpc.receivePlaceCode.isNotEmpty ? assetGrpc.receivePlaceCode : assetGrpc.placeCode,
        currentPlaceName: assetGrpc.receivePlaceName.isNotEmpty ? assetGrpc.receivePlaceName : assetGrpc.placeName,
        branchCode: assetGrpc.branchCode,
        currentBranchCode: assetGrpc.receiveBranchCode.isNotEmpty ? assetGrpc.receiveBranchCode : assetGrpc.branchCode,
        currentBranchName: assetGrpc.receiveBranchName.isNotEmpty ? assetGrpc.receiveBranchName : assetGrpc.branchName,
        hasTransferred: assetGrpc.receiveBranchCode == assetGrpc.branchCode,
        isEnabledMove: assetGrpc.enableMove,
      );

  /// 資産QRコード
  final String code;

  /// 資産名称
  final String name;

  /// 製品名
  final String productName;

  /// 理由コード
  final int reasonCode;

  /// 移動店舗
  /// true : 移動先店舗番号=PACER登録の店舗コード
  /// false: 移動先店舗番号<>PACERの登録店舗コード
  final bool hasTransferred;

  /// 場所コード
  final String? placeCode;

  /// 移動先場所コード
  final String? currentPlaceCode;

  /// 移動先場所名
  final String? currentPlaceName;

  /// 支店コード
  final String? branchCode;

  /// 宛先支店コード
  final String? currentBranchCode;

  /// 宛先ブランチ名
  final String? currentBranchName;

  /// 移動可能かの判定
  final bool? isEnabledMove;

  /// copy with
  FixedAsset copyWith({
    String? code,
    String? name,
    String? productName,
    int? reasonCode,
    bool? hasTransferred,
  }) =>
      FixedAsset(
        code: code ?? this.code,
        name: name ?? this.name,
        productName: productName ?? this.productName,
        reasonCode: reasonCode ?? this.reasonCode,
        hasTransferred: hasTransferred ?? this.hasTransferred,
      );
}
