import 'package:shinise_core_client/asset/v1/v1.dart';

/// 設置場所詳細
class PlaceDetail {
  /// コンストラクタ関数
  PlaceDetail({
    required this.placeCode,
    required this.placeName,
    this.isEnabledMove,
  });

  /// grpc 応答からのコンストラクター関数
  factory PlaceDetail.fromGrpc(GetPlacesResponse_PlacesInfo placeGrpc) => PlaceDetail(
        placeCode: placeGrpc.placeCode,
        placeName: placeGrpc.placeName,
        isEnabledMove: placeGrpc.enableMove,
      );

  /// 設置場所コード
  final String placeCode;

  /// 設置場所名
  final String placeName;

  /// 移動可能かの判定
  final bool? isEnabledMove;
}
