import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../application/delivery_slip_service.dart';
import '../../../domain/delivery_product_code.dart';
import '../../../domain/delivery_vender.dart';

part 'search_vender_controller.g.dart';

/// 納品伝票（再発行）の導線で共有するsystemDate。
/// 画面遷移時にAPI経由で初期化する。
@riverpod
Future<DateTime> deliveryReissueSystemDate(DeliveryReissueSystemDateRef ref) {
  return ref.watch(deliverySlipServiceProvider).systemDate;
}

/// 納品伝票再発行 ベンダー情報検索ロジック
@riverpod
class SearchVenderController extends _$SearchVenderController {
  /// [DeliverySlipService]
  DeliverySlipService get _service => ref.watch(deliverySlipServiceProvider);

  @override
  Future<DeliveryVender?> build() async {
    try {
      state = const AsyncLoading();

      /// サーバの現在時刻。
      final deliveryDate = await _service.systemDate;

      // 画面表示時は、納品日のみをサーバのシステム時刻で初期化する。
      return DeliveryVender(deliveryDate: deliveryDate);
    } catch (e) {
      log('$e');

      // API失敗時は納品日をDateTime.nowで初期化する
      return DeliveryVender(deliveryDate: DateTime.now());
    }
  }

  /// ユーザー入力による、納品日の更新。
  void updateDeliveryDate(DateTime? deliverydate) {
    state = const AsyncLoading();
    state = AsyncData(state.value?.copyWith(deliveryDate: deliverydate));
  }

  /// ベンダー番号からベンダー情報を取得する
  Future<void> getVender({required String venderCode}) async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;
    // venderCodeがemptyでリクエストするとシステムエラーになるため早期リターンする
    if (venderCode.isEmpty) {
      // 早期リターンのみでは、ベンダー名はクリアされない
      state = switch (state.value?.deliveryDate) {
        final currentDeliveryDate? => AsyncData(DeliveryVender(deliveryDate: currentDeliveryDate)),
        null => const AsyncData(null)
      };

      return;
    }

    try {
      state = const AsyncLoading();

      final response = await _service.venderByCode(venderCode: venderCode);

      // deliveryDateはユーザ入力を前提としており、responseでそのまま上書きすると消えてしまう
      state = switch (state.value) {
        final vender? => AsyncData(
            vender.copyWith(
              venderCode: response.venderCode,
              venderName: response.venderName,
            ),
          ),
        _ => AsyncData(response),
      };
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }

  /// ベンダー番号からベンダー情報を取得する
  Future<void> getVenderByJan({required String? productCode}) async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;
    if (productCode == null || productCode.isEmpty) return;
    try {
      state = const AsyncLoading();

      final response = await _service.venderByJan(
        productCode: DeliveryProductCode.parse(productCode),
      );

      // deliveryDateはユーザ入力を前提としており、responseでそのまま上書きすると消えてしまう
      state = switch (state.value) {
        final vender? => AsyncData(
            vender.copyWith(
              venderCode: response.venderCode,
              venderName: response.venderName,
            ),
          ),
        _ => AsyncData(response),
      };
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }
}
