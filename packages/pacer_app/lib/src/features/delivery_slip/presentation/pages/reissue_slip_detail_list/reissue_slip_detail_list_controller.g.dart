// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reissue_slip_detail_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$allowEditingDeliveryQuantityHash() => r'43736cff9f3717f4282814e10c31336661a46c71';

/// [DeliveryVender]のallowEditingを参照。
///
/// Copied from [allowEditingDeliveryQuantity].
@ProviderFor(allowEditingDeliveryQuantity)
final allowEditingDeliveryQuantityProvider = AutoDisposeFutureProvider<bool>.internal(
  allowEditingDeliveryQuantity,
  name: r'allowEditingDeliveryQuantityProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$allowEditingDeliveryQuantityHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AllowEditingDeliveryQuantityRef = AutoDisposeFutureProviderRef<bool>;
String _$reissueSlipDetailListControllerHash() => r'2b461115064541a9400547eef30d31336564a1ee';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ReissueSlipDetailListController extends BuildlessAutoDisposeAsyncNotifier<List<DeliverySlipDetail>> {
  late final int slipNumber;

  FutureOr<List<DeliverySlipDetail>> build(
    int slipNumber,
  );
}

/// 納品/再発行 伝票一覧画面のロジック
///
/// Copied from [ReissueSlipDetailListController].
@ProviderFor(ReissueSlipDetailListController)
const reissueSlipDetailListControllerProvider = ReissueSlipDetailListControllerFamily();

/// 納品/再発行 伝票一覧画面のロジック
///
/// Copied from [ReissueSlipDetailListController].
class ReissueSlipDetailListControllerFamily extends Family {
  /// 納品/再発行 伝票一覧画面のロジック
  ///
  /// Copied from [ReissueSlipDetailListController].
  const ReissueSlipDetailListControllerFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'reissueSlipDetailListControllerProvider';

  /// 納品/再発行 伝票一覧画面のロジック
  ///
  /// Copied from [ReissueSlipDetailListController].
  ReissueSlipDetailListControllerProvider call(
    int slipNumber,
  ) {
    return ReissueSlipDetailListControllerProvider(
      slipNumber,
    );
  }

  @visibleForOverriding
  @override
  ReissueSlipDetailListControllerProvider getProviderOverride(
    covariant ReissueSlipDetailListControllerProvider provider,
  ) {
    return call(
      provider.slipNumber,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(ReissueSlipDetailListController Function() create) {
    return _$ReissueSlipDetailListControllerFamilyOverride(this, create);
  }
}

class _$ReissueSlipDetailListControllerFamilyOverride implements FamilyOverride {
  _$ReissueSlipDetailListControllerFamilyOverride(this.overriddenFamily, this.create);

  final ReissueSlipDetailListController Function() create;

  @override
  final ReissueSlipDetailListControllerFamily overriddenFamily;

  @override
  ReissueSlipDetailListControllerProvider getProviderOverride(
    covariant ReissueSlipDetailListControllerProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// 納品/再発行 伝票一覧画面のロジック
///
/// Copied from [ReissueSlipDetailListController].
class ReissueSlipDetailListControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<ReissueSlipDetailListController, List<DeliverySlipDetail>> {
  /// 納品/再発行 伝票一覧画面のロジック
  ///
  /// Copied from [ReissueSlipDetailListController].
  ReissueSlipDetailListControllerProvider(
    int slipNumber,
  ) : this._internal(
          () => ReissueSlipDetailListController()..slipNumber = slipNumber,
          from: reissueSlipDetailListControllerProvider,
          name: r'reissueSlipDetailListControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$reissueSlipDetailListControllerHash,
          dependencies: ReissueSlipDetailListControllerFamily._dependencies,
          allTransitiveDependencies: ReissueSlipDetailListControllerFamily._allTransitiveDependencies,
          slipNumber: slipNumber,
        );

  ReissueSlipDetailListControllerProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.slipNumber,
  }) : super.internal();

  final int slipNumber;

  @override
  FutureOr<List<DeliverySlipDetail>> runNotifierBuild(
    covariant ReissueSlipDetailListController notifier,
  ) {
    return notifier.build(
      slipNumber,
    );
  }

  @override
  Override overrideWith(ReissueSlipDetailListController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ReissueSlipDetailListControllerProvider._internal(
        () => create()..slipNumber = slipNumber,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        slipNumber: slipNumber,
      ),
    );
  }

  @override
  (int,) get argument {
    return (slipNumber,);
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ReissueSlipDetailListController, List<DeliverySlipDetail>> createElement() {
    return _ReissueSlipDetailListControllerProviderElement(this);
  }

  ReissueSlipDetailListControllerProvider _copyWith(
    ReissueSlipDetailListController Function() create,
  ) {
    return ReissueSlipDetailListControllerProvider._internal(
      () => create()..slipNumber = slipNumber,
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      slipNumber: slipNumber,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is ReissueSlipDetailListControllerProvider && other.slipNumber == slipNumber;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, slipNumber.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ReissueSlipDetailListControllerRef on AutoDisposeAsyncNotifierProviderRef<List<DeliverySlipDetail>> {
  /// The parameter `slipNumber` of this provider.
  int get slipNumber;
}

class _ReissueSlipDetailListControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ReissueSlipDetailListController, List<DeliverySlipDetail>>
    with ReissueSlipDetailListControllerRef {
  _ReissueSlipDetailListControllerProviderElement(super.provider);

  @override
  int get slipNumber => (origin as ReissueSlipDetailListControllerProvider).slipNumber;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
