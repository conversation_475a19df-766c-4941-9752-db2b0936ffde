import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../common_widgets/snack_bars.dart';
import '../../../../../exceptions/app_exception.dart';
import '../../../../app/application/global_loading_service.dart';
import '../../../domain/delivery_slip_detail.dart';
import '../../../domain/get_parsed_quantity.dart';
import '../../components/delivery_async_error_ui.dart';
import '../../components/delivery_slip_empty_view.dart';
import '../../components/quantity_form_field.dart';
import '../../components/sales_price_sum_sliver_app_bar.dart';
import '../../components/selected_delivery_date_sliver_app_bar.dart';
import '../../components/slip_number_sliver_app_bar.dart';
import '../../components/target_vender_sliver_app_bar.dart';
import 'reissue_slip_detail_list_controller.dart';

/// See also ReissueSlipDetailListPage
class ReissueSlipDetailListPageBody extends HookConsumerWidget {
  /// init
  const ReissueSlipDetailListPageBody({
    super.key,
    required this.slipNumber,
    this.optionalVenderName,
  });

  /// 選択した納品伝票の伝票番号。
  final int slipNumber;

  /// 選択したベンダー名。
  /// nullの場合は、venderProviderの値を見にいく。
  final String? optionalVenderName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(
      reissueSlipDetailListControllerProvider(slipNumber),
    );
    final focusNode = useFocusNode();
    final selectedIndex = useState(0);
    final quantityController = useTextEditingController();

    Future<void> updateSlipQuantity({bool ignoreValidation = false}) async {
      return ref.read(globalLoadingServiceProvider.notifier).wrap(
            ref
                .read(
                  reissueSlipDetailListControllerProvider(slipNumber).notifier,
                )
                .updateSlipQuantity(
                  index: selectedIndex.value,
                  deliveryQuantityText: quantityController.text,
                  ignoreValidation: ignoreValidation,
                  onSuccess: () => showSnackBar(context, '納品数量を修正しました'),
                ),
          );
    }

    ref.listen<AsyncValue<void>>(
      reissueSlipDetailListControllerProvider(slipNumber),
      (previous, next) async {
        final userConfirmed = await next.showAlertOnRetryableError(
              context: ref.context,
              cancelActionText: 'キャンセル',
            ) ??
            false;

        if (!userConfirmed) return;

        await updateSlipQuantity(ignoreValidation: userConfirmed);
      },
    );

    Future<void> onPressedEditbutton(
      WidgetRef ref,
      int index,
      String jan,
    ) async {
      selectedIndex.value = index;

      final allowEditing = await ref.read(allowEditingDeliveryQuantityProvider.future);

      if (!allowEditing && context.mounted) {
        await showAlertDialog(
          context: context,
          title: 'エラー',
          content: DenyEditDeliveryQuantityException().message,
        );

        return;
      }

      if (!context.mounted) return;

      final ok = await showAlertDialog(
            context: context,
            title: '編集',
            content: '納品数量を修正しますか？',
            cancelActionText: 'キャンセル',
          ) ??
          false;

      if (!context.mounted || !ok) return;

      final quantity = await _showEditingQuantityDialog(
            context: context,
            controller: quantityController,
            focusNode: focusNode,
            jan: jan,
          ) ??
          '';

      if (quantity.isEmpty) return;

      await updateSlipQuantity();
    }

    return switch (state) {
      AsyncData(:final value) || AsyncError(:final value?) => _SlipDetailListView(
          value,
          venderName: optionalVenderName,
          slipNumber: slipNumber,
          onPressedEditbutton: onPressedEditbutton,
        ),
      _ => const Center(
          child: CircularProgressIndicator(),
        ),
    };
  }
}

/// 伝票IDで取得した納品伝票詳細情報の一覧を表示します。
class _SlipDetailListView extends HookConsumerWidget {
  _SlipDetailListView(
    this.slipDetailList, {
    this.venderName,
    required this.slipNumber,
    required this.onPressedEditbutton,
  });

  final String? venderName;

  final int slipNumber;

  final List<DeliverySlipDetail> slipDetailList;

  final void Function(WidgetRef, int, String) onPressedEditbutton;

  final formatter = NumberFormat.decimalPattern('ja');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allowEditing = ref.watch(allowEditingDeliveryQuantityProvider);
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return CustomScrollView(
      slivers: [
        SlipNumberSliverAppBar(slipNumber: slipNumber),
        TargetVenderSliverAppBar(optionalVenderName: venderName),
        const SelectedDeliveryDateSliverAppBar(),
        SalesPriceSumSliverAppBar(slipNumber: slipNumber),
        SliverList(
          delegate: SliverChildBuilderDelegate(
            childCount: slipDetailList.length,
            (BuildContext context, int index) {
              final detail = slipDetailList.elementAtOrNull(index);
              if (slipDetailList.isEmpty) {
                return const DeliverySlipEmptyView(message: '伝票詳細情報がありません');
              }

              return Card(
                child: ListTile(
                  tileColor: index.isEven ? colors.primaryContainer : null,
                  title: _TitleText('${detail?.productName}'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _TitleText(
                        '''
JAN: ${detail?.productCode.value}
売単価: ${formatter.format(detail?.salesPrice)} 売価合計: ${formatter.format(detail?.salesSum)}''',
                      ),
                      FilledButton.icon(
                        icon: const Icon(Icons.edit),
                        onPressed: () => switch (allowEditing) {
                          AsyncData() => onPressedEditbutton(
                              ref,
                              index,
                              detail?.productCode.value ?? '',
                            ),
                          _ => null,
                        },
                        label: Text(
                          '数量: ${getParsedQuantity(detail?.quantity ?? 0)}',
                          style: texts.bodyLarge?.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  dense: true,
                  isThreeLine: true,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class _TitleText extends StatelessWidget {
  const _TitleText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: Theme.of(context).textTheme.bodyLarge,
    );
  }
}

Future<String?> _showEditingQuantityDialog({
  required BuildContext context,
  required TextEditingController controller,
  required FocusNode focusNode,
  required String jan,
}) async {
  // 入力前初期化
  controller.clear();

  return showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('納品数量を入力してください'),
      content: QuantityFormField(
        autofocus: true,
        controller: controller,
        onFieldSubmitted: (value) => Navigator.of(context).pop(value),
        jan: jan,
      ),
    ),
  );
}
