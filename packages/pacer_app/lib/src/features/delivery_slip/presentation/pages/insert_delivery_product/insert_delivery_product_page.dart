import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../common_widgets/snack_bars.dart';
import '../../../../../exceptions/app_exception.dart';
import '../../../../../routing/app_router.dart';
import '../../../../../themes/app_color_scheme.dart';
import '../../../../../utils/adaptive_number_input_type.dart';
import '../../../../app/application/global_loading_service.dart';
import '../../../../device/application/pacer_service.dart';
import '../../../../device/data/pacer_repository.dart';
import '../../../../device/domain/extension.dart';
import '../../../../device/presentation/scan_window.dart';
import '../../../domain/delivery_product.dart';
import '../../../domain/delivery_product_code.dart';
import '../../components/delivery_async_error_ui.dart';
import '../../components/delivery_progress_indicator.dart';
import '../../components/disabled_outlined_button_style.dart';
import '../../components/quantity_form_field.dart';
import '../../routing/delivery_slip_route.dart';
import 'insert_delivery_product_controller.dart';

/// ユーザが納品数量を手動入力しない際に、固定値として入力される値。
/// 数量は１。
const _fixedQuantity = 1;
const _onSuccessRegisterProductMessage = '商品を登録しました';

/// 画面遷移の種類。
/// 商品情報や納品数量が入力されている際に、画面遷移時の制御が必要。
/// 画面遷移種類ごとに微妙に制御方法が異なる。
enum _RequiredHandleTransition { pop, goListPage }

/// 納品伝票 データ登録 納品/入力 画面
class InsertDeliveryProductPage extends HookConsumerWidget {
  /// init
  const InsertDeliveryProductPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productState = ref.watch(insertDeliveryProductControllerProvider);
    final notifier = ref.watch(insertDeliveryProductControllerProvider.notifier);
    final isNotITG = !ref.watch(deviceInfoProvider).requireValue.isITG;

    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final janEditingController = useTextEditingController();
    final quantityEditingController = useTextEditingController();

    /// 最後にスキャンされたJAN。
    /// ITGの物理スキャンやScan FABでスキャンした値をここに保存する。
    /// 数量チェック等でのリトライ成功後のonSuccessで連続スキャン時の商品情報取得に用いる。
    final lastScannedJan = useState('');

    /// 納品数量の入力値を固定するか？初期値はfalse。
    final isFixedQuantity = useState(false);

    /// スキャン、またはキーボード入力によりJANを入力した際(scanCodeProvider,ScanFAB更新時)にtrueで更新。
    /// 「登録」ボタンをタップして処理成功したらfalseで更新。
    /// true: 数量チェック結果OK後に画面を全て更新しない。
    /// false: 数量チェック結果OK後に画面を全て初期化する。
    final hasUserScanned = useState(false);

    /// 画面遷移時の制御が必要な場合、この値を更新する。
    /// nullの場合は何もしない。
    final requiredHandleTransition = useState<_RequiredHandleTransition?>(null);

    /// 商品が納品可能か判断する。
    /// isNotEmptyJanとisNotEmptyQuantityが両方trueの時、納品可能。
    /// それ以外のパターンは納品させない。
    bool canRegisterProduct() => janEditingController.text.isNotEmpty && quantityEditingController.text.isNotEmpty;

    /// 納品数量の初期化処理。
    /// JANが入力済の場合のみ、固定する数量で初期化する。（JANが空の場合に数量を先に入力した場合）
    /// それ以外は空で初期化。
    void resetQuantity() {
      quantityEditingController.text = switch (isFixedQuantity.value) {
        true when janEditingController.text.isNotEmpty => _fixedQuantity.toString(),
        _ => '',
      };
    }

    /// 「取消」ボタンの挙動。
    /// 画面の表示情報を初期化する。
    void onTapResetButton() {
      if (isNotITG) {
        // itg端末ではscanCodeProviderは初期化されないので注意.
        ref.invalidate(scanCodeProvider);
      }
      janEditingController.clear();
      ref.invalidate(insertDeliveryProductControllerProvider);
      resetQuantity();
    }

    /// [lastScannedJan]と画面表示情報を全てクリアする。
    ///
    /// hasUserScanned: true => 納品数量のみ初期化。
    /// hasUserScanned: false => 画面を全て初期化。
    void refresh() {
      lastScannedJan.value = '';

      if (hasUserScanned.value) {
        resetQuantity();
      } else {
        onTapResetButton();
      }
    }

    /// 一覧画面に遷移する際の関連処理。
    /// 画面遷移処理と、画面情報のリセットを行う。
    void goListPage() {
      DeliveredProductsList().go(ref.context);
      // 一覧画面から戻った時に、情報を残さないためリセットしておく
      onTapResetButton();
      // 画面遷移制御状態を初期化する
      requiredHandleTransition.value = null;
      // 一覧画面から戻った際にキーボード表示されたままの状態防止
      primaryFocus?.unfocus();
    }

    Future<void> onTapRegisterButton() async {
      hasUserScanned.value = false;

      await ref.read(globalLoadingServiceProvider.notifier).wrap(
            notifier.insertCurrentProduct(
              deliveryQuantity: quantityEditingController.text,
              currentJan: janEditingController.text,
              onSuccess: () {
                refresh();
                showSnackBar(context, _onSuccessRegisterProductMessage);
              },
            ),
          );
    }

    /// 閉じる リターンポップアップ、登録操作を行う
    Future<void> insertOnTransition({
      required _RequiredHandleTransition transition,
    }) async {
      final context = ref.context
        // 確認ダイアログを閉じる
        ..pop();

      await ref.read(globalLoadingServiceProvider.notifier).wrap(
            ref.read(insertDeliveryProductControllerProvider.notifier).insertCurrentProduct(
                  currentJan: janEditingController.text,
                  deliveryQuantity: quantityEditingController.text,
                  onSuccess: () {
                    if (!context.mounted) return;
                    switch (transition) {
                      case _RequiredHandleTransition.goListPage:
                        goListPage();
                      case _RequiredHandleTransition.pop:
                        ref.context.pop();
                    }
                    showSnackBar(context, _onSuccessRegisterProductMessage);
                  },
                ),
          );
    }

    /// PopScopeで制御する処理。
    ///
    /// 次のデータが全て存在する場合、登録処理をするかユーザに確認する。
    /// - 入力したJAN
    /// - APIから取得した商品情報
    /// - 入力した納品数量
    Future<bool> onTransition({
      required _RequiredHandleTransition transition,
    }) async {
      requiredHandleTransition.value = transition;

      final product = ref.read(insertDeliveryProductControllerProvider).value;

      final canTransition =
          janEditingController.text.isEmpty || product == null || quantityEditingController.text.isEmpty;

      switch ((canTransition, transition)) {
        case (true, _RequiredHandleTransition.goListPage):
          goListPage();
          return true;
        case (true, _RequiredHandleTransition.pop):
          ref.context.pop();
          return true;
        case _:
      }

      return await showDialog<bool>(
            barrierDismissible: false,
            context: ref.context,
            builder: (context) => AlertDialog(
              title: const Text('未登録の情報が残っています。登録しますか？'),
              actions: [
                OutlinedButton(
                  onPressed: () {
                    showSnackBar(ref.context, '登録をキャンセルしました');
                    switch (transition) {
                      case _RequiredHandleTransition.goListPage:
                        ref.context.pop();
                        goListPage();
                      case _RequiredHandleTransition.pop:
                        ref.context
                          ..pop()
                          ..pop();
                    }
                  },
                  child: const Text('いいえ'),
                ),
                OutlinedButton(
                  onPressed: () async => insertOnTransition(
                    transition: transition,
                  ),
                  child: const Text('はい'),
                ),
              ],
            ),
          ) ??
          false;
    }

    Future<void> onScanningComplete(String? scannedValue) async {
      hasUserScanned.value = true;

      final scannedProductCode = DeliveryProductCode.parse(
        scannedValue ?? '',
      );

      /// 数量チェックエラーで弾かれた時のリトライ用の変数に保存。
      lastScannedJan.value = scannedProductCode.value;

      await ref.read(globalLoadingServiceProvider.notifier).wrap(
            notifier.onScan(
              currentJan: janEditingController.text,
              nextJan: scannedProductCode.value,
              deliveryQuantityText: quantityEditingController.text,
              onSuccess: () {
                // 画面をリフレッシュする前にcanRegisterProductを実行しないと
                // canRegisterProductの結果が正しくない
                if (canRegisterProduct()) {
                  showSnackBar(context, _onSuccessRegisterProductMessage);
                }
                if (janEditingController.text != scannedProductCode.value) {
                  janEditingController.text = scannedProductCode.value;
                }
                refresh();
              },
            ),
          );
    }

    ref
      ..listen(insertDeliveryProductControllerProvider, (_, insertDeliveryProductState) {
        if (insertDeliveryProductState case AsyncLoading()) {
          // ローディング中のキーボード操作による、誤作動防止
          primaryFocus?.unfocus();
        }
      })
      ..listen<AsyncValue<void>>(
        insertDeliveryProductControllerProvider,
        (_, state) async {
          // この行以下は全て例外時の処理のため早期リターンする
          if (!state.hasError) return;

          final userConfirmed = await state.showAlertOnRetryableError(
                context: context,
                cancelActionText: 'キャンセル',
              ) ??
              false;

          if (userConfirmed) {
            await ref.read(globalLoadingServiceProvider.notifier).wrap(
                  notifier.insertCurrentProduct(
                    deliveryQuantity: quantityEditingController.text,
                    currentJan: janEditingController.text,
                    nextJan: lastScannedJan.value,
                    ignoreValidation: true,
                    onSuccess: () {
                      if (janEditingController.text != lastScannedJan.value) {
                        janEditingController.text = lastScannedJan.value;
                      }

                      refresh();
                      showSnackBar(context, _onSuccessRegisterProductMessage);
                      switch (requiredHandleTransition.value) {
                        case _RequiredHandleTransition.pop:
                          ref.context.pop();
                        case _RequiredHandleTransition.goListPage:
                          goListPage();
                        case null:
                      }
                    },
                  ),
                );
          }
        },
      )
      ..listen<AsyncValue<void>>(insertDeliveryProductControllerProvider, (_, state) async {
        if (state.error case ProductNotFoundException() || ProductNotRegisteredInMasterByStore()) {
          // マスタ未登録商品がスキャンされたら、画面をリセットする
          onTapResetButton();
        }
      })
      ..listen(scanCodeProvider, (_, scanned) async {
        if (scanned case AsyncData(:final value)) {
          final thisPageLocation = DeliverySlipInput().location;
          final globalLocation = GoRouterState.of(context).uri.toString();
          if (globalLocation != thisPageLocation) {
            return;
          }
          await onScanningComplete(value);
        }
      });

    useEffect(() => resetQuantity, [isFixedQuantity.value]);

    return Stack(
      children: [
        PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, _) async {
            if (didPop) return;
            await onTransition(
              transition: requiredHandleTransition.value ?? _RequiredHandleTransition.pop,
            );
          },
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: AppBar(
              automaticallyImplyLeading: false,
              backgroundColor: colors.primary,
              centerTitle: true,
              iconTheme: IconThemeData(color: colors.button),
              title: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  '納品入力',
                  style: texts.titleMedium?.copyWith(color: colors.onPrimary),
                ),
              ),
            ),
            bottomNavigationBar: BottomAppBar(
              shape: const CircularNotchedRectangle(),
              child: Row(
                children: [
                  PacerBackButton(
                    onPressed: () async => onTransition(
                      transition: _RequiredHandleTransition.pop,
                    ),
                  ),
                  const Spacer(flex: 2),
                  OutlinedButton(
                    onPressed: switch ((janEditingController.text.isEmpty, productState.valueOrNull)) {
                      (true, null) => null,
                      _ => onTapResetButton
                    },
                    style: disabledOutlinedButtonStyle(context),
                    child: const Text('取消'),
                  ),
                  const Spacer(),
                  ElevatedButton(
                    onPressed: () async => onTransition(
                      transition: _RequiredHandleTransition.goListPage,
                    ),
                    child: const Text('一覧'),
                  ),
                  const Spacer(),
                  FilledButton(
                    onPressed: onTapRegisterButton,
                    child: const Text('登録'),
                  ),
                ],
              ),
            ),
            body: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _JanTextField(
                      controller: janEditingController,
                      onFieldSubmitted: onScanningComplete,
                      onTap: () async {
                        if (janEditingController.text.isEmpty || quantityEditingController.text.isEmpty) {
                          return;
                        }
                        await onTapRegisterButton();
                      },
                      onTapOutSide: () async {
                        await onScanningComplete(janEditingController.text);
                        primaryFocus?.unfocus();
                      },
                    ),
                    switch (productState) {
                      AsyncLoading() => const SizedBox.shrink(),
                      AsyncData(:final value) || AsyncError(:final value) => _ProductTable(deliveryProduct: value),
                    },
                    _QuantityTextField(
                      controller: quantityEditingController,
                      isFixedQuantity: isFixedQuantity.value,
                      jan: janEditingController.text,
                      janEditingController: janEditingController,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: ListTile(
                        trailing: Switch(
                          value: isFixedQuantity.value,
                          onChanged: (newValue) async {
                            if (productState.value == null) {
                              await ref
                                  .read(
                                    insertDeliveryProductControllerProvider.notifier,
                                  )
                                  .fetchProduct(
                                    productCode: janEditingController.text,
                                  );
                            }
                            isFixedQuantity.value = newValue;
                          },
                        ),
                        title: const Text('納品数量入力値を１に固定'),
                        tileColor: colors.button,
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(10),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            floatingActionButton: ScanFloatingIconButton(
              onScan: onScanningComplete,
            ),
            floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
          ),
        ),
        switch (productState) {
          AsyncLoading() => const DeliveryProgressIndicator(),
          AsyncError() || AsyncData() => const SizedBox.shrink(),
        },
      ],
    );
  }
}

class _JanTextField extends ConsumerWidget {
  const _JanTextField({
    required this.controller,
    required this.onFieldSubmitted,
    required this.onTap,
    required this.onTapOutSide,
  });

  final TextEditingController controller;

  final void Function(String) onFieldSubmitted;

  final void Function() onTap;

  final void Function() onTapOutSide;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    return TextFormField(
      key: const Key('jan'),
      maxLines: null,
      // 数量チェックエラー付ダイアログ表示時にカーソルが操作可能な場合
      // キーボードが予期せず出現し、JANを途中で変更できてしまう現象の回避
      enableInteractiveSelection: false,
      controller: controller,
      style: theme.textTheme.titleMedium?.copyWith(
        color: theme.colorScheme.text,
        fontWeight: FontWeight.bold,
      ),
      onFieldSubmitted: onFieldSubmitted,
      onTap: onTap,
      onTapOutside: (_) => onTapOutSide(),
      decoration: const InputDecoration(
        border: InputBorder.none,
        filled: true,
        hintText: 'スキャンまたは入力',
      ),
      keyboardType: TextInputType.number.withEnter(),
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(26),
      ],
    );
  }
}

class _QuantityTextField extends ConsumerWidget {
  const _QuantityTextField({
    required this.controller,
    required this.isFixedQuantity,
    required this.jan,
    required this.janEditingController,
  });

  final TextEditingController controller;

  /// true: 数量が１に固定される
  /// false: ユーザが数量を入力できる
  final bool isFixedQuantity;

  /// JANの種類によって挙動が変える必要があるので、現在のJANを渡す
  final String jan;
  final TextEditingController janEditingController;

  void showEmptyJanAlertDialog(BuildContext context) {
    showAlertDialog(context: context, title: '先にJANを\n入力してください');
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentProductState = ref.watch(insertDeliveryProductControllerProvider);

    return GestureDetector(
      onTap: () {
        if (janEditingController.text.isEmpty) showEmptyJanAlertDialog(context);
      },
      child: QuantityFormField(
        key: const Key('quantity'),
        controller: controller,
        onTap: () async {
          controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: controller.text.length,
          );
          if (currentProductState.value == null) {
            await ref
                .read(insertDeliveryProductControllerProvider.notifier)
                .fetchProduct(productCode: janEditingController.text);
          }
        },
        // JAN未入力の場合は、数量入力すると
        // マスタ未登録商品のJANでも登録ができてしまうためJAN入力が必須
        enabled: !isFixedQuantity && janEditingController.text.isNotEmpty,
        jan: jan,
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.all(8),
          helperText: switch (isFixedQuantity) {
            true => '納品数量入力値は１に固定されています',
            false => '',
          },
          helperStyle: const TextStyle(fontWeight: FontWeight.bold),
          hintText: '納品数量を入力してください',
          isDense: true,
        ),
      ),
    );
  }
}

class _LabelText extends StatelessWidget {
  const _LabelText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.titleMedium,
        textAlign: TextAlign.right,
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(text, style: texts.titleMedium),
    );
  }
}

class _ProductTable extends StatelessWidget {
  const _ProductTable({required this.deliveryProduct});

  final DeliveryProduct? deliveryProduct;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3),
        1: FractionColumnWidth(0.7),
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      children: [
        TableRow(
          children: [
            const _LabelText('商品名'),
            _ValueText(deliveryProduct?.productName ?? ''),
          ],
        ),
        TableRow(
          children: [
            const _LabelText('ベンダー'),
            _ValueText(deliveryProduct?.venderName ?? ''),
          ],
        ),
        TableRow(
          children: [
            const _LabelText('規格'),
            _ValueText(deliveryProduct?.specName ?? ''),
          ],
        ),
        TableRow(
          children: [
            const _LabelText('ブランド'),
            _ValueText(deliveryProduct?.brand ?? ''),
          ],
        ),
        TableRow(
          children: [
            const _LabelText('カラー'),
            _ValueText(deliveryProduct?.color ?? ''),
          ],
        ),
        TableRow(
          children: [
            const _LabelText('サイズ'),
            _ValueText(deliveryProduct?.size ?? ''),
          ],
        ),
      ],
    );
  }
}
