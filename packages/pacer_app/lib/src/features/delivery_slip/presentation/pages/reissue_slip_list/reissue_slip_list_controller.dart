import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../exceptions/app_exception.dart';
import '../../../application/delivery_slip_service.dart';
import '../../../domain/delivery_slip.dart';
import '../search_vender/search_vender_controller.dart';

part 'reissue_slip_list_controller.g.dart';

/// 納品/再発行 伝票一覧画面のロジック
@riverpod
class ReissueSlipListController extends _$ReissueSlipListController {
  /// [DeliverySlipService]
  DeliverySlipService get _service => ref.watch(deliverySlipServiceProvider);

  @override
  FutureOr<List<DeliverySlip>> build() async {
    final deliveryVender = ref.read(searchVenderControllerProvider);

    try {
      state = const AsyncLoading();

      return switch (deliveryVender.value) {
        final vender? => await _service.listDeliverySlip(
            venderCode: vender.venderCode,
            deliveryDate: vender.deliveryDate ?? DateTime.now(),
          ),
        _ => [],
      };
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }

    return [];
  }

  /// タップした伝票データのisSelectedをトグルする。
  /// slipNumberを指定する。
  void toggle(int? slipNumber) {
    if (slipNumber == null) return;

    switch (state.value) {
      case final value?:
        state = AsyncData(
          [
            for (final todo in value)
              if (todo.slipNumber == slipNumber) todo.copyWith(isSelected: !todo.isSelected) else todo,
          ],
        );
    }
  }

  /// 再発行（印刷）する伝票データを投入します。
  /// 画面上で、再発行したい伝票データを１つ以上選択する必要があります。
  Future<void> print({VoidCallback? onSuccess}) async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;
    try {
      state = const AsyncLoading();

      // 1. プリンター確認

      await _service.checkConnectionWithPrinter();

      // 2. 印刷用データ投入

      final slipNumbers = state.valueOrNull?.where((element) => element.isSelected).map((e) => e.slipNumber).toList();

      if (slipNumbers == null || slipNumbers.isEmpty) {
        throw UnknownException('再発行する伝票を選択してください');
      }

      final printId = await _service.insertPrintSlipList(
        slipNumbers: slipNumbers,
      );

      // 3. 印刷処理リクエスト

      await _service.requestPrint(printId: printId);

      ref.invalidateSelf();

      if (!state.hasError && !state.isReloading) {
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }
}
