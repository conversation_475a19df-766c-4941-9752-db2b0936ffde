// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'insert_delivery_product_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$insertDeliveryProductControllerHash() => r'6d474da793e4ed09bf31582f1725d986db95e4c4';

/// データ登録 納品/入力画面のビジネスロジック
///
/// Copied from [InsertDeliveryProductController].
@ProviderFor(InsertDeliveryProductController)
final insertDeliveryProductControllerProvider =
    AutoDisposeAsyncNotifierProvider<InsertDeliveryProductController, DeliveryProduct?>.internal(
  InsertDeliveryProductController.new,
  name: r'insertDeliveryProductControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$insertDeliveryProductControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InsertDeliveryProductController = AutoDisposeAsyncNotifier<DeliveryProduct?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
