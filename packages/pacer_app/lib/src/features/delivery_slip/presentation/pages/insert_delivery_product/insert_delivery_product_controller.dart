import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../exceptions/app_exception.dart';
import '../../../../../utils/validator.dart';
import '../../../application/delivery_slip_service.dart';
import '../../../domain/delivery_product.dart';
import '../../../domain/delivery_product_code.dart';

part 'insert_delivery_product_controller.g.dart';

/// データ登録 納品/入力画面のビジネスロジック
@riverpod
class InsertDeliveryProductController extends _$InsertDeliveryProductController {
  /// [DeliverySlipService]
  DeliverySlipService get _service => ref.watch(deliverySlipServiceProvider);

  @override
  FutureOr<DeliveryProduct?> build() async {
    return null;
  }

  /// 商品コードから商品情報を取得する。
  Future<void> fetchProduct({
    required String productCode,
    VoidCallback? onSuccess,
  }) async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;

    try {
      if (productCode.isEmpty) throw UnknownException('商品コードを入力してください');

      state = const AsyncLoading();

      final product = await _service.fetchProduct(
        productCode: DeliveryProductCode.parse(productCode),
      );

      state = AsyncData(product);

      if (!state.hasError && !state.isReloading) {
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }

  /// 納品した商品を登録する。
  /// 登録後にレスポンスとして、連続でスキャン、または手動で入力した商品コードから
  /// 商品情報を取得するため、この情報で画面を更新する。
  /// [currentJan]このJANが登録の対象。
  /// [nextJan]は登録完了レスポンスに含まれる商品情報データの対象JAN。登録はされない。
  /// [ignoreValidation] がtrueの時、納品数量のチェックは行わない。
  Future<void> insertCurrentProduct({
    required String deliveryQuantity,
    required String currentJan,
    String? nextJan,
    bool ignoreValidation = false,
    VoidCallback? onSuccess,
  }) async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;

    try {
      state = const AsyncLoading();

      if (currentJan.isEmpty) throw UnknownException('商品コードを入力してください');

      if (!isValidBarcodeLength(currentJan.length)) {
        throw UnknownException('正しい商品コードを入力してください');
      }

      final parsedQuantity = double.tryParse(deliveryQuantity);

      if (parsedQuantity == null || parsedQuantity <= 0) {
        throw UnknownException('納品数量を入力してください');
      }

      switch (state.value) {
        case final product? when !ignoreValidation:
          product.validateQuantity(parsedQuantity);
      }

      final deliveryDate = await _service.systemDate;

      final response = await _service.insertCurrentProduct(
        deliveryQuantity: parsedQuantity,
        currentJan: DeliveryProductCode.parse(currentJan),
        deliveryDate: deliveryDate,
        nextJan: DeliveryProductCode.parse(nextJan ?? ''),
      );

      state = AsyncData(response);

      if (!state.hasError && !state.isReloading) {
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }

  /// 商品コードスキャン時、またはJAN入力欄の入力確定時に実行する。
  /// 納品数量が空欄の際は(スキャンまたは入力したJANで)商品情報取得。
  /// そうでなければ、商品の登録を実行する。
  /// [currentJan]は現在画面に表示されているJAN。
  /// [nextJan]はスキャン、またはキーボード入力したJAN。
  Future<void> onScan({
    required String currentJan,
    required String deliveryQuantityText,
    String? nextJan,
    VoidCallback? onSuccess,
  }) async {
    final cannotInsertProduct = (
      emptyCurrentJan: currentJan.isEmpty,
      emptyDeliveryQuantity: deliveryQuantityText.isEmpty,
    );

    return switch (cannotInsertProduct) {
      (emptyCurrentJan: true, emptyDeliveryQuantity: _) ||
      (emptyCurrentJan: false, emptyDeliveryQuantity: true) =>
        await fetchProduct(
          productCode: nextJan ?? '',
          onSuccess: onSuccess,
        ),
      (emptyCurrentJan: false, emptyDeliveryQuantity: false) => await insertCurrentProduct(
          deliveryQuantity: deliveryQuantityText,
          currentJan: currentJan,
          nextJan: nextJan,
          onSuccess: onSuccess,
        )
    };
  }
}
