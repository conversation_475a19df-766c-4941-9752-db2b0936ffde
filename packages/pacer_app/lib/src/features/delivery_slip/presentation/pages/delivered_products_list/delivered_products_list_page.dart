import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../common_widgets/snack_bars.dart';
import '../../../../app/application/global_loading_service.dart';
import '../../components/delivery_progress_indicator.dart';
import '../../components/disabled_outlined_button_style.dart';
import 'delivered_products_list_controller.dart';
import 'delivered_products_list_page_body.dart';

/// 納品伝票 データ登録 納品/一覧 画面
/// 納品伝票を印刷する手前の確認画面。
/// 商品の納品数量の修正や商品の削除をこの画面で担う。
class DeliveredProductsListPage extends StatelessWidget {
  /// init
  const DeliveredProductsListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            backgroundColor: colors.primary,
            title: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                '納品商品一覧',
                style: texts.titleMedium?.copyWith(color: colors.onPrimary),
              ),
            ),
            centerTitle: true,
            automaticallyImplyLeading: false,
          ),
          body: const SafeArea(
            child: DeliveredProductsListPageBody(),
          ),
          bottomNavigationBar: BottomAppBar(
            child: Row(
              children: [
                const PacerBackButton(),
                const Spacer(flex: 2),
                _DeleteButton(),
                const Spacer(),
                _PrintButton(),
              ],
            ),
          ),
        ),
        Consumer(
          builder: (context, ref, _) {
            final deliveryProductsListLoadingState = ref.watch(deliveryProductsListControllerProvider);

            return switch (deliveryProductsListLoadingState) {
              AsyncLoading() => const DeliveryProgressIndicator(),
              _ => const SizedBox.shrink(),
            };
          },
        ),
      ],
    );
  }
}

class _DeleteButton extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.watch(deliveryProductsListControllerProvider.notifier);
    final selectingProduct = ref.watch(selectingDeliveryProductProvider);
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    Future<void> onPressedDelete(BuildContext context) async {
      final ok = await showAlertDialog(
        context: context,
        title: '削除しますか？',
        content: ref.read(selectingDeliveryProductProvider).toString(),
        cancelActionText: 'キャンセル',
      );
      if (ok ?? false) {
        await ref.read(globalLoadingServiceProvider.notifier).wrap(
              notifier.deleteCurrentProduct(
                onSuccess: () => showSnackBar(context, '削除しました'),
              ),
            );
      }
    }

    return OutlinedButton.icon(
      onPressed: switch (selectingProduct) {
        null => null,
        _ => () => onPressedDelete(ref.context),
      },
      icon: const Icon(Icons.delete),
      label: Text(
        '削除',
        style: texts.titleMedium?.copyWith(
          color: colors.primary,
        ),
      ),
      style: disabledOutlinedButtonStyle(context),
    );
  }
}

class _PrintButton extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(deliveryProductsListControllerProvider);
    final notifier = ref.watch(deliveryProductsListControllerProvider.notifier);
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final isNotDialogShowing = useState(true);
    final canPressPrintButton = (
      isNotEmptyProducts: state.value?.isNotEmpty,
      isNotDialogShowing: isNotDialogShowing.value,
    );

    Future<void> onPressed(BuildContext context) async {
      isNotDialogShowing.value = false;

      final isAllToday = await notifier.isAllToday();

      if (context.mounted) {
        final ok = await showAlertDialog(
              context: context,
              title: switch (isAllToday) { true => '印刷しますか？', false => '本日納品ではない商品が存在します。\nこのまま印刷しますか？' },
              cancelActionText: 'キャンセル',
            ) ??
            false;

        if (ok) {
          if (!isAllToday) {
            await ref.read(globalLoadingServiceProvider.notifier).wrap(
                  notifier.updateDeliveryDate(),
                );
          }

          await ref.read(globalLoadingServiceProvider.notifier).wrap(
                notifier.print(
                  onSuccess: () => showSnackBar(context, '印刷しました'),
                ),
              );
        }
      }

      isNotDialogShowing.value = true;
    }

    return FilledButton.icon(
      onPressed: switch (canPressPrintButton) {
        (isNotEmptyProducts: true, isNotDialogShowing: true) => () => onPressed(context),
        _ => null,
      },
      icon: const Icon(Icons.print),
      label: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          '印刷',
          style: texts.titleMedium?.copyWith(
            color: colors.onPrimary,
          ),
        ),
      ),
    );
  }
}
