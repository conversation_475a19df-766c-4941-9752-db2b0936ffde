import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../common_widgets/snack_bars.dart';
import '../../../../../exceptions/app_exception.dart';
import '../../../../../themes/app_color_scheme.dart';
import '../../../../../utils/date_time.dart';
import '../../../../app/application/global_loading_service.dart';
import '../../../domain/delivered_product.dart';
import '../../../domain/get_parsed_quantity.dart';
import '../../components/delivery_async_error_ui.dart';
import '../../components/delivery_slip_empty_view.dart';
import '../../components/quantity_form_field.dart';
import 'delivered_products_list_controller.dart';
import 'delivered_products_list_page.dart';

const _empryViewMessage = '納品商品がありません';
const _borderRadius = BorderRadius.all(Radius.circular(10));
final _formatter = NumberFormat.decimalPattern('ja');

/// Body of [DeliveredProductsListPage].
class DeliveredProductsListPageBody extends HookConsumerWidget {
  /// init.
  const DeliveredProductsListPageBody({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(deliveryProductsListControllerProvider);
    final notifier = ref.watch(deliveryProductsListControllerProvider.notifier);
    final selectingProduct = ref.watch(selectingDeliveryProductProvider);
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final quantityController = useTextEditingController();
    final focusNode = useFocusNode();
    final salesPriceSum = notifier.getSalesPriceSum();

    Future<void> updateDeliveryQuantity({bool ignoreValidation = false}) async {
      return ref.read(globalLoadingServiceProvider.notifier).wrap(
            notifier.updateDeliveryQuantity(
              deliveryQuantityText: quantityController.text,
              ignoreValidation: ignoreValidation,
              onSuccess: () => showSnackBar(context, '納品数量を更新しました'),
            ),
          );
    }

    /// ダイアログの「修正する」ボタンをタップした時の処理。
    Future<void> onPressedEdit(DeliveredProduct deliveredProduct) async {
      final ok = await showAlertDialog(
            context: context,
            title: '修正',
            content: '納品数量を修正しますか？',
            defaultActionText: '修正する',
            cancelActionText: 'キャンセル',
          ) ??
          false;

      if (!context.mounted || !ok) return;

      ref.read(selectingDeliveryProductProvider.notifier).select(deliveredProduct);

      final quantity = await _showEditingQuantityDialog(
            context: context,
            controller: quantityController,
            focusNode: focusNode,
            jan: deliveredProduct.productCode.value,
          ) ??
          '';

      if (quantity.isEmpty) return;

      await updateDeliveryQuantity();
    }

    ref.listen<AsyncValue<void>>(
      deliveryProductsListControllerProvider,
      (_, next) async {
        final userConfirmed = await next.showAlertOnRetryableError(
              context: context,
              cancelActionText: 'キャンセル',
            ) ??
            false;

        if (userConfirmed) {
          await updateDeliveryQuantity(ignoreValidation: true);
        }
      },
    );

    return switch (state) {
      AsyncError(:final AppException error, hasValue: false) => DeliverySlipEmptyView(message: error.message),
      AsyncError(:final value?) ||
      AsyncData(:final value) when value.isEmpty =>
        const DeliverySlipEmptyView(message: _empryViewMessage),
      AsyncData(:final value) || AsyncError(:final value?) => CustomScrollView(
          slivers: [
            SliverAppBar(
              automaticallyImplyLeading: false,
              pinned: true,
              title: Text(
                '売価金額合計: ${_formatter.format(salesPriceSum)}',
                style: texts.titleMedium?.copyWith(
                  color: colors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (BuildContext context, int index) {
                  // Ink側のpaddingを指定するとInk色範囲とTableとの枠線位置がズレるため
                  // PaddingでInkをラップする
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Ink(
                      decoration: BoxDecoration(
                        borderRadius: _borderRadius,
                        color: selectingProduct == value[index] ? colors.sub : colors.surface,
                      ),
                      child: InkWell(
                        borderRadius: _borderRadius,
                        onTap: () => ref
                            .read(
                              selectingDeliveryProductProvider.notifier,
                            )
                            .select(value[index]),
                        child: Card(
                          color: index.isEven ? colors.inversePrimary : null,
                          child: _DeliveredProductTable(
                            value: value[index],
                            onPressedEdit: onPressedEdit,
                          ),
                        ),
                      ),
                    ),
                  );
                },
                childCount: value.length,
              ),
            ),
          ],
        ),
      _ => const Center(child: CircularProgressIndicator()),
    };
  }
}

class _DeliveredProductTable extends StatelessWidget {
  const _DeliveredProductTable({
    required this.value,
    required this.onPressedEdit,
  });

  final DeliveredProduct value;
  final void Function(DeliveredProduct) onPressedEdit;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3),
        1: FractionColumnWidth(0.7),
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: _borderRadius,
      ),
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      children: [
        _tableRow(
          const _RowText('JAN', isLabel: true),
          _RowText(value.productCode.value),
        ),
        _tableRow(
          const _RowText('商品名', isLabel: true),
          _RowText(value.productName),
        ),
        _tableRow(
          const _RowText('ベンダー', isLabel: true),
          _RowText(value.venderName),
        ),
        _tableRow(
          const _RowText('数量', isLabel: true),
          FilledButton.icon(
            icon: const Icon(Icons.edit),
            onPressed: () => onPressedEdit(value),
            label: Text(
              getParsedQuantity(value.quantity),
              style: texts.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        _tableRow(
          const _RowText('納品日', isLabel: true),
          _RowText(
            value.deliveryDate.toYearMonthDaySlash,
          ),
        ),
        _tableRow(
          const _RowText('売単価', isLabel: true),
          _RowText(_formatter.format(value.salesPrice)),
        ),
        _tableRow(
          const _RowText('売価合計', isLabel: true),
          _RowText(_formatter.format(value.salesSum)),
        ),
      ],
    );
  }
}

TableRow _tableRow(Widget label, Widget value) => TableRow(children: [label, value]);

class _RowText extends StatelessWidget {
  const _RowText(this.text, {this.isLabel = false});

  final String text;
  final bool isLabel;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 6,
      ),
      child: Text(
        text,
        style: texts.titleMedium?.copyWith(
          color: colors.text,
          fontWeight: FontWeight.bold,
        ),
        textAlign: isLabel ? TextAlign.right : null,
      ),
    );
  }
}

Future<String?> _showEditingQuantityDialog({
  required BuildContext context,
  required TextEditingController controller,
  required FocusNode focusNode,
  required String jan,
}) async {
  // 入力前初期化
  controller.clear();

  return showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('納品数量を入力してください'),
      content: QuantityFormField(
        autofocus: true,
        controller: controller,
        onFieldSubmitted: (value) => Navigator.of(context).pop(value),
        jan: jan,
      ),
    ),
  );
}
