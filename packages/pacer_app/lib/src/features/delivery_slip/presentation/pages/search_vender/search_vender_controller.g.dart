// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_vender_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deliveryReissueSystemDateHash() => r'b12ec5a087e64f08517601e1e0e6e5d16cac684f';

/// 納品伝票（再発行）の導線で共有するsystemDate。
/// 画面遷移時にAPI経由で初期化する。
///
/// Copied from [deliveryReissueSystemDate].
@ProviderFor(deliveryReissueSystemDate)
final deliveryReissueSystemDateProvider = AutoDisposeFutureProvider<DateTime>.internal(
  deliveryReissueSystemDate,
  name: r'deliveryReissueSystemDateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deliveryReissueSystemDateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DeliveryReissueSystemDateRef = AutoDisposeFutureProviderRef<DateTime>;
String _$searchVenderControllerHash() => r'c6dc9f7e3c71e2edaed4ce32eccd30c8419f5d1f';

/// 納品伝票再発行 ベンダー情報検索ロジック
///
/// Copied from [SearchVenderController].
@ProviderFor(SearchVenderController)
final searchVenderControllerProvider =
    AutoDisposeAsyncNotifierProvider<SearchVenderController, DeliveryVender?>.internal(
  SearchVenderController.new,
  name: r'searchVenderControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$searchVenderControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SearchVenderController = AutoDisposeAsyncNotifier<DeliveryVender?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
