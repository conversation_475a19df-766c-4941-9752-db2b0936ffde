import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../routing/app_router.dart';
import '../../../../../themes/app_color_scheme.dart';
import '../../../../../utils/adaptive_number_input_type.dart';
import '../../../../../utils/async_value_ui.dart';
import '../../../../../utils/remove_zero_formatter.dart';
import '../../../../device/application/pacer_service.dart';
import '../../../../device/presentation/scan_window.dart';
import '../../components/delivery_progress_indicator.dart';
import '../../routing/delivery_slip_route.dart';
import 'search_vender_controller.dart';

final _thisPageLocation = DeliverySearchVender().location;
final _dateFormatter = DateFormat('yyyy年MM月dd日');

/// 納品伝票 データ登録 納品/入力 画面
class SearchVenderPage extends HookConsumerWidget {
  /// init
  const SearchVenderPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchVenderState = ref.watch(searchVenderControllerProvider);
    final notifier = ref.watch(searchVenderControllerProvider.notifier);
    final topFocusNode = useFocusNode();
    final venderCodeController = useTextEditingController();
    final venderNameController = useTextEditingController();
    final deliveryDateController = useTextEditingController(
      text: _dateFormatter.format(searchVenderState.value?.deliveryDate ?? DateTime.now()),
    );

    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    Future<void> onTapSearchButton() async {
      DeliveryReissueSlipListRoute().go(context);
    }

    ref
      ..listen<AsyncValue<void>>(
        searchVenderControllerProvider,
        (_, state) {
          state.showSnackBarOnError(context);
          if (state.hasError) {
            ref.invalidate(searchVenderControllerProvider);
          }
        },
      )
      ..listen(scanCodeProvider, (_, scanned) {
        final globalLocation = GoRouterState.of(context).uri.toString();
        if (globalLocation != _thisPageLocation) return;

        _onScanningComplete(ref, scanned.value);
      })
      ..listen(searchVenderControllerProvider, (previous, next) {
        // AsyncLoading時に、以前入力した内容が一瞬表示される現象防止
        if (previous?.value == next.value) return;

        venderCodeController.text = switch (next.value) {
          final vender? => vender.venderCode == null ? '' : vender.venderCode.toString(),
          _ => ''
        };

        venderNameController.text = switch (next.value) {
          final vender? => vender.venderName ?? '',
          _ => '',
        };
      });

    return Stack(
      children: [
        Focus(
          focusNode: topFocusNode,
          child: GestureDetector(
            onTap: topFocusNode.requestFocus,
            child: Scaffold(
              resizeToAvoidBottomInset: false,
              appBar: AppBar(
                automaticallyImplyLeading: false,
                backgroundColor: colors.primary,
                centerTitle: true,
                iconTheme: IconThemeData(color: colors.button),
                title: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    '納品/再発行',
                    style: texts.titleMedium?.copyWith(color: colors.onPrimary),
                  ),
                ),
              ),
              bottomNavigationBar: BottomAppBar(
                shape: const CircularNotchedRectangle(),
                child: Row(
                  children: [
                    const PacerBackButton(),
                    const Spacer(),
                    _SearchButton(onPressed: onTapSearchButton),
                  ],
                ),
              ),
              body: SafeArea(
                child: switch (searchVenderState) {
                  AsyncLoading() => const SizedBox.shrink(),
                  AsyncData() || AsyncError() => SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 24,
                      ),
                      child: Column(
                        children: [
                          _VenderCodeField(
                            venderCodeController,
                            onScan: (code) async {
                              final globalLocation = GoRouterState.of(context).uri.toString();
                              if (globalLocation == _thisPageLocation) {
                                await notifier.getVenderByJan(
                                  productCode: code,
                                );
                              }
                            },
                          ),
                          const Gap(16),
                          _VenderNameField(venderNameController),
                          const Gap(36),
                          _DeliveryDateField(deliveryDateController),
                        ],
                      ),
                    ),
                },
              ),
              floatingActionButton: ScanFloatingIconButton(
                onScan: (scannedValue) => _onScanningComplete(ref, scannedValue),
              ),
              floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
            ),
          ),
        ),
        switch (searchVenderState) {
          AsyncLoading() => const DeliveryProgressIndicator(),
          AsyncData() || AsyncError() => const SizedBox.shrink(),
        },
      ],
    );
  }

  void _onScanningComplete(
    WidgetRef ref,
    String? scannedValue,
  ) {
    ref.read(searchVenderControllerProvider.notifier).getVenderByJan(productCode: scannedValue);
  }
}

class _VenderCodeField extends HookConsumerWidget {
  const _VenderCodeField(this.controller, {required this.onScan});

  final TextEditingController controller;
  final void Function(String) onScan;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.watch(searchVenderControllerProvider.notifier);
    final venderCodeFocusNode = useFocusNode();
    const maxLengthVenderCode = 4;

    return TextField(
      controller: controller,
      decoration: const InputDecoration(
        filled: true,
        border: OutlineInputBorder(),
        labelText: 'ベンダー番号',
        hintText: 'ベンダー番号を入力してください',
      ),
      focusNode: venderCodeFocusNode,
      textInputAction: TextInputAction.done,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        RemoveZeroTextInputFormatter(),
      ],
      keyboardType: TextInputType.number.withEnter(),
      maxLength: maxLengthVenderCode,
      onSubmitted: (venderCode) => notifier.getVender(venderCode: venderCode),
    );
  }
}

class _VenderNameField extends StatelessWidget {
  const _VenderNameField(this.controller);

  final TextEditingController controller;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: IgnorePointer(
        child: TextField(
          controller: controller,
          readOnly: true,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'ベンダー名',
          ),
        ),
      ),
    );
  }
}

class _DeliveryDateField extends ConsumerWidget {
  const _DeliveryDateField(this.controller);

  final TextEditingController controller;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Future<void> showDeliveryDateDatePicker(BuildContext context) async {
      final pickedDate = await showDatePicker(
        context: context,
        initialDate: _dateFormatter.parse(controller.text),
        firstDate: DateTime(1900),
        lastDate: DateTime(2101),
        locale: const Locale('ja'),
        fieldLabelText: '納品日',
      );

      if (pickedDate != null && pickedDate != DateTime(2101)) {
        ref.read(searchVenderControllerProvider.notifier).updateDeliveryDate(pickedDate);
        controller.text = _dateFormatter.format(pickedDate);
      }
    }

    return InkWell(
      onTap: () => showDeliveryDateDatePicker(context),
      child: IgnorePointer(
        child: TextFormField(
          controller: controller,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            filled: true,
            labelText: '納品日',
            suffixIcon: Icon(Icons.calendar_today),
          ),
        ),
      ),
    );
  }
}

class _SearchButton extends StatelessWidget {
  const _SearchButton({required this.onPressed});

  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.search),
      label: Text(
        '検索',
        style: texts.labelMedium?.copyWith(color: colors.primary),
      ),
    );
  }
}
