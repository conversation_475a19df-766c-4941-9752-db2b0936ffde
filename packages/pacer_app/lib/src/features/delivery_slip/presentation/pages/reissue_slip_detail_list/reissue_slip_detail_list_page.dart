import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/pacer_back_button.dart';
import '../../components/delivery_progress_indicator.dart';
import 'reissue_slip_detail_list_controller.dart';
import 'reissue_slip_detail_list_page_body.dart';

/// 納品/再発行 納品伝票詳細情報一覧画面
class ReissueSlipDetailListPage extends StatelessWidget {
  /// init
  const ReissueSlipDetailListPage({
    super.key,
    required this.slipNumber,
    this.optionalVenderName,
  });

  /// 選択した納品伝票の伝票番号。
  final int slipNumber;

  /// 納品伝票に対応するベンダー名。
  final String? optionalVenderName;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            backgroundColor: colors.primary,
            centerTitle: true,
            title: FittedBox(
              fit: BoxFit.scaleDown,
              child: _AppBarTitle(),
            ),
          ),
          body: SafeArea(
            child: ReissueSlipDetailListPageBody(
              slipNumber: slipNumber,
              optionalVenderName: optionalVenderName,
            ),
          ),
          bottomNavigationBar: const BottomAppBar(
            child: Row(
              children: [
                PacerBackButton(),
                Spacer(),
              ],
            ),
          ),
        ),
        Consumer(
          builder: (context, ref, _) {
            final reissueSlipDetailListLoadingState = ref.watch(reissueSlipDetailListControllerProvider(slipNumber));

            return switch (reissueSlipDetailListLoadingState) {
              AsyncLoading() => const DeliveryProgressIndicator(),
              _ => const SizedBox.shrink(),
            };
          },
        ),
      ],
    );
  }
}

class _AppBarTitle extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Text(
      '納品/再発行 伝票詳細一覧',
      style: texts.titleMedium?.copyWith(
        color: colors.onPrimary,
      ),
    );
  }
}
