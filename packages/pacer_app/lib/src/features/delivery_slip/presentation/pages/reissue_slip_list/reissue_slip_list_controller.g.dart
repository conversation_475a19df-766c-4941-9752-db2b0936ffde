// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reissue_slip_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$reissueSlipListControllerHash() => r'a435b45bebea8908a52079be99a58eb3d9695392';

/// 納品/再発行 伝票一覧画面のロジック
///
/// Copied from [ReissueSlipListController].
@ProviderFor(ReissueSlipListController)
final reissueSlipListControllerProvider =
    AutoDisposeAsyncNotifierProvider<ReissueSlipListController, List<DeliverySlip>>.internal(
  ReissueSlipListController.new,
  name: r'reissueSlipListControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$reissueSlipListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ReissueSlipListController = AutoDisposeAsyncNotifier<List<DeliverySlip>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
