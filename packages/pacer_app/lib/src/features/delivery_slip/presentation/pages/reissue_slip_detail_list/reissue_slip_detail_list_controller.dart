import 'package:flutter/cupertino.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../exceptions/app_exception.dart';
import '../../../application/delivery_slip_service.dart';
import '../../../domain/delivery_product_code.dart';
import '../../../domain/delivery_slip_detail.dart';
import '../../../domain/delivery_vender.dart';
import '../reissue_slip_list/reissue_slip_list_controller.dart';
import '../search_vender/search_vender_controller.dart';

part 'reissue_slip_detail_list_controller.g.dart';

/// 納品/再発行 伝票一覧画面のロジック
@riverpod
class ReissueSlipDetailListController extends _$ReissueSlipDetailListController {
  /// [DeliverySlipService]
  DeliverySlipService get _service => ref.watch(deliverySlipServiceProvider);

  @override
  FutureOr<List<DeliverySlipDetail>> build(int slipNumber) async {
    try {
      state = const AsyncLoading();
      return await _service.listDeliverySlipDetail(slipNumber: slipNumber);
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }

    return [];
  }

  /// 納品伝票詳細情報の売価合計金額を取得する
  /// リストのヘッダー部分に表示する
  int? getSalesPriceSum() {
    final slipDetails = state.valueOrNull;
    if (slipDetails == null || slipDetails.isEmpty) return null;

    return slipDetails.map((e) => e.salesSum).reduce((value, element) => value + element);
  }

  /// （再発行）納品伝票の納品数量を修正する
  Future<void> updateSlipQuantity({
    required String deliveryQuantityText,
    required int index,
    bool ignoreValidation = false,
    VoidCallback? onSuccess,
  }) async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;
    try {
      state = const AsyncLoading();

      final allowEditing = await ref.read(allowEditingDeliveryQuantityProvider.future);

      if (!allowEditing) {
        throw DenyEditDeliveryQuantityException();
      }

      final slipDetail = state.value?.elementAtOrNull(index);

      if (slipDetail == null) throw UnknownException('伝票詳細情報がありません');

      final productInfo = await _service.fetchProduct(
        productCode: DeliveryProductCode.parse(slipDetail.productCode.value),
      );

      if (productInfo == null) throw ProductNotFoundException();

      final parsedQuantity = double.tryParse(deliveryQuantityText);

      if (parsedQuantity == null || parsedQuantity < 0) {
        throw UnknownException('納品数量が不正です');
      }

      if (!ignoreValidation) {
        productInfo.validateQuantity(
          parsedQuantity,
          optionalSalesPrice: slipDetail.salesPrice.toDouble(),
        );
      }

      await AsyncValue.guard(
        () => _service.updateSlipQuantity(
          deliveryQuantity: parsedQuantity,
          slipId: slipDetail.id,
        ),
      );

      ref
        ..invalidateSelf()
        ..invalidate(reissueSlipListControllerProvider);

      if (!state.hasError && !state.isReloading) {
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }
}

/// [DeliveryVender]のallowEditingを参照。
@riverpod
Future<bool> allowEditingDeliveryQuantity(
  AllowEditingDeliveryQuantityRef ref,
) async {
  final vender = ref.watch(searchVenderControllerProvider).value;
  final systemDate = await ref.read(deliverySlipServiceProvider).systemDate;

  return switch ((vender, systemDate)) {
    (final vender?, final systemDate) => vender.allowEditing(systemDate: systemDate),
    _ => false
  };
}
