import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../common_widgets/snack_bars.dart';
import '../../../../../routing/app_router.dart';
import '../../../../../themes/app_color_scheme.dart';
import '../../../../../utils/async_value_ui.dart';
import '../../../../app/application/global_loading_service.dart';
import '../../../domain/delivery_slip.dart';
import '../../components/delivery_progress_indicator.dart';
import '../../components/delivery_slip_empty_view.dart';
import '../../components/selected_delivery_date_sliver_app_bar.dart';
import '../../components/target_vender_sliver_app_bar.dart';
import '../../routing/delivery_slip_route.dart';
import 'reissue_slip_list_controller.dart';

/// 納品/再発行 伝票一覧画面
class ReissueSlipListPage extends ConsumerWidget {
  /// init
  const ReissueSlipListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final reissueSlipListLoadingState = ref.watch(reissueSlipListControllerProvider);
    final reissueSlipListNotifier = ref.watch(reissueSlipListControllerProvider.notifier);

    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    ref.listen<AsyncValue<void>>(reissueSlipListControllerProvider, (_, state) {
      state.showSnackBarOnError(context);
    });

    void onTapSlipTile(DeliverySlip slip) {
      DeliveryReissueSlipDetailListRoute(
        slipNumber: slip.slipNumber,
        optionalVenderName: slip.supplierName,
      ).go(context);
    }

    Future<void> onPressedPrint(BuildContext context) async {
      final isNotSelected = reissueSlipListLoadingState.value?.every((e) => e.isSelected == false) ?? false;
      if (isNotSelected) {
        showSnackBar(context, '再発行する伝票を選択してください', isErrorStyle: true);
        return;
      }

      final ok = await showAlertDialog(
            context: context,
            title: '印刷しますか？',
            cancelActionText: 'キャンセル',
          ) ??
          false;

      if (ok) {
        await ref.read(globalLoadingServiceProvider.notifier).wrap(
              reissueSlipListNotifier.print(
                onSuccess: () => showSnackBar(context, '再発行しました'),
              ),
            );
      }
    }

    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            backgroundColor: colors.primary,
            centerTitle: true,
            iconTheme: IconThemeData(color: colors.button),
            title: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                '納品/再発行 伝票一覧',
                style: texts.titleMedium?.copyWith(color: colors.onPrimary),
              ),
            ),
          ),
          bottomNavigationBar: BottomAppBar(
            child: Row(
              children: [
                const PacerBackButton(),
                const Spacer(),
                FilledButton.icon(
                  onPressed: () => onPressedPrint(context),
                  icon: const Icon(Icons.print),
                  label: const Text('印刷'),
                ),
              ],
            ),
          ),
          body: SafeArea(
            child: switch (reissueSlipListLoadingState) {
              AsyncData(:final value) => _ReissueSlipList(
                  onChanged: reissueSlipListNotifier.toggle,
                  onTap: onTapSlipTile,
                  slipList: value,
                ),
              AsyncError(:final value) => _ReissueSlipList(
                  onChanged: reissueSlipListNotifier.toggle,
                  onTap: onTapSlipTile,
                  slipList: value ?? [],
                ),
              _ => const SizedBox.shrink()
            },
          ),
        ),
        switch (reissueSlipListLoadingState) {
          AsyncLoading() => const DeliveryProgressIndicator(),
          _ => const SizedBox.shrink(),
        },
      ],
    );
  }
}

class _ReissueSlipList extends StatelessWidget {
  _ReissueSlipList({
    required this.slipList,
    required this.onChanged,
    required this.onTap,
  });

  final List<DeliverySlip> slipList;
  final void Function(int?) onChanged;
  final void Function(DeliverySlip) onTap;

  final formatter = NumberFormat.decimalPattern('ja');

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    if (slipList.isEmpty) {
      return const DeliverySlipEmptyView(message: '対象の伝票が見つかりませんでした');
    }

    return CustomScrollView(
      slivers: [
        const TargetVenderSliverAppBar(),
        const SelectedDeliveryDateSliverAppBar(),
        SliverList(
          delegate: SliverChildBuilderDelegate(
              // slipList.length個分のデータ生成はDataTable側で実施。
              childCount: 1, (context, index) {
            return SizedBox(
              width: double.infinity,
              child: DataTable(
                dataRowMaxHeight: double.infinity,
                columns: <DataColumn>[
                  DataColumn(
                    label: Padding(
                      // ListTileのdefault paddingに合わせる
                      padding: const EdgeInsets.only(left: 16),
                      child: Text('納品伝票', style: texts.bodyLarge),
                    ),
                  ),
                ],
                rows: List<DataRow>.generate(
                  slipList.length,
                  (index) {
                    final slip = slipList.elementAtOrNull(index);

                    return DataRow(
                      color: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
                        if (states.contains(WidgetState.selected)) {
                          return colors.line;
                        }

                        if (index.isEven) return colors.primaryContainer;

                        return null;
                      }),
                      cells: <DataCell>[
                        DataCell(
                          ListTile(
                            title: Text(
                              '伝票No: ${slip?.slipNumber}',
                              style: texts.bodyLarge,
                            ),
                            subtitle: Text(
                              '''
ベンダー: ${slip?.supplierName}
原価金額合計: ${formatter.format(slip?.costPriceSum ?? 0)}
売価金額合計: ${formatter.format(slip?.salesPriceSum ?? 0)}
担当者: ${slip?.employeeCode} ${slip?.employeeName}''',
                              style: texts.bodyLarge,
                            ),
                            isThreeLine: true,
                            dense: true,
                            onTap: () => switch (slip) { final targetSlip? => onTap(targetSlip), _ => null },
                          ),
                        ),
                      ],
                      selected: slipList[index].isSelected,
                      onSelectChanged: (_) => onChanged(slip?.slipNumber),
                    );
                  },
                ),
              ),
            );
          }),
        ),
      ],
    );
  }
}
