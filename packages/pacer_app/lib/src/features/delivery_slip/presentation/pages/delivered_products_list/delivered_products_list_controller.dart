import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../constants/environment.dart';
import '../../../../../exceptions/app_exception.dart';
import '../../../application/delivery_slip_service.dart';
import '../../../domain/delivered_product.dart';
import '../../../domain/delivery_product_code.dart';

part 'delivered_products_list_controller.g.dart';

/// 納品伝票 データ登録 納品/一覧 画面のビジネスロジック
@riverpod
class DeliveryProductsListController extends _$DeliveryProductsListController {
  /// service
  DeliverySlipService get _service => ref.watch(deliverySlipServiceProvider);

  @override
  FutureOr<List<DeliveredProduct>> build() async {
    try {
      return _service.deliveredProduct();
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }

    return [];
  }

  /// サーバの現在時刻データ。
  /// 以下のタイミングで利用する。
  /// - 納品登録されたデータの納品日＝＝当日のチェック
  /// - 印刷データ作成API実行時
  /// - 納品日修正API実行時
  Future<DateTime> get _systemDate async => _service.systemDate;

  /// 納品数量を修正する。
  /// 納品/入力 画面で登録した納品数量が正しくない場合などに使用する。
  Future<void> updateDeliveryQuantity({
    required String deliveryQuantityText,
    bool ignoreValidation = false,
    VoidCallback? onSuccess,
  }) async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;
    try {
      state = const AsyncLoading();

      final targetItem = ref.read(selectingDeliveryProductProvider);

      if (targetItem == null) {
        throw UnknownException('納品商品が選択されていません');
      }

      final parsedQuantity = double.tryParse(deliveryQuantityText);

      if (parsedQuantity == 0) {
        throw DeliveryQuantityZeroUpdateException();
      }

      if (parsedQuantity == null) {
        throw UnknownException('納品数量が入力されていないか、形式が不正です');
      }

      final productInfo = await _service.fetchProduct(
        productCode: DeliveryProductCode.parse(targetItem.productCode.value),
      );

      if (productInfo == null) throw ProductNotFoundException();

      if (!ignoreValidation) {
        productInfo.validateQuantity(
          parsedQuantity,
          optionalSalesPrice: targetItem.salesPrice.toDouble(),
        );
      }

      await AsyncValue.guard(
        () => _service.updateDeliveryQuantity(
          id: targetItem.id,
          deliveryQuantity: parsedQuantity,
        ),
      );

      if (Env.isEnableMock) {
        final temp = state.value ?? [];
        final index = temp.indexWhere((item) => item == targetItem);
        temp[index] = targetItem.copyWith(
          quantity: parsedQuantity,
          salesSum: (targetItem.salesPrice * parsedQuantity).toInt(),
        );
        state = AsyncData(temp);
      } else {
        ref.invalidateSelf();
      }

      if (!state.hasError && !state.isReloading) {
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }

  /// 納品登録した商品を削除する。
  /// 納品登録した商品が間違っていた場合などに使用する。
  Future<void> deleteCurrentProduct({
    VoidCallback? onSuccess,
  }) async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;
    try {
      state = const AsyncLoading();

      final selecting = ref.read(selectingDeliveryProductProvider);

      final productId = selecting?.id.toString();

      await AsyncValue.guard(
        () => ref.read(deliverySlipServiceProvider).deleteDeliveredProduct(productId: productId),
      );

      if (Env.isEnableMock) {
        final products = state.value ?? [];
        final _ = products.removeWhere((item) => item == selecting);
        state = AsyncData(products);
      } else {
        ref.invalidateSelf();
      }

      if (!state.hasError && !state.isReloading) {
        ref.invalidate(selectingDeliveryProductProvider);
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }

  /// 印刷をリクエストする。
  /// ユーザが商品情報の確認を終え、印刷ボタンをタップしたときに実行する。
  Future<void> print({
    VoidCallback? onSuccess,
  }) async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;
    try {
      state = const AsyncLoading();

      // 1. プリンターチェック

      await ref.read(deliverySlipServiceProvider).checkConnectionWithPrinter();

      // 2. 印刷データ投入

      final deliveryDate = await _systemDate;

      final printId = await ref.read(deliverySlipServiceProvider).insertDeliveryRetail(deliveryDate: deliveryDate);

      // 3. 印刷リクエスト

      await _service.requestPrint(printId: printId);

      if (Env.isEnableMock) {
        // 印刷後は納品商品一覧が一括Clearされるため。
        state = const AsyncData([]);
      } else {
        ref.invalidateSelf();
      }

      if (!state.hasError && !state.isReloading) {
        ref.invalidate(selectingDeliveryProductProvider);
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }

  /// 納品した商品の納品日を全て当日の日付で更新する
  Future<void> updateDeliveryDate() async {
    if (state.isLoading || state.isReloading || state.isRefreshing) return;
    try {
      await AsyncValue.guard(
        () => _service.updateDeliveryDate(),
      );
    } catch (err, stack) {
      state = AsyncError(err, stack);
    }
  }

  /// 納品日が全て当日かチェックする。
  /// 納品伝票を印刷する際に確認が必要。
  Future<bool> isAllToday() async {
    final today = await _systemDate;

    return switch (state.value) {
      final products? => products.every(
          (product) =>
              product.deliveryDate.year == today.year &&
              product.deliveryDate.month == today.month &&
              product.deliveryDate.day == today.day,
        ),
      _ => false,
    };
  }

  /// 納品商品の売価合計金額を取得する
  /// リストのヘッダー部分に表示する
  int? getSalesPriceSum() {
    final slipDetails = state.valueOrNull;
    if (slipDetails == null || slipDetails.isEmpty) return null;

    return slipDetails.map((e) => e.salesSum).reduce((value, element) => value + element);
  }
}

/// 選択中の納品商品。
/// ユーザがList Itemをタップして更新する。
@riverpod
class SelectingDeliveryProduct extends _$SelectingDeliveryProduct {
  @override
  DeliveredProduct? build() {
    return null;
  }

  /// ユーザがタップしたら呼び出してStateを更新する。
  void select(DeliveredProduct? selectedItem) {
    if (state == selectedItem) return;

    state = selectedItem;
  }
}
