// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivered_products_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deliveryProductsListControllerHash() => r'376bcb750c99bb298ac5ddaae14755f3981aa2dc';

/// 納品伝票 データ登録 納品/一覧 画面のビジネスロジック
///
/// Copied from [DeliveryProductsListController].
@ProviderFor(DeliveryProductsListController)
final deliveryProductsListControllerProvider =
    AutoDisposeAsyncNotifierProvider<DeliveryProductsListController, List<DeliveredProduct>>.internal(
  DeliveryProductsListController.new,
  name: r'deliveryProductsListControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deliveryProductsListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeliveryProductsListController = AutoDisposeAsyncNotifier<List<DeliveredProduct>>;
String _$selectingDeliveryProductHash() => r'ad8bb3849f0352fd12d24ae9a24c7ee9f55d4bc9';

/// 選択中の納品商品。
/// ユーザがList Itemをタップして更新する。
///
/// Copied from [SelectingDeliveryProduct].
@ProviderFor(SelectingDeliveryProduct)
final selectingDeliveryProductProvider =
    AutoDisposeNotifierProvider<SelectingDeliveryProduct, DeliveredProduct?>.internal(
  SelectingDeliveryProduct.new,
  name: r'selectingDeliveryProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectingDeliveryProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectingDeliveryProduct = AutoDisposeNotifier<DeliveredProduct?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
