// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../routing/app_router.dart';
import '../../routing/delivery_slip_route.dart';

class DeliverySlipTopPage extends ConsumerWidget {
  const DeliverySlipTopPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: colors.primary,
        centerTitle: true,
        title: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            '納品伝票メニュー',
            style: texts.titleLarge?.copyWith(color: colors.onPrimary),
          ),
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: TextButton(
            onPressed: () => context.pop(),
            child: Text(
              '終了',
              style: texts.titleLarge?.copyWith(color: colors.primary),
            ),
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Card(
              child: ListTile(
                onTap: () => DeliverySlipInput().go(context),
                contentPadding: const EdgeInsets.all(20),
                leading: const CircleAvatar(
                  child: Icon(Icons.add),
                ),
                title: Text(
                  'データ登録',
                  textAlign: TextAlign.center,
                  style: texts.displaySmall,
                ),
              ),
            ),
            Card(
              child: ListTile(
                onTap: () => DeliverySearchVender().go(context),
                contentPadding: const EdgeInsets.all(20),
                leading: const CircleAvatar(
                  child: Icon(Icons.local_print_shop),
                ),
                title: Text(
                  '再発行',
                  textAlign: TextAlign.center,
                  style: texts.displaySmall,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
