// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../pages/delivered_products_list/delivered_products_list_page.dart';
import '../pages/delivery_slip_top/delivery_slip_top_page.dart';
import '../pages/insert_delivery_product/insert_delivery_product_page.dart';
import '../pages/reissue_slip_detail_list/reissue_slip_detail_list_page.dart';
import '../pages/reissue_slip_list/reissue_slip_list_page.dart';
import '../pages/search_vender/search_vender_page.dart';

class DeliverySlipRoute extends GoRouteData {
  @override
  Widget build(BuildContext context, GoRouterState state) => const DeliverySlipTopPage();
}

class DeliverySlipInput extends GoRouteData {
  @override
  Widget build(BuildContext context, GoRouterState state) => const InsertDeliveryProductPage();
}

class DeliveredProductsList extends GoRouteData {
  @override
  Widget build(BuildContext context, GoRouterState state) => const DeliveredProductsListPage();
}

class DeliverySearchVender extends GoRouteData {
  @override
  Widget build(BuildContext context, GoRouterState state) => const SearchVenderPage();
}

class DeliveryReissueSlipListRoute extends GoRouteData {
  @override
  Widget build(BuildContext context, GoRouterState state) => const ReissueSlipListPage();
}

class DeliveryReissueSlipDetailListRoute extends GoRouteData {
  DeliveryReissueSlipDetailListRoute({
    required this.slipNumber,
    this.optionalVenderName,
  });

  final int slipNumber;
  final String? optionalVenderName;

  @override
  Widget build(BuildContext context, GoRouterState state) => ReissueSlipDetailListPage(
        slipNumber: slipNumber,
        optionalVenderName: optionalVenderName,
      );
}
