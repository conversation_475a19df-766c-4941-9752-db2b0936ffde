import 'dart:async';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../common_widgets/alert_dialogs.dart';
import '../../../../exceptions/app_exception.dart';
import '../../../../utils/async_value_ui.dart';

/// AsyncNotifierProviderをlistenする際のダイアログ
extension DeliveryAsyncValueUI on AsyncValue<dynamic> {
  /// errorが存在する場合はダイアログを表示する。
  /// 納品数量チェック結果をハンドリングする必要がある場面で使用する。
  ///
  /// 1. データ登録 納品/入力 (onScan, onTapListButton)
  /// 2. データ登録 納品/一覧 (納品数量更新)
  /// 3. 再発行 納品/一覧 (納品数量入力欄のonFieldSubmitted)
  ///
  /// showExceptionAlertDialogはリトライ不可。
  /// showAlertDialogはリトライ可能。
  Future<bool?> showAlertOnRetryableError({
    required BuildContext context,
    String? cancelActionText,
    String defaultActionText = 'OK',
    VoidCallback? onPressed,
  }) async {
    if (!isLoading && hasError) {
      switch (_asAppException(error)) {
        case final exception &&
              (OverMaxQuantityException() || OverMaxPriceSumException() || OverMaxCostPriceSumException()):
          await showExceptionAlertDialog(
            context: context,
            title: '数量エラー',
            exception: exception,
          );

          return false;
        case final exception && OverDeliveryNormalQuantityException() when context.mounted:
          return showAlertDialog(
            cancelActionText: cancelActionText,
            context: context,
            title: '数量エラー',
            content: exception.message,
          );

        case _ when context.mounted:
          showSnackBarOnError(context);

          return false;
        case _:
      }
    }

    return null;
  }

  AppException _asAppException(Object? error) => switch (error) {
        final AppException e => e,
        _ => UnknownException(error.toString()),
      };
}
