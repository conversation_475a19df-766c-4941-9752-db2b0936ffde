import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../pages/reissue_slip_detail_list/reissue_slip_detail_list_controller.dart';

/// 現在選択中のベンダー、納品日から取得した納品伝票の詳細情報内の
/// 売価合計金額を表示する。
/// 伝票詳細一覧の画面で使う。
class SalesPriceSumSliverAppBar extends ConsumerWidget {
  /// init.
  const SalesPriceSumSliverAppBar({super.key, required this.slipNumber});

  /// 選択した納品伝票の伝票番号。
  final int slipNumber;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final slipDetailSalesPriceSum =
        ref.watch(reissueSlipDetailListControllerProvider(slipNumber).notifier).getSalesPriceSum();

    return SliverAppBar(
      automaticallyImplyLeading: false,
      pinned: true,
      title: _DecimalText(number: slipDetailSalesPriceSum),
      titleTextStyle: Theme.of(context).textTheme.titleMedium,
      toolbarHeight: kToolbarHeight / 2,
    );
  }
}

class _DecimalText extends StatelessWidget {
  _DecimalText({required this.number});

  final num? number;

  final _formatter = NumberFormat.decimalPattern('ja');

  @override
  Widget build(BuildContext context) {
    return Text('売価合計金額: ${_formatter.format(number)}');
  }
}
