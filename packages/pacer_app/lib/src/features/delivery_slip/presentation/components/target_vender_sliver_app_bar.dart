import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../domain/delivery_vender.dart';
import '../pages/search_vender/search_vender_controller.dart';

/// 現在選択中のベンダーと納品日を表示する。
/// 納品/再発行の伝票一覧、伝票詳細一覧の画面で使う。
class TargetVenderSliverAppBar extends StatelessWidget {
  /// init.
  const TargetVenderSliverAppBar({super.key, this.optionalVenderName});

  /// 再発行TOP画面で選択したベンダー。
  /// 未指定なら基本Null。
  /// 例外で、未指定で伝票一覧を取得後に、
  /// 詳細画面へ遷移した場合
  final String? optionalVenderName;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SliverAppBar(
      automaticallyImplyLeading: false,
      pinned: true,
      title: _VenderText(optionalVenderName: optionalVenderName),
      titleTextStyle: theme.textTheme.titleMedium?.copyWith(
        color: theme.colorScheme.primary,
      ),
      toolbarHeight: kToolbarHeight / 2,
    );
  }
}

class _VenderText extends ConsumerWidget {
  const _VenderText({this.optionalVenderName});

  final String? optionalVenderName;

  /// ベンダーが取得できた場合は、ベンダー番号とベンダー名が表示する
  /// Nullなら初期値「指定されていません」
  ///
  String? _displayVender(DeliveryVender? vender) => switch ((vender?.venderCode, vender?.venderName)) {
        (final venderCode?, final venderName) => '$venderCode $venderName',
        _ => null
      };

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final vender = ref.watch(searchVenderControllerProvider).value;

    return Text(
      'ベンダー: ${_displayVender(vender) ?? (optionalVenderName ?? '指定されていません')}',
    );
  }
}
