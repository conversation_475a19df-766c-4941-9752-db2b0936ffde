// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../utils/adaptive_number_input_type.dart';
import '../../../../utils/quantity_28jan_formatter.dart';
import '../../../../utils/remove_zero_formatter.dart';

/// 納品数量を入力する際に使用する
class QuantityFormField extends StatelessWidget {
  const QuantityFormField({
    super.key,
    this.autofocus = false,
    this.onTap,
    required this.controller,
    this.enabled = true,
    this.focusNode,
    this.inputFormatters,
    this.onChanged,
    this.onFieldSubmitted,
    this.style,
    required this.jan,
    this.decoration,
  });

  final bool autofocus;
  final VoidCallback? onTap;
  final TextEditingController controller;
  final bool enabled;
  final FocusNode? focusNode;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;
  final TextStyle? style;

  /// JAN。
  ///
  /// 先頭2桁が28かどうかによってinputFormatterをswitchするために必要。
  final String jan;
  final InputDecoration? decoration;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TextFormField(
      autofocus: autofocus,
      onTap: onTap,
      onTapOutside: (_) => primaryFocus?.unfocus(),
      controller: controller,
      decoration: decoration ??
          const InputDecoration(
            contentPadding: EdgeInsets.all(8),
          ),
      enabled: enabled,
      focusNode: focusNode,
      inputFormatters: switch (jan.startsWith('28')) {
        true => [Quantity28JanFormatter()],
        false => [
            FilteringTextInputFormatter.digitsOnly,
            RemoveZeroTextInputFormatter(),
            LengthLimitingTextInputFormatter(4),
          ],
      },
      keyboardType: TextInputType.number.withEnter(),
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
      textAlignVertical: TextAlignVertical.center,
      textInputAction: TextInputAction.done,
      style: texts.titleMedium,
    );
  }
}
