import 'package:flutter/material.dart';

import '../../../../themes/app_color_scheme.dart';

/// 納品伝票のemptyView。ListViewなどで表示する予定のデータが空の場合に表示する。
class DeliverySlipEmptyView extends StatelessWidget {
  /// init
  const DeliverySlipEmptyView({super.key, required this.message});

  /// EmptyViewがユーザに伝える文言。
  final String message;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            color: colors.primary,
          ),
          Text(
            message,
            style: texts.bodyLarge?.copyWith(color: colors.text),
          ),
        ],
      ),
    );
  }
}
