import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../utils/date_time.dart';
import '../pages/search_vender/search_vender_controller.dart';

/// 現在選択中の納品日を表示する。
/// 納品/再発行の伝票一覧、伝票詳細一覧の画面で使う。
class SelectedDeliveryDateSliverAppBar extends StatelessWidget {
  /// init.
  const SelectedDeliveryDateSliverAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      automaticallyImplyLeading: false,
      pinned: true,
      title: _DeliveryDateText(),
      titleTextStyle: Theme.of(context).textTheme.titleMedium,
      toolbarHeight: kToolbarHeight / 2,
    );
  }
}

class _DeliveryDateText extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final targetVender = ref.watch(searchVenderControllerProvider).value;

    return switch (targetVender?.deliveryDate) {
      final deliveryDate? => Text(
          '納品日: ${deliveryDate.toYearMonthDaySlash}',
        ),
      _ => const SizedBox.shrink()
    };
  }
}
