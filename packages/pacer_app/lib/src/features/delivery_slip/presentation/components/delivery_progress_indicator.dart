import 'package:flutter/material.dart';

import '../../../../themes/app_color_scheme.dart';

/// 納品伝票アプリで共通利用するローディングインジケータ。
class DeliveryProgressIndicator extends StatelessWidget {
  /// constructor
  const DeliveryProgressIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return ColoredBox(
      color: colors.loadingBackground,
      child: Center(
        child: CircularProgressIndicator(color: colors.primary),
      ),
    );
  }
}
