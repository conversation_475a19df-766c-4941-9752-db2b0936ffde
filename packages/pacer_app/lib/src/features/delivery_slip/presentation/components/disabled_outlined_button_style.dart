import 'package:flutter/material.dart';

import '../../../../themes/app_color_scheme.dart';

/// 何らかの理由でdisableになるOutlinedButtonに適用するButtonStyle。
ButtonStyle disabledOutlinedButtonStyle(BuildContext context) {
  final colors = Theme.of(context).colorScheme;

  return ButtonStyle(
    iconColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        return switch (states.contains(WidgetState.disabled)) { true => colors.disabled, false => colors.primary };
      },
    ),
    side: WidgetStateProperty.resolveWith<BorderSide>(
      (Set<WidgetState> states) {
        return switch (states.contains(WidgetState.disabled)) {
          true => BorderSide(color: colors.disabled),
          false => BorderSide(color: colors.primary)
        };
      },
    ),
  );
}
