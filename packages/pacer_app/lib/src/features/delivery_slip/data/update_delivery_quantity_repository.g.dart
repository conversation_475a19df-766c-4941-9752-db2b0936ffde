// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_delivery_quantity_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$updateDeliveryQuantityRepositoryHash() => r'52d8ed1710dd62d2b64c7d4ef675efb32f94b73d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [updateDeliveryQuantityRepository].
@ProviderFor(updateDeliveryQuantityRepository)
const updateDeliveryQuantityRepositoryProvider = UpdateDeliveryQuantityRepositoryFamily();

/// See also [updateDeliveryQuantityRepository].
class UpdateDeliveryQuantityRepositoryFamily extends Family {
  /// See also [updateDeliveryQuantityRepository].
  const UpdateDeliveryQuantityRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'updateDeliveryQuantityRepositoryProvider';

  /// See also [updateDeliveryQuantityRepository].
  UpdateDeliveryQuantityRepositoryProvider call({
    required AppUser? caller,
  }) {
    return UpdateDeliveryQuantityRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  UpdateDeliveryQuantityRepositoryProvider getProviderOverride(
    covariant UpdateDeliveryQuantityRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(UpdateDeliveryQuantityRepository Function(UpdateDeliveryQuantityRepositoryRef ref) create) {
    return _$UpdateDeliveryQuantityRepositoryFamilyOverride(this, create);
  }
}

class _$UpdateDeliveryQuantityRepositoryFamilyOverride implements FamilyOverride {
  _$UpdateDeliveryQuantityRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final UpdateDeliveryQuantityRepository Function(UpdateDeliveryQuantityRepositoryRef ref) create;

  @override
  final UpdateDeliveryQuantityRepositoryFamily overriddenFamily;

  @override
  UpdateDeliveryQuantityRepositoryProvider getProviderOverride(
    covariant UpdateDeliveryQuantityRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// See also [updateDeliveryQuantityRepository].
class UpdateDeliveryQuantityRepositoryProvider extends AutoDisposeProvider<UpdateDeliveryQuantityRepository> {
  /// See also [updateDeliveryQuantityRepository].
  UpdateDeliveryQuantityRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => updateDeliveryQuantityRepository(
            ref as UpdateDeliveryQuantityRepositoryRef,
            caller: caller,
          ),
          from: updateDeliveryQuantityRepositoryProvider,
          name: r'updateDeliveryQuantityRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$updateDeliveryQuantityRepositoryHash,
          dependencies: UpdateDeliveryQuantityRepositoryFamily._dependencies,
          allTransitiveDependencies: UpdateDeliveryQuantityRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  UpdateDeliveryQuantityRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    UpdateDeliveryQuantityRepository Function(UpdateDeliveryQuantityRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UpdateDeliveryQuantityRepositoryProvider._internal(
        (ref) => create(ref as UpdateDeliveryQuantityRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  AutoDisposeProviderElement<UpdateDeliveryQuantityRepository> createElement() {
    return _UpdateDeliveryQuantityRepositoryProviderElement(this);
  }

  UpdateDeliveryQuantityRepositoryProvider _copyWith(
    UpdateDeliveryQuantityRepository Function(UpdateDeliveryQuantityRepositoryRef ref) create,
  ) {
    return UpdateDeliveryQuantityRepositoryProvider._internal(
      (ref) => create(ref as UpdateDeliveryQuantityRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is UpdateDeliveryQuantityRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin UpdateDeliveryQuantityRepositoryRef on AutoDisposeProviderRef<UpdateDeliveryQuantityRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _UpdateDeliveryQuantityRepositoryProviderElement
    extends AutoDisposeProviderElement<UpdateDeliveryQuantityRepository> with UpdateDeliveryQuantityRepositoryRef {
  _UpdateDeliveryQuantityRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as UpdateDeliveryQuantityRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
