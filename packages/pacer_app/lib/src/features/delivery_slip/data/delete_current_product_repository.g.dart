// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delete_current_product_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deleteCurrentProductRepositoryHash() => r'cffdc633665bbecbb8fe5a085159a1956e52dcb3';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// DeleteCurrentProductRepositoryのprovider
///
/// Copied from [deleteCurrentProductRepository].
@ProviderFor(deleteCurrentProductRepository)
const deleteCurrentProductRepositoryProvider = DeleteCurrentProductRepositoryFamily();

/// DeleteCurrentProductRepositoryのprovider
///
/// Copied from [deleteCurrentProductRepository].
class DeleteCurrentProductRepositoryFamily extends Family {
  /// DeleteCurrentProductRepositoryのprovider
  ///
  /// Copied from [deleteCurrentProductRepository].
  const DeleteCurrentProductRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'deleteCurrentProductRepositoryProvider';

  /// DeleteCurrentProductRepositoryのprovider
  ///
  /// Copied from [deleteCurrentProductRepository].
  DeleteCurrentProductRepositoryProvider call({
    required AppUser? caller,
  }) {
    return DeleteCurrentProductRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  DeleteCurrentProductRepositoryProvider getProviderOverride(
    covariant DeleteCurrentProductRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(DeleteCurrentProductRepository Function(DeleteCurrentProductRepositoryRef ref) create) {
    return _$DeleteCurrentProductRepositoryFamilyOverride(this, create);
  }
}

class _$DeleteCurrentProductRepositoryFamilyOverride implements FamilyOverride {
  _$DeleteCurrentProductRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final DeleteCurrentProductRepository Function(DeleteCurrentProductRepositoryRef ref) create;

  @override
  final DeleteCurrentProductRepositoryFamily overriddenFamily;

  @override
  DeleteCurrentProductRepositoryProvider getProviderOverride(
    covariant DeleteCurrentProductRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// DeleteCurrentProductRepositoryのprovider
///
/// Copied from [deleteCurrentProductRepository].
class DeleteCurrentProductRepositoryProvider extends AutoDisposeProvider<DeleteCurrentProductRepository> {
  /// DeleteCurrentProductRepositoryのprovider
  ///
  /// Copied from [deleteCurrentProductRepository].
  DeleteCurrentProductRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => deleteCurrentProductRepository(
            ref as DeleteCurrentProductRepositoryRef,
            caller: caller,
          ),
          from: deleteCurrentProductRepositoryProvider,
          name: r'deleteCurrentProductRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$deleteCurrentProductRepositoryHash,
          dependencies: DeleteCurrentProductRepositoryFamily._dependencies,
          allTransitiveDependencies: DeleteCurrentProductRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  DeleteCurrentProductRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    DeleteCurrentProductRepository Function(DeleteCurrentProductRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DeleteCurrentProductRepositoryProvider._internal(
        (ref) => create(ref as DeleteCurrentProductRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  AutoDisposeProviderElement<DeleteCurrentProductRepository> createElement() {
    return _DeleteCurrentProductRepositoryProviderElement(this);
  }

  DeleteCurrentProductRepositoryProvider _copyWith(
    DeleteCurrentProductRepository Function(DeleteCurrentProductRepositoryRef ref) create,
  ) {
    return DeleteCurrentProductRepositoryProvider._internal(
      (ref) => create(ref as DeleteCurrentProductRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is DeleteCurrentProductRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DeleteCurrentProductRepositoryRef on AutoDisposeProviderRef<DeleteCurrentProductRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _DeleteCurrentProductRepositoryProviderElement extends AutoDisposeProviderElement<DeleteCurrentProductRepository>
    with DeleteCurrentProductRepositoryRef {
  _DeleteCurrentProductRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as DeleteCurrentProductRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
