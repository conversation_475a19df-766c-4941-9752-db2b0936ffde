import 'dart:developer';

import '../../authentication/domain/app_user.dart';
import '../domain/delivery_product_code.dart';
import '../domain/delivery_slip.dart';
import '../domain/delivery_slip_detail.dart';
import 'delivery_print_repository.dart';
import 'delivery_slip_repository.dart';

/// Fake[DeliverySlipRepository]
class FakeDeliverySlipRepository implements DeliverySlipRepository {
  /// init
  FakeDeliverySlipRepository({required this.caller});

  @override
  final AppUser? caller;

  final _fakeDeliverySlips = List.generate(
    10,
    (index) => DeliverySlip(
      slipNumber: index,
      supplierName: '(fake)コカ・コーラウエスト㈱',
      deliveryDate: DateTime.now(),
      costPriceSum: 100 * double.parse(index.toString()),
      salesPriceSum: 200 * double.parse(index.toString()),
      employeeCode: '(fake)$index',
      employeeName: '(fake)employee$index',
    ),
  );

  final _fakeDeliverySlipDetails = List.generate(
    10,
    (index) => DeliverySlipDetail(
      deliveryDate: DateTime.now(),
      id: index,
      productCode: DeliveryProductCode.parse('499999999999$index'),
      productName: '(fake)productName',
      venderName: '(fake)venderName',
      quantity: double.parse(index.toString()),
      salesPrice: 100 * index,
      salesSum: 200 * index,
    ),
  );

  @override
  Future<List<DeliverySlip>> getSlip({
    required int? venderCode,
    required DateTime deliveryDate,
    required String storeCode,
  }) async {
    log('fake deliverySlips: $_fakeDeliverySlips');
    return Future.delayed(
      Duration.zero,
      () => _fakeDeliverySlips,
    );
  }

  @override
  Future<List<DeliverySlipDetail>> getSlipDetail({
    required int slipNumber,
    required String storeCode,
  }) {
    log('fake deliverySlipDetails $_fakeDeliverySlipDetails');
    return Future.delayed(Duration.zero, () => _fakeDeliverySlipDetails);
  }

  @override
  Future<DeliverySlipPrintID> insertPrintSlipList({
    required List<int> slipNumbers,
    required String storeCode,
  }) async {
    const fakePrintId = '99999';

    log('fake printId: $fakePrintId');

    return Future.delayed(
      Duration.zero,
      () => fakePrintId,
    );
  }

  @override
  Future<void> updateSlipQuantity({
    required double deliveryQuantity,
    required int slipId,
    required String storeCode,
  }) {
    log('fake updateSlipQuantity success');

    return Future.value();
  }
}
