// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'insert_delivery_retail_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$insertDeliveryRetailRepositoryHash() => r'03997f4804c1b8580c9c7027e4bf1d04bcf535df';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [insertDeliveryRetailRepository].
@ProviderFor(insertDeliveryRetailRepository)
const insertDeliveryRetailRepositoryProvider = InsertDeliveryRetailRepositoryFamily();

/// See also [insertDeliveryRetailRepository].
class InsertDeliveryRetailRepositoryFamily extends Family {
  /// See also [insertDeliveryRetailRepository].
  const InsertDeliveryRetailRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'insertDeliveryRetailRepositoryProvider';

  /// See also [insertDeliveryRetailRepository].
  InsertDeliveryRetailRepositoryProvider call({
    required AppUser? caller,
  }) {
    return InsertDeliveryRetailRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  InsertDeliveryRetailRepositoryProvider getProviderOverride(
    covariant InsertDeliveryRetailRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(InsertDeliveryRetailRepository Function(InsertDeliveryRetailRepositoryRef ref) create) {
    return _$InsertDeliveryRetailRepositoryFamilyOverride(this, create);
  }
}

class _$InsertDeliveryRetailRepositoryFamilyOverride implements FamilyOverride {
  _$InsertDeliveryRetailRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final InsertDeliveryRetailRepository Function(InsertDeliveryRetailRepositoryRef ref) create;

  @override
  final InsertDeliveryRetailRepositoryFamily overriddenFamily;

  @override
  InsertDeliveryRetailRepositoryProvider getProviderOverride(
    covariant InsertDeliveryRetailRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// See also [insertDeliveryRetailRepository].
class InsertDeliveryRetailRepositoryProvider extends Provider<InsertDeliveryRetailRepository> {
  /// See also [insertDeliveryRetailRepository].
  InsertDeliveryRetailRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => insertDeliveryRetailRepository(
            ref as InsertDeliveryRetailRepositoryRef,
            caller: caller,
          ),
          from: insertDeliveryRetailRepositoryProvider,
          name: r'insertDeliveryRetailRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$insertDeliveryRetailRepositoryHash,
          dependencies: InsertDeliveryRetailRepositoryFamily._dependencies,
          allTransitiveDependencies: InsertDeliveryRetailRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  InsertDeliveryRetailRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    InsertDeliveryRetailRepository Function(InsertDeliveryRetailRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: InsertDeliveryRetailRepositoryProvider._internal(
        (ref) => create(ref as InsertDeliveryRetailRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  ProviderElement<InsertDeliveryRetailRepository> createElement() {
    return _InsertDeliveryRetailRepositoryProviderElement(this);
  }

  InsertDeliveryRetailRepositoryProvider _copyWith(
    InsertDeliveryRetailRepository Function(InsertDeliveryRetailRepositoryRef ref) create,
  ) {
    return InsertDeliveryRetailRepositoryProvider._internal(
      (ref) => create(ref as InsertDeliveryRetailRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is InsertDeliveryRetailRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin InsertDeliveryRetailRepositoryRef on ProviderRef<InsertDeliveryRetailRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _InsertDeliveryRetailRepositoryProviderElement extends ProviderElement<InsertDeliveryRetailRepository>
    with InsertDeliveryRetailRepositoryRef {
  _InsertDeliveryRetailRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as InsertDeliveryRetailRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
