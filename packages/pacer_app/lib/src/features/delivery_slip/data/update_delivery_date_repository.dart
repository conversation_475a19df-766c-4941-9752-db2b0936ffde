// ignore_for_file: public_member_api_docs

import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart' show Date;
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import 'fake_update_delivery_date_repository.dart';

part 'update_delivery_date_repository.g.dart';

@riverpod
UpdateDeliveryDateRepository updateDeliveryDateRepository(
  UpdateDeliveryDateRepositoryRef ref, {
  required AppUser? caller,
}) =>
    switch (Env.isEnableMock) {
      true => FakeUpdateDeliveryDateRepository(caller: caller),
      false => UpdateDeliveryDateRepository(caller: caller)
    };

class UpdateDeliveryDateRepository {
  UpdateDeliveryDateRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  Future<void> execute({
    required DateTime deliveryDate,
    required String storeCode,
  }) async {
    final uri = Env.getApiBaseUrl();

    final channel = ClientChannel(
      uri.host,
      port: uri.port,
    );

    final stub = DeliveryServiceClient(
      channel,
      options: CallOptions(timeout: const Duration(seconds: 20)),
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = UpdateDeliveryDateRequest(
      storeCode: storeCode,
      deliveryDate: Date(
        year: deliveryDate.year,
        month: deliveryDate.month,
        day: deliveryDate.day,
      ),
    );

    log('UpdateDeliveryDateRequest $request');

    try {
      final response = await stub.updateDeliveryDate(request);
      log('UpdateDeliveryDateResponse $response');
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError() => UnknownException(e.codeName),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
