// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivered_product_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deliveredProductRepositoryHash() => r'298d2665a935d0c6939cdb3076cdda8cf163d428';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// provider
///
/// Copied from [deliveredProductRepository].
@ProviderFor(deliveredProductRepository)
const deliveredProductRepositoryProvider = DeliveredProductRepositoryFamily();

/// provider
///
/// Copied from [deliveredProductRepository].
class DeliveredProductRepositoryFamily extends Family {
  /// provider
  ///
  /// Copied from [deliveredProductRepository].
  const DeliveredProductRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'deliveredProductRepositoryProvider';

  /// provider
  ///
  /// Copied from [deliveredProductRepository].
  DeliveredProductRepositoryProvider call({
    required AppUser? caller,
  }) {
    return DeliveredProductRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  DeliveredProductRepositoryProvider getProviderOverride(
    covariant DeliveredProductRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(DeliveredProductRepository Function(DeliveredProductRepositoryRef ref) create) {
    return _$DeliveredProductRepositoryFamilyOverride(this, create);
  }
}

class _$DeliveredProductRepositoryFamilyOverride implements FamilyOverride {
  _$DeliveredProductRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final DeliveredProductRepository Function(DeliveredProductRepositoryRef ref) create;

  @override
  final DeliveredProductRepositoryFamily overriddenFamily;

  @override
  DeliveredProductRepositoryProvider getProviderOverride(
    covariant DeliveredProductRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// provider
///
/// Copied from [deliveredProductRepository].
class DeliveredProductRepositoryProvider extends Provider<DeliveredProductRepository> {
  /// provider
  ///
  /// Copied from [deliveredProductRepository].
  DeliveredProductRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => deliveredProductRepository(
            ref as DeliveredProductRepositoryRef,
            caller: caller,
          ),
          from: deliveredProductRepositoryProvider,
          name: r'deliveredProductRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$deliveredProductRepositoryHash,
          dependencies: DeliveredProductRepositoryFamily._dependencies,
          allTransitiveDependencies: DeliveredProductRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  DeliveredProductRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    DeliveredProductRepository Function(DeliveredProductRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DeliveredProductRepositoryProvider._internal(
        (ref) => create(ref as DeliveredProductRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  ProviderElement<DeliveredProductRepository> createElement() {
    return _DeliveredProductRepositoryProviderElement(this);
  }

  DeliveredProductRepositoryProvider _copyWith(
    DeliveredProductRepository Function(DeliveredProductRepositoryRef ref) create,
  ) {
    return DeliveredProductRepositoryProvider._internal(
      (ref) => create(ref as DeliveredProductRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is DeliveredProductRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DeliveredProductRepositoryRef on ProviderRef<DeliveredProductRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _DeliveredProductRepositoryProviderElement extends ProviderElement<DeliveredProductRepository>
    with DeliveredProductRepositoryRef {
  _DeliveredProductRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as DeliveredProductRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
