// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vender_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$venderRepositoryHash() => r'55a78357f06fa67a69d0ce48bf1b1cab4b52386e';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// riverpod
///
/// Copied from [venderRepository].
@ProviderFor(venderRepository)
const venderRepositoryProvider = VenderRepositoryFamily();

/// riverpod
///
/// Copied from [venderRepository].
class VenderRepositoryFamily extends Family {
  /// riverpod
  ///
  /// Copied from [venderRepository].
  const VenderRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'venderRepositoryProvider';

  /// riverpod
  ///
  /// Copied from [venderRepository].
  VenderRepositoryProvider call({
    required AppUser? caller,
  }) {
    return VenderRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  VenderRepositoryProvider getProviderOverride(
    covariant VenderRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(VenderRepository Function(VenderRepositoryRef ref) create) {
    return _$VenderRepositoryFamilyOverride(this, create);
  }
}

class _$VenderRepositoryFamilyOverride implements FamilyOverride {
  _$VenderRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final VenderRepository Function(VenderRepositoryRef ref) create;

  @override
  final VenderRepositoryFamily overriddenFamily;

  @override
  VenderRepositoryProvider getProviderOverride(
    covariant VenderRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// riverpod
///
/// Copied from [venderRepository].
class VenderRepositoryProvider extends AutoDisposeProvider<VenderRepository> {
  /// riverpod
  ///
  /// Copied from [venderRepository].
  VenderRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => venderRepository(
            ref as VenderRepositoryRef,
            caller: caller,
          ),
          from: venderRepositoryProvider,
          name: r'venderRepositoryProvider',
          debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$venderRepositoryHash,
          dependencies: VenderRepositoryFamily._dependencies,
          allTransitiveDependencies: VenderRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  VenderRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    VenderRepository Function(VenderRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VenderRepositoryProvider._internal(
        (ref) => create(ref as VenderRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  AutoDisposeProviderElement<VenderRepository> createElement() {
    return _VenderRepositoryProviderElement(this);
  }

  VenderRepositoryProvider _copyWith(
    VenderRepository Function(VenderRepositoryRef ref) create,
  ) {
    return VenderRepositoryProvider._internal(
      (ref) => create(ref as VenderRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is VenderRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin VenderRepositoryRef on AutoDisposeProviderRef<VenderRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _VenderRepositoryProviderElement extends AutoDisposeProviderElement<VenderRepository> with VenderRepositoryRef {
  _VenderRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as VenderRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
