import '../../authentication/domain/app_user.dart';
import '../domain/delivered_product.dart';
import 'delivered_product_repository.dart';

/// [DeliveredProductRepository]のfake repository.
class FakeDeliveredProductRepository implements DeliveredProductRepository {
  /// init.
  FakeDeliveredProductRepository({required this.caller});

  /// 操作を行うユーザー
  @override
  final AppUser? caller;

  @override
  Future<List<DeliveredProduct>> deliveredProduct({required String storeCode}) {
    return Future.delayed(
      const Duration(seconds: 2),
      () => List.generate(
        10,
        DeliveredProduct.fake,
      ),
    );
  }
}
