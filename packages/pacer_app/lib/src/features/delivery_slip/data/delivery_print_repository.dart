import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/print/v1/print.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import 'fake_delivery_print_repository.dart';

part 'delivery_print_repository.g.dart';

/// print APIのリクエストとして渡すID。
/// 納品伝票の印刷、再発行の際に必要。
typedef DeliverySlipPrintID = String;

/// [DeliveryPrintRepository]'s provider
@riverpod
DeliveryPrintRepository deliveryPrintRepository(
  DeliveryPrintRepositoryRef ref, {
  required AppUser? caller,
}) =>
    switch (Env.isEnableMock) {
      true => FakeDeliveryPrintRepository(caller: caller),
      false => DeliveryPrintRepository(caller: caller)
    };

/// 納品伝票で利用する印刷APIに関するRipository
class DeliveryPrintRepository {
  /// init
  DeliveryPrintRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  ClientChannel get _clientChannel {
    final uri = Env.getApiBaseUrl();

    return ClientChannel(uri.host, port: uri.port);
  }

  PrintServiceClient _apiClient({required int timeoutSeconds}) {
    return PrintServiceClient(
      _clientChannel,
      options: CallOptions(timeout: Duration(seconds: timeoutSeconds)),
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
  }

  /// printerシステムのチェック処理
  Future<void> checkConnectionWithPrinter({required String storeCode}) async {
    try {
      final request = CheckConnectionWithPrinterRequest(storeCode: storeCode);

      final response = await _apiClient(timeoutSeconds: 60).checkConnectionWithPrinter(request);

      if (response.code != '000') throw UnknownException(response.message);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(_handleException(e), stack);
    } finally {
      await _clientChannel.shutdown();
    }
  }

  /// 実際のプリント処理命令
  Future<void> print({
    required DeliverySlipPrintID printId,
    required String storeCode,
  }) async {
    try {
      final request = RequestPrintRequest(
        printId: printId,
        storeCode: storeCode,
        systemId: RequestPrintRequest_SystemId.SYSTEM_ID_DELIVERY_SLIP,
        varietyCode: RequestPrintRequest_VarietyCode.VARIETY_CODE_DELIVERY_SLIP,
      );

      final response = await _apiClient(timeoutSeconds: 300).requestPrint(request);

      if (response.code != '000') throw UnknownException(response.message);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(_handleException(e), stack);
    } finally {
      await _clientChannel.shutdown();
    }
  }

  AppException _handleException(Exception e) {
    return switch (e) {
      AppException() => e,
      GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
      GrpcError(code: StatusCode.internal) => InternalException(),
      GrpcError() => UnknownException(e.message ?? 'unknown expention'),
      _ => UnknownException(e.toString()),
    };
  }
}
