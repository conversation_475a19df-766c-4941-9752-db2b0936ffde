import '../../authentication/domain/app_user.dart';
import '../domain/delivery_vender.dart';
import 'vender_repository.dart';

/// Fake[VenderRepository]
class FakeVenderRepository implements VenderRepository {
  /// init
  FakeVenderRepository({required this.caller});

  @override
  final AppUser? caller;

  @override
  Future<DeliveryVender> getVenderByCode({
    required String venderCode,
    required String storeCode,
  }) async {
    return Future.delayed(
      Duration.zero,
      () => _fakeVender,
    );
  }

  @override
  Future<DeliveryVender> getVenderByJan({
    required String productCode,
    required String storeCode,
  }) {
    return Future.delayed(
      Duration.zero,
      () => _fakeVender,
    );
  }
}

final _fakeVender = DeliveryVender(
  venderCode: 377,
  venderName: '(fake)コカ・コーラウエスト㈱',
  deliveryDate: DateTime.now(),
);
