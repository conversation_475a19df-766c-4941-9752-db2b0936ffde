import 'dart:async';
import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/delivery_vender.dart';
import 'fake_vender_repository.dart';

part 'vender_repository.g.dart';

/// riverpod
@riverpod
VenderRepository venderRepository(
  VenderRepositoryRef ref, {
  required AppUser? caller,
}) =>
    switch (Env.isEnableMock) {
      true => FakeVenderRepository(caller: caller),
      false => VenderRepository(caller: caller)
    };

/// ベンダー情報を取得する処理を実装
class VenderRepository {
  /// init
  VenderRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  final _options = CallOptions(timeout: const Duration(seconds: 20));

  ClientChannel get _clientChannel {
    final uri = Env.getApiBaseUrl();

    return ClientChannel(uri.host, port: uri.port);
  }

  /// ベンダー情報を取得する。
  Future<DeliveryVender> getVenderByCode({
    required String storeCode,
    required String venderCode,
  }) async {
    try {
      final stub = DeliveryServiceClient(
        _clientChannel,
        options: _options,
        interceptors: [ShiniseInterceptor(caller: caller)],
      );

      final req = GetVenderInfoRequest(
        storeCode: storeCode,
        venderCode: venderCode,
      );

      log('GetVenderInfoRequest $req');

      final response = await stub.getVenderInfo(req);

      log('GetVenderInfoResponse $response');

      return DeliveryVender.fromApi(response);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError(code: StatusCode.internal) => InternalException(),
          GrpcError() => UnknownException(e.message ?? 'unknown expention'),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await _clientChannel.shutdown();
    }
  }

  /// JANからベンダー情報を取得する
  Future<DeliveryVender> getVenderByJan({
    required String productCode,
    required String storeCode,
  }) async {
    try {
      final stub = DeliveryServiceClient(
        _clientChannel,
        options: _options,
        interceptors: [ShiniseInterceptor(caller: caller)],
      );

      final req = GetJanVenderInfoRequest(
        storeCode: storeCode,
        productCode: productCode,
      );

      log('GetJanVenderInfoRequest $req');

      final response = await stub.getJanVenderInfo(req);

      log('GetJanVenderInfoResponse $response');

      return DeliveryVender.fromApiByJan(response);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError(code: StatusCode.internal) => InternalException(),
          GrpcError() => UnknownException(e.message ?? 'unknown expention'),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await _clientChannel.shutdown();
    }
  }
}
