import '../../authentication/domain/app_user.dart';
import 'delete_current_product_repository.dart';

/// [DeleteCurrentProductRepository]のfake repository.
class FakeDeleteCurrentProductRepository implements DeleteCurrentProductRepository {
  /// init.
  FakeDeleteCurrentProductRepository({required this.caller});

  /// 操作を行うユーザー
  @override
  final AppUser? caller;

  @override
  Future<void> execute({
    required String? id,
    required String storeCode,
  }) {
    return Future.delayed(const Duration(seconds: 1));
  }
}
