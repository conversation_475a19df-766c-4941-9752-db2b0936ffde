// ignore_for_file: public_member_api_docs

import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart' show Date;
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import 'delivery_print_repository.dart';
import 'fake_insert_delivery_retail_repository.dart';

part 'insert_delivery_retail_repository.g.dart';

@Riverpod(keepAlive: true)
InsertDeliveryRetailRepository insertDeliveryRetailRepository(
  InsertDeliveryRetailRepositoryRef ref, {
  required AppUser? caller,
}) =>
    switch (Env.isEnableMock) {
      true => FakeInsertDeliveryRetailRepository(caller: caller),
      false => InsertDeliveryRetailRepository(caller: caller)
    };

class InsertDeliveryRetailRepository {
  InsertDeliveryRetailRepository({
    required this.caller,
  });

  /// 操作を行うユーザー
  final AppUser? caller;

  /// 印刷データ作成、納品明細書データを投入。
  /// 印刷APIに必要なprintIdを返却する。
  Future<DeliverySlipPrintID> execute({
    required DateTime deliveryDate,
    required String storeCode,
  }) async {
    final uri = Env.getApiBaseUrl();

    final channel = ClientChannel(uri.host, port: uri.port);

    final stub = DeliveryServiceClient(
      channel,
      options: CallOptions(timeout: const Duration(seconds: 20)),
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    final request = InsertDeliveryRetailRequest(
      storeCode: storeCode,
      deliveryDate: Date(
        year: deliveryDate.year,
        month: deliveryDate.month,
        day: deliveryDate.day,
      ),
    );

    log('InsertDeliveryRetailRequest $request');

    try {
      final response = await stub.insertDeliveryRetail(request);
      log('InsertDeliveryRetailResponse $response');
      if (response.code != '000') throw UnknownException(response.message);
      return response.printId.toString();
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError() => UnknownException(e.codeName),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
