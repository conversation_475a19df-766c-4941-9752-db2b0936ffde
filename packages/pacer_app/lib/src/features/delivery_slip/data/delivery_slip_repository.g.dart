// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_slip_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deliverySlipRepositoryHash() => r'02634e3f1777e022a52eba95a0bab8a5fe906862';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// provider
///
/// Copied from [deliverySlipRepository].
@ProviderFor(deliverySlipRepository)
const deliverySlipRepositoryProvider = DeliverySlipRepositoryFamily();

/// provider
///
/// Copied from [deliverySlipRepository].
class DeliverySlipRepositoryFamily extends Family {
  /// provider
  ///
  /// Copied from [deliverySlipRepository].
  const DeliverySlipRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'deliverySlipRepositoryProvider';

  /// provider
  ///
  /// Copied from [deliverySlipRepository].
  DeliverySlipRepositoryProvider call({
    required AppUser? caller,
  }) {
    return DeliverySlipRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  DeliverySlipRepositoryProvider getProviderOverride(
    covariant DeliverySlipRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(DeliverySlipRepository Function(DeliverySlipRepositoryRef ref) create) {
    return _$DeliverySlipRepositoryFamilyOverride(this, create);
  }
}

class _$DeliverySlipRepositoryFamilyOverride implements FamilyOverride {
  _$DeliverySlipRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final DeliverySlipRepository Function(DeliverySlipRepositoryRef ref) create;

  @override
  final DeliverySlipRepositoryFamily overriddenFamily;

  @override
  DeliverySlipRepositoryProvider getProviderOverride(
    covariant DeliverySlipRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// provider
///
/// Copied from [deliverySlipRepository].
class DeliverySlipRepositoryProvider extends AutoDisposeProvider<DeliverySlipRepository> {
  /// provider
  ///
  /// Copied from [deliverySlipRepository].
  DeliverySlipRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => deliverySlipRepository(
            ref as DeliverySlipRepositoryRef,
            caller: caller,
          ),
          from: deliverySlipRepositoryProvider,
          name: r'deliverySlipRepositoryProvider',
          debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deliverySlipRepositoryHash,
          dependencies: DeliverySlipRepositoryFamily._dependencies,
          allTransitiveDependencies: DeliverySlipRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  DeliverySlipRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    DeliverySlipRepository Function(DeliverySlipRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DeliverySlipRepositoryProvider._internal(
        (ref) => create(ref as DeliverySlipRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  AutoDisposeProviderElement<DeliverySlipRepository> createElement() {
    return _DeliverySlipRepositoryProviderElement(this);
  }

  DeliverySlipRepositoryProvider _copyWith(
    DeliverySlipRepository Function(DeliverySlipRepositoryRef ref) create,
  ) {
    return DeliverySlipRepositoryProvider._internal(
      (ref) => create(ref as DeliverySlipRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is DeliverySlipRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DeliverySlipRepositoryRef on AutoDisposeProviderRef<DeliverySlipRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _DeliverySlipRepositoryProviderElement extends AutoDisposeProviderElement<DeliverySlipRepository>
    with DeliverySlipRepositoryRef {
  _DeliverySlipRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as DeliverySlipRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
