import '../../authentication/domain/app_user.dart';
import 'update_delivery_quantity_repository.dart';

/// [UpdateDeliveryQuantityRepository]のfake repository.
class FakeUpdateDeliveryQuantityRepository implements UpdateDeliveryQuantityRepository {
  /// init.
  FakeUpdateDeliveryQuantityRepository({required this.caller});

  /// 操作を行うユーザー
  @override
  final AppUser? caller;

  @override
  Future<void> execute({
    int? id,
    double? deliveryQuantity,
    required String storeCode,
  }) {
    return Future.delayed(const Duration(seconds: 1));
  }
}
