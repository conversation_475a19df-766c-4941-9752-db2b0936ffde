import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart' hide DateTime;
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/delivery_slip.dart';
import '../domain/delivery_slip_detail.dart';
import 'delivery_print_repository.dart';
import 'fake_delivery_slip_repository.dart';

part 'delivery_slip_repository.g.dart';

/// 納品伝票(再発行) 伝票一覧に関する処理
class DeliverySlipRepository {
  /// init
  DeliverySlipRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  ClientChannel get _clientChannel {
    final uri = Env.getApiBaseUrl();

    return ClientChannel(uri.host, port: uri.port);
  }

  DeliveryServiceClient get _apiClient {
    return DeliveryServiceClient(
      _clientChannel,
      options: CallOptions(timeout: const Duration(seconds: 20)),
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
  }

  /// 納品伝票情報を取得する。
  Future<List<DeliverySlip>> getSlip({
    required int? venderCode,
    required DateTime deliveryDate,
    required String storeCode,
  }) async {
    try {
      final req = GetSlipInfoRequest(
        storeCode: storeCode,
        venderCode: venderCode.toString(),
        deliveryDate: Date(
          year: deliveryDate.year,
          month: deliveryDate.month,
          day: deliveryDate.day,
        ),
      );

      log('GetSlipInfoRequest $req');

      final response = await _apiClient.getSlipInfo(req);

      log('GetSlipInfoRequest $response');

      return response.slipInfo.map(DeliverySlip.fromApi).toList();
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(_handleException, stack);
    } finally {
      await _clientChannel.shutdown();
    }
  }

  /// 納品伝票の詳細情報を取得する。
  Future<List<DeliverySlipDetail>> getSlipDetail({
    required int slipNumber,
    required String storeCode,
  }) async {
    try {
      final req = GetSlipDataRequest(
        slipNo: slipNumber,
        storeCode: storeCode,
      );

      log('GetSlipDataRequest $req');

      final response = await _apiClient.getSlipData(req);

      log('GetSlipDataResponse $response');

      return response.deliveryInfo.map(DeliverySlipDetail.fromApi).toList();
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(_handleException, stack);
    } finally {
      await _clientChannel.shutdown();
    }
  }

  /// 伝票の再発行データを投入する。
  /// 印刷APIに必要なprintIdを返却する。
  Future<DeliverySlipPrintID> insertPrintSlipList({
    required List<int> slipNumbers,
    required String storeCode,
  }) async {
    try {
      final req = InsertPrintSlipListRequest(
        storeCode: storeCode,
        slipNo: slipNumbers,
      );

      log('InsertPrintSlipListRequest $req');

      final response = await _apiClient.insertPrintSlipList(req);

      log('InsertPrintSlipListRequest $response');

      return response.printId.toString();
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(_handleException, stack);
    } finally {
      await _clientChannel.shutdown();
    }
  }

  /// 再発行する納品伝票の納品数量を更新する
  Future<void> updateSlipQuantity({
    required double deliveryQuantity,
    required int slipId,
    required String storeCode,
  }) async {
    try {
      final req = UpdateSlipDeliveryQtyRequest(
        deliveryQty: deliveryQuantity,
        id: slipId,
        storeCode: storeCode,
      );

      log('UpdateSlipDeliveryQtyRequest $req');

      final response = await _apiClient.updateSlipDeliveryQty(req);

      log('UpdateSlipDeliveryQtyResponse $response');
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(_handleException, stack);
    } finally {
      await _clientChannel.shutdown();
    }
  }
}

/// provider
@riverpod
DeliverySlipRepository deliverySlipRepository(
  DeliverySlipRepositoryRef ref, {
  required AppUser? caller,
}) =>
    switch (Env.isEnableMock) {
      true => FakeDeliverySlipRepository(caller: caller),
      false => DeliverySlipRepository(caller: caller)
    };

AppException _handleException(Exception e) {
  return switch (e) {
    AppException() => e,
    GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
    GrpcError(code: StatusCode.internal) => InternalException(),
    GrpcError() => UnknownException(e.message ?? 'unknown expention'),
    _ => UnknownException(e.toString()),
  };
}
