import '../../authentication/domain/app_user.dart';
import 'update_delivery_date_repository.dart';

/// [UpdateDeliveryDateRepository]のfake repository.
class FakeUpdateDeliveryDateRepository implements UpdateDeliveryDateRepository {
  /// init.
  FakeUpdateDeliveryDateRepository({required this.caller});

  /// 操作を行うユーザー
  @override
  final AppUser? caller;

  @override
  Future<void> execute({
    required DateTime deliveryDate,
    required String storeCode,
  }) async {
    return Future.delayed(const Duration(seconds: 1));
  }
}
