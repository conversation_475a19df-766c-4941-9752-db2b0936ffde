// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'system_date_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$systemDateRepositoryHash() => r'adac16d6f2a2eada9892094018881a31c4910c01';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [systemDateRepository].
@ProviderFor(systemDateRepository)
const systemDateRepositoryProvider = SystemDateRepositoryFamily();

/// See also [systemDateRepository].
class SystemDateRepositoryFamily extends Family {
  /// See also [systemDateRepository].
  const SystemDateRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'systemDateRepositoryProvider';

  /// See also [systemDateRepository].
  SystemDateRepositoryProvider call({
    required AppUser? caller,
  }) {
    return SystemDateRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  SystemDateRepositoryProvider getProviderOverride(
    covariant SystemDateRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(SystemDateRepository Function(SystemDateRepositoryRef ref) create) {
    return _$SystemDateRepositoryFamilyOverride(this, create);
  }
}

class _$SystemDateRepositoryFamilyOverride implements FamilyOverride {
  _$SystemDateRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final SystemDateRepository Function(SystemDateRepositoryRef ref) create;

  @override
  final SystemDateRepositoryFamily overriddenFamily;

  @override
  SystemDateRepositoryProvider getProviderOverride(
    covariant SystemDateRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// See also [systemDateRepository].
class SystemDateRepositoryProvider extends AutoDisposeProvider<SystemDateRepository> {
  /// See also [systemDateRepository].
  SystemDateRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => systemDateRepository(
            ref as SystemDateRepositoryRef,
            caller: caller,
          ),
          from: systemDateRepositoryProvider,
          name: r'systemDateRepositoryProvider',
          debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$systemDateRepositoryHash,
          dependencies: SystemDateRepositoryFamily._dependencies,
          allTransitiveDependencies: SystemDateRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  SystemDateRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    SystemDateRepository Function(SystemDateRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SystemDateRepositoryProvider._internal(
        (ref) => create(ref as SystemDateRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  AutoDisposeProviderElement<SystemDateRepository> createElement() {
    return _SystemDateRepositoryProviderElement(this);
  }

  SystemDateRepositoryProvider _copyWith(
    SystemDateRepository Function(SystemDateRepositoryRef ref) create,
  ) {
    return SystemDateRepositoryProvider._internal(
      (ref) => create(ref as SystemDateRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is SystemDateRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SystemDateRepositoryRef on AutoDisposeProviderRef<SystemDateRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _SystemDateRepositoryProviderElement extends AutoDisposeProviderElement<SystemDateRepository>
    with SystemDateRepositoryRef {
  _SystemDateRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as SystemDateRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
