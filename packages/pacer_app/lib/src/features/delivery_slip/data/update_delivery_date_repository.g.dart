// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_delivery_date_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$updateDeliveryDateRepositoryHash() => r'8a86c6e1555ed2c966e50538ef1385e748d2f1ec';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [updateDeliveryDateRepository].
@ProviderFor(updateDeliveryDateRepository)
const updateDeliveryDateRepositoryProvider = UpdateDeliveryDateRepositoryFamily();

/// See also [updateDeliveryDateRepository].
class UpdateDeliveryDateRepositoryFamily extends Family {
  /// See also [updateDeliveryDateRepository].
  const UpdateDeliveryDateRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'updateDeliveryDateRepositoryProvider';

  /// See also [updateDeliveryDateRepository].
  UpdateDeliveryDateRepositoryProvider call({
    required AppUser? caller,
  }) {
    return UpdateDeliveryDateRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  UpdateDeliveryDateRepositoryProvider getProviderOverride(
    covariant UpdateDeliveryDateRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(UpdateDeliveryDateRepository Function(UpdateDeliveryDateRepositoryRef ref) create) {
    return _$UpdateDeliveryDateRepositoryFamilyOverride(this, create);
  }
}

class _$UpdateDeliveryDateRepositoryFamilyOverride implements FamilyOverride {
  _$UpdateDeliveryDateRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final UpdateDeliveryDateRepository Function(UpdateDeliveryDateRepositoryRef ref) create;

  @override
  final UpdateDeliveryDateRepositoryFamily overriddenFamily;

  @override
  UpdateDeliveryDateRepositoryProvider getProviderOverride(
    covariant UpdateDeliveryDateRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// See also [updateDeliveryDateRepository].
class UpdateDeliveryDateRepositoryProvider extends AutoDisposeProvider<UpdateDeliveryDateRepository> {
  /// See also [updateDeliveryDateRepository].
  UpdateDeliveryDateRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => updateDeliveryDateRepository(
            ref as UpdateDeliveryDateRepositoryRef,
            caller: caller,
          ),
          from: updateDeliveryDateRepositoryProvider,
          name: r'updateDeliveryDateRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$updateDeliveryDateRepositoryHash,
          dependencies: UpdateDeliveryDateRepositoryFamily._dependencies,
          allTransitiveDependencies: UpdateDeliveryDateRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  UpdateDeliveryDateRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    UpdateDeliveryDateRepository Function(UpdateDeliveryDateRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UpdateDeliveryDateRepositoryProvider._internal(
        (ref) => create(ref as UpdateDeliveryDateRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  AutoDisposeProviderElement<UpdateDeliveryDateRepository> createElement() {
    return _UpdateDeliveryDateRepositoryProviderElement(this);
  }

  UpdateDeliveryDateRepositoryProvider _copyWith(
    UpdateDeliveryDateRepository Function(UpdateDeliveryDateRepositoryRef ref) create,
  ) {
    return UpdateDeliveryDateRepositoryProvider._internal(
      (ref) => create(ref as UpdateDeliveryDateRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is UpdateDeliveryDateRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin UpdateDeliveryDateRepositoryRef on AutoDisposeProviderRef<UpdateDeliveryDateRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _UpdateDeliveryDateRepositoryProviderElement extends AutoDisposeProviderElement<UpdateDeliveryDateRepository>
    with UpdateDeliveryDateRepositoryRef {
  _UpdateDeliveryDateRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as UpdateDeliveryDateRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
