// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_product_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deliveryProductRepositoryHash() => r'f6abafcd13262331878a780549f677ea606ed559';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// riverpod
///
/// Copied from [deliveryProductRepository].
@ProviderFor(deliveryProductRepository)
const deliveryProductRepositoryProvider = DeliveryProductRepositoryFamily();

/// riverpod
///
/// Copied from [deliveryProductRepository].
class DeliveryProductRepositoryFamily extends Family {
  /// riverpod
  ///
  /// Copied from [deliveryProductRepository].
  const DeliveryProductRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'deliveryProductRepositoryProvider';

  /// riverpod
  ///
  /// Copied from [deliveryProductRepository].
  DeliveryProductRepositoryProvider call({
    required AppUser? caller,
  }) {
    return DeliveryProductRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  DeliveryProductRepositoryProvider getProviderOverride(
    covariant DeliveryProductRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(DeliveryProductRepository Function(DeliveryProductRepositoryRef ref) create) {
    return _$DeliveryProductRepositoryFamilyOverride(this, create);
  }
}

class _$DeliveryProductRepositoryFamilyOverride implements FamilyOverride {
  _$DeliveryProductRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final DeliveryProductRepository Function(DeliveryProductRepositoryRef ref) create;

  @override
  final DeliveryProductRepositoryFamily overriddenFamily;

  @override
  DeliveryProductRepositoryProvider getProviderOverride(
    covariant DeliveryProductRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// riverpod
///
/// Copied from [deliveryProductRepository].
class DeliveryProductRepositoryProvider extends AutoDisposeProvider<DeliveryProductRepository> {
  /// riverpod
  ///
  /// Copied from [deliveryProductRepository].
  DeliveryProductRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => deliveryProductRepository(
            ref as DeliveryProductRepositoryRef,
            caller: caller,
          ),
          from: deliveryProductRepositoryProvider,
          name: r'deliveryProductRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$deliveryProductRepositoryHash,
          dependencies: DeliveryProductRepositoryFamily._dependencies,
          allTransitiveDependencies: DeliveryProductRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  DeliveryProductRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    DeliveryProductRepository Function(DeliveryProductRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DeliveryProductRepositoryProvider._internal(
        (ref) => create(ref as DeliveryProductRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  AutoDisposeProviderElement<DeliveryProductRepository> createElement() {
    return _DeliveryProductRepositoryProviderElement(this);
  }

  DeliveryProductRepositoryProvider _copyWith(
    DeliveryProductRepository Function(DeliveryProductRepositoryRef ref) create,
  ) {
    return DeliveryProductRepositoryProvider._internal(
      (ref) => create(ref as DeliveryProductRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is DeliveryProductRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DeliveryProductRepositoryRef on AutoDisposeProviderRef<DeliveryProductRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _DeliveryProductRepositoryProviderElement extends AutoDisposeProviderElement<DeliveryProductRepository>
    with DeliveryProductRepositoryRef {
  _DeliveryProductRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as DeliveryProductRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
