// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_print_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deliveryPrintRepositoryHash() => r'0f94cfef2f397b7497d998857d1c5cc12c6a51c2';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// [DeliveryPrintRepository]'s provider
///
/// Copied from [deliveryPrintRepository].
@ProviderFor(deliveryPrintRepository)
const deliveryPrintRepositoryProvider = DeliveryPrintRepositoryFamily();

/// [DeliveryPrintRepository]'s provider
///
/// Copied from [deliveryPrintRepository].
class DeliveryPrintRepositoryFamily extends Family {
  /// [DeliveryPrintRepository]'s provider
  ///
  /// Copied from [deliveryPrintRepository].
  const DeliveryPrintRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'deliveryPrintRepositoryProvider';

  /// [DeliveryPrintRepository]'s provider
  ///
  /// Copied from [deliveryPrintRepository].
  DeliveryPrintRepositoryProvider call({
    required AppUser? caller,
  }) {
    return DeliveryPrintRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  DeliveryPrintRepositoryProvider getProviderOverride(
    covariant DeliveryPrintRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(DeliveryPrintRepository Function(DeliveryPrintRepositoryRef ref) create) {
    return _$DeliveryPrintRepositoryFamilyOverride(this, create);
  }
}

class _$DeliveryPrintRepositoryFamilyOverride implements FamilyOverride {
  _$DeliveryPrintRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final DeliveryPrintRepository Function(DeliveryPrintRepositoryRef ref) create;

  @override
  final DeliveryPrintRepositoryFamily overriddenFamily;

  @override
  DeliveryPrintRepositoryProvider getProviderOverride(
    covariant DeliveryPrintRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// [DeliveryPrintRepository]'s provider
///
/// Copied from [deliveryPrintRepository].
class DeliveryPrintRepositoryProvider extends AutoDisposeProvider<DeliveryPrintRepository> {
  /// [DeliveryPrintRepository]'s provider
  ///
  /// Copied from [deliveryPrintRepository].
  DeliveryPrintRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => deliveryPrintRepository(
            ref as DeliveryPrintRepositoryRef,
            caller: caller,
          ),
          from: deliveryPrintRepositoryProvider,
          name: r'deliveryPrintRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$deliveryPrintRepositoryHash,
          dependencies: DeliveryPrintRepositoryFamily._dependencies,
          allTransitiveDependencies: DeliveryPrintRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  DeliveryPrintRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    DeliveryPrintRepository Function(DeliveryPrintRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DeliveryPrintRepositoryProvider._internal(
        (ref) => create(ref as DeliveryPrintRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  AutoDisposeProviderElement<DeliveryPrintRepository> createElement() {
    return _DeliveryPrintRepositoryProviderElement(this);
  }

  DeliveryPrintRepositoryProvider _copyWith(
    DeliveryPrintRepository Function(DeliveryPrintRepositoryRef ref) create,
  ) {
    return DeliveryPrintRepositoryProvider._internal(
      (ref) => create(ref as DeliveryPrintRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is DeliveryPrintRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DeliveryPrintRepositoryRef on AutoDisposeProviderRef<DeliveryPrintRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _DeliveryPrintRepositoryProviderElement extends AutoDisposeProviderElement<DeliveryPrintRepository>
    with DeliveryPrintRepositoryRef {
  _DeliveryPrintRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as DeliveryPrintRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
