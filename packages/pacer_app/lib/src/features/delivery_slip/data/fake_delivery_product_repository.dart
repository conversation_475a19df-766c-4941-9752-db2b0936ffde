import '../../authentication/domain/app_user.dart';
import '../domain/delivery_product.dart';
import 'delivery_product_repository.dart';

/// [DeliveryProductRepository]のfake repository.
class FakeDeliveryProductRepository implements DeliveryProductRepository {
  /// init.
  FakeDeliveryProductRepository({required this.caller});

  /// 操作を行うユーザー
  @override
  final AppUser? caller;

  @override
  Future<DeliveryProduct> searchItem({
    required String productCode,
    required String storeCode,
  }) {
    return Future.delayed(
      const Duration(seconds: 1),
      DeliveryProduct.mock,
    );
  }
}
