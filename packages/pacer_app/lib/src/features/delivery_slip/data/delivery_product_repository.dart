import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/delivery_product.dart';
import 'fake_delivery_product_repository.dart';

part 'delivery_product_repository.g.dart';

/// riverpod
@riverpod
DeliveryProductRepository deliveryProductRepository(
  DeliveryProductRepositoryRef ref, {
  required AppUser? caller,
}) =>
    switch (Env.isEnableMock) {
      true => FakeDeliveryProductRepository(caller: caller),
      false => DeliveryProductRepository(caller: caller)
    };

/// 納品する予定の商品情報を検索する処理。
class DeliveryProductRepository {
  /// init
  DeliveryProductRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  /// 商品情報を取得する
  Future<DeliveryProduct> searchItem({
    required String productCode,
    required String storeCode,
  }) async {
    final uri = Env.getApiBaseUrl();

    final channel = ClientChannel(uri.host, port: uri.port);

    try {
      final stub = DeliveryServiceClient(
        channel,
        options: CallOptions(timeout: const Duration(seconds: 20)),
        interceptors: [ShiniseInterceptor(caller: caller)],
      );

      final req = GetProductInfoRequest(
        storeCode: storeCode,
        productCode: productCode,
      );

      log('GetProductInfoRequest $req');

      final response = await stub.getProductInfo(req);

      log('GetProductInfoResponse $response');

      return DeliveryProduct.fromApi(response);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.notFound) => ProductNotFoundException(),
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError(code: StatusCode.invalidArgument) => WrongProductCodeException(),
          GrpcError(code: StatusCode.internal) => InternalException(),
          GrpcError() => UnknownException(e.message ?? 'unknown expention'),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
