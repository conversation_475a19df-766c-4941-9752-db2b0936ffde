import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/delivered_product.dart';
import 'fake_delivered_product_repository.dart';

part 'delivered_product_repository.g.dart';

/// provider
@Riverpod(keepAlive: true)
DeliveredProductRepository deliveredProductRepository(
  DeliveredProductRepositoryRef ref, {
  required AppUser? caller,
}) =>
    switch (Env.isEnableMock) {
      true => FakeDeliveredProductRepository(caller: caller),
      false => DeliveredProductRepository(caller: caller)
    };

/// 納品（ユーザがJANをスキャンまたは入力し、納品数量を入力後に登録）した商品の情報を取得する。
class DeliveredProductRepository {
  /// init
  DeliveredProductRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  final _uri = Env.getApiBaseUrl();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));

  /// 納品した商品の一覧情報を取得する。
  Future<List<DeliveredProduct>> deliveredProduct({
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final callOpt = _callOptions;

    final stub = DeliveryServiceClient(
      channel,
      options: callOpt,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final req = GetDeliveryDataRequest(storeCode: storeCode);
    log('納品情報 $req');

    try {
      final response = await stub.getDeliveryData(req);
      log('納品情報レスポンス $response');

      return response.deliveryInfo.map(DeliveredProduct.fromResponse).toList();
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.notFound) => ProductNotFoundException(),
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError(code: StatusCode.invalidArgument) => WrongProductCodeException(),
          GrpcError() => UnknownException(e.codeName),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
