import '../../authentication/domain/app_user.dart';
import 'delivery_print_repository.dart';

/// 納品伝票で利用する印刷APIに関するRipository
class FakeDeliveryPrintRepository implements DeliveryPrintRepository {
  /// init
  FakeDeliveryPrintRepository({required this.caller});

  /// 操作を行うユーザー
  @override
  final AppUser? caller;

  /// printerシステムのチェック処理
  @override
  Future<void> checkConnectionWithPrinter({required String storeCode}) async {
    return Future.delayed(const Duration(seconds: 1));
  }

  /// 実際のプリント処理命令
  @override
  Future<void> print({
    required String printId,
    required String storeCode,
  }) async {
    return Future.delayed(const Duration(seconds: 1));
  }
}
