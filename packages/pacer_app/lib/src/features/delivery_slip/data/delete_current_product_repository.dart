import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import 'fake_delete_current_product_repository.dart';

part 'delete_current_product_repository.g.dart';

/// DeleteCurrentProductRepositoryのprovider
@riverpod
DeleteCurrentProductRepository deleteCurrentProductRepository(
  DeleteCurrentProductRepositoryRef ref, {
  required AppUser? caller,
}) =>
    switch (Env.isEnableMock) {
      true => FakeDeleteCurrentProductRepository(caller: caller),
      false => DeleteCurrentProductRepository(caller: caller)
    };

/// 納品登録済、かつ印刷前の商品を削除する処理。
class DeleteCurrentProductRepository {
  /// init
  DeleteCurrentProductRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  /// 納品登録済、かつ印刷前の商品を削除する処理を実行する。
  Future<void> execute({required String? id, required String storeCode}) async {
    final uri = Env.getApiBaseUrl();

    final channel = ClientChannel(
      uri.host,
      port: uri.port,
    );

    final stub = DeliveryServiceClient(
      channel,
      options: CallOptions(timeout: const Duration(seconds: 20)),
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = DeleteCurrentProductRequest(
      id: id,
      storeCode: storeCode,
    );

    log('納品商品削除リクエスト $request');

    try {
      final response = await stub.deleteCurrentProduct(request);

      log('納品商品削除レスポンス $response');

      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError() => UnknownException(e.codeName),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
