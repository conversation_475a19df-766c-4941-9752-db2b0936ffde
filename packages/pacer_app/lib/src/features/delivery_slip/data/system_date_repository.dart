// ignore_for_file: public_member_api_docs

import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';
import 'package:shinise_core_client/delivery/v1/v1.dart';
import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import 'fake_system_date_repository.dart';

part 'system_date_repository.g.dart';

@riverpod
SystemDateRepository systemDateRepository(
  SystemDateRepositoryRef ref, {
  required AppUser? caller,
}) =>
    switch (Env.isEnableMock) {
      true => FakeSystemDateRepository(caller: caller),
      false => SystemDateRepository(caller: caller)
    };

class SystemDateRepository {
  SystemDateRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  /// システム時間を取得する
  Future<DateTime> systemDate({required String storeCode}) async {
    final uri = Env.getApiBaseUrl();
    final channel = ClientChannel(uri.host, port: uri.port);
    final stub = DeliveryServiceClient(
      channel,
      options: CallOptions(timeout: const Duration(seconds: 20)),
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    log('システム時間取得(delivery)');

    try {
      final request = GetSystemDateRequest(storeCode: storeCode);
      final response = await stub.getSystemDate(request);

      log('システム時間取得(delivery)レスポンス $response');

      return DateFormat('yyyy/MM/dd').parse(response.dateCode);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError() => UnknownException(e.codeName),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
