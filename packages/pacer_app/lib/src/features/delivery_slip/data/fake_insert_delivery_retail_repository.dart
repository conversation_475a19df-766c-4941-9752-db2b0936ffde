import '../../authentication/domain/app_user.dart';
import 'insert_delivery_retail_repository.dart';

/// [InsertDeliveryRetailRepository]のfake repository.
class FakeInsertDeliveryRetailRepository implements InsertDeliveryRetailRepository {
  /// init.
  FakeInsertDeliveryRetailRepository({required this.caller});

  /// 操作を行うユーザー
  @override
  final AppUser? caller;

  @override
  Future<String> execute({
    required DateTime deliveryDate,
    required String storeCode,
  }) {
    return Future.delayed(const Duration(seconds: 1), () {
      return '9999';
    });
  }
}
