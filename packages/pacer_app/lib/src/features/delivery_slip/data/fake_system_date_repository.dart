import '../../authentication/domain/app_user.dart';

import 'system_date_repository.dart';

/// [SystemDateRepository]のfake repository.
class FakeSystemDateRepository implements SystemDateRepository {
  /// init.
  FakeSystemDateRepository({required this.caller});

  /// 操作を行うユーザー
  @override
  final AppUser? caller;

  @override
  Future<DateTime> systemDate({required String storeCode}) {
    return Future.delayed(
      const Duration(seconds: 1),
      DateTime.now,
    );
  }
}
