// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'insert_current_product_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$insertCurrentProductRepositoryHash() => r'17f6faa4dd8063f0587cb3e753b491575fc27458';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [insertCurrentProductRepository].
@ProviderFor(insertCurrentProductRepository)
const insertCurrentProductRepositoryProvider = InsertCurrentProductRepositoryFamily();

/// See also [insertCurrentProductRepository].
class InsertCurrentProductRepositoryFamily extends Family {
  /// See also [insertCurrentProductRepository].
  const InsertCurrentProductRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'insertCurrentProductRepositoryProvider';

  /// See also [insertCurrentProductRepository].
  InsertCurrentProductRepositoryProvider call({
    required AppUser? caller,
  }) {
    return InsertCurrentProductRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  InsertCurrentProductRepositoryProvider getProviderOverride(
    covariant InsertCurrentProductRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(InsertCurrentProductRepository Function(InsertCurrentProductRepositoryRef ref) create) {
    return _$InsertCurrentProductRepositoryFamilyOverride(this, create);
  }
}

class _$InsertCurrentProductRepositoryFamilyOverride implements FamilyOverride {
  _$InsertCurrentProductRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final InsertCurrentProductRepository Function(InsertCurrentProductRepositoryRef ref) create;

  @override
  final InsertCurrentProductRepositoryFamily overriddenFamily;

  @override
  InsertCurrentProductRepositoryProvider getProviderOverride(
    covariant InsertCurrentProductRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// See also [insertCurrentProductRepository].
class InsertCurrentProductRepositoryProvider extends AutoDisposeProvider<InsertCurrentProductRepository> {
  /// See also [insertCurrentProductRepository].
  InsertCurrentProductRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => insertCurrentProductRepository(
            ref as InsertCurrentProductRepositoryRef,
            caller: caller,
          ),
          from: insertCurrentProductRepositoryProvider,
          name: r'insertCurrentProductRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$insertCurrentProductRepositoryHash,
          dependencies: InsertCurrentProductRepositoryFamily._dependencies,
          allTransitiveDependencies: InsertCurrentProductRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  InsertCurrentProductRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    InsertCurrentProductRepository Function(InsertCurrentProductRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: InsertCurrentProductRepositoryProvider._internal(
        (ref) => create(ref as InsertCurrentProductRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  AutoDisposeProviderElement<InsertCurrentProductRepository> createElement() {
    return _InsertCurrentProductRepositoryProviderElement(this);
  }

  InsertCurrentProductRepositoryProvider _copyWith(
    InsertCurrentProductRepository Function(InsertCurrentProductRepositoryRef ref) create,
  ) {
    return InsertCurrentProductRepositoryProvider._internal(
      (ref) => create(ref as InsertCurrentProductRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is InsertCurrentProductRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin InsertCurrentProductRepositoryRef on AutoDisposeProviderRef<InsertCurrentProductRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _InsertCurrentProductRepositoryProviderElement extends AutoDisposeProviderElement<InsertCurrentProductRepository>
    with InsertCurrentProductRepositoryRef {
  _InsertCurrentProductRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as InsertCurrentProductRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
