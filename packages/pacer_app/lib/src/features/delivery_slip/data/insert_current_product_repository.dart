// ignore_for_file: public_member_api_docs

import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart' show Date;
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/delivery_product.dart';

part 'insert_current_product_repository.g.dart';

@riverpod
InsertCurrentProductRepository insertCurrentProductRepository(
  InsertCurrentProductRepositoryRef ref, {
  required AppUser? caller,
}) =>
    InsertCurrentProductRepository(caller: caller);

class InsertCurrentProductRepository {
  InsertCurrentProductRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  /// 納品する商品を登録する.
  /// レスポンスで次にスキャン、またはキーボードから入力した商品コードの詳細情報を受け取る。
  Future<DeliveryProduct?> execute({
    required String storeCode,
    required double? deliveryQuantity,
    required String currentProductCode,
    required DateTime deliveryDate,
    String? nextProductCode = '',
  }) async {
    final uri = Env.getApiBaseUrl();

    final channel = ClientChannel(uri.host, port: uri.port);

    final stub = DeliveryServiceClient(
      channel,
      options: CallOptions(timeout: const Duration(seconds: 20)),
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = InsertCurrentProductRequest(
      userName: caller?.name,
      deliveryNumber: deliveryQuantity,
      productCode: currentProductCode,
      storeCode: storeCode,
      deliveryDate: Date(
        year: deliveryDate.year,
        month: deliveryDate.month,
        day: deliveryDate.day,
      ),
      productCodeNew: nextProductCode,
    );

    log('InsertCurrentProductRequest $request');

    try {
      final response = await stub.insertCurrentProduct(request);
      log('InsertCurrentProductResponse $response');

      if (response.code != '000') {
        throw UnknownException(response.message);
      }

      final product = GetProductInfoResponse(
        code: '000',
        productInfo: response.productInfo,
        priceInfo: response.priceInfo,
      );

      return switch (nextProductCode?.isEmpty ?? true) { true => null, false => DeliveryProduct.fromApi(product) };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.notFound) => ProductNotFoundException(),
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError(code: StatusCode.invalidArgument) => WrongProductCodeException(),
          GrpcError() => UnknownException(e.codeName),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
