/// 入力とtruncateToDoubleした値(2.5->2.0のように変換したもの）が一致する場合、
/// 小数点以下を排除した文字列を返す。(この場合は2を返す)
/// それ以外の場合、double型.toStringを返す。
/// UI、エラーメッセージにドメイン層の納品数量や売価金額合計値などを出力する際に呼び出す。
String getParsedQuantity(double quantity) {
  // tryParceの前準備。
  final source = quantity.toString();

  return switch (double.tryParse(source)) {
    /// truncateToDouble() メソッドは、this から小数点以下の桁数を取り除いた整数 double 値を返す。
    final parsed? when parsed.truncateToDouble() == quantity =>

      /// parsed.toStringAsFixed() メソッドは、浮動小数点数を指定された桁数で文字列に変換するために使用する。
      /// このメソッドの引数は、小数部分の桁数を指定する。0を指定すると、整数部分のみを返す。
      parsed.toStringAsFixed(0),
    final parsed? => parsed.toString(),
    null => quantity.toString(),
  };
}
