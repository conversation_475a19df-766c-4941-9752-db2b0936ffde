import 'package:flutter/material.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../exceptions/app_exception.dart';
import '../../../utils/date_time.dart';

/// ベンダー情報。
/// 納品伝票再発行機能のTOPで利用する。
class DeliveryVender {
  /// init.
  DeliveryVender({
    this.venderCode,
    this.venderName,
    this.deliveryDate,
  });

  /// init from api response.
  factory DeliveryVender.fromApi(GetVenderInfoResponse response) {
    return switch (response) {
      GetVenderInfoResponse(
        code: != '000',
        message: != '' && final message,
      ) =>
        throw UnknownException(message),
      GetVenderInfoResponse(
        venderCode: 0,
      ) ||
      GetVenderInfoResponse(
        venderName: '',
      ) =>
        throw UnknownException('ベンダーがありません'),
      _ => DeliveryVender(
          venderCode: response.venderCode,
          venderName: response.venderName,
        ),
    };
  }

  /// init from api response by JAN.
  factory DeliveryVender.fromApiByJan(GetJanVenderInfoResponse response) {
    return switch (response) {
      GetJanVenderInfoResponse(
        code: != '000',
        message: != '' && final message,
      ) =>
        throw UnknownException(message),
      GetJanVenderInfoResponse(
        venderCode: 0,
      ) ||
      GetJanVenderInfoResponse(
        venderName: '',
      ) =>
        throw UnknownException('ベンダーがありません'),
      _ => DeliveryVender(
          venderCode: response.venderCode,
          venderName: response.venderName,
        ),
    };
  }

  /// ベンダーの識別番号。
  final int? venderCode;

  /// ベンダーの名前。
  final String? venderName;

  /// 納品日。APIのレスポンスでは初期化しない。
  /// ユーザの入力でのみ更新される。
  DateTime? deliveryDate;

  /// copyWith.
  DeliveryVender copyWith({
    int? venderCode,
    String? venderName,
    DateTime? deliveryDate,
  }) {
    return DeliveryVender(
      venderCode: venderCode ?? this.venderCode,
      venderName: venderName ?? this.venderName,
      deliveryDate: deliveryDate ?? this.deliveryDate,
    );
  }

  /// [systemDate] サーバーから取得した日付をセットする。この日付を基に各種判定を行う。
  ///
  /// true: 納品日が３日以内のため、仕入れ修正が可能。納品日と現在の日付が同じ場合も可能。
  ///
  /// e.g.
  /// [systemDate]: 2023/08/30
  ///
  /// [deliveryDate]: 2023/08/30 => true
  ///
  /// [deliveryDate]: 2023/08/27 => true
  ///
  /// [deliveryDate]: 2023/08/26 => false
  ///
  bool allowEditing({required DateTime systemDate}) {
    // 計算を合わせるため、年月日で初期化する
    final fixedSystemDate = DateTime(systemDate.year, systemDate.month, systemDate.day);
    // 計算を合わせるため、年月日で初期化する
    final targetDeliveryDate = deliveryDate;
    if (targetDeliveryDate == null) {
      return false;
    }

    final fixedDeliveryDate = DateTime(
      targetDeliveryDate.year,
      targetDeliveryDate.month,
      targetDeliveryDate.day,
    );

    // サーバーシステム時間から３日前の日付
    final threeDaysAgo = fixedSystemDate.subtract(const Duration(days: 3));
    final allowEditingRange = DateTimeRange(
      start: threeDaysAgo,
      end: fixedSystemDate,
    );

    return fixedDeliveryDate.isSameDate(threeDaysAgo) ||
        fixedDeliveryDate.inDateRange(allowEditingRange) ||
        fixedDeliveryDate.isSameDate(fixedSystemDate);
  }
}
