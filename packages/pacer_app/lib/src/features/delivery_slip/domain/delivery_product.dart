import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';

import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../exceptions/app_exception.dart';
import 'delivery_product_price.dart';

const _unregisteredProductName = '未登録';
final _formatter = NumberFormat.decimalPattern('ja');

/// 商品情報
/// 納品/入力 画面にてJANをスキャンまたはキーボード入力し検索を行う。
/// 検索結果は画面に表示すること。
class DeliveryProduct with EquatableMixin {
  /// init
  const DeliveryProduct({
    required this.productName,
    required this.venderName,
    required this.brand,
    required this.color,
    required this.size,
    required this.specName,
    required this.costPrice,
    required this.salesPrice,
    required this.isFresh,
    required this.deliveryProductPrice,
  });

  /// init from api response
  factory DeliveryProduct.fromApi(GetProductInfoResponse response) {
    return switch (response) {
      GetProductInfoResponse(
        code: '000',
        productInfo: ProductInfo(productName: _unregisteredProductName),
      ) ||
      GetProductInfoResponse(
        code: '000',
        productInfo: ProductInfo(productName: == ''),
      ) =>
        throw ProductNotFoundException(),
      GetProductInfoResponse(
        code: '000',
        productInfo: ProductInfo(venderName: == ''),
      ) =>
        throw ProductNotRegisteredInMasterByStore(),
      GetProductInfoResponse(
        code: != '000',
        message: != '' && final message,
      ) =>
        throw UnknownException(message),
      _ => DeliveryProduct(
          productName: response.productInfo.productName,
          venderName: response.productInfo.venderName,
          brand: response.productInfo.brand,
          color: response.productInfo.productColor,
          size: response.productInfo.productSize,
          specName: response.productInfo.subName,
          costPrice: response.productInfo.costPrice,
          salesPrice: response.productInfo.salesPrice,
          isFresh: response.productInfo.isFresh,
          deliveryProductPrice: DeliveryProductPrice.fromApi(response.priceInfo),
        ),
    };
  }

  /// init mock
  factory DeliveryProduct.mock() {
    return DeliveryProduct(
      productName: 'mock商品',
      venderName: 'mockベンダー',
      brand: 'mockブランド',
      color: 'mockカラー',
      size: 'mockサイズ',
      costPrice: 0,
      salesPrice: 0,
      specName: 'mock規格',
      isFresh: false,
      deliveryProductPrice: DeliveryProductPrice.mock(),
    );
  }

  /// 商品名
  final String productName;

  /// ベンダー名
  final String venderName;

  /// 商品のブランド
  final String brand;

  /// 商品の色
  final String color;

  /// 商品のサイズ
  final String size;

  /// 商品の規格
  final String specName;

  /// 原価(小数点2桁)
  final double costPrice;

  /// 売価(小数点2桁)
  final double salesPrice;

  /// 生鮮JANフラグ 0:非生鮮商品 1：生鮮商品
  final bool isFresh;

  /// 原価、売価、納品数量に関する情報
  final DeliveryProductPrice deliveryProductPrice;

  @override
  List<Object?> get props => [
        productName,
        venderName,
        brand,
        color,
        size,
        specName,
        isFresh,
      ];

  /// 納品数量チェック。
  ///
  /// 次のバリデーション処理でのエラーは、納品数量修正を強制的に中断する。
  ///
  /// エラーメッセージはエラー固有のものを表示する。
  /// 原価合計金額と売価合計金額は、計算後に四捨五入した値を表示する。
  ///
  /// 1. overMaxQuantity: OverMaxQuantityException
  /// 2. overMaxPriceSum: OverMaxPriceSumException
  /// 3. overMaxCostPriceSum: OverMaxCostPriceSumException
  ///
  /// 次のバリデーション処理でのエラーは、ユーザが許可すれば納品数量修正が可能。
  ///
  /// 次のいずれかの数量チェックがtrueの場合、共通してOverDeliveryNormalQuantityExceptionをスローする。
  ///
  /// 1. overNormalQuantity
  /// 2. overNormalPriceSum
  /// 3. overNormalCostPriceSum
  ///
  ///[optionalCostPrice]と[optionalSalesPrice]は基本的に未指定だが、
  /// 納品一覧画面などで生鮮商品(25から始まるJANの商品)の数量変更を行う際に原価と売価が０になる場合がある。
  /// そのような際に、別のAPIで取得した実際の売価情報をセットする。
  ///
  void validateQuantity(
    double quantity, {
    double? optionalCostPrice,
    double? optionalSalesPrice,
  }) {
    // 計算に使用する値
    final targetCostPrice =
        switch (costPrice == 0) { true => optionalCostPrice ?? deliveryProductPrice.costPrice, false => costPrice };
    // 計算に使用する値
    final targetSalesPrice = switch (salesPrice == 0) {
      true => optionalSalesPrice ?? deliveryProductPrice.standardPrice,
      false => salesPrice
    };

    if (deliveryProductPrice.overMaxQuantity(quantity)) {
      throw OverMaxQuantityException(
        quantity: _formatter.format(quantity),
        maxQuantity: _formatter.format(deliveryProductPrice.maxQuantity),
      );
    }

    if (deliveryProductPrice.overMaxPriceSum(
      quantity,
      optionalSalesPrice: targetSalesPrice,
    )) {
      throw OverMaxPriceSumException(
        priceSum: _formatter.format(quantity * targetSalesPrice),
        maxPriceSum: _formatter.format(deliveryProductPrice.maxPriceSum),
      );
    }

    if (deliveryProductPrice.overMaxCostPriceSum(
      quantity,
      optionalCostPrice: targetCostPrice,
    )) {
      throw OverMaxCostPriceSumException(
        costPriceSum: _formatter.format(quantity * targetCostPrice),
        maxCostPriceSum: _formatter.format(deliveryProductPrice.maxCostPriceSum),
      );
    }

    if (deliveryProductPrice.overNormalQuantity(quantity) ||
        deliveryProductPrice.overNormalPriceSum(quantity) ||
        deliveryProductPrice.overNormalCostPriceSum(quantity)) {
      throw OverDeliveryNormalQuantityException(
        isOverNormalQuantity: deliveryProductPrice.overNormalQuantity(quantity),
        isOverNormalPriceSum: deliveryProductPrice.overNormalPriceSum(quantity),
        isOverNormalCostPriceSum: deliveryProductPrice.overNormalCostPriceSum(quantity),
        quantity: _formatter.format(quantity),
        normalQuantity: _formatter.format(deliveryProductPrice.normalQuantity),
        priceSum: _formatter.format((quantity * targetSalesPrice).round()),
        normalPriceSum: _formatter.format(deliveryProductPrice.normalPriceSum),
        costPriceSum: _formatter.format((quantity * targetCostPrice).round()),
        normalCostPriceSum: _formatter.format(deliveryProductPrice.normalCostPriceSum),
      );
    }
  }
}
