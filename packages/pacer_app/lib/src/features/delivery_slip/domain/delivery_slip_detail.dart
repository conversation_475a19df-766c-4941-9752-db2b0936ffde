import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import 'delivery_product_code.dart';

/// 納品伝票情報(DeliverySlip)の詳細情報。
/// 基本的にはこのクラスがListでAPIから返却される。
class DeliverySlipDetail extends Equatable {
  /// init.
  const DeliverySlipDetail({
    required this.id,
    required this.productCode,
    required this.productName,
    required this.deliveryDate,
    required this.venderName,
    required this.quantity,
    required this.salesPrice,
    required this.salesSum,
  });

  /// init from api response.
  factory DeliverySlipDetail.fromApi(
    DeliveryInfo response,
  ) =>
      DeliverySlipDetail(
        id: response.id,
        productCode: DeliveryProductCode.parse(response.productCode),
        productName: response.productName,
        deliveryDate: DateFormat('yyyy/MM/dd').parse(response.deliDate),
        venderName: response.venderName,
        quantity: response.quantity,
        salesPrice: response.salesPrice.toInt(),
        salesSum: response.salesSum.toInt(),
      );

  /// id
  final int id;

  /// 商品コード
  final DeliveryProductCode productCode;

  /// 商品名
  final String productName;

  /// 納品日
  final DateTime deliveryDate;

  /// ベンダー名
  final String venderName;

  /// 納品数量
  final double quantity;

  /// 売価
  final int salesPrice;

  /// 売価合計
  final int salesSum;

  @override
  List<Object?> get props => [
        id,
        productCode,
        productName,
        deliveryDate,
        venderName,
        quantity,
        salesPrice,
        salesSum,
      ];
}
