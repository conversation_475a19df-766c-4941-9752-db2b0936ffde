import 'package:equatable/equatable.dart';

import '../../../utils/string_extensions_itf14.dart';

/// 納品伝票アプリ内で扱う商品コードクラス
class DeliveryProductCode with EquatableMixin {
  /// init
  const DeliveryProductCode(this.value);

  /// 様々なパース処理を実施するファクトリーメソッド。
  /// 基本的にこのFactoryメソッドで初期化する。
  ///
  /// 結果は次の三通りで、結果がnullの場合は空文字となる。
  /// 1. 値下JAN(20桁か26桁)の場合、前13桁を取得して、先頭の0を消去した文字列を返す。
  /// 2. ITF-14の場合、再計算されたEAN-13を取得する。
  /// 3. それ以外の場合、先頭の０を消去したJANを返す。
  ///
  factory DeliveryProductCode.parse(
    String productCode,
  ) {
    return switch (productCode.length) {
      20 || 26 => DeliveryProductCode(
          BigInt.tryParse(productCode.substring(0, 13))?.toString() ?? '',
        ),
      14 => DeliveryProductCode(productCode.ean13FromITF14),
      _ => DeliveryProductCode(int.tryParse(productCode)?.toString() ?? ''),
    };
  }

  /// 商品コードとして渡される文字列
  final String value;

  @override
  List<Object?> get props => [value];
}
