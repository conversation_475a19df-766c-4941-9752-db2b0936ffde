import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pbgrpc.dart';

import '../../../utils/date_time.dart';
import 'delivery_product_code.dart';

final _formatter = NumberFormat.decimalPattern('ja');

/// 納品された商品の情報。
class DeliveredProduct with EquatableMixin {
  /// init
  const DeliveredProduct({
    required this.id,
    required this.productCode,
    required this.productName,
    required this.deliveryDate,
    required this.venderName,
    required this.quantity,
    required this.salesPrice,
    required this.salesSum,
  });

  /// init from response
  factory DeliveredProduct.fromResponse(
    DeliveryInfo delivery,
  ) =>
      DeliveredProduct(
        id: delivery.id,
        productCode: DeliveryProductCode.parse(delivery.productCode),
        productName: delivery.productName,
        deliveryDate: DateFormat('yyyy/MM/dd').parse(delivery.deliDate),
        venderName: delivery.venderName,
        quantity: delivery.quantity,
        salesPrice: delivery.salesPrice.toInt(),
        salesSum: delivery.salesSum.toInt(),
      );

  /// init fake
  factory DeliveredProduct.fake(int index) => DeliveredProduct(
        id: index,
        productCode: DeliveryProductCode.parse('9999999999999'),
        productName: 'fake商品$index',
        deliveryDate: DateTime.now(),
        venderName: 'fakeベンダー$index',
        quantity: 1,
        salesPrice: 9999,
        salesSum: 9999,
      );

  /// 納品された商品に採番されるID。
  final int id;

  /// 納品された商品の商品コード。
  final DeliveryProductCode productCode;

  /// 納品された商品の商品名。
  final String productName;

  /// 商品の納品日。
  final DateTime deliveryDate;

  /// 納品された商品のベンダー名。
  final String venderName;

  /// 納品された商品の納品数量
  final double quantity;

  /// 納品された商品の売単価。
  final int salesPrice;

  /// 納品された商品の売価合計。
  /// 納品数量と売単価の積。
  final int salesSum;

  @override
  List<Object?> get props => [
        id,
        productCode,
        productName,
        venderName,
        deliveryDate,
        venderName,
        quantity,
        salesPrice,
        salesSum,
      ];

  @override
  String toString() {
    return '''
JAN: ${productCode.value}
商品名: $productName
納品日: ${deliveryDate.toYearMonthDayHyphen}
ベンダー: $venderName
数量: ${_formatter.format(quantity)}
売単価: ${_formatter.format(salesPrice)}
売価合計: ${_formatter.format(salesSum)}
''';
  }

  /// copyWith
  DeliveredProduct copyWith({
    int? id,
    DeliveryProductCode? productCode,
    String? productName,
    DateTime? deliveryDate,
    String? venderName,
    double? quantity,
    int? salesPrice,
    int? salesSum,
  }) {
    return DeliveredProduct(
      id: id ?? this.id,
      productCode: productCode ?? this.productCode,
      productName: productName ?? this.productName,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      venderName: venderName ?? this.venderName,
      quantity: quantity ?? this.quantity,
      salesPrice: salesPrice ?? this.salesPrice,
      salesSum: salesSum ?? this.salesSum,
    );
  }
}
