import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pb.dart';

/// 商品価格と納品数量に関する基準値と最大値を持つ。
/// 納品伝票機能(delivery_slip)で、主にMutation処理前のValidationに活用する。
class DeliveryProductPrice with EquatableMixin {
  /// init
  const DeliveryProductPrice({
    required this.costPrice,
    required this.standardPrice,
    required this.normalCostPriceSum,
    required this.normalPriceSum,
    required this.normalQuantity,
    required this.maxCostPriceSum,
    required this.maxPriceSum,
    required this.maxQuantity,
  });

  /// init from api response
  factory DeliveryProductPrice.fromApi(PriceInfo response) => DeliveryProductPrice(
        costPrice: response.costPrice,
        standardPrice: response.standardPrice,
        normalCostPriceSum: response.costSum.toDouble(),
        normalPriceSum: response.salesSum,
        normalQuantity: response.saleNum.toDouble(),
        maxCostPriceSum: response.maxCostSum.toDouble(),
        maxPriceSum: response.maxSalesSum,
        maxQuantity: response.maxSaleNum.toDouble(),
      );

  /// init mock
  factory DeliveryProductPrice.mock() {
    return const DeliveryProductPrice(
      costPrice: 50,
      standardPrice: 100,
      normalCostPriceSum: 2500,
      normalPriceSum: 5000,
      normalQuantity: 50,
      maxCostPriceSum: 250000,
      maxPriceSum: 500000,
      maxQuantity: 5000,
    );
  }

  /// 原価
  final double costPrice;

  /// 売価
  final double standardPrice;

  /// 原価の基準値
  final double normalCostPriceSum;

  /// 売価の基準値
  final double normalPriceSum;

  /// 納品数量の基準値
  final double normalQuantity;

  /// 原価の最大値
  final double maxCostPriceSum;

  /// 売価の最大値
  final double maxPriceSum;

  /// 納品数量の最大値
  final double maxQuantity;

  @override
  List<Object?> get props => [
        costPrice,
        standardPrice,
        normalCostPriceSum,
        normalPriceSum,
        normalQuantity,
        maxCostPriceSum,
        maxPriceSum,
        maxQuantity,
      ];

  /// true:　数量 * 原価 > 原価の基準値　AND　数量 * 原価 <= 最大値
  /// データ登録直前にValidationする(InsertCurrentProduct)。
  /// ユーザがOKすればデータ登録を続行する。
  bool overNormalCostPriceSum(double quantity, {double? optionalCostPrice}) {
    return (quantity * (optionalCostPrice ?? costPrice)) > normalCostPriceSum && !overMaxCostPriceSum(quantity);
  }

  /// true:　数量 * 売価 > 売価の基準値　AND　数量 * 売価 <= 最大値
  /// データ登録直前にValidationする(InsertCurrentProduct)。
  /// ユーザがOKすればデータ登録を続行する。
  bool overNormalPriceSum(double quantity, {double? optionalSalesPrice}) {
    return (quantity * (optionalSalesPrice ?? standardPrice)) > normalPriceSum && !overMaxPriceSum(quantity);
  }

  /// true: 数量 > 納品数量の基準値　AND　数量 <= 最大値
  /// データ登録直前にValidationする(InsertCurrentProduct)。
  /// ユーザがOKすればデータ登録を続行する。
  bool overNormalQuantity(double quantity) {
    return quantity > normalQuantity && !overMaxQuantity(quantity);
  }

  /// true: 納品数量 * 原価 > 原価の最大値
  /// データ登録直前にValidationする(InsertCurrentProduct)。
  /// ユーザはデータ登録を続行できない。
  bool overMaxCostPriceSum(double quantity, {double? optionalCostPrice}) {
    return (quantity * (optionalCostPrice ?? costPrice)) > maxCostPriceSum;
  }

  /// true: 納品数量 * 売価 > 売価の最大値
  /// データ登録直前にValidationする(InsertCurrentProduct)。
  /// ユーザはデータ登録を続行できない。
  bool overMaxPriceSum(double quantity, {double? optionalSalesPrice}) {
    return (quantity * (optionalSalesPrice ?? standardPrice)) > maxPriceSum;
  }

  /// true: 納品数量 > 納品数量の最大値
  /// データ登録直前にValidationする(InsertCurrentProduct)。
  /// ユーザはデータ登録を続行できない。
  bool overMaxQuantity(double quantity) {
    return quantity > maxQuantity;
  }
}
