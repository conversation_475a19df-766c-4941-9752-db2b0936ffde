import 'package:intl/intl.dart';
import 'package:shinise_core_client/delivery/v1/delivery.pb.dart';

/// 納品/再発行 伝票一覧画面で表示する情報
class DeliverySlip {
  /// init.
  DeliverySlip({
    required this.slipNumber,
    required this.supplierName,
    required this.deliveryDate,
    required this.costPriceSum,
    required this.salesPriceSum,
    required this.employeeCode,
    required this.employeeName,
    this.isSelected = false,
  });

  /// init from api response.
  factory DeliverySlip.fromApi(GetSlipInfoResponse_SlipInfo response) {
    return DeliverySlip(
      slipNumber: int.parse(response.slipNo),
      supplierName: response.supplierName,
      deliveryDate: DateFormat('yyyy/MM/dd').parse(response.deliveryDate),
      costPriceSum: response.costSum,
      salesPriceSum: response.salesSum,
      employeeCode: response.empCode,
      employeeName: response.empName,
    );
  }

  /// 伝票識別番号。伝票詳細情報取得に必要。
  final int slipNumber;

  /// 伝票識別番号に該当するベンダーの名称。
  final String supplierName;

  /// 伝票識別番号に該当する納品日。
  final DateTime deliveryDate;

  /// 伝票識別番号に該当する原価金額合計。
  final double costPriceSum;

  /// 伝票識別番号に該当する売価金額合計。
  final double salesPriceSum;

  /// 伝票識別番号に該当する社員コード。
  final String employeeCode;

  /// 伝票識別番号に該当する社員名。
  final String employeeName;

  /// 選択状態を管理する。
  /// trueの場合、修正もしくは印刷が可能。
  bool isSelected;

  /// copyWith
  DeliverySlip copyWith({
    int? slipNumber,
    String? supplierName,
    DateTime? deliveryDate,
    double? costPriceSum,
    double? salesPriceSum,
    String? employeeCode,
    String? employeeName,
    bool? isSelected,
  }) {
    return DeliverySlip(
      slipNumber: slipNumber ?? this.slipNumber,
      supplierName: supplierName ?? this.supplierName,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      costPriceSum: costPriceSum ?? this.costPriceSum,
      salesPriceSum: salesPriceSum ?? this.salesPriceSum,
      employeeCode: employeeCode ?? this.employeeCode,
      employeeName: employeeName ?? this.employeeName,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  String toString() {
    return '''
DeliverySlip(
  slipNumber: $slipNumber,
  supplierName: $supplierName,
  deliveryDate: $deliveryDate,
  costPriceSum: $costPriceSum,
  salesPriceSum: $salesPriceSum,
  employeeCode: $employeeCode,
  employeeName: $employeeName,
  isSelected: $isSelected,
);''';
  }
}
