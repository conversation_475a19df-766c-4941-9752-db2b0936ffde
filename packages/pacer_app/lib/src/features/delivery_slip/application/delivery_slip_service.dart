import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../../authentication/domain/app_user.dart';
import '../data/delete_current_product_repository.dart';
import '../data/delivered_product_repository.dart';
import '../data/delivery_print_repository.dart';
import '../data/delivery_product_repository.dart';
import '../data/delivery_slip_repository.dart';
import '../data/insert_current_product_repository.dart';
import '../data/insert_delivery_retail_repository.dart';
import '../data/system_date_repository.dart';
import '../data/update_delivery_date_repository.dart';
import '../data/update_delivery_quantity_repository.dart';
import '../data/vender_repository.dart';
import '../domain/delivered_product.dart';
import '../domain/delivery_product.dart';
import '../domain/delivery_product_code.dart';
import '../domain/delivery_slip.dart';
import '../domain/delivery_slip_detail.dart';
import '../domain/delivery_vender.dart';

part 'delivery_slip_service.g.dart';

/// 納品伝票アプリ共通のビジネスロジック
class DeliverySlipService {
  /// init
  DeliverySlipService(this.ref);

  /// ref
  final Ref ref;

  /// 操作するユーザ
  AppUser? get _caller => ref.read(authRepositoryProvider).currentUser;

  /// ユーザがログイン中の店舗番号を取得する
  String get _storeCode =>
      switch (_caller) { final caller? => caller.clockInStore.code, _ => throw ParseAuthFailure(_caller.toString()) };

  /// 納品登録をするために必要な、商品情報を取得する。
  /// 商品コードから商品情報を取得する。
  Future<DeliveryProduct?> fetchProduct({
    required DeliveryProductCode productCode,
  }) async =>
      switch (productCode.value.isEmpty) {
        true => throw WrongProductCodeException(),
        false => ref
            .read(deliveryProductRepositoryProvider(caller: _caller))
            .searchItem(productCode: productCode.value, storeCode: _storeCode)
      };

  /// サーバーの時刻を取得する。
  /// 納品日の指定時や、再発行の際の日付け判定に使用する。
  Future<DateTime> get systemDate async =>
      ref.read(systemDateRepositoryProvider(caller: _caller)).systemDate(storeCode: _storeCode);

  /// ログイン中の店舗で、納品登録した商品一覧情報を取得する。
  Future<List<DeliveredProduct>> deliveredProduct() async {
    return ref.read(deliveredProductRepositoryProvider(caller: _caller)).deliveredProduct(storeCode: _storeCode);
  }

  /// 納品伝票を印刷する前段階で、商品を納品登録をする処理。
  Future<DeliveryProduct?> insertCurrentProduct({
    required double? deliveryQuantity,
    required DeliveryProductCode currentJan,
    required DateTime deliveryDate,
    required DeliveryProductCode? nextJan,
  }) async {
    return ref.read(insertCurrentProductRepositoryProvider(caller: _caller)).execute(
          deliveryQuantity: deliveryQuantity,
          currentProductCode: currentJan.value,
          deliveryDate: deliveryDate,
          nextProductCode: nextJan?.value ?? '',
          storeCode: _storeCode,
        );
  }

  /// ログイン中の店舗で、納品登録した商品のデータを印刷データとして投入する。
  /// レスポンスとして、PrintIDを受け取る。PrintIDは印刷処理APIに渡す。
  Future<DeliverySlipPrintID> insertDeliveryRetail({
    required DateTime deliveryDate,
  }) async {
    return ref
        .read(insertDeliveryRetailRepositoryProvider(caller: _caller))
        .execute(deliveryDate: deliveryDate, storeCode: _storeCode);
  }

  /// 納品登録した商品の納品数量を更新する。
  /// この時、商品はまだ伝票印刷されていない。
  Future<void> updateDeliveryQuantity({
    int? id,
    double? deliveryQuantity,
  }) async {
    return ref.read(updateDeliveryQuantityRepositoryProvider(caller: _caller)).execute(
          id: id,
          deliveryQuantity: deliveryQuantity,
          storeCode: _storeCode,
        );
  }

  /// 納品登録された商品の納品日がシステム時間（操作当日）ではない時、
  /// 全ての納品登録した商品の納品日をシステム時間に統一する処理。
  /// 納品伝票印刷前に実行する。
  Future<void> updateDeliveryDate() async {
    final deliveryDate = await systemDate;
    return ref
        .read(updateDeliveryDateRepositoryProvider(caller: _caller))
        .execute(deliveryDate: deliveryDate, storeCode: _storeCode);
  }

  /// 一旦納品登録したが、伝票印刷不要になった商品を削除する。
  Future<void> deleteDeliveredProduct({
    required String? productId,
  }) async {
    return ref
        .read(deleteCurrentProductRepositoryProvider(caller: _caller))
        .execute(id: productId, storeCode: _storeCode);
  }

  /// ベンダー番号からベンダー情報を検索して取得する。
  /// 納品伝票再発行のTOP画面で使用する。
  Future<DeliveryVender> venderByCode({
    required String venderCode,
  }) async {
    return ref
        .read(venderRepositoryProvider(caller: _caller))
        .getVenderByCode(venderCode: venderCode, storeCode: _storeCode);
  }

  /// 商品コードからベンダー情報を検索して取得する。
  /// 納品伝票再発行のTOP画面で使用する。
  Future<DeliveryVender> venderByJan({
    required DeliveryProductCode productCode,
  }) async {
    return ref.read(venderRepositoryProvider(caller: _caller)).getVenderByJan(
          productCode: productCode.value,
          storeCode: _storeCode,
        );
  }

  /// 納品伝票の一覧を取得する。
  /// [venderCode]がnullの場合、ログイン中のユーザ以外も含めた全ての納品伝票を取得する。
  Future<List<DeliverySlip>> listDeliverySlip({
    int? venderCode,
    required DateTime deliveryDate,
  }) async {
    return ref.read(deliverySlipRepositoryProvider(caller: _caller)).getSlip(
          venderCode: venderCode,
          deliveryDate: deliveryDate,
          storeCode: _storeCode,
        );
  }

  /// 納品伝票の一覧を取得する。
  /// [slipNumber]: 伝票番号を指定が必須。
  Future<List<DeliverySlipDetail>> listDeliverySlipDetail({
    required int slipNumber,
  }) async {
    return ref
        .read(deliverySlipRepositoryProvider(caller: _caller))
        .getSlipDetail(slipNumber: slipNumber, storeCode: _storeCode);
  }

  /// 印刷済伝票内の商品納品数量を更新する
  Future<void> updateSlipQuantity({
    required double deliveryQuantity,
    required int slipId,
  }) async {
    return ref.read(deliverySlipRepositoryProvider(caller: _caller)).updateSlipQuantity(
          deliveryQuantity: deliveryQuantity,
          slipId: slipId,
          storeCode: _storeCode,
        );
  }

  /// ログイン中の店舗で、再発行する伝票を印刷データとして投入する。
  /// [slipNumbers]: ユーザが画面上で選択した伝票の伝票IDのリスト
  /// レスポンスとして、PrintIDを受け取る。PrintIDは印刷処理APIに渡す。
  Future<DeliverySlipPrintID> insertPrintSlipList({
    required List<int> slipNumbers,
  }) async {
    return ref.read(deliverySlipRepositoryProvider(caller: _caller)).insertPrintSlipList(
          slipNumbers: slipNumbers,
          storeCode: _storeCode,
        );
  }

  /// 印刷前にプリンター使用できるか確認する。
  Future<void> checkConnectionWithPrinter() async {
    return ref.read(deliveryPrintRepositoryProvider(caller: _caller)).checkConnectionWithPrinter(storeCode: _storeCode);
  }

  /// 印刷実行をリクエストする。
  /// printIdを渡す必要があります。
  Future<void> requestPrint({required DeliverySlipPrintID printId}) async {
    return ref.read(deliveryPrintRepositoryProvider(caller: _caller)).print(printId: printId, storeCode: _storeCode);
  }
}

/// 納品伝票アプリ共通のビジネスロジックのProvider
@riverpod
DeliverySlipService deliverySlipService(DeliverySlipServiceRef ref) {
  return DeliverySlipService(ref);
}
