// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_slip_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deliverySlipServiceHash() => r'0be743e11a7f775ba2fc1de19157aeda93f3b54c';

/// 納品伝票アプリ共通のビジネスロジックのProvider
///
/// Copied from [deliverySlipService].
@ProviderFor(deliverySlipService)
final deliverySlipServiceProvider = AutoDisposeProvider<DeliverySlipService>.internal(
  deliverySlipService,
  name: r'deliverySlipServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deliverySlipServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DeliverySlipServiceRef = AutoDisposeProviderRef<DeliverySlipService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
