// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shelf_life_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shelfLifeRepositoryHash() => r'474d2d99391a9db78d73edc5512b39f65c2d2924';

/// プロバイダー
///
/// Copied from [shelfLifeRepository].
@ProviderFor(shelfLifeRepository)
final shelfLifeRepositoryProvider = Provider<ShelfLifeRepository>.internal(
  shelfLifeRepository,
  name: r'shelfLifeRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$shelfLifeRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ShelfLifeRepositoryRef = ProviderRef<ShelfLifeRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
