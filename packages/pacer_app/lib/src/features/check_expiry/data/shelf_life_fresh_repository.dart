// ignore_for_file: public_member_api_docs

import 'dart:developer';

import 'package:grpc/grpc.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart' show Date;
import 'package:shinise_core_client/shelf_life/v1/common.pbenum.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pbgrpc.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life_fresh.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/fresh_discount_product.dart';
import '../domain/fresh_maintenance_list_product.dart';
import '../domain/fresh_maintenance_product.dart';
import '../domain/fresh_new_product.dart';
import '../domain/fresh_new_product_regist_info.dart';
import '../domain/product_check_info.dart';
import '../domain/shelf_work_info.dart';
import 'handle_grpc_error.dart';

part 'shelf_life_fresh_repository.g.dart';

@Riverpod(keepAlive: true)
ShelfLifeFreshRepository shelfLifeFreshRepository(
  ShelfLifeFreshRepositoryRef ref,
) =>
    ShelfLifeFreshRepository();

class ShelfLifeFreshRepository {
  ShelfLifeFreshRepository();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));
  final _uri = Env.getApiBaseUrl();

  /// 棚番設定画面、値下データをクリック時、作業中設定
  Future<void> setFreshDCTShelfWork({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final request = SetFreshDCTShelfWorkRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      shelfNo: shelfInfo?.shelfNumberText,
      checkPlanWeek: shelfInfo?.checkPlanWeek,
    );

    log('setFreshDCTShelfWork request $request');

    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.setFreshDCTShelfWork(request);
      log('setFreshDCTShelfWork response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮新規商品一覧取得
  Future<List<FreshNewProduct>> fetchFreshNewProductList({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final request = GetFreshProdNewInputListRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
    );

    log('fetchFreshProdNewInputList request $request');
    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.getFreshProdNewInputList(request);

      log('fetchFreshProdNewInputList response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }

      return response.prodNewInput.map(FreshNewProduct.fromFetchFreshProdNewInputListRes).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮棚情チェック情報取得
  Future<ProductCheckInfo> fetchFreshProductCheckInfo({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final shelfFreshStub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final getFreshCheckProductInfoReq = GetFreshCheckProductInfoRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      shelfNo: shelfInfo?.shelfNumberText,
      shelfName: shelfInfo?.shelfName,
      checkPlanWeek: shelfInfo?.checkPlanWeek,
      shelfClassCode: shelfInfo?.shelfClassCode.toString(),
    );

    log('fetchFreshProductCheckInfo request $getFreshCheckProductInfoReq');

    try {
      /// 棚情報取得
      final (getFreshCheckProductInfoRes, getDateLimitRes) = await (
        shelfFreshStub.getFreshCheckProductInfo(getFreshCheckProductInfoReq),
        shelfStub.getDateLimit(
          GetDateLimitRequest(
            storeCode: storeCode,
            standardFlag: ShelfType.valueOf(
              shelfInfo?.shelfType.value ?? ShelfType.SHELF_TYPE_STANDARD.value,
            ),
          ),
        ),
      ).wait;

      log('fetchFreshProductCheckInfo response $getFreshCheckProductInfoRes');
      log('getDateLimit response $getDateLimitRes');

      if (getFreshCheckProductInfoRes.productInfo.isEmpty) {
        throw UnknownException('商品情報がありません');
      }

      return ProductCheckInfo.fromFreshProductCheckInfoRes(
        getFreshCheckProductInfoRes,
        getDateLimitRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          ParallelWaitError() => UnknownException('生鮮棚情チェック情報,最大期限情報取得に失敗しました'),
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮新規商品一覧を削除して再取得
  Future<List<FreshNewProduct>> deleteFreshNewProductList({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required FreshNewProduct product,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final request = DeleteFreshNewProdInListRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      shelfNo: product.shelfNumberText,
      productCode: product.productCode.value,
      delFlag: FreshStatus.FRESH_STATUS_DELETE,
    );

    log('deleteFreshNewProductList request $request');

    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.deleteFreshNewProdInList(request);

      log('deleteFreshNewProductList response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }

      return response.product.map(FreshNewProduct.fromDeleteFreshNewProdInListRes).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮新規商品の登録を取得
  Future<FreshNewProductRegisterInfo> fetchFreshNewProductRegisterInfo({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required String? productCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final getFreshNewInputAlreadySetSpeedRequest = GetFreshNewInputAlreadySetSpeedRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      productCode: productCode,
    );
    final getDateLimitRequest = GetDateLimitRequest(
      storeCode: storeCode,
      standardFlag: ShelfType.valueOf(
        ShelfType.SHELF_TYPE_FRESH.value,
      ),
    );

    log('request $getFreshNewInputAlreadySetSpeedRequest');
    log('$getDateLimitRequest');

    final freshStub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final (getFreshNewInputAlreadySetSpeedRes, getDateLimitRes) = await (
        freshStub.getFreshNewInputAlreadySetSpeed(
          getFreshNewInputAlreadySetSpeedRequest,
        ),
        shelfStub.getDateLimit(
          getDateLimitRequest,
        ),
      ).wait;

      log('response $getFreshNewInputAlreadySetSpeedRes');
      log('$getDateLimitRes');

      return FreshNewProductRegisterInfo.fromFetchFreshProdNewInputListRes(
        getFreshNewInputAlreadySetSpeedRes,
        getDateLimitRes,
      );
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          ParallelWaitError() => UnknownException('新規生鮮商品の作業進捗情報,最大期限情報取得に失敗しました'),
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  //新規生鮮商品登録
  Future<void> setFreshNewProdInfo({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required DateTime nextExpiryDate,
    required String? shelfNumberText,
    required FreshNewProduct? product,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final request = SetFreshNewProdInfoRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      productCode: product?.productCode.value,
      shelfNo: shelfNumberText,
      nextBestBefore: Date(
        year: nextExpiryDate.year,
        month: nextExpiryDate.month,
        day: nextExpiryDate.day,
      ),
    );

    log('setFreshNewProdInfo request $request');

    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.setFreshNewProdInfo(request);

      log('setFreshNewProdInfo response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮新規商品一覧か削除して再取得
  Future<void> deleteFreshNewProductInfo({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required String? productCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final request = DeleteFreshNewProdInfoRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      productCode: productCode,
      delFlag: FreshStatus.FRESH_STATUS_END_OF_SALES.value,
    );

    log('deleteFreshNewProductList request $request');

    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.deleteFreshNewProdInfo(request);

      log('deleteFreshNewProductList response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮棚情報のアップデート
  Future<ProductCheckInfo?> updateFreshProductCheckInfo({
    required String storeCode,
    required AppUser? caller,
    required ShelfWorkInfo? shelfInfo,
    required String? productCode,
    required DateTime nextExpiryDate,
    required int? zoneCode,
    required int workStatusValue,
  }) async {
    final workStatus = WorkStatus.valueOf(workStatusValue);
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = UpdateFreshFlagRequest(
      storeCode: storeCode,
      shelfNo: shelfInfo?.shelfNumberText,
      zoneCode: zoneCode,
      shelfName: shelfInfo?.shelfName,
      checkPlanWeek: shelfInfo?.checkPlanWeek,
      shelfClassCode: shelfInfo?.shelfClassCode,
      productCode: productCode,
      nextBestBefore: Date(
        year: nextExpiryDate.year,
        month: nextExpiryDate.month,
        day: nextExpiryDate.day,
      ),
      state: workStatus,
    );

    log('updateFreshProductCheckInfo request $request');

    try {
      /// 棚情報取得
      final updateFreshFlagRes = await stub.updateFreshFlag(request);

      log('updateFreshProductCheckInfo response $updateFreshFlagRes');

      if (updateFreshFlagRes.productInfo.isEmpty) {
        return null;
      }

      /// 在庫情報と最大期限情報取得
      final getDateLimitRes = await shelfStub.getDateLimit(
        GetDateLimitRequest(
          storeCode: storeCode,
          standardFlag: ShelfType.valueOf(
            shelfInfo?.shelfType.value ?? ShelfType.SHELF_TYPE_STANDARD.value,
          ),
        ),
      );

      return ProductCheckInfo.fromUpdateFreshFlagResponse(
        updateFreshFlagRes,
        getDateLimitRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          ParallelWaitError() => UnknownException('在庫情報,最大期限情報取得に失敗しました'),
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮棚チェック情報の削除
  Future<void> deleteFreshProductCheckInfo({
    required String storeCode,
    required String? shelfNo,
    required int? zoneCode,
    required String? productCode,
    required AppUser? caller,
    required DateTime expirationDate,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = DeleteFreshProdInfoRequest(
      shelfNo: shelfNo,
      storeCode: storeCode,
      productCode: productCode,
      zoneCode: zoneCode,
      nextBestBefore: Date(
        year: expirationDate.year,
        month: expirationDate.month,
        day: expirationDate.day,
      ),
    );

    log('deleteFreshProductCheckInfo request $request');

    try {
      /// 棚情報削除
      final response = await stub.deleteFreshProdInfo(request);
      log('deleteFreshProductCheckInfo response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 値下賞味期限登録
  Future<void> registerExpiryDate({
    required String storeCode,
    required int? zoneCode,
    required ProductCheckInfo productCheckInfo,
    required AppUser? caller,
    required DateTime closeoutExpirationDate, //見切り商品の賞味期限
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final expirationDate = productCheckInfo.productInfo.expirationDate;
    final bestBeforeDateText = expirationDate != null ? DateFormat('yyyy-MM-dd').format(expirationDate) : '';
    final closeoutExpirationDateText = DateFormat('yy-MM-dd').format(closeoutExpirationDate);

    final request = InsertDCTLabelDataRequest(
      shelfNo: productCheckInfo.shelfSchedule.shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
      productCode: productCheckInfo.productInfo.productCode.value,
      bestBefore: bestBeforeDateText,
      checkPlanWeek: productCheckInfo.shelfSchedule.checkPlanWeek,
      dctLifetime: closeoutExpirationDateText,
    );

    log('registExpiryDate request $request');

    try {
      /// 棚情報削除
      final response = await stub.insertDCTLabelData(request);
      log('registExpiryDate response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮の値下一覧データ取得
  Future<List<FreshDiscountProduct>> fetchFreshDiscountList({
    required String storeCode,
    required int zoneCode,
    required String shelfNo,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = GetDCTLabelDataRequest(
      shelfNo: shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
    );

    log('fetchFreshDiscountList request $request');

    try {
      /// 棚情報削除
      final response = await stub.getDCTLabelData(request);
      log('fetchFreshDiscountList response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }

      return response.dctLabel.map(FreshDiscountProduct.fromFetchFreshDiscountListRes).toList();
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 値下完了
  Future<void> completeDiscount({
    required String storeCode,
    required String shelfNo,
    required int? zoneCode,
    required AppUser? caller,
    required List<String> productCodeList,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = SetFreshDCTLabelDataFinishRequest(
      shelfNo: shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
      productCode: productCodeList,
    );

    log('completeDiscount request $request');

    try {
      final response = await stub.setFreshDCTLabelDataFinish(request);
      log('completeDiscount response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮のメンテナンス商品取得
  Future<FreshMaintenanceProduct> fetchFreshMaintenanceProduct({
    required String storeCode,
    required int zoneCode,
    required String shelfNo,
    required String productCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final checkFreshProdInfoReq = CheckFreshProdInfoRequest(
      shelfNo: shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
      productCode: productCode,
    );

    final getFreshProductInfoReq = GetFreshProductInfoRequest(
      storeCode: storeCode,
      productCode: productCode,
    );

    log('checkFreshProdInfo request $checkFreshProdInfoReq');
    log('getFreshProductInfo request $getFreshProductInfoReq');

    try {
      /// 取得
      final (checkFreshProdInfoRes, getDategetFreshProductInfoRes) = await (
        stub.checkFreshProdInfo(checkFreshProdInfoReq),
        stub.getFreshProductInfo(getFreshProductInfoReq),
      ).wait;

      log('checkFreshProdInfo response $checkFreshProdInfoRes');
      log('getFreshProductInfo response $getDategetFreshProductInfoRes');

      if (checkFreshProdInfoRes.freshProdInfo.isEmpty || getDategetFreshProductInfoRes.freshProdInfo.isEmpty) {
        throw UnknownException('商品情報がありません');
      }

      return FreshMaintenanceProduct.fetchFreshMaintenanceProductRes(
        checkFreshProdInfoRes.freshProdInfo.first,
        getDategetFreshProductInfoRes.freshProdInfo.first,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮商品の連続スキャンの時、前の商品登録
  Future<void> registerFreshProduct({
    required String storeCode,
    required int zoneCode,
    required String shelfNo,
    required FreshMaintenanceProduct freshProductInfo,
    required AppUser? caller,
    required DateTime nextExpiryDate,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final request = SetFreshProdInfoRequest(
      shelfNo: shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
      productCode: freshProductInfo.productCode.value,
      nextBestBefore: Date(
        year: nextExpiryDate.year,
        month: nextExpiryDate.month,
        day: nextExpiryDate.day,
      ),
    );
    log('setFreshProdInfo request $request');

    try {
      final setFreshProdInfoRes = await stub.setFreshProdInfo(request);

      log('setFreshProdInfo response $setFreshProdInfoRes');

      if (setFreshProdInfoRes.code != '000') {
        throw UnknownException(setFreshProdInfoRes.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 生鮮のメンテナンスリストを取得
  Future<List<FreshMaintenanceListProduct>> fetchFreshMaintenanceProductList({
    required String storeCode,
    required String shelfNo,
    required int? zoneCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = GetFreshProdInfoListRequest(
      shelfNo: shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
    );
    log('fetchFreshProductList request $request');

    try {
      /// リクエスト
      final response = await stub.getFreshProdInfoList(request);
      log('fetchFreshProductList response $response');

      return response.freshProdInfo.map(FreshMaintenanceListProduct.fetchFreshMaintenanceListProductRes).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
