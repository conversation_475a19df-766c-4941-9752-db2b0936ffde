import 'package:grpc/grpc.dart';

import '../../../exceptions/app_exception.dart';

/// Grpcエラーの拡張
extension GrpcErrorExt on GrpcError {
  /// grpcエラーをハンドリングする
  AppException handleGrpcError() {
    switch (code) {
      case StatusCode.notFound:
        return ProductNotFoundException();
      case StatusCode.deadlineExceeded:
        return TimeOutException();
      case StatusCode.invalidArgument:
        return WrongProductCodeException();
      default:
        return UnknownException(
          'サーバーとの通信に失敗しました。しばらくしてから再度試してください。\nエラーコード: $code',
        );
    }
  }
}
