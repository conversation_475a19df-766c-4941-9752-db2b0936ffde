// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shelf_life_drug_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shelfLifeDrugRepositoryHash() => r'48dd17ff01e500e722bcee43d23626bfdba9a69c';

/// See also [shelfLifeDrugRepository].
@ProviderFor(shelfLifeDrugRepository)
final shelfLifeDrugRepositoryProvider = Provider<ShelfLifeDrugRepository>.internal(
  shelfLifeDrugRepository,
  name: r'shelfLifeDrugRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$shelfLifeDrugRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ShelfLifeDrugRepositoryRef = ProviderRef<ShelfLifeDrugRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
