import 'dart:developer';

import 'package:clock/clock.dart';
import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/shelf_life/v1/v1.dart';
import 'package:shinise_core_client/system/v1/system.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/check_cycle.dart';
import '../domain/check_min_cycle.dart';
import '../domain/product_code.dart';
import '../domain/request_special_product_info.dart';
import '../domain/special_product.dart';
import 'handle_grpc_error.dart';

part 'shelf_life_special_repository.g.dart';

/// プロバイダー
@Riverpod(keepAlive: true)
ShelfLifeSpecialRepository shelfLifeSpecialRepository(
  ShelfLifeSpecialRepositoryRef ref,
) =>
    ShelfLifeSpecialRepository();

/// 賞味期限チェック特殊商品のリポジトリ
class ShelfLifeSpecialRepository {
  /// constructor
  ShelfLifeSpecialRepository();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));
  final _uri = Env.getApiBaseUrl();

  /// チェック頻度取得
  Future<List<CheckCycle>> fetchCheckCycle(
    String? storeCode,
    AppUser? caller,
  ) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.getCheckCycle(GetCheckCycleRequest(storeCode: storeCode));
      log('fetchCheckCycle response $response');

      return response.checkCycle.map(CheckCycle.fromResponse).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 特殊商品の取得
  Future<SpecialProduct> fetchSpecialProduct({
    required String? storeCode,
    required String? productCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final getSpecialProdInfoReq = GetSpecialProdInfoRequest(
      storeCode: storeCode,
      productCode: productCode,
    );
    log('getSpecialProdInfo request $getSpecialProdInfoReq');

    try {
      /// リクエスト
      final getSpecialProdInfoRes = await stub.getSpecialProdInfo(getSpecialProdInfoReq);
      log('getSpecialProdInfo response $getSpecialProdInfoRes');

      if (getSpecialProdInfoRes.productInfo.isEmpty) {
        throw UnknownException('取り扱いのない商品のため、登録できません。');
      }

      final parsedProductCode = ProductCode.parseProductCode(
        getSpecialProdInfoRes.productInfo.first.productCode,
      );

      // 生鮮商品の場合は在庫情報は取得しない
      if (parsedProductCode.isFreshProduct) {
        return SpecialProduct.fromGetSpecialProdInfoResponse(
          getSpecialProdInfoRes.productInfo,
          null,
        );
      }

      final now = clock.now();
      final inventoryRequest = GetTmsts0085Request(
        productCode: productCode,
        storeCode: storeCode,
        date: GetTmsts0085Request_Date(
          year: now.year,
          month: now.month,
          day: now.day,
        ),
      );

      /// 在庫情報
      final inventoryRes = await systemStub.getTmsts0085(inventoryRequest);
      log('getTmsts0085 response $inventoryRequest');

      return SpecialProduct.fromGetSpecialProdInfoResponse(
        getSpecialProdInfoRes.productInfo,
        inventoryRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 定番商品の棚のチェック頻度取得
  Future<List<CheckMinCycle>> fetchMinCheckCycle({
    required String? storeCode,
    required String? productCode,
    required AppUser? caller,
    required String? shelfNo1,
    required String? shelfNo2,
    required String? shelfNo3,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    // ShelfNoについて、nullの場合は代わりに'0'を渡す
    final request = GetMinCheckCycleDataRequest(
      storeCode: storeCode,
      productCode: productCode,
      newShelfNo: shelfNo1 ?? '0',
      newShelfNo2: shelfNo2 ?? '0',
      newShelfNo3: shelfNo3 ?? '0',
    );

    log('getMinCheckCycleData request $request');

    try {
      /// リクエスト
      final response = await stub.getMinCheckCycleData(request);
      log('getMinCheckCycleData response $response');

      return response.cycle.map(CheckMinCycle.fromResponse).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 特殊商品連続スキャンの時、前の商品登録、次の商品情報取得
  Future<SpecialProduct> scanRegisterSpecialProduct({
    required String? storeCode,
    required AppUser? caller,
    required RequestSpecialProductInfo oldProductInfo,
    required RequestSpecialProductInfo newProductInfo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final callProcSpecialProdInfoReq = CallProcSpecialProdInfoRequest(
      storeCode: storeCode,
      newProductCode: newProductInfo.productCode?.value,
      oldProductCode: oldProductInfo.productCode?.value,
      oldShelfNo: oldProductInfo.shelfNo,
      oldShelfNo2: oldProductInfo.shelfNo2,
      oldShelfNo3: oldProductInfo.shelfNo3,
      oldZoneCode: oldProductInfo.zoneCode,
      oldZoneCode2: oldProductInfo.zoneCode2,
      oldZoneCode3: oldProductInfo.zoneCode3,
      newShelfNo: newProductInfo.shelfNo,
      newShelfNo2: newProductInfo.shelfNo2,
      newShelfNo3: newProductInfo.shelfNo3,
      newZoneCode: newProductInfo.zoneCode,
      newZoneCode2: newProductInfo.zoneCode2,
      newZoneCode3: newProductInfo.zoneCode3,
      oldCheckPlanWeek: oldProductInfo.checkPlanWeek,
      newCheckPlanWeek: newProductInfo.checkPlanWeek,
      oldCheckCycleCode: oldProductInfo.checkCycleCode ?? '0',
      newCheckCycleCode: newProductInfo.checkCycleCode ?? '1',
    );

    log('callProcSpecialProdInfo request $callProcSpecialProdInfoReq');

    try {
      /// リクエスト
      final callProcSpecialProdInfoRes = await stub.callProcSpecialProdInfo(callProcSpecialProdInfoReq);
      log('callProcSpecialProdInfo response $callProcSpecialProdInfoRes');

      // 生鮮商品の場合は在庫情報は取得しない
      if (newProductInfo.productCode?.isFreshProduct ?? false) {
        return SpecialProduct.fromCallProcSpecialProdInfoResponse(
          callProcSpecialProdInfoRes.productInfo,
          null,
        );
      }

      final now = clock.now();
      final inventoryRequest = GetTmsts0085Request(
        productCode: newProductInfo.productCode?.value,
        storeCode: storeCode,
        date: GetTmsts0085Request_Date(
          year: now.year,
          month: now.month,
          day: now.day,
        ),
      );

      /// 在庫情報
      final inventoryRes = await systemStub.getTmsts0085(inventoryRequest);
      log('getTmsts0085 response $inventoryRequest');

      return SpecialProduct.fromCallProcSpecialProdInfoResponse(
        callProcSpecialProdInfoRes.productInfo,
        inventoryRes,
      );
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 特殊商品情報登録
  Future<void> registerSpecialProduct({
    required String? storeCode,
    required AppUser? caller,
    required RequestSpecialProductInfo oldProductInfo,
    required RequestSpecialProductInfo newProductInfo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final setSpecialProdInfoReq = SetSpecialProdInfoRequest(
      storeCode: storeCode,
      productCode: newProductInfo.productCode?.value,
      oldShelfNo: oldProductInfo.shelfNo,
      oldShelfNo2: oldProductInfo.shelfNo2,
      oldShelfNo3: oldProductInfo.shelfNo3,
      oldZoneCode: oldProductInfo.zoneCode,
      oldZoneCode2: oldProductInfo.zoneCode2,
      oldZoneCode3: oldProductInfo.zoneCode3,
      newShelfNo: newProductInfo.shelfNo,
      newShelfNo2: newProductInfo.shelfNo2,
      newShelfNo3: newProductInfo.shelfNo3,
      newZoneCode: newProductInfo.zoneCode,
      newZoneCode2: newProductInfo.zoneCode2,
      newZoneCode3: newProductInfo.zoneCode3,
      oldCheckPlanWeek: oldProductInfo.checkPlanWeek,
      newCheckPlanWeek: newProductInfo.checkPlanWeek,
      oldCheckCycleCode: int.tryParse(oldProductInfo.checkCycleCode ?? '0'),
      newCheckCycleCode: int.tryParse(newProductInfo.checkCycleCode ?? '1'),
    );

    log('setSpecialProdInfo request $setSpecialProdInfoReq');

    try {
      /// リクエスト
      final response = await stub.setSpecialProdInfo(setSpecialProdInfoReq);

      log('setFreshNewProdInfo response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 特殊商品削除
  Future<void> deleteSpecialProduct({
    required String? storeCode,
    required AppUser? caller,
    required RequestSpecialProductInfo productInfo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final deleteSpecialProdInfoReq = DeleteSpecialProdInfoRequest(
      storeCode: storeCode,
      productCode: productInfo.productCode?.value,
      oldShelfNo: productInfo.shelfNo,
      oldShelfNo2: productInfo.shelfNo2,
      oldShelfNo3: productInfo.shelfNo3,
      oldZoneCode: productInfo.zoneCode,
      oldZoneCode2: productInfo.zoneCode2,
      oldZoneCode3: productInfo.zoneCode3,
      oldCheckPlanWeek: productInfo.checkPlanWeek,
      oldCheckCycleCode: int.tryParse(productInfo.checkCycleCode ?? ''),
    );

    log('deleteSpecialProdInfo request $deleteSpecialProdInfoReq');

    try {
      /// リクエスト
      final response = await stub.deleteSpecialProdInfo(deleteSpecialProdInfoReq);

      log('setFreshNewProdInfo response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
