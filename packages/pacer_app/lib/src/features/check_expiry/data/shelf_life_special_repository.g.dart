// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shelf_life_special_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shelfLifeSpecialRepositoryHash() => r'ab360e3eba7d8d2046b935d302ab6bb717314bae';

/// プロバイダー
///
/// Copied from [shelfLifeSpecialRepository].
@ProviderFor(shelfLifeSpecialRepository)
final shelfLifeSpecialRepositoryProvider = Provider<ShelfLifeSpecialRepository>.internal(
  shelfLifeSpecialRepository,
  name: r'shelfLifeSpecialRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$shelfLifeSpecialRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ShelfLifeSpecialRepositoryRef = ProviderRef<ShelfLifeSpecialRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
