import 'dart:developer';

import 'package:clock/clock.dart';
import 'package:collection/collection.dart';
import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/shelf_life/v1/v1.dart';
import 'package:shinise_core_client/shelf_life/v2/v2.dart' as v2;
import 'package:shinise_core_client/system/v1/system.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/check_shelf_info.dart';
import '../domain/check_worker.dart';
import '../domain/end_return_product.dart';
import '../domain/product_code.dart';
import '../domain/shelf_history.dart';
import '../domain/shelf_list_info.dart';
import '../domain/shelf_work_info.dart';
import '../domain/special_product_summary.dart';
import '../domain/zone_info.dart';
import '../domain/zoon_shelf_info.dart';
import '../presentation/common_widgets/work_stop_alert_dialog.dart';
import 'handle_grpc_error.dart';

part 'shelf_life_repository.g.dart';

/// プロバイダー
@Riverpod(keepAlive: true)
ShelfLifeRepository shelfLifeRepository(ShelfLifeRepositoryRef ref) => ShelfLifeRepository();

/// 賞味期限チェックリポジトリ
class ShelfLifeRepository {
  /// constructor
  ShelfLifeRepository();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));
  final _uri = Env.getApiBaseUrl();

  /// ゾーン一覧取得
  Future<List<Zone>> fetchZoneList({
    required String storeCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    log('request storeCode $storeCode');
    final request = GetZoneListRequest(storeCode: storeCode);
    log('fetchZoneList request $request');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.getZoneList(request);
      log('fetchZoneList response $response');

      return response.zoneInfo.map(Zone.fromGetZoonInfoResponse).sorted(Zone.sort).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ゾーンの棚リストを取得
  Future<List<ZoonShelfInfo>> fetchShelfListByZone({
    required String storeCode,
    required int? zoneCode,
    required int shelfTypeValue,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    log('request storeCode $storeCode');
    final request = GetShelfListByZoneRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      standardFlag: ShelfType.valueOf(shelfTypeValue),
    );
    log('fetchZoneListByZone request $request');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.getShelfListByZone(request);
      log('fetchZoneListByZone response $response');

      return response.shelf.map(ZoonShelfInfo.fromGetShelfListByZoneResponse).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 棚の賞味期限情報一覧の取得
  Future<List<ShelfWorkInfo>> fetchShelfNoList({
    required String storeCode,
    required int? zoneCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    log('request storeCode $storeCode , zoneCode $zoneCode');
    final request = v2.GetShelfNoListRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
    );
    log('fetchShelfNoList request $request');

    final stub = v2.ShelfLifeV2ServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.getShelfNoList(request);
      log('fetchShelfNoList response $response');
      final originalList = response.shelfInfo.map(ShelfWorkInfo.fromGetShelfNoListResponse).toList();
      final notDoneList = originalList.where((info) => !info.isDone).toList();
      final doneList = originalList.where((info) => info.isDone).toList();
      return [...notDoneList, ...doneList];
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 棚一覧取得（棚の最大賞味期限も取得）
  Future<ShelfListInfo> fetchShelfList({
    required String storeCode,
    required int shelfTypeValue,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfType = ShelfType.valueOf(shelfTypeValue);
    final getShelfListReq = GetShelfListRequest(storeCode: storeCode, standardFlag: [shelfType].nonNulls);
    final getDateLimitReq = GetDateLimitRequest(storeCode: storeCode, standardFlag: shelfType);

    log('fetchShelfList request $getShelfListReq');
    log('getDateLimit request $getDateLimitReq');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final (getShelfListRes, getDateLimitRes) = await (
        stub.getShelfList(getShelfListReq),
        stub.getDateLimit(getDateLimitReq),
      ).wait;

      log('fetchShelfList response $getShelfListRes');
      log('getDateLimit response $getDateLimitReq');

      return ShelfListInfo.fromGetShelfListResponse(
        getShelfListRes,
        getDateLimitRes,
      );
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          ParallelWaitError() => UnknownException('棚の賞味期限一覧情報,最大期限情報取得に失敗しました'),
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 棚一覧取得
  Future<List<ShelfInfo>> fetchShelfInfoList({
    required String storeCode,
    required int shelfTypeValue,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfType = ShelfType.valueOf(shelfTypeValue);
    final getShelfListReq = GetShelfListRequest(storeCode: storeCode, standardFlag: [shelfType].nonNulls);

    log('fetchShelfList request $getShelfListReq');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final getShelfListRes = await stub.getShelfList(getShelfListReq);

      log('fetchShelfList response $getShelfListRes');

      return getShelfListRes.shelf.map(ShelfInfo.fromGetShelfListResponse).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 賞味期限の中断処理
  Future<void> workStopShelfCheck({
    required String storeCode,
    required int? zoneCode,
    required String? shelfNumberText,
    required int? checkPlanWeek,
    required AppUser? caller,
    required WorkStopType workStopType,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final workProgress = switch (workStopType) {
      WorkStopType.abort => WorkProgress.WORK_PROGRESS_COMPLETE,
      WorkStopType.pause => WorkProgress.WORK_PROGRESS_PAUSE,
    };

    final request = GoBackShelfNoListRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      checkPlanWeek: checkPlanWeek,
      shelfNo: shelfNumberText,
      finish: workProgress,
    );

    log('workStopShelfCheck request $request');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.goBackShelfNoList(request);
      log('workStopShelfCheck response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 賞味期限の中断処理(値下げ)
  Future<void> workStopPriceReductionShelfCheck({
    required String storeCode,
    required int? zoneCode,
    required String? shelfNumberText,
    required int? checkPlanWeek,
    required AppUser? caller,
    required WorkStopType workStopType,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final workProgress = switch (workStopType) {
      WorkStopType.abort => SetFreshDCTShelfGoBackRequest_RollBack.ROLL_BACK_CANCEL,
      WorkStopType.pause => SetFreshDCTShelfGoBackRequest_RollBack.ROLL_BACK_PAUSE,
    };

    final request = SetFreshDCTShelfGoBackRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      checkPlanWeek: checkPlanWeek,
      shelfNo: shelfNumberText,
      finish: workProgress,
    );

    log('workStopShelfCheck request $request');

    final stub = ShelfLifeFreshServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.setFreshDCTShelfGoBack(request);
      log('workStopShelfCheck response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 賞味期限作業している担当者取得
  Future<WorkerInfo> fetchWorkmanager({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final request = GetWorkManagerRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      shelfNo: shelfInfo?.shelfNumberText,
      standardFlag: ShelfType.valueOf(
        shelfInfo?.shelfType.value ?? ShelfType.SHELF_TYPE_STANDARD.value,
      ),
    );

    log('fetchWorkmanager request $request');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.getWorkManager(request);
      log('fetchWorkmanager response $response');

      if (response.code != '000') {
        throw UnknownException(response.message);
      }

      return WorkerInfo.fromSetShelfWorkResponse(response);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 棚ステータス設定処理
  Future<void> setShelfWork({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final request = SetShelfWorkRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      shelfNo: shelfInfo?.shelfNumberText,
      checkPlanWeek: shelfInfo?.checkPlanWeek,
    );

    log('setShelfWork request $request');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.setShelfWork(request);
      log('setShelfWork response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 棚履歴情報を取得
  Future<ShelfHistory> fetchShelfHistory({
    required String storeCode,
    required String shelfNo,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final getShelfNameReq = GetShelfNameRequest(
      shelfNo: shelfNo,
      storeCode: storeCode,
    );
    final getShelfHistoryReq = GetShelfHistoryRequest(
      shelfNo: shelfNo,
      storeCode: storeCode,
    );

    log('getShelfName request $getShelfNameReq');
    log('getShelfHistory request $getShelfHistoryReq');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final (getShelfNameRes, getShelfHistoryRes) = await (
        stub.getShelfName(getShelfNameReq),
        stub.getShelfHistory(getShelfHistoryReq),
      ).wait;

      log('getShelfName response $getShelfNameRes');
      log('getShelfHistory response $getShelfHistoryRes');

      if (getShelfNameRes.shelf.isEmpty) {
        throw UnknownException('棚情報が取得できませんでした');
      }

      return ShelfHistory.fromResponse(
        getShelfNameRes.shelf.first,
        getShelfHistoryRes,
      );
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          ParallelWaitError() => UnknownException('棚の情報,棚の商品履歴情報に失敗しました'),
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 定番の棚情報を取得
  Future<StandardCheckShelfInfo> fetchStandardShelfInfo({
    required String storeCode,
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    // 清酒の場合は[shelfProductType]に「1」が、米の場合は「２」、それ以外の通常商品は「3」が入る
    final request = v2.GetShelfInfoRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      shelfClassCode: shelfInfo?.shelfClassCode.toString(),
      checkPlanWeek: shelfInfo?.checkPlanWeek,
      shelfNo: shelfInfo?.shelfNumberText,
      shelfName: shelfInfo?.shelfName,
      shelfProductType: switch (shelfInfo?.shelfProductType.value) {
        0 => v2.ShelfProductType.SHELF_PRODUCT_TYPE_UNSPECIFIED,
        1 => v2.ShelfProductType.SHELF_PRODUCT_TYPE_ALCOHOL,
        2 => v2.ShelfProductType.SHELF_PRODUCT_TYPE_RICE,
        3 => v2.ShelfProductType.SHELF_PRODUCT_TYPE_OTHER,
        _ => throw ArgumentError('Invalid shelf product type value: ${shelfInfo?.shelfProductType.value}'),
      },
    );

    log('fetchStandardShelfInfo request $request');

    final stub = v2.ShelfLifeV2ServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.getShelfInfo(request);
      log('fetchStandardShelfInfo response $response');

      return StandardCheckShelfInfo.fromResponse(response);
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 特殊商品一覧の取得
  Future<List<SpecialProductSummary>> fetchSpecialProductList({
    required String storeCode,
    required String? shelfNo,
    required int? zoneCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final request = GetSpecialListRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      shelfNo: shelfNo,
    );
    log('fetchSpecialProduct request $request');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.getSpecialList(request);
      log('fetchSpecialProduct response $response');

      return response.productInfo.map(SpecialProductSummary.fromResponse).toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 賞味期限作業の作業完了登録
  Future<void> finishShelfWork({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
  }) async {
    const complete = 2; //完了は固定で2
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = RegisterShelfFinishRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      shelfNo: shelfInfo?.shelfNumberText,
      checkPlanWeek: shelfInfo?.checkPlanWeek,
      finish: complete,
    );

    log('shelfWorkFinish request $request');

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// リクエスト
      final response = await stub.registerShelfFinish(request);
      log('shelfWorkFinish response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// エンド戻しの商品情報取得
  Future<EndReturnProduct> fetchEndReturnProduct({
    required String storeCode,
    required String productCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final getProductInfoReq = GetProductInfoRequest(
      storeCode: storeCode,
      productCode: productCode,
    );
    log('getProductInfo response $getProductInfoReq');

    final now = clock.now();
    final inventoryReq = GetTmsts0085Request(
      productCode: productCode,
      storeCode: storeCode,
      date: GetTmsts0085Request_Date(
        year: now.year,
        month: now.month,
        day: now.day,
      ),
    );

    log('getProductInfo request $getProductInfoReq');
    log('$inventoryReq');

    try {
      // エンド戻し商品情報取得
      final getProductInfoRes = await shelfStub.getProductInfo(getProductInfoReq);
      log('getProductInfo response $getProductInfoRes');

      if (getProductInfoRes.productInfo.isEmpty) {
        throw UnknownException('商品情報がありません');
      }

      final parsedProductCode = ProductCode.parseProductCode(
        productCode,
      );
      // 生鮮商品の場合は在庫情報は取得しない
      if (parsedProductCode.isFreshProduct) {
        return EndReturnProduct.fromGetProductInfoResponse(
          productCode,
          getProductInfoRes.productInfo.first,
          null,
        );
      }

      // 在庫情報
      final inventoryRes = await systemStub.getTmsts0085(inventoryReq);
      log('getTmsts0085Request response $inventoryRes');

      return EndReturnProduct.fromGetProductInfoResponse(
        productCode,
        getProductInfoRes.productInfo.first,
        inventoryRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
