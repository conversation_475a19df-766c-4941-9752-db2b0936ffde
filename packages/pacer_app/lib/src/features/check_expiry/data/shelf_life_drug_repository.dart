// ignore_for_file: public_member_api_docs

import 'dart:developer';

import 'package:clock/clock.dart';
import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart' show Date;
import 'package:shinise_core_client/shelf_life/v1/v1.dart';
import 'package:shinise_core_client/system/v1/system.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/drug_history.dart';
import '../domain/drug_product_info.dart';
import '../domain/product_check_info.dart';
import '../domain/shelf_work_info.dart';
import 'handle_grpc_error.dart';

part 'shelf_life_drug_repository.g.dart';

@Riverpod(keepAlive: true)
ShelfLifeDrugRepository shelfLifeDrugRepository(
  ShelfLifeDrugRepositoryRef ref,
) =>
    ShelfLifeDrugRepository();

class ShelfLifeDrugRepository {
  ShelfLifeDrugRepository();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));
  final _uri = Env.getApiBaseUrl();

  /// ドラッグ棚チェック情報取得
  Future<ProductCheckInfo> fetchDrugProductCheckInfo({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// 棚情報取得
      final (drugProductCheckInfoRes, getDateLimitRes) = await (
        shelfStub.getDrugProductCheckInfo(
          GetDrugProductCheckInfoRequest(
            storeCode: storeCode,
            zoneCode: zoneCode,
            shelfNo: shelfInfo?.shelfNumberText,
            shelfName: shelfInfo?.shelfName,
            checkPlanWeek: shelfInfo?.checkPlanWeek,
            shelfClassCode: shelfInfo?.shelfClassCode,
          ),
        ),
        shelfStub.getDateLimit(
          GetDateLimitRequest(
            storeCode: storeCode,
            standardFlag: ShelfType.valueOf(
              shelfInfo?.shelfType.value ?? ShelfType.SHELF_TYPE_STANDARD.value,
            ),
          ),
        ),
      ).wait;

      log('getDrugProductCheckInfo response $drugProductCheckInfoRes');
      log('getDateLimit response $getDateLimitRes');

      if (drugProductCheckInfoRes.productInfo.isEmpty) {
        throw UnknownException('商品情報がありません');
      }

      final now = clock.now();
      final inventoryRequest = GetTmsts0085Request(
        productCode: drugProductCheckInfoRes.productInfo.first.productCode,
        storeCode: storeCode,
        date: GetTmsts0085Request_Date(
          year: now.year,
          month: now.month,
          day: now.day,
        ),
      );

      /// 在庫情報
      final inventoryRes = await systemStub.getTmsts0085(inventoryRequest);

      return ProductCheckInfo.drugProductCheckInfoRes(
        drugProductCheckInfoRes,
        getDateLimitRes,
        inventoryRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          ParallelWaitError() => UnknownException('ドラッグ棚チェック情報,最大期限情報取得に失敗しました'),
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ドラッグ商品情報取得
  Future<DrugProductInfo> fetchDrugProductInfo({
    required String storeCode,
    required String? productCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final getDrugProductInfoReq = GetDrugProductInfoRequest(
      storeCode: storeCode,
      productCode: productCode,
    );

    final now = clock.now();
    final inventoryReq = GetTmsts0085Request(
      productCode: productCode,
      storeCode: storeCode,
      date: GetTmsts0085Request_Date(
        year: now.year,
        month: now.month,
        day: now.day,
      ),
    );

    log('fetchDrugProductInfo request $getDrugProductInfoReq');
    log('$inventoryReq');

    try {
      // 棚情報取得

      final getDrugProductInfoRes = await shelfStub.getDrugProductInfo(getDrugProductInfoReq);
      log('getDrugProductInfo response $getDrugProductInfoRes');

      if (getDrugProductInfoRes.productInfo.isEmpty) {
        throw UnknownException('商品情報がありません');
      }
      // 在庫情報
      final inventoryRes = await systemStub.getTmsts0085(inventoryReq);
      log('getTmsts0085Request response $inventoryRes');

      return DrugProductInfo.fromGetDrugProductInfoRes(
        getDrugProductInfoRes.productInfo,
        inventoryRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ドラッグ棚情報のアップデート
  Future<ProductCheckInfo?> updateDrugProductCheckInfo({
    required String storeCode,
    required AppUser? caller,
    required ShelfWorkInfo? shelfInfo,
    required String? productCode,
    required DateTime nextExpiryDate,
    required int? zoneCode,
    required bool? isMid,
    required bool? isEnd,
    required int workStatusValue,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final workStatus = WorkStatus.valueOf(workStatusValue);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = UpdateDrugFlagRequest(
      storeCode: storeCode,
      shelfNo: shelfInfo?.shelfNumberText,
      zoneCode: zoneCode,
      shelfName: shelfInfo?.shelfName,
      checkPlanWeek: shelfInfo?.checkPlanWeek,
      shelfClassCode: shelfInfo?.shelfClassCode,
      isMid: isMid,
      isEnd: isEnd,
      productCode: productCode,
      nextBestBefore: Date(
        year: nextExpiryDate.year,
        month: nextExpiryDate.month,
        day: nextExpiryDate.day,
      ),
      state: workStatus,
    );

    log('updateDrugProductCheckInfo request $request');

    try {
      /// 棚情報取得
      final updateDrugFlagRes = await shelfStub.updateDrugFlag(
        request,
      );

      log('updateDrugProductCheckInfo response $updateDrugFlagRes');

      if (updateDrugFlagRes.productInfo.isEmpty) {
        return null;
      }

      final now = clock.now();

      /// 在庫情報と最大期限情報取得
      final (getDateLimitRes, inventoryRes) = await (
        shelfStub.getDateLimit(
          GetDateLimitRequest(
            storeCode: storeCode,
            standardFlag: ShelfType.valueOf(
              shelfInfo?.shelfType.value ?? ShelfType.SHELF_TYPE_STANDARD.value,
            ),
          ),
        ),
        systemStub.getTmsts0085(
          GetTmsts0085Request(
            productCode: updateDrugFlagRes.productInfo.first.productCode,
            storeCode: storeCode,
            date: GetTmsts0085Request_Date(
              year: now.year,
              month: now.month,
              day: now.day,
            ),
          ),
        ),
      ).wait;

      return ProductCheckInfo.fromUpdateDrugFlagResponse(
        updateDrugFlagRes,
        getDateLimitRes,
        inventoryRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          ParallelWaitError() => UnknownException('在庫情報,最大期限情報取得に失敗しました'),
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ドラッグ棚チェック情報の削除
  Future<void> deleteDrugProductCheckInfo({
    required String storeCode,
    required String? shelfNo,
    required int? zoneCode,
    required String? productCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      /// 棚情報削除
      final response = await shelfStub.deleteDrugCheckProdInfo(
        DeleteDrugCheckProdInfoRequest(
          shelfNo: shelfNo,
          storeCode: storeCode,
          productCode: productCode,
          zoneCode: zoneCode,
        ),
      );
      log('deleteDrugCheckProdInfo response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ドラッグ商品情報の登録
  Future<void> setDrugProdInfo({
    required String storeCode,
    required DrugProductInfo drugProductInfo,
    required AppUser? caller,
    required bool isEnd,
    required bool isMid,
    required DateTime nextExpiryDate,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final oldNextExpirationDate = drugProductInfo.nextExpirationDate;

      final request = SetDrugProdInfoRequest(
        storeCode: storeCode,
        shelfNo1: drugProductInfo.shelfNumberText1,
        shelfNo2: drugProductInfo.shelfNumberText2,
        zoneCode1: drugProductInfo.zoneCode1,
        zoneCode2: drugProductInfo.zoneCode2,
        isEnd: isEnd,
        isMid: isMid,
        productCode: drugProductInfo.productCode.value,
        oldNextBestBefore: oldNextExpirationDate != null
            ? Date(
                year: oldNextExpirationDate.year,
                month: oldNextExpirationDate.month,
                day: oldNextExpirationDate.day,
              )
            : null,
        nextBestBefore: Date(
          year: nextExpiryDate.year,
          month: nextExpiryDate.month,
          day: nextExpiryDate.day,
        ),
      );

      log('setDrugProdInfo request $request');

      final response = await shelfStub.setDrugProdInfo(request);
      log('setDrugProdInfo response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ドラッグ商品情報の削除
  Future<void> deleteDrugProdInfo({
    required String storeCode,
    required DrugProductInfo drugProductInfo,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final oldNextExpirationDate = drugProductInfo.nextExpirationDate;

    try {
      final request = DeleteDrugProdInfoRequest(
        storeCode: storeCode,
        shelfNo1: drugProductInfo.shelfNumberText1,
        shelfNo2: drugProductInfo.shelfNumberText2,
        zoneCode1: drugProductInfo.zoneCode1,
        zoneCode2: drugProductInfo.zoneCode2,
        productCode: drugProductInfo.productCode.value,
        nextBestBefore: oldNextExpirationDate != null
            ? Date(
                year: oldNextExpirationDate.year,
                month: oldNextExpirationDate.month,
                day: oldNextExpirationDate.day,
              )
            : null,
      );

      log('deleteDrugProdInfo request $request');

      /// 棚情報削除
      final response = await shelfStub.deleteDrugProdInfo(
        request,
      );

      log('deleteDrugProdInfo response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ドラッグ商品の連続スキャンの時、前の商品登録、次の商品情報取得
  Future<DrugProductInfo> getProcDrugProdInfo({
    required String storeCode,
    required String newProductCode,
    required DrugProductInfo drugProductInfo,
    required AppUser? caller,
    required bool isEnd,
    required bool isMid,
    required DateTime nextExpiryDate,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    try {
      final oldNextExpirationDate = drugProductInfo.nextExpirationDate;

      final request = GetProcDrugProdInfoRequest(
        storeCode: storeCode,
        oldShelfNo1: drugProductInfo.shelfNumberText1,
        oldShelfNo2: drugProductInfo.shelfNumberText2,
        oldZoneCd1: drugProductInfo.zoneCode1,
        oldZoneCd2: drugProductInfo.zoneCode2,
        oldIsEnd: isEnd,
        oldIsMid: isMid,
        newProductCode: newProductCode,
        oldProductCode: drugProductInfo.productCode.value,
        oldNextBestBefore: oldNextExpirationDate != null
            ? Date(
                year: oldNextExpirationDate.year,
                month: oldNextExpirationDate.month,
                day: oldNextExpirationDate.day,
              )
            : null,
        nextBestBefore: Date(
          year: nextExpiryDate.year,
          month: nextExpiryDate.month,
          day: nextExpiryDate.day,
        ),
      );

      log('getProcDrugProdInfo request $request');

      final getProcDrugProdInfoRes = await shelfStub.getProcDrugProdInfo(request);

      log('getProcDrugProdInfo response $getProcDrugProdInfoRes');

      if (getProcDrugProdInfoRes.code != '000') {
        throw UnknownException(getProcDrugProdInfoRes.message);
      }
      if (getProcDrugProdInfoRes.productInfo.isEmpty) {
        throw UnknownException('商品情報がありません');
      }
      final now = clock.now();
      final inventoryReq = GetTmsts0085Request(
        productCode: getProcDrugProdInfoRes.productInfo.first.productCode,
        storeCode: storeCode,
        date: GetTmsts0085Request_Date(
          year: now.year,
          month: now.month,
          day: now.day,
        ),
      );

      // 在庫情報
      final inventoryRes = await systemStub.getTmsts0085(inventoryReq);
      log('getTmsts0085Request response $inventoryRes');

      return DrugProductInfo.fromGetProcDrugProdInfoResponseRes(
        getProcDrugProdInfoRes.productInfo,
        inventoryRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ドラッグ商品情報の作業完了取得
  Future<List<DrugHistory>> getDrugHistory({
    required String storeCode,
    required String? productCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    try {
      final request = GetDrugHistoryRequest(
        storeCode: storeCode,
        productCode: productCode,
      );

      log('getDrugHistory request $request');

      final getProcDrugProdInfoRes = await shelfStub.getDrugHistory(request);

      log('getDrugHistory response $getProcDrugProdInfoRes');

      if (getProcDrugProdInfoRes.code != '000') {
        throw UnknownException(getProcDrugProdInfoRes.message);
      }
      if (getProcDrugProdInfoRes.productInfo.isEmpty) {
        throw UnknownException('商品情報がありません');
      }

      final now = clock.now();
      final inventoryReq = GetTmsts0085Request(
        productCode: getProcDrugProdInfoRes.productInfo.first.productCode,
        storeCode: storeCode,
        date: GetTmsts0085Request_Date(
          year: now.year,
          month: now.month,
          day: now.day,
        ),
      );

      // 在庫情報
      final inventoryRes = await systemStub.getTmsts0085(inventoryReq);
      log('getTmsts0085Request response $inventoryRes');

      return getProcDrugProdInfoRes.productInfo
          .map(
            (history) => DrugHistory.fromResponse(
              history,
              inventoryRes,
            ),
          )
          .toList();
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
