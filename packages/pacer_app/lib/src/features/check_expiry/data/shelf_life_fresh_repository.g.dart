// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shelf_life_fresh_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shelfLifeFreshRepositoryHash() => r'e7fd82d1028538aee6f49a3741749ae36de00a68';

/// See also [shelfLifeFreshRepository].
@ProviderFor(shelfLifeFreshRepository)
final shelfLifeFreshRepositoryProvider = Provider<ShelfLifeFreshRepository>.internal(
  shelfLifeFreshRepository,
  name: r'shelfLifeFreshRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$shelfLifeFreshRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ShelfLifeFreshRepositoryRef = ProviderRef<ShelfLifeFreshRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
