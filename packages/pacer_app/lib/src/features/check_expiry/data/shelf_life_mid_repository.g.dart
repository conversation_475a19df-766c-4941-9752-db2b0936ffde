// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shelf_life_mid_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shelfLifeMidRepositoryHash() => r'ee6ea8b4260e3c0a05df87dea7a5e3652b7f81d7';

/// プロバイダー
///
/// Copied from [shelfLifeMidRepository].
@ProviderFor(shelfLifeMidRepository)
final shelfLifeMidRepositoryProvider = Provider<ShelfLifeMidRepository>.internal(
  shelfLifeMidRepository,
  name: r'shelfLifeMidRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$shelfLifeMidRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ShelfLifeMidRepositoryRef = ProviderRef<ShelfLifeMidRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
