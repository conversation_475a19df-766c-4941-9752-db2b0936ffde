import 'dart:developer';

import 'package:clock/clock.dart';
import 'package:collection/collection.dart';
import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart' show Date;
import 'package:shinise_core_client/shelf_life/v1/v1.dart';
import 'package:shinise_core_client/system/v1/system.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/mid_maintenance_list_product.dart';
import '../domain/mid_maintenance_product.dart';
import '../domain/product_check_info.dart';
import '../domain/product_code.dart' as code;
import '../domain/shelf_work_info.dart';
import 'handle_grpc_error.dart';

part 'shelf_life_mid_repository.g.dart';

/// プロバイダー
@Riverpod(keepAlive: true)
ShelfLifeMidRepository shelfLifeMidRepository(ShelfLifeMidRepositoryRef ref) => ShelfLifeMidRepository();

/// ミッドの賞味期限チェックリポジトリ
class ShelfLifeMidRepository {
  /// constructor
  ShelfLifeMidRepository();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));
  final _uri = Env.getApiBaseUrl();

  ///  ミッド棚チェック情報取得
  Future<ProductCheckInfo> fetchMidProductCheckInfo({
    required String storeCode,
    required AppUser? caller,
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final getMidProductInfoReq = GetMidProductInfoRequest(
      storeCode: storeCode,
      zoneCode: zoneCode,
      shelfNo: shelfInfo?.shelfNumberText,
      shelfName: shelfInfo?.shelfName,
      checkPlanWeek: shelfInfo?.checkPlanWeek,
      shelfClassCode: shelfInfo?.shelfClassCode.toString(),
    );

    final getDateLimitReq = GetDateLimitRequest(
      storeCode: storeCode,
      standardFlag: ShelfType.valueOf(
        shelfInfo?.shelfType.value ?? ShelfType.SHELF_TYPE_STANDARD.value,
      ),
    );

    log('getMidProductInfo request $getMidProductInfoReq');
    log('getDateLimit request $getDateLimitReq');

    try {
      /// 棚情報取得
      final (midProductCheckInfoRes, getDateLimitRes) = await (
        shelfStub.getMidProductInfo(getMidProductInfoReq),
        shelfStub.getDateLimit(getDateLimitReq),
      ).wait;

      log('getMidProductInfo response $midProductCheckInfoRes');
      log('getDateLimit response $getDateLimitRes');

      if (midProductCheckInfoRes.productInfo.isEmpty) {
        throw UnknownException('商品情報がありません');
      }

      final parsedProductCode = code.ProductCode.parseProductCode(
        midProductCheckInfoRes.productInfo.first.productCode,
      );
      // 生鮮商品の場合は在庫情報は取得しない
      if (parsedProductCode.isFreshProduct) {
        return ProductCheckInfo.fromMidProductCheckInfoRes(
          midProductCheckInfoRes,
          getDateLimitRes,
          null,
        );
      }

      final now = clock.now();
      final inventoryRequest = GetTmsts0085Request(
        productCode: midProductCheckInfoRes.productInfo.first.productCode,
        storeCode: storeCode,
        date: GetTmsts0085Request_Date(
          year: now.year,
          month: now.month,
          day: now.day,
        ),
      );

      /// 在庫情報
      final inventoryRes = await systemStub.getTmsts0085(inventoryRequest);

      return ProductCheckInfo.fromMidProductCheckInfoRes(
        midProductCheckInfoRes,
        getDateLimitRes,
        inventoryRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          ParallelWaitError() => UnknownException('ミッド棚チェック情報,最大期限情報取得に失敗しました'),
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 賞味期限チェックの更新（ミッド）
  Future<void> updateMidProductCheckInfo({
    required String storeCode,
    required AppUser? caller,
    required ShelfWorkInfo? shelfInfo,
    required String? productCode,
    required DateTime nextExpiryDate,
    required int? zoneCode,
    required int workStatusValue,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final workStatus = WorkStatus.valueOf(workStatusValue);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = UpdateMidFlagRequest(
      storeCode: storeCode,
      shelfNo: shelfInfo?.shelfNumberText,
      zoneCode: zoneCode,
      shelfName: shelfInfo?.shelfName,
      checkPlanWeek: shelfInfo?.checkPlanWeek,
      shelfClassCode: shelfInfo?.shelfClassCode,
      productCode: productCode,
      nextBestBefore: Date(
        year: nextExpiryDate.year,
        month: nextExpiryDate.month,
        day: nextExpiryDate.day,
      ),
      state: workStatus,
    );

    log('updateMidProductCheckInfo request $request');

    try {
      /// 棚情報取得
      final response = await shelfStub.updateMidFlag(request);

      log('updateMidProductCheckInfo response $response');

      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 賞味期限チェックの削除（ミッド）
  Future<void> deleteMidProductCheckInfo({
    required String storeCode,
    required String? shelfNo,
    required int? zoneCode,
    required String? productCode,
    required AppUser? caller,
    required DateTime? nextExpiryDate,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final shelfStub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final request = DeleteMidProdInfoRequest(
      shelfNo: shelfNo,
      storeCode: storeCode,
      productCode: productCode,
      zoneCode: zoneCode,
      nextBestBefore: Date(
        year: nextExpiryDate?.year,
        month: nextExpiryDate?.month,
        day: nextExpiryDate?.day,
      ),
    );

    log('deleteMidProductCheckInfo request $request');

    try {
      /// 棚情報削除
      final response = await shelfStub.deleteMidProdInfo(request);
      log('deleteMidCheckProdInfo response $response');
      if (response.code != '000') {
        throw UnknownException(response.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ミッドのメンテナンス商品取得
  Future<MidMaintenanceProduct> fetchMidMaintenanceProduct({
    required String storeCode,
    required int zoneCode,
    required String shelfNo,
    required String productCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final checkMidProdInfoReq = CheckMidProdInfoRequest(
      shelfNo: shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
      productCode: productCode,
    );

    final getProductInfoReq = GetProductInfoRequest(
      storeCode: storeCode,
      productCode: productCode,
    );

    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    log('checkMidProdInfo request $checkMidProdInfoReq');
    log('getProductInfo request $getProductInfoReq');

    try {
      /// 取得
      final (checkMidProdInfoRes, getProductInfoRes) = await (
        stub.checkMidProdInfo(checkMidProdInfoReq),
        stub.getProductInfo(getProductInfoReq),
      ).wait;

      log('checkMidProdInfo response $checkMidProdInfoRes');
      log('getProductInfo response $getProductInfoRes');

      if (checkMidProdInfoRes.checkInfo.isEmpty || getProductInfoRes.productInfo.isEmpty) {
        throw UnknownException('商品情報がありません');
      }

      final parsedProductCode = code.ProductCode.parseProductCode(
        productCode,
      );
      // 生鮮商品の場合は在庫情報は取得しない
      if (parsedProductCode.isFreshProduct) {
        return MidMaintenanceProduct.fetchMidMaintenanceProductRes(
          checkMidProdInfoRes.checkInfo.first,
          getProductInfoRes.productInfo.first,
          null,
        );
      }

      final now = clock.now();
      final inventoryReq = GetTmsts0085Request(
        productCode: productCode,
        storeCode: storeCode,
        date: GetTmsts0085Request_Date(
          year: now.year,
          month: now.month,
          day: now.day,
        ),
      );

      log('getTmsts0085 request $inventoryReq');

      // 在庫情報
      final inventoryRes = await systemStub.getTmsts0085(inventoryReq);
      log('getTmsts0085 response $inventoryRes');

      return MidMaintenanceProduct.fetchMidMaintenanceProductRes(
        checkMidProdInfoRes.checkInfo.first,
        getProductInfoRes.productInfo.first,
        inventoryRes,
      );
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ミッドのメンテナンスリストを取得
  Future<List<MidMaintenanceListProduct>> fetchMidMaintenanceProductList({
    required String storeCode,
    required String shelfNo,
    required int? zoneCode,
    required AppUser? caller,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final systemStub = SystemServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = GetMidProdInfoListRequest(
      shelfNo: shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
    );
    log('fetchMidProductList request $request');

    try {
      /// リクエスト
      final response = await stub.getMidProdInfoList(request);
      log('fetchMidProductList response $response');

      //在庫の取得
      final now = clock.now();
      final requestFutureList = response.productInfo
          .map((product) {
            final parsedProductCode = code.ProductCode.parseProductCode(
              product.productCode,
            );
            if (parsedProductCode.isFreshProduct) {
              return null;
            }
            final inventoryRequest = GetTmsts0085Request(
              productCode: product.productCode,
              storeCode: storeCode,
              date: GetTmsts0085Request_Date(
                year: now.year,
                month: now.month,
                day: now.day,
              ),
            );
            return systemStub.getTmsts0085(inventoryRequest);
          })
          .nonNulls
          .toList();

      final responseList = await Future.wait(requestFutureList);

      log('getTmsts0085Request response $responseList');

      return response.productInfo
          .map(
            (product) => MidMaintenanceListProduct.fetchMidMaintenanceListProductRes(
              product,
              responseList
                  .firstWhereOrNull(
                    (inventory) => inventory.rec.itemCode == product.productCode,
                  )
                  ?.rec
                  .logicalQy
                  .toInt(),
            ),
          )
          .toList();
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ミッド商品の連続スキャンの時、前の商品登録
  Future<void> scanRegisterMidProduct({
    required String storeCode,
    required int zoneCode,
    required String shelfNo,
    required String newProductCode,
    required MidMaintenanceProduct midProductInfo,
    required AppUser? caller,
    required DateTime nextExpiryDate,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final request = CallProcMidProdInfoRequest(
      shelfNo: shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
      newProductCode: newProductCode,
      oldProductCode: midProductInfo.productCode.value,
      nextBestBefore: Date(
        year: nextExpiryDate.year,
        month: nextExpiryDate.month,
        day: nextExpiryDate.day,
      ),
    );
    log('callProcMidProdInfo request $request');

    try {
      final callProcMidProdInfoRes = await stub.callProcMidProdInfo(request);

      log('callProcMidProdInfo response $callProcMidProdInfoRes');

      if (callProcMidProdInfoRes.code != '000') {
        throw UnknownException(callProcMidProdInfoRes.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ミッド商品登録
  Future<void> registerMidProduct({
    required String storeCode,
    required int zoneCode,
    required String shelfNo,
    required String productCode,
    required AppUser? caller,
    required DateTime nextExpiryDate,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final stub = ShelfServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    final request = SetMidProdInfoRequest(
      shelfNo: shelfNo,
      zoneCode: zoneCode,
      storeCode: storeCode,
      productCode: productCode,
      nextBestBefore: Date(
        year: nextExpiryDate.year,
        month: nextExpiryDate.month,
        day: nextExpiryDate.day,
      ),
    );
    log('setMidProdInfo request $request');

    try {
      final setMidProdInfoRes = await stub.setMidProdInfo(request);

      log('setMidProdInfo response $setMidProdInfoRes');

      if (setMidProdInfoRes.code != '000') {
        throw UnknownException(setMidProdInfoRes.message);
      }
    } catch (e, stack) {
      log('error $e');
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
