import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';
import 'package:shinise_core_client/system/v1/system.pb.dart';

import '../../../exceptions/app_exception.dart';
import 'enum/shelf_type.dart';
import 'product_code.dart';

/// 特殊商品の棚情報の最大数
const maxSpecialShelfInfoCount = 3;

/// 特殊商品情報
class SpecialProduct extends Equatable {
  /// constructor
  const SpecialProduct({
    required this.productCode,
    required this.productName,
    required this.brandName,
    required this.specName,
    required this.shelfInfoList,
    required this.zoneName,
    required this.checkCycle,
    required this.checkCycleName,
    required this.checkPlanWeek,
    required this.isDone,
    required this.shelfType,
    required this.stock,
  });

  /// constructor
  factory SpecialProduct.fromGetSpecialProdInfoResponse(
    List<GetSpecialProdInfoResponse_ProductInfo> getSpecialProdInfoResList,
    GetTmsts0085Response? inventoryRes,
  ) {
    final productInfo = getSpecialProdInfoResList.firstOrNull;
    if (productInfo == null) {
      throw UnknownException('商品情報がありません');
    }

    return SpecialProduct(
      productCode: ProductCode.parseProductCode(productInfo.productCode),
      productName: productInfo.productName,
      brandName: productInfo.brandName,
      specName: productInfo.specName,
      zoneName: productInfo.zoneName,
      checkCycle: int.tryParse(productInfo.checkCycle) ?? 0,
      checkCycleName: productInfo.checkCycleName,
      checkPlanWeek: int.tryParse(productInfo.checkPlanWeek) ?? 0,
      isDone: productInfo.isDone,
      shelfType: ShelfType.fromValue(productInfo.standardFlag.value),
      stock: inventoryRes?.rec.logicalQy.toInt(),
      shelfInfoList: getSpecialProdInfoResList
          .map(
            (e) => SpecialShelfInfo(
              shelfNumberText: e.shelfNo.isEmpty ? null : e.shelfNo,
              shelfName: e.shelfName.isEmpty ? null : e.shelfName,
              zoneCode: int.tryParse(e.zoneCode),
              zoneName: e.zoneName.isEmpty ? null : e.zoneName,
            ),
          )
          .toList(),
    );
  }

  /// constructor
  factory SpecialProduct.fromCallProcSpecialProdInfoResponse(
    List<CallProcSpecialProdInfoResponse_ProductInfo> procSpecialProdInfoResList,
    GetTmsts0085Response? inventoryRes,
  ) {
    final productInfo = procSpecialProdInfoResList.firstOrNull;
    if (productInfo == null) {
      throw UnknownException('商品情報がありません');
    }

    return SpecialProduct(
      productCode: ProductCode.parseProductCode(productInfo.productCode),
      productName: productInfo.productName,
      brandName: productInfo.brandName,
      specName: productInfo.specName,
      zoneName: productInfo.zoneName,
      checkCycle: productInfo.checkCycle,
      checkCycleName: productInfo.checkCycleName,
      checkPlanWeek: productInfo.checkPlanWeek,
      isDone: productInfo.isDone,
      shelfType: ShelfType.fromValue(productInfo.standardFlag.value),
      stock: inventoryRes?.rec.logicalQy.toInt(),
      shelfInfoList: procSpecialProdInfoResList
          .map(
            (e) => SpecialShelfInfo(
              shelfNumberText: e.shelfNo.isEmpty ? null : e.shelfNo,
              shelfName: e.shelfName.isEmpty ? null : e.shelfName,
              zoneCode: e.zoneCode == 0 ? null : e.zoneCode,
              zoneName: e.zoneName.isEmpty ? null : e.zoneName,
            ),
          )
          .toList(),
    );
  }

  /// 商品コード
  final ProductCode productCode;

  /// 商品名
  final String productName;

  /// ブランド名
  final String brandName;

  ///  規格名
  final String specName;

  /// 特殊商品の棚情報リスト
  final List<SpecialShelfInfo> shelfInfoList;

  ///ゾーン名
  final String zoneName;

  ///チェック週
  final int checkCycle;

  ///チェック週の名前
  final String checkCycleName;

  /// 賞味期限チェック予定週 YYYYWW(年＋週)
  final int checkPlanWeek;

  /// チェックの完了フラグ
  final bool isDone;

  /// 棚タイプ
  final ShelfType shelfType;

  /// 在庫数
  final int? stock;

  @override
  List<Object?> get props => [
        productCode,
        productName,
        brandName,
        specName,
        shelfInfoList,
        zoneName,
        checkCycle,
        checkCycleName,
        checkPlanWeek,
        isDone,
        shelfType,
        stock,
      ];
}

/// 特殊商品の棚情報
class SpecialShelfInfo with EquatableMixin {
  /// constructor
  SpecialShelfInfo({
    required this.shelfNumberText,
    required this.shelfName,
    required this.zoneCode,
    required this.zoneName,
  });

  /// constructor
  factory SpecialShelfInfo.empty() {
    return SpecialShelfInfo(
      shelfNumberText: null,
      shelfName: null,
      zoneCode: null,
      zoneName: null,
    );
  }

  /// 棚番号
  final String? shelfNumberText;

  /// 棚名称
  final String? shelfName;

  ///ゾーンコード
  final int? zoneCode;

  /// ゾーン名称
  final String? zoneName;

  @override
  List<Object?> get props => [
        shelfNumberText,
        shelfName,
        zoneCode,
        zoneName,
      ];
}
