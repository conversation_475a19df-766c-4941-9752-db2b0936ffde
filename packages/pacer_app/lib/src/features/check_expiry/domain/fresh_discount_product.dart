// ignore_for_file: public_member_api_docs

import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life_fresh.pb.dart';

import 'product_code.dart';

/// 生鮮新規商品
class FreshDiscountProduct with EquatableMixin {
  FreshDiscountProduct({
    required this.branchCode,
    required this.zoneCode,
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.discountLifeDate,
    required this.shelfNumberText,
    required this.checkPlanWeek,
  });

  factory FreshDiscountProduct.fromFetchFreshDiscountListRes(
    GetDCTLabelDataResponse_DCTLabel discountProductInfo,
  ) =>
      FreshDiscountProduct(
        branchCode: discountProductInfo.branchCode,
        zoneCode: discountProductInfo.zoneCode,
        productCode: ProductCode.parseProductCode(discountProductInfo.productCode),
        productName: discountProductInfo.productName,
        specName: discountProductInfo.specName,
        brandName: discountProductInfo.brandName,
        discountLifeDate: DateFormat('yyyy/MM/dd').tryParse(discountProductInfo.discountLife),
        shelfNumberText: discountProductInfo.shelfNo,
        checkPlanWeek: discountProductInfo.checkPlanWeek,
      );

  // 店舗コード
  final String branchCode;
  // ゾーンコード
  final String zoneCode;
  // 商品コード
  final ProductCode productCode;
  // 商品名
  final String productName;
  // 規格
  final String specName;
  // ブランド名
  final String brandName;
  // 次回賞味期限の週の日付
  final DateTime? discountLifeDate;

  // 棚番号
  final String shelfNumberText;
  // 賞味期限チェック予定週 YYYYWW(年＋週)
  final int? checkPlanWeek;

  @override
  List<Object?> get props => [
        branchCode,
        zoneCode,
        productCode,
        productName,
        specName,
        brandName,
        discountLifeDate,
        shelfNumberText,
        checkPlanWeek,
      ];
}
