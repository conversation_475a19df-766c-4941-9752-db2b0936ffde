// ignore_for_file: public_member_api_docs

import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';
import 'package:shinise_core_client/system/v1/system.pb.dart';

import '../../../exceptions/app_exception.dart';
import '../../order/domain/inventory.dart';
import 'product_code.dart';

/// 商品情報
class DrugProductInfo with EquatableMixin {
  DrugProductInfo({
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.nextExpirationDate,
    required this.checkDate,
    required this.shelfNumberText1,
    required this.shelfNumberText2,
    required this.shelfName1,
    required this.shelfName2,
    required this.zoneCode1,
    required this.zoneCode2,
    required this.isEnd,
    required this.isMid,
    required this.inventoryInfo,
    required this.midcheckDate,
    required this.isExist,
    this.isFragile,
    this.isShow,
  });

  factory DrugProductInfo.fromGetDrugProductInfoRes(
    List<GetDrugProductInfoResponse_ProductInfo> productInfoList,
    GetTmsts0085Response inventoryRes,
  ) {
    if (productInfoList.isEmpty) {
      throw UnknownException('商品情報がありません');
    }
    final productInfo = productInfoList.first;
    //productInfoが2つより多く返ってくることはない
    //productInfoが複数ある場合、shelfNo、shelfName、zoneCode2以外のパラメータは同じである
    final productInfo2 = productInfoList.length > 1 ? productInfoList[1] : null;

    return DrugProductInfo(
      productCode: ProductCode.parseProductCode(productInfo.productCode),
      productName: productInfo.productName,
      specName: productInfo.specName,
      brandName: productInfo.brandName,
      nextExpirationDate: DateTime.tryParse(productInfo.nextBestBefore),
      checkDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo.checkDay),
      midcheckDate: DateTime.tryParse(productInfo.midCheckDay),
      isEnd: productInfo.isEnd,
      isMid: productInfo.isMid,
      inventoryInfo: Inventory.fromGRPC(inventoryRes),
      shelfNumberText1: productInfo.shelfNo,
      shelfNumberText2: productInfo2?.shelfNo ?? '',
      shelfName1: productInfo.shelfName,
      shelfName2: productInfo2 != null ? productInfo2.shelfName : '',
      zoneCode1: productInfo.zoneCode,
      zoneCode2: productInfo2?.zoneCode ?? 0,
      isExist: productInfo.isExist,
      isFragile: productInfo.isFragile,
    );
  }

  factory DrugProductInfo.fromGetProcDrugProdInfoResponseRes(
    List<GetProcDrugProdInfoResponse_ProductInfo> productInfoList,
    GetTmsts0085Response inventoryRes,
  ) {
    if (productInfoList.isEmpty) {
      throw UnknownException('商品情報がありません');
    }
    final productInfo = productInfoList.first;
    //productInfoが2つより多く返ってくることはない
    //productInfoが複数ある場合、shelfNo、shelfName、zoneCode2以外のパラメータは同じである
    final productInfo2 = productInfoList.length > 1 ? productInfoList[1] : null;

    return DrugProductInfo(
      productCode: ProductCode.parseProductCode(productInfo.productCode),
      productName: productInfo.productName,
      specName: productInfo.specName,
      brandName: productInfo.brandName,
      nextExpirationDate: DateTime.tryParse(productInfo.nextBestBefore),
      checkDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo.checkDay),
      midcheckDate: DateTime.tryParse(productInfo.midCheckDay),
      isEnd: productInfo.isEnd,
      isMid: productInfo.isMid,
      inventoryInfo: Inventory.fromGRPC(inventoryRes),
      shelfNumberText1: productInfo.shelfNo,
      shelfNumberText2: productInfo2?.shelfNo ?? '',
      shelfName1: productInfo.shelfName,
      shelfName2: productInfo2 != null ? productInfo2.shelfName : '',
      zoneCode1: productInfo.zoneCode,
      zoneCode2: productInfo2?.zoneCode ?? 0,
      isExist: productInfo.isExist,
      isShow: productInfo.isShow,
    );
  }

  // 商品コード
  final ProductCode productCode;
  // 商品名
  final String productName;
  // 規格
  final String specName;
  // ブランド
  final String brandName;
  //棚番号1
  final String shelfNumberText1;
  //棚番号2
  final String shelfNumberText2;
  //棚名称1
  final String shelfName1;
  //棚名称2
  final String shelfName2;
  //ゾーンコード（棚割の階層)
  final int zoneCode1;
  //ゾーンコード（棚割の階層)
  final int zoneCode2;
  // 次回賞味期限の日時
  final DateTime? nextExpirationDate;
  // チェックサイクルによって、この商品のチェック日を取得(日付フォーマット:YYYY年MM月DD月)
  final DateTime? checkDate;
  // チェックサイクルによって、この商品のチェック日を取得(日付フォーマット：YYYY-MM-DD 00:00:00)
  final DateTime? midcheckDate;
  // エンド有無 0無し、１有り
  final bool isEnd;
  // ミッド有無 0無し、１有り
  final bool isMid;
  // 0：該当商品を設定なし　1：該当商品設定あり
  final bool isExist;
  // 生鮮フラグ 1：生鮮商品 0：非生鮮
  final bool? isFragile;
  // 0 : 新規商品自動登録場合、表示しない 1: 表示する
  final bool? isShow;
  // 商品の在庫情報
  final Inventory inventoryInfo;

  @override
  List<Object?> get props => [
        productCode,
        productName,
        specName,
        brandName,
        nextExpirationDate,
        checkDate,
        shelfNumberText1,
        shelfNumberText2,
        shelfName1,
        shelfName2,
        zoneCode1,
        zoneCode2,
        isEnd,
        isMid,
        inventoryInfo,
        midcheckDate,
        isExist,
        isFragile,
        isShow,
      ];
}
