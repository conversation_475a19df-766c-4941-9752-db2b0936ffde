import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';

import 'enum/shelf_type.dart';
import 'product_code.dart';

/// 棚の履歴情報
class ShelfHistory with EquatableMixin {
  ///constructor
  const ShelfHistory({
    required this.productList,
    required this.shelfNumberText,
    required this.shelfName,
    required this.zoneCode,
    required this.zoneName,
    required this.shelfType,
  });

  /// constructor
  factory ShelfHistory.fromResponse(
    GetShelfNameResponse_Shelf getShelfNameRes,
    GetShelfHistoryResponse getShelfHistoryRes,
  ) =>
      ShelfHistory(
        productList: getShelfHistoryRes.shelfHistory.map(HistoryProduct.fromResponse).toList(),
        shelfNumberText: getShelfNameRes.shelfNo,
        shelfName: getShelfNameRes.shelfName,
        zoneCode: getShelfNameRes.zoneCode,
        zoneName: getShelfNameRes.zoneName,
        shelfType: ShelfType.fromValue(getShelfNameRes.standardFlag.value),
      );

  /// 商品リスト
  final List<HistoryProduct> productList;

  /// 棚番号
  final String shelfNumberText;

  /// 棚名称
  final String shelfName;

  /// ゾーンコード
  final int zoneCode;

  /// ゾーン名称
  final String zoneName;

  /// 棚タイプ
  final ShelfType shelfType;

  @override
  List<Object?> get props => [
        productList,
        shelfNumberText,
        shelfName,
        zoneCode,
        zoneName,
        shelfType,
      ];
}

/// 商品の履歴情報
class HistoryProduct with EquatableMixin {
  /// constructor
  const HistoryProduct({
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.startDate,
    required this.author,
    required this.employeeName,
    required this.shelfType,
    required this.checkPlanWeek,
    required this.checkWeek,
  });

  /// constructor
  factory HistoryProduct.fromResponse(
    GetShelfHistoryResponse_ShelfHistory shelfInfoRes,
  ) =>
      HistoryProduct(
        productCode: ProductCode.parseProductCode(shelfInfoRes.productCode),
        productName: shelfInfoRes.productName,
        specName: shelfInfoRes.specName,
        brandName: shelfInfoRes.brandName,
        startDate: DateTime.tryParse(shelfInfoRes.startDate),
        author: shelfInfoRes.operator,
        employeeName: shelfInfoRes.employeeName,
        shelfType: ShelfType.fromValue(shelfInfoRes.shelfFlag.value),
        checkPlanWeek: shelfInfoRes.checkPlanWeek,
        checkWeek: shelfInfoRes.checkWeek,
      );

  /// 商品コード
  final ProductCode productCode;

  /// 商品名称
  final String productName;

  /// 商品規格
  final String specName;

  /// ブランド名称
  final String brandName;

  /// 開始時間
  final DateTime? startDate;

  ///作業者コード
  final String author;

  ///作業者名前
  final String employeeName;

  /// 棚タイプ
  final ShelfType shelfType;

  ///賞味期限チェック予定週 YYYYWW(年＋週)
  final int checkPlanWeek;

  ///賞味期限の作業チェック週　YYYYWW(年＋週)
  final String checkWeek;

  @override
  List<Object?> get props => [
        productCode,
        productName,
        specName,
        brandName,
        startDate,
        author,
        employeeName,
        shelfType,
        checkPlanWeek,
        checkWeek,
      ];
}
