import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life_fresh.pb.dart';
import 'package:shinise_core_client/system/v1/system.pb.dart';

import '../../../exceptions/app_exception.dart';
import '../../order/domain/inventory.dart';
import 'product_info.dart';
import 'shelf_schedule.dart';

/// 商品チェック情報
class ProductCheckInfo with EquatableMixin {
  /// constructor
  const ProductCheckInfo({
    required this.productInfo,
    required this.shelfSchedule,
    required this.limitDate,
    required this.inventoryInfo,
  });

  /// constructor
  factory ProductCheckInfo.drugProductCheckInfoRes(
    GetDrugProductCheckInfoResponse drugProductCheckInfoRes,
    GetDateLimitResponse getDateLimitRes,
    GetTmsts0085Response inventoryRes,
  ) {
    final productInfos = drugProductCheckInfoRes.productInfo.map(ProductInfo.fromCheckInfoResponseProductInfo).toList();

    final shelfSchedules =
        drugProductCheckInfoRes.shelfSchedule.map(ShelfSchedule.fromCheckInfoResponseShelfSchedule).toList();

    return ProductCheckInfo.checkedParse(
      productInfos: productInfos,
      shelfSchedules: shelfSchedules,
      getDateLimitRes: getDateLimitRes,
      inventoryRes: inventoryRes,
    );
  }

  /// constructor
  factory ProductCheckInfo.fromUpdateDrugFlagResponse(
    UpdateDrugFlagResponse updateDrugFlagResponse,
    GetDateLimitResponse getDateLimitRes,
    GetTmsts0085Response inventoryRes,
  ) {
    final productInfos =
        updateDrugFlagResponse.productInfo.map(ProductInfo.fromUpdateDrugFlagResponseProductInfo).toList();

    final shelfSchedules =
        updateDrugFlagResponse.shelfSchedule.map(ShelfSchedule.fromUpdateDrugFlagResponseShelfSchedule).toList();

    return ProductCheckInfo.checkedParse(
      productInfos: productInfos,
      shelfSchedules: shelfSchedules,
      getDateLimitRes: getDateLimitRes,
      inventoryRes: inventoryRes,
    );
  }

  /// constructor
  factory ProductCheckInfo.fromFreshProductCheckInfoRes(
    GetFreshCheckProductInfoResponse freshProductCheckInfoRes,
    GetDateLimitResponse getDateLimitRes,
  ) {
    final productInfos = freshProductCheckInfoRes.productInfo.map(ProductInfo.fromGetFreshCheckProductInfo).toList();

    final shelfSchedules =
        freshProductCheckInfoRes.productPlan.map(ShelfSchedule.fromGetFreshCheckProductInfo).toList();

    return ProductCheckInfo.checkedParse(
      productInfos: productInfos,
      shelfSchedules: shelfSchedules,
      getDateLimitRes: getDateLimitRes,
      inventoryRes: null,
    );
  }

  /// constructor
  factory ProductCheckInfo.fromUpdateFreshFlagResponse(
    UpdateFreshFlagResponse updateFreshFlagResponse,
    GetDateLimitResponse getDateLimitRes,
  ) {
    final productInfos =
        updateFreshFlagResponse.productInfo.map(ProductInfo.fromUpdateFreshFlagResponseProductInfo).toList();

    final shelfSchedules =
        updateFreshFlagResponse.productPlan.map(ShelfSchedule.fromUpdateFreshFlagResponseShelfSchedule).toList();

    return ProductCheckInfo.checkedParse(
      productInfos: productInfos,
      shelfSchedules: shelfSchedules,
      getDateLimitRes: getDateLimitRes,
      inventoryRes: null,
    );
  }

  /// constructor
  factory ProductCheckInfo.checkedParse({
    required List<ProductInfo> productInfos,
    required List<ShelfSchedule> shelfSchedules,
    required GetDateLimitResponse getDateLimitRes,
    required GetTmsts0085Response? inventoryRes,
  }) {
    return switch ((productInfos.isEmpty, shelfSchedules.isEmpty)) {
      (true, false) => throw UnknownException('productInfo cannot be empty'),
      (false, true) => throw UnknownException('shelfSchedule cannot be empty'),
      (true, true) => throw UnknownException('productInfo and shelfSchedule cannot be empty'),
      _ => ProductCheckInfo(
          productInfo: productInfos.first,
          shelfSchedule: shelfSchedules.first,
          limitDate: DateFormat('yyyy-MM-dd').tryParse(getDateLimitRes.limitDate),
          inventoryInfo: inventoryRes != null ? Inventory.fromGRPC(inventoryRes) : null,
        )
    };
  }

  /// constructor
  factory ProductCheckInfo.fromMidProductCheckInfoRes(
    GetMidProductInfoResponse getMidProductInfoRes,
    GetDateLimitResponse getDateLimitRes,
    GetTmsts0085Response? inventoryRes,
  ) {
    final productInfos = getMidProductInfoRes.productInfo.map(ProductInfo.fromGetMidCheckProductInfo).toList();

    final shelfSchedules = getMidProductInfoRes.shelfSchedule.map(ShelfSchedule.fromGetMidCheckProductInfo).toList();

    return ProductCheckInfo.checkedParse(
      productInfos: productInfos,
      shelfSchedules: shelfSchedules,
      getDateLimitRes: getDateLimitRes,
      inventoryRes: inventoryRes,
    );
  }

  /// 商品情報
  final ProductInfo productInfo;

  /// 最大期限取得
  final ShelfSchedule shelfSchedule;

  /// ミッド・生鮮・ドラッグ次回賞味期限最大日付制御
  final DateTime? limitDate;

  /// 商品の在庫情報
  final Inventory? inventoryInfo;

  @override
  List<Object?> get props => [
        productInfo,
        shelfSchedule,
        limitDate,
        inventoryInfo,
      ];
}
