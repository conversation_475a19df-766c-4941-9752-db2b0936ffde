// ignore_for_file: public_member_api_docs

import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';
import 'package:shinise_core_client/system/v1/system.pb.dart';

import '../../order/domain/inventory.dart';
import 'product_code.dart';

///　ドラッグ履歴
class DrugHistory with EquatableMixin {
  DrugHistory({
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.shelfNumberText,
    required this.checkPlanWeek,
    required this.checkWeek,
    required this.startDate,
    required this.author,
    required this.employeeName,
    required this.inventoryInfo,
  });

  factory DrugHistory.fromResponse(
    GetDrugHistoryResponse_ProductInfo productInfo,
    GetTmsts0085Response inventoryRes,
  ) {
    return DrugHistory(
      productCode: ProductCode.parseProductCode(productInfo.productCode),
      productName: productInfo.productName,
      specName: productInfo.specName,
      brandName: productInfo.brandName,
      shelfNumberText: productInfo.shelfNo,
      checkPlanWeek: productInfo.checkPlanWeek.toString(),
      checkWeek: productInfo.checkWeek,
      startDate: parseDateTimeText(productInfo.startDate),
      author: productInfo.author,
      employeeName: productInfo.employeeName,
      inventoryInfo: Inventory.fromGRPC(inventoryRes),
    );
  }

  // 商品コード
  final ProductCode productCode;
  // 商品名
  final String productName;
  // 規格
  final String specName;
  // ブランド
  final String brandName;
  //棚番号
  final String shelfNumberText;
  //賞味期限チェック予定週 YYYYWW(年＋週)
  final String checkPlanWeek;
  //賞味期限の作業チェック週　YYYYWW(年＋週)
  final String checkWeek;
  //作業開始時間
  final DateTime? startDate;
  //作業者コード
  final String author;
  //作業者名前
  final String employeeName;
  // 商品の在庫情報
  final Inventory inventoryInfo;

  // '2023-4-21 15:09'のような文字列をDateTimeに変換する
  static DateTime? parseDateTimeText(String input) {
    try {
      final parts = input.split(' ');
      final dateParts = parts.first.split('-');
      final timeParts = parts[1].split(':');

      final year = int.parse(dateParts.first);
      final month = int.parse(dateParts[1]);
      final day = int.parse(dateParts[2]);
      final hour = int.parse(timeParts.first);
      final minute = int.parse(timeParts[1]);

      return DateTime(year, month, day, hour, minute);
    } catch (e) {
      return null;
    }
  }

  @override
  List<Object?> get props => [
        productCode,
        productName,
        specName,
        brandName,
        checkWeek,
        shelfNumberText,
        checkPlanWeek,
        startDate,
        author,
        employeeName,
        inventoryInfo,
      ];
}
