// ignore_for_file: public_member_api_docs

import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v2/v2.dart' as v2;

import 'enum/shelf_product_type.dart';
import 'enum/shelf_type.dart';
import 'enum/work_status.dart';

// shelfNoに「過」「得」も文字列があるかどうかでフラグ
// 例 : 18001 【過】, 21028【特】【過】
const String excessFlgText = '【過】';
const String specialFlgText = '【特】';
const String discountFlgText = '【値下】';

class ShelfWorkInfo with EquatableMixin {
  const ShelfWorkInfo({
    required this.shelfNumberText,
    required this.shelfName,
    required this.checkFlag,
    required this.checkPlanWeek,
    required this.shelfClassCode,
    required this.shelfType,
    required this.shelfProductType,
    required this.isExcess,
    required this.isSpecial,
    required this.isDiscount,
  });

  factory ShelfWorkInfo.fromGetShelfNoListResponse(
    v2.GetShelfNoListResponse_ShelfInfo shelfInfoRes,
  ) =>
      ShelfWorkInfo(
        shelfNumberText: parseShelfNumberText(shelfInfoRes.shelfNo),
        shelfName: shelfInfoRes.shelfName,
        checkFlag: WorkStatus.fromValue(shelfInfoRes.checkFlag.value),
        checkPlanWeek: shelfInfoRes.checkPlanWeek,
        shelfClassCode: shelfInfoRes.shelfClassCd,
        shelfType: ShelfType.fromValue(shelfInfoRes.standardFlag.value),
        shelfProductType: ShelfProductType.fromValue(shelfInfoRes.shelfProductType.value),
        isExcess: shelfInfoRes.shelfNo.contains(excessFlgText),
        isSpecial: shelfInfoRes.shelfNo.contains(specialFlgText),
        isDiscount: shelfInfoRes.shelfNo.contains(discountFlgText),
      );

  /// 棚番号
  final String shelfNumberText;
  // 棚名称
  final String shelfName;
  // 作業フラグ ０：未作業 １：作業中 2:正常完了 3：作業遅れ完了
  final WorkStatus checkFlag;
  // 賞味期限チェック予定週 YYYYWW(年＋週)
  final int checkPlanWeek;
  // 棚名称CD
  final int shelfClassCode;
  // 棚フラグstandardFlag: 0定番、１エンド、２ミッド、３エンド混合 4 生鮮 5 ドラッグ、40:新規データ、41:値下データ
  final ShelfType shelfType;

  /// See also [ShelfProductType]
  final ShelfProductType shelfProductType;
  // 【過】かどうか
  final bool isExcess;
  // 【特】かどうか
  final bool isSpecial;
  // 【値下】かどうか
  final bool isDiscount;
  // 完了フラグ
  bool get isDone => checkFlag == WorkStatus.normalCompletion || checkFlag == WorkStatus.delayedCompletion;
  // 作業中フラグ
  bool get isWorking => checkFlag == WorkStatus.working;

  static String parseShelfNumberText(String shelfNo) {
    // 正規表現で大文字または小文字のアルファベットまたは数字に続く数字部分を抽出
    final regExp = RegExp(r'[A-Za-z0-9]\d*');

    return regExp.stringMatch(shelfNo) ?? '';
  }

  @override
  List<Object?> get props => [
        shelfNumberText,
        shelfName,
        checkFlag,
        checkPlanWeek,
        shelfClassCode,
        shelfType,
        shelfProductType,
        isExcess,
        isSpecial,
        isDiscount,
      ];
}
