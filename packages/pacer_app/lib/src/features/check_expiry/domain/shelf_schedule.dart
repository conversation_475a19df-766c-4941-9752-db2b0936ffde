import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life_fresh.pb.dart';

/// 棚のスケジュール情報
class ShelfSchedule with EquatableMixin {
  /// constructor
  ShelfSchedule({
    required this.endTime,
    required this.shelfNo,
    required this.shelfName,
    required this.checkPlanWeek,
  });

  /// constructor
  factory ShelfSchedule.fromCheckInfoResponseShelfSchedule(
    GetDrugProductCheckInfoResponse_ShelfSchedule selfSchedule,
  ) =>
      ShelfSchedule(
        endTime: DateTime.parse(selfSchedule.endTime),
        shelfNo: selfSchedule.shelfNo,
        shelfName: selfSchedule.shelfNo,
        checkPlanWeek: selfSchedule.checkPlanWeek,
      );

  /// constructor
  factory ShelfSchedule.fromUpdateDrugFlagResponseShelfSchedule(
    UpdateDrugFlagResponse_ShelfSchedule selfSchedule,
  ) =>
      ShelfSchedule(
        endTime: DateTime.parse(selfSchedule.endTime),
        shelfNo: selfSchedule.shelfNo,
        shelfName: selfSchedule.shelfNo,
        checkPlanWeek: int.parse(selfSchedule.checkPlanWeek),
      );

  /// constructor
  factory ShelfSchedule.fromGetFreshCheckProductInfo(
    GetFreshCheckProductInfoResponse_ProductPlan selfSchedule,
  ) =>
      ShelfSchedule(
        endTime: DateTime.parse(selfSchedule.endTime),
        shelfNo: selfSchedule.shelfNo,
        shelfName: selfSchedule.shelfNo,
        checkPlanWeek: selfSchedule.checkPlanWeek,
      );

  /// constructor
  factory ShelfSchedule.fromUpdateFreshFlagResponseShelfSchedule(
    UpdateFreshFlagResponse_ProductPlan selfSchedule,
  ) =>
      ShelfSchedule(
        endTime: DateTime.parse(selfSchedule.endTime),
        shelfNo: selfSchedule.shelfNo,
        shelfName: selfSchedule.shelfNo,
        checkPlanWeek: selfSchedule.checkPlanWeek,
      );

  /// constructor
  factory ShelfSchedule.fromGetMidCheckProductInfo(
    GetMidProductInfoResponse_ShelfSchedule selfSchedule,
  ) =>
      ShelfSchedule(
        endTime: DateTime.parse(selfSchedule.endTime),
        shelfNo: selfSchedule.shelfNo,
        shelfName: selfSchedule.shelfNo,
        checkPlanWeek: selfSchedule.checkPlanWeek,
      );

  /// 賞味期限チェック作業終了予定時間
  final DateTime endTime;

  /// 棚番号
  final String shelfNo;

  /// 棚名称
  final String shelfName;

  /// 賞味期限チェック予定週 YYYYWW(年＋週)
  final int checkPlanWeek;

  @override
  List<Object?> get props => [
        endTime,
        shelfNo,
        shelfName,
        checkPlanWeek,
      ];
}
