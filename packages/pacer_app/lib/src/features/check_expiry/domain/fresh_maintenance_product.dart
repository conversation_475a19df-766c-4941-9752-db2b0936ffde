import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life_fresh.pb.dart';

import 'product_code.dart';

/// 生鮮メンテナンス商品
class FreshMaintenanceProduct with EquatableMixin {
  /// constructor
  FreshMaintenanceProduct({
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.checkDay,
    required this.midCheckDay,
    required this.freshFlg,
    required this.amount,
    required this.nextExpirationDate,
    required this.checkdate,
  });

  /// constructor
  factory FreshMaintenanceProduct.fetchFreshMaintenanceProductRes(
    CheckFreshProdInfoResponse_FreshProdInfo checkFreshProdInfo,
    GetFreshProductInfoResponse_FreshProdInfo productInfo,
  ) =>
      FreshMaintenanceProduct(
        productCode: ProductCode.parseProductCode(checkFreshProdInfo.jan),
        productName: productInfo.productName,
        specName: productInfo.specName,
        brandName: productInfo.brandName,
        nextExpirationDate: DateFormat('yyyy-MM-dd').tryParse(checkFreshProdInfo.nextBestBefore),
        checkDay: productInfo.checkDay,
        midCheckDay: productInfo.midCheckDay,
        freshFlg: productInfo.freshFlg,
        amount: int.tryParse(checkFreshProdInfo.amount) ?? 0,
        checkdate: DateFormat('yyyy/MM/dd').tryParse(checkFreshProdInfo.checkDate),
      );

  /// 商品名
  final String productName;

  /// コード
  final ProductCode productCode;

  /// 規格
  final String specName;

  /// ブランド名
  final String brandName;

  /// チェックサイクルによって、この商品のチェック日を取得
  final String checkDay;

  ///チェックサイクルによって、この商品のチェック日を取得
  final String midCheckDay;

  ///生鮮フラグ　0：非生鮮　1：生鮮商品
  final String freshFlg;

  /// 該当商品未作業件数
  final int amount;

  /// 次回賞味期限の日時
  final DateTime? nextExpirationDate;

  /// 賞味期限のチェック日
  final DateTime? checkdate;

  /// 登録済みかどうか
  bool get isRegistered => amount > 0;

  /// 生鮮商品かどうか
  bool get isFreshProduct => freshFlg == '1';

  @override
  List<Object?> get props => [
        productCode,
        productName,
        specName,
        brandName,
        checkDay,
        midCheckDay,
        freshFlg,
        amount,
        nextExpirationDate,
        checkdate,
      ];
}
