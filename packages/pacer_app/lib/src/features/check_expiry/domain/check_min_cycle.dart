import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';

/// 定番棚のチェック頻度情報
class CheckMinCycle extends Equatable {
  /// constructor
  const CheckMinCycle({
    required this.shelfName,
    required this.minCycle,
  });

  /// constructor
  factory CheckMinCycle.fromResponse(
    GetMinCheckCycleDataResponse_Cycle response,
  ) {
    return CheckMinCycle(
      shelfName: response.cycleFlag,
      minCycle: response.minCycle,
    );
  }

  /// 棚名
  final String shelfName;

  /// サイクル週数
  final String minCycle;

  @override
  List<Object> get props => [
        shelfName,
        minCycle,
      ];
}
