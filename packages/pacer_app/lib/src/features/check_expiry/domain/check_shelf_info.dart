import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';

import 'package:shinise_core_client/shelf_life/v2/v2.dart';

import '../../../exceptions/app_exception.dart';
import 'product_code.dart';

/// 通常商品の判別用の商品名
const standardBestBeforeProductName = '通常賞味期限チェックの商品分';

/// 賞味期限チェックをする棚情報
class StandardCheckShelfInfo with EquatableMixin {
  /// constructor
  StandardCheckShelfInfo({
    required this.endTime,
    required this.shelfNumberText,
    required this.shelfName,
    required this.checkPlanWeek,
    required this.productInfoList,
  });

  /// constructor
  factory StandardCheckShelfInfo.fromResponse(
    GetShelfInfoResponse response,
  ) {
    final shelfInfo = response.shelfInfo.firstOrNull;
    if (shelfInfo == null) {
      throw UnknownException('棚情報が取得できませんでした');
    }
    return StandardCheckShelfInfo(
      endTime: DateTime.tryParse(shelfInfo.endTime),
      shelfNumberText: shelfInfo.shelfNo,
      shelfName: shelfInfo.shelfName,
      checkPlanWeek: shelfInfo.checkPlanWeek,
      productInfoList: response.productInfo.map(StandardProductInfo.fromResponse).toList(),
    );
  }

  /// 賞味期限チェック作業終了予定時間
  final DateTime? endTime;

  /// 棚番号
  final String shelfNumberText;

  /// 棚名称
  final String shelfName;

  /// 賞味期限の週の日付
  final int checkPlanWeek;

  /// 商品情報
  final List<StandardProductInfo> productInfoList;

  /// 特殊商品情報を賞味期限でグループ化したもの
  List<GroupedProductInfo> get groupedProductInfoList {
    final groupedMap = groupBy(productInfoList, (StandardProductInfo p) => p.bestBefore);

    return groupedMap.entries
        .map(
          (entry) => GroupedProductInfo(
            bestBefore: entry.key,
            producedDate: entry.value.firstOrNull?.producedDate,
            productInfoList: entry.value,
          ),
        )
        .toList();
  }

  @override
  List<Object?> get props => [
        endTime,
        shelfNumberText,
        shelfName,
        checkPlanWeek,
        productInfoList,
      ];
}

/// 定番商品
class StandardProductInfo with EquatableMixin {
  /// constructor
  StandardProductInfo({
    required this.bestBefore,
    required this.producedDate,
    required this.productCode,
    required this.productName,
    required this.isSpecial,
  });

  /// constructor
  factory StandardProductInfo.fromResponse(
    GetShelfInfoResponse_ProductInfo response,
  ) {
    return StandardProductInfo(
      bestBefore: DateTime.tryParse(response.bestBefore),
      producedDate: DateTime.tryParse(response.birth),
      productCode: ProductCode(response.productCode),
      productName: response.productName,
      isSpecial: response.productName != standardBestBeforeProductName,
    );
  }

  /// 賞味期限チェック予定週 YYYYWW(年＋週)
  final DateTime? bestBefore;

  /// 賞味期限チェックにて「清酒」や「米」の場合に用いる期限データ
  final DateTime? producedDate;

  /// 商品コード
  final ProductCode productCode;

  /// 商品コード
  final String productName;

  /// 特殊商品かどうか
  final bool isSpecial;

  @override
  List<Object?> get props => [
        bestBefore,
        producedDate,
        productCode,
        productName,
        isSpecial,
      ];
}

/// 特殊商品一覧
class GroupedProductInfo with EquatableMixin {
  /// constructor
  GroupedProductInfo({
    required this.bestBefore,
    required this.producedDate,
    required this.productInfoList,
  });

  /// 賞味期限チェック予定週 YYYYWW(年＋週)
  final DateTime? bestBefore;

  /// 賞味期限チェックにて「清酒」や「米」の場合に用いる期限データ
  final DateTime? producedDate;

  /// 商品情報
  final List<StandardProductInfo> productInfoList;

  /// 特殊商品リスト
  List<StandardProductInfo> get specialProductInfoList => productInfoList.where((e) => e.isSpecial).toList();

  /// 通常商品リスト
  List<StandardProductInfo> get standardProductInfoList => productInfoList.where((e) => !e.isSpecial).toList();

  /// 清酒は「2023年4月」、米は「2023年4月1日」という期限表記を行う必要がある。それを判断するためのflag
  bool get hasProducedDate => producedDate != null;

  @override
  List<Object?> get props => [
        bestBefore,
        producedDate,
        productInfoList,
      ];
}
