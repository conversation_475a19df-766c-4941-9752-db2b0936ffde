import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/common.pbenum.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life_fresh.pb.dart';

import 'product_code.dart';

/// 生鮮新規商品
class FreshNewProduct with EquatableMixin {
  /// constructor
  FreshNewProduct({
    required this.branchCode,
    required this.zoneCode,
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.nextBestBeforeDate,
    required this.shelfNumberText,
    required this.checkPlanWeek,
    required this.shelfName,
    required this.checkFlag,
  });

  /// constructor
  factory FreshNewProduct.fromFetchFreshProdNewInputListRes(
    GetFreshProdNewInputListResponse_ProdNewInput productInfo,
  ) =>
      FreshNewProduct(
        branchCode: productInfo.branchCode,
        zoneCode: productInfo.zoneCode,
        productCode: ProductCode.parseProductCode(productInfo.productCode),
        productName: productInfo.productName,
        specName: productInfo.specNameRead,
        brandName: productInfo.brandName,
        nextBestBeforeDate: DateFormat('yyyy/MM/dd').tryParse(productInfo.nextBestBefore),
        shelfName: productInfo.shelfName,
        shelfNumberText: productInfo.shelfNo,
        checkPlanWeek: int.tryParse(productInfo.checkPlanWeek),
        checkFlag: productInfo.checkFlag,
      );

  /// constructor
  factory FreshNewProduct.fromDeleteFreshNewProdInListRes(
    DeleteFreshNewProdInListResponse_Product productInfo,
  ) =>
      FreshNewProduct(
        branchCode: productInfo.branchCode,
        zoneCode: productInfo.zoneCode,
        productCode: ProductCode.parseProductCode(productInfo.productCode),
        productName: productInfo.productName,
        specName: productInfo.specNameRead,
        brandName: productInfo.brandName,
        nextBestBeforeDate: DateFormat('yyyy/MM/dd').tryParse(productInfo.nextBestBefore),
        shelfName: productInfo.shelfName,
        shelfNumberText: productInfo.shelfNo,
        checkPlanWeek: int.tryParse(productInfo.checkPlanWeek),
        checkFlag: productInfo.checkFlag,
      );

  /// 店舗コード
  final String branchCode;

  /// ゾーンコード
  final String zoneCode;

  /// 商品コード
  final ProductCode productCode;

  /// 商品名
  final String productName;

  /// 規格
  final String specName;

  /// ブランド名
  final String brandName;

  /// 次回賞味期限の週の日付
  final DateTime? nextBestBeforeDate;

  /// 棚名
  final String shelfName;

  /// 棚番号
  final String shelfNumberText;

  /// 賞味期限チェック予定週 YYYYWW(年＋週)
  final int? checkPlanWeek;

  /// 作業フラグ ０：未作業 １：作業中 2:正常完了 3：作業遅れ完了
  final WorkStatus checkFlag;

  @override
  List<Object?> get props => [
        branchCode,
        zoneCode,
        productCode,
        productName,
        specName,
        brandName,
        nextBestBeforeDate,
        shelfNumberText,
        checkPlanWeek,
        shelfName,
        checkFlag,
      ];
}
