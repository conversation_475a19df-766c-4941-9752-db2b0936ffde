// ignore_for_file: public_member_api_docs

import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';

import 'enum/shelf_type.dart';

class ZoonShelfInfo with EquatableMixin {
  const ZoonShelfInfo({
    required this.shelfNumberText,
    required this.shelfName,
    required this.zoneName,
    required this.zoneCode,
    required this.shelfType,
  });

  factory ZoonShelfInfo.fromGetShelfListByZoneResponse(
    GetShelfListByZoneResponse_Shelf shelfListByZoneRes,
  ) =>
      ZoonShelfInfo(
        shelfNumberText: shelfListByZoneRes.shelfNo,
        shelfName: shelfListByZoneRes.shelfName,
        zoneName: shelfListByZoneRes.zoneName,
        zoneCode: shelfListByZoneRes.zoneCode,
        shelfType: ShelfType.fromValue(shelfListByZoneRes.standardFlag.value),
      );

  final String shelfNumberText;
  final String shelfName;
  final String zoneName;
  final int zoneCode;
  final ShelfType shelfType;

  @override
  List<Object?> get props => [shelfNumberText, shelfName, zoneName, shelfType];
}
