import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';
import 'package:shinise_core_client/system/v1/system.pb.dart';

import 'product_code.dart';

/// ミッドメンテナンス商品
class MidMaintenanceProduct with EquatableMixin {
  /// constructor
  MidMaintenanceProduct({
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.checkDay,
    required this.midCheckDay,
    required this.amount,
    required this.stock,
  });

  /// constructor
  factory MidMaintenanceProduct.fetchMidMaintenanceProductRes(
    CheckMidProdInfoResponse_CheckInfo checkMidProdInfo,
    GetProductInfoResponse_ProductInfo productInfo,
    GetTmsts0085Response? inventory,
  ) =>
      MidMaintenanceProduct(
        productCode: ProductCode.parseProductCode(checkMidProdInfo.productCode),
        productName: productInfo.productName,
        specName: productInfo.specName,
        brandName: productInfo.brandName,
        checkDay: productInfo.checkDay,
        midCheckDay: productInfo.midCheckDay,
        amount: checkMidProdInfo.amount,
        stock: inventory?.rec.logicalQy.toInt(),
      );

  /// 商品名
  final String productName;

  /// コード
  final ProductCode productCode;

  /// 規格
  final String specName;

  /// ブランド名
  final String brandName;

  /// チェックサイクルによって、この商品のチェック日を取得
  final String checkDay;

  ///チェックサイクルによって、この商品のチェック日を取得
  final String midCheckDay;

  /// 該当商品未作業件数
  final int amount;

  /// 該当商品未作業件数
  final int? stock;

  /// 登録済みかどうか
  bool get isRegistered => amount > 0;

  @override
  List<Object?> get props => [
        productCode,
        productName,
        specName,
        brandName,
        checkDay,
        midCheckDay,
        amount,
        stock,
      ];
}
