import 'package:intl/intl.dart';

/// 次回賞味期限の変換
String formatNextExpiryDate(String input) {
  // yyyy年mm月dd日のパターンにマッチする正規表現
  final pattern = RegExp(r'^\d{4}年\d{2}月\d{2}日$');
  // yyyy年mm月dd日のパターンにマッチする正規表現ならそのまま返す
  if (pattern.hasMatch(input)) {
    return input;
  }

  // 入力が8文字で、すべて数字かどうかを確認
  if (input.length != 8 || !input.codeUnits.every((element) => element >= 48 && element <= 57)) {
    return input.replaceAll(RegExp(r'\D'), '');
  }

  try {
    // 入力を解析して日付を作成
    final year = int.tryParse(input.substring(0, 4));
    final month = int.tryParse(input.substring(4, 6));
    final day = int.tryParse(input.substring(6, 8));

    if (year == null || month == null || day == null) {
      return input;
    }

    final date = DateTime(year, month, day);

    // 日付の妥当性をチェック
    if (date.year != year || date.month != month || date.day != day) {
      return input;
    }

    // 日付をフォーマット
    final formatter = DateFormat('yyyy年MM月dd日');

    return formatter.format(date);
  } catch (e) {
    // パースに失敗した場合は元の文字列を返す
    return input;
  }
}

/// 次回賞味のバリデーション
bool isValidNextExpiryDate(String input) {
  // yyyy年mm月dd日のパターンにマッチする正規表現
  final pattern = RegExp(r'^\d{4}年\d{2}月\d{2}日$');

  if (!pattern.hasMatch(input)) {
    return false;
  }

  // パターンにマッチするが、実在しない日付（例：2月30日）のための検証
  try {
    final year = int.parse(input.substring(0, 4));
    final month = int.parse(input.substring(5, 7));
    final day = int.parse(input.substring(8, 10));

    final date = DateTime(year, month, day);

    return date.year == year && date.month == month && date.day == day;
  } catch (e) {
    return false;
  }
}
