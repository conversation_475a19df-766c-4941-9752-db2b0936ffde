import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';

/// 作業頻度情報
class CheckCycle extends Equatable {
  /// constructor
  const CheckCycle({
    required this.checkCycleCode,
    required this.checkCycleName,
    required this.checkCycleNumber,
    required this.currentWeek,
  });

  /// constructor
  factory CheckCycle.fromResponse(
    GetCheckCycleResponse_CheckCycle response,
  ) {
    return CheckCycle(
      checkCycleCode: response.checkCycleCode,
      checkCycleName: response.checkCycleName,
      checkCycleNumber: response.checkCycle,
      currentWeek: response.curWeek,
    );
  }

  /// チェック作業頻度コード
  final int checkCycleCode;

  /// チェック作業頻度名（2週）
  final String checkCycleName;

  /// チェック作業頻度の週数（作業頻度名が2週なら2、4週なら4が入ります）
  final int checkCycleNumber;

  /// 現在週（202337）
  final int currentWeek;

  @override
  List<Object> get props => [
        checkCycleCode,
        checkCycleName,
        checkCycleNumber,
        currentWeek,
      ];
}
