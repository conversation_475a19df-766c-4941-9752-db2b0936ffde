// ignore_for_file: public_member_api_docs

enum ShelfType {
  standard(value: 0),
  end(value: 1),
  mid(value: 2),
  endmid(value: 3),
  fresh(value: 4),
  drug(value: 5),
  newData(value: 40),
  discount(value: 41);

  const ShelfType({
    required this.value,
  });

  factory ShelfType.fromValue(int value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => ShelfType.standard,
    );
  }

  final int value;
}
