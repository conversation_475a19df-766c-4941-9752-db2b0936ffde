/// 棚に置かれている商品のタイプ
enum ShelfProductType {
  /// 不特定
  unspecified(value: 0),

  /// 酒ゾーンの商品。清酒、冷蔵ケース（酒）など
  alcohol(value: 1),

  /// 米
  rice(value: 2),

  /// その他の通常商品
  other(value: 3);

  const ShelfProductType({
    required this.value,
  });

  factory ShelfProductType.fromValue(int value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => throw ArgumentError('Invalid ShelfProductType value: $value'),
    );
  }

  /// 棚に置かれている商品のタイプの値
  final int value;
}
