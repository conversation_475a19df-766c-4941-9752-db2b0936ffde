// ignore_for_file: public_member_api_docs

enum WorkStatus {
  // 未作業
  not(value: 0),
  // 作業中
  working(value: 1),
  // 正常完了
  normalCompletion(value: 2),
  // 作業遅れ完了
  delayedCompletion(value: 3);

  const WorkStatus({
    required this.value,
  });

  factory WorkStatus.fromValue(int value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => WorkStatus.not,
    );
  }

  final int value;
}
