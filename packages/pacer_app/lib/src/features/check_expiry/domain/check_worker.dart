// ignore_for_file: public_member_api_docs

import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';

/// 作業者情報
class WorkerInfo extends Equatable {
  const WorkerInfo({
    required this.userCode,
    required this.userName,
  });
  factory WorkerInfo.fromSetShelfWorkResponse(
    GetWorkManagerResponse getWorkManagerRes,
  ) {
    final managerInfo = getWorkManagerRes.managerInfo.firstOrNull;

    return WorkerInfo(
      userCode: managerInfo?.userCode ?? '',
      userName: managerInfo?.userName ?? '',
    );
  }

  final String userCode;
  final String userName;

  @override
  List<Object> get props => [userCode, userName];
}
