import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life_fresh.pb.dart';

import 'product_code.dart';

//新規商品の日時と作成者
final _newproductDateTime = DateTime(2099, 12, 31);
const _newproductAuthor = '999999999';

/// 商品情報
class ProductInfo with EquatableMixin {
  /// constructor
  ProductInfo({
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.expirationDate,
    required this.taskCount,
    required this.jobDate,
    required this.isEnd,
    required this.isMid,
    required this.author,
  });

  /// constructor
  factory ProductInfo.fromCheckInfoResponseProductInfo(
    GetDrugProductCheckInfoResponse_ProductInfo productInfo,
  ) =>
      ProductInfo(
        productCode: ProductCode.parseProductCode(productInfo.productCode),
        productName: productInfo.productName,
        specName: productInfo.specName,
        brandName: productInfo.brandName,
        expirationDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo.bestBefore),
        taskCount: productInfo.sum,
        jobDate: DateTime.tryParse(productInfo.checkDay),
        isEnd: productInfo.isEnd,
        isMid: productInfo.isMid,
        author: productInfo.author,
      );

  /// constructor
  factory ProductInfo.fromUpdateDrugFlagResponseProductInfo(
    UpdateDrugFlagResponse_ProductInfo productInfo,
  ) =>
      ProductInfo(
        productCode: ProductCode.parseProductCode(productInfo.productCode),
        productName: productInfo.productName,
        specName: productInfo.specName,
        brandName: productInfo.brandName,
        expirationDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo.bestBefore),
        taskCount: productInfo.sum,
        jobDate: DateTime.tryParse(productInfo.checkDay),
        isEnd: productInfo.isEnd,
        isMid: productInfo.isMid,
        author: productInfo.author,
      );

  /// constructor
  factory ProductInfo.fromGetFreshCheckProductInfo(
    GetFreshCheckProductInfoResponse_ProductInfo productInfo,
  ) =>
      ProductInfo(
        productCode: ProductCode.parseProductCode(productInfo.productCode),
        productName: productInfo.productName,
        specName: productInfo.specName,
        brandName: productInfo.brandName,
        expirationDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo.bestBefore),
        taskCount: productInfo.sum,
        jobDate: DateTime.tryParse(productInfo.checkDay),
        isEnd: false,
        isMid: false,
        author: '',
      );

  /// constructor
  factory ProductInfo.fromUpdateFreshFlagResponseProductInfo(
    UpdateFreshFlagResponse_ProductInfo productInfo,
  ) =>
      ProductInfo(
        productCode: ProductCode.parseProductCode(productInfo.productCode),
        productName: productInfo.productName,
        specName: productInfo.specName,
        brandName: productInfo.brandName,
        expirationDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo.bestBefore),
        taskCount: productInfo.sum,
        jobDate: DateTime.tryParse(productInfo.checkDay),
        isEnd: false,
        isMid: false,
        author: '',
      );

  /// constructor
  factory ProductInfo.fromGetMidCheckProductInfo(
    GetMidProductInfoResponse_ProductInfo productInfo,
  ) =>
      ProductInfo(
        productCode: ProductCode.parseProductCode(productInfo.productCode),
        productName: productInfo.productName,
        specName: productInfo.specName,
        brandName: productInfo.brandName,
        expirationDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo.bestBefore),
        taskCount: productInfo.sum,
        jobDate: DateTime.tryParse(productInfo.checkDay),
        isEnd: false,
        isMid: false,
        author: '',
      );

  /// 商品コード
  final ProductCode productCode;

  /// 商品名
  final String productName;

  /// 規格
  final String specName;

  /// ブランド
  final String brandName;

  /// 見切り日
  final DateTime? expirationDate;

  /// 賞味期限の作業数 (作業完了件数/作業総件数)
  final String taskCount;

  /// 賞味期限チェックの作業日
  final DateTime? jobDate;

  /// エンド有無 0無し、１有り
  final bool isEnd;

  /// ミッド有無 0無し、１有り
  final bool isMid;

  /// 賞味期限チェックの作業者
  final String author;

  /// 新規商品かどうか
  bool get isNewproduct {
    return expirationDate == _newproductDateTime && author == _newproductAuthor;
  }

  /// 最終チェックの商品かどうか
  bool get isLastCheckProduct {
    final regExp = RegExp(r'\(\d+/\d+\)'); // (1/2)のような文字列の正規表現
    if (!regExp.hasMatch(taskCount)) {
      return false;
    }
    final digits = taskCount.substring(1, taskCount.length - 1);
    final parts = digits.split('/');
    final leftDigit = parts.first;
    final rightDigit = (int.parse(parts[1]) - 1).toString();

    return leftDigit == rightDigit;
  }

  @override
  List<Object?> get props => [
        productCode,
        productName,
        specName,
        brandName,
        expirationDate,
        taskCount,
        jobDate,
        isEnd,
        isMid,
        author,
      ];
}
