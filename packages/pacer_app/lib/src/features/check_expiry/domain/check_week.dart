/// チェック週の変換
String formatWeekText(String input) {
  // 入力が6文字で、すべて数字かどうかを確認
  if (input.length != 6 || !input.codeUnits.every((element) => element >= 48 && element <= 57)) {
    return input; // 入力が6文字でない、または数字でない場合はエラーメッセージを返す
  }

  try {
    // 入力を解析して年と週を作成
    final year = int.tryParse(input.substring(0, 4));
    final week = int.tryParse(input.substring(4, 6));

    if (year == null || week == null) {
      return input; // パースに失敗した場合はエラーメッセージを返す
    }

    // 週が1桁の場合、0を追加して2桁にする
    final formattedWeek = week.toString().padLeft(2, '0');

    return '$year年$formattedWeek週';
  } catch (e) {
    // パースに失敗した場合は元の文字列を返す
    return input;
  }
}

/// チェック週のバリデーション
bool isValidCheckWeekText(String input) {
  // yyyy年ww週のパターンにマッチする正規表現
  final pattern = RegExp(r'^\d{4}年\d{2}週$');

  if (!pattern.hasMatch(input)) {
    return false;
  }

  // パターンにマッチするが、実在しない週（例：53週）のための検証
  try {
    final week = int.tryParse(input.substring(5, 7));

    // 週数は通常1〜52の範囲内であるが、53週ある年もある
    if (week == null || week < 1 || week > 53) {
      return false;
    }

    return true;
  } catch (e) {
    return false;
  }
}
