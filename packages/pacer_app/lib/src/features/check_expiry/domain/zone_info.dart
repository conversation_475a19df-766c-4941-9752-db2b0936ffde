import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';

/// チェックする対象のゾーン
/// ゾーン選択ページと、棚選択ぺージで使用
class Zone with EquatableMixin {
  /// init
  const Zone({
    required this.zoneCode,
    required this.zoneName,
    required this.checkedTextOfNumber,
    required this.isDone,
    required this.isDanger,
  });

  /// ゾーン情報を取得
  factory Zone.fromGetZoonInfoResponse(GetZoneListResponse_ZoneInfo res) => Zone(
        zoneCode: res.zoneCode,
        zoneName: res.zoneName,
        checkedTextOfNumber: res.checkedNum,
        isDone: res.isDone,
        isDanger: res.isDanger,
      );

  /// ゾーンコード（棚割の階層)
  final int zoneCode;

  /// ゾーン名（棚割の階層)
  final String zoneName;

  /// 作業進捗(作業済件数/総件数)
  final String checkedTextOfNumber;

  /// ゾーン作業フラグ
  final bool isDone;

  /// 遅れフラグ
  final bool isDanger;

  /// 未完了を上部に、完了済みを下部に表示順はzoneCode順
  static int sort(Zone a, Zone b) {
    if (a.isDone && !b.isDone) {
      return 1;
    } else if (!a.isDone && b.isDone) {
      return -1;
    } else {
      return a.zoneCode.compareTo(b.zoneCode);
    }
  }

  @override
  List<Object?> get props => [zoneCode, zoneName, checkedTextOfNumber, isDone, isDanger];
}
