import 'package:equatable/equatable.dart';

import 'product_code.dart';
import 'special_product.dart';

/// 特殊商品の登録用情報
class RequestSpecialProductInfo extends Equatable {
  /// constructor
  const RequestSpecialProductInfo({
    required this.productCode,
    required this.shelfNo,
    required this.shelfNo2,
    required this.shelfNo3,
    required this.zoneCode,
    required this.zoneCode2,
    required this.zoneCode3,
    required this.checkPlanWeek,
    required this.checkCycleCode,
  });

  /// constructor
  factory RequestSpecialProductInfo.fromSpecialProduct(
    SpecialProduct productInfo,
    int checkCycleCode,
  ) =>
      RequestSpecialProductInfo(
        productCode: productInfo.productCode,
        shelfNo: formatToZeroIfNullOrEmpty(
          productInfo.shelfInfoList.elementAtOrNull(0)?.shelfNumberText,
        ),
        shelfNo2: formatToZeroIfNullOrEmpty(
          productInfo.shelfInfoList.elementAtOrNull(1)?.shelfNumberText,
        ),
        shelfNo3: formatToZeroIfNullOrEmpty(
          productInfo.shelfInfoList.elementAtOrNull(2)?.shelfNumberText,
        ),
        zoneCode: productInfo.shelfInfoList.elementAtOrNull(0)?.zoneCode ?? 0,
        zoneCode2: productInfo.shelfInfoList.elementAtOrNull(1)?.zoneCode ?? 0,
        zoneCode3: productInfo.shelfInfoList.elementAtOrNull(2)?.zoneCode ?? 0,
        checkPlanWeek: productInfo.checkPlanWeek,
        checkCycleCode: checkCycleCode.toString(),
      );

  /// constructor
  factory RequestSpecialProductInfo.fromShelfInfoList({
    required String productCode,
    required List<SpecialShelfInfo> shelfInfoList,
    required int checkPlanWeek,
    required int checkCycleCode,
  }) =>
      RequestSpecialProductInfo(
        productCode: ProductCode.parseProductCode(productCode),
        shelfNo: formatToZeroIfNullOrEmpty(
          shelfInfoList.elementAtOrNull(0)?.shelfNumberText,
        ),
        shelfNo2: formatToZeroIfNullOrEmpty(
          shelfInfoList.elementAtOrNull(1)?.shelfNumberText,
        ),
        shelfNo3: formatToZeroIfNullOrEmpty(
          shelfInfoList.elementAtOrNull(2)?.shelfNumberText,
        ),
        zoneCode: shelfInfoList.elementAtOrNull(0)?.zoneCode ?? 0,
        zoneCode2: shelfInfoList.elementAtOrNull(1)?.zoneCode ?? 0,
        zoneCode3: shelfInfoList.elementAtOrNull(2)?.zoneCode ?? 0,
        checkPlanWeek: checkPlanWeek,
        checkCycleCode: checkCycleCode.toString(),
      );

  /// 商品コード
  final ProductCode? productCode;

  /// 棚番
  final String? shelfNo;

  /// 棚番2
  final String? shelfNo2;

  /// 棚番3
  final String? shelfNo3;

  /// ゾーンコード
  final int? zoneCode;

  /// ゾーンコード2
  final int? zoneCode2;

  /// ゾーンコード3
  final int? zoneCode3;

  ///  チェック予定週
  final int? checkPlanWeek;

  /// 作業頻度コード
  final String? checkCycleCode;

  /// nullまたは空文字の場合、0を返す
  static String formatToZeroIfNullOrEmpty(String? text) {
    if (text == null || text.isEmpty) {
      return '0';
    } else {
      return text;
    }
  }

  @override
  List<Object?> get props => [
        productCode,
        shelfNo,
        shelfNo2,
        shelfNo3,
        zoneCode,
        zoneCode2,
        zoneCode3,
        checkPlanWeek,
        checkCycleCode,
      ];
}
