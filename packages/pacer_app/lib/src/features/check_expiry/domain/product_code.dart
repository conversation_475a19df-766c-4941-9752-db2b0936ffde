import 'package:equatable/equatable.dart';

import '../../../utils/string_extensions_itf14.dart';

/// 商品コード
class ProductCode with EquatableMixin {
  /// constructor
  const ProductCode(this.value);

  /// Factory constructor
  factory ProductCode.parseProductCode(
    String productCode,
  ) {
    return switch (productCode) {
      // 最初の2桁が25の場合後ろから7桁を0000000に置き換えて返す
      _ when productCode.startsWith('25') => ProductCode('${productCode.substring(0, 6)}0000000'),
      // 商品コードが20桁または26桁の場合は先頭13桁を返す
      String(length: 20 || 26) => ProductCode(productCode.substring(0, 13)),
      // 商品コードがITF(14桁)の場合は変換して返す
      String(length: 14) => ProductCode(productCode.ean13FromITF14),
      // 13桁入力で先頭が0ではない場合はそのまま返す
      String(length: 13) when !productCode.startsWith('0') => ProductCode(productCode),
      // 上記以外の場合は先頭の0を削除して返す
      _ => ProductCode(int.tryParse(productCode)?.toString() ?? ''),
    };
  }

  /// 商品コード
  final String value;

  /// 生鮮商品かどうか
  bool get isFreshProduct {
    if (value.length != 13) {
      return false;
    }
    return value.startsWith('25') || value.startsWith('28');
  }

  @override
  List<Object?> get props => [
        value,
      ];
}
