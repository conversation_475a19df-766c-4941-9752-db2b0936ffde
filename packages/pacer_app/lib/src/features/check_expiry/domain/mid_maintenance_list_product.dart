import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';

import 'product_code.dart';

/// ミッドのメンテナンス一覧の商品
class MidMaintenanceListProduct with EquatableMixin {
  /// constructor
  MidMaintenanceListProduct({
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.nextExpirationDate,
    required this.stockQuantity,
  });

  /// constructor
  factory MidMaintenanceListProduct.fetchMidMaintenanceListProductRes(
    GetMidProdInfoListResponse_ProductInfo midProductInfo,
    int? stockQuantity,
  ) =>
      MidMaintenanceListProduct(
        productCode: ProductCode.parseProductCode(midProductInfo.productCode),
        productName: midProductInfo.productNameRead,
        specName: midProductInfo.specNameRead,
        brandName: midProductInfo.brandName,
        nextExpirationDate: DateFormat('yyyy/MM/dd').tryParse(midProductInfo.nextBestBefore),
        stockQuantity: stockQuantity,
      );

  /// 商品名
  final String productName;

  /// コード
  final ProductCode productCode;

  /// 規格
  final String specName;

  /// ブランド名
  final String brandName;

  /// 次回賞味期限の日時
  final DateTime? nextExpirationDate;

  /// 在庫数
  final int? stockQuantity;

  @override
  List<Object?> get props => [
        productCode,
        productName,
        specName,
        brandName,
        nextExpirationDate,
        stockQuantity,
      ];
}
