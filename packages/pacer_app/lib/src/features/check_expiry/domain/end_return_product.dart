import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';
import 'package:shinise_core_client/system/v1/system.pb.dart';

import 'product_code.dart';

/// 商品情報
class EndReturnProduct with EquatableMixin {
  /// constructor
  EndReturnProduct({
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.checkDate,
    required this.midCheckDate,
    required this.stock,
  });

  /// constructor
  factory EndReturnProduct.fromGetProductInfoResponse(
    String productCode,
    GetProductInfoResponse_ProductInfo productInfo,
    GetTmsts0085Response? inventoryRes,
  ) =>
      EndReturnProduct(
        productCode: ProductCode.parseProductCode(productCode),
        productName: productInfo.productName,
        specName: productInfo.specName,
        brandName: productInfo.brandName,
        checkDate: DateTime.tryParse(productInfo.checkDay),
        midCheckDate: DateTime.tryParse(productInfo.midCheckDay),
        stock: inventoryRes?.rec.logicalQy.toInt(),
      );

  /// 商品コード
  final ProductCode productCode;

  /// 商品名
  final String productName;

  /// 規格
  final String specName;

  /// ブランド
  final String brandName;

  /// 賞味期限チェック日
  final DateTime? checkDate;

  /// ミッドの賞味期限チェック日
  final DateTime? midCheckDate;

  /// 在庫数
  final int? stock;

  @override
  List<Object?> get props => [
        productCode,
        productName,
        specName,
        brandName,
        checkDate,
        midCheckDate,
      ];
}
