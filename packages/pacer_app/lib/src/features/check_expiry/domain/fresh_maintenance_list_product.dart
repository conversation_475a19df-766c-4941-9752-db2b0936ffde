import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life_fresh.pb.dart';

import 'product_code.dart';

/// 生鮮メンテナンス一覧の商品
class FreshMaintenanceListProduct with EquatableMixin {
  /// constructor
  FreshMaintenanceListProduct({
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.nextExpirationDate,
  });

  /// constructor
  factory FreshMaintenanceListProduct.fetchFreshMaintenanceListProductRes(
    GetFreshProdInfoListResponse_FreshProdInfo freshProductInfo,
  ) =>
      FreshMaintenanceListProduct(
        productCode: ProductCode.parseProductCode(freshProductInfo.productCode),
        productName: freshProductInfo.productNameRead,
        specName: freshProductInfo.specNameRead,
        brandName: freshProductInfo.brandName,
        nextExpirationDate: DateFormat('yyyy/MM/dd').tryParse(freshProductInfo.nextBestBefore),
      );

  /// 商品名
  final String productName;

  /// コード
  final ProductCode productCode;

  /// 規格
  final String specName;

  /// ブランド名
  final String brandName;

  /// 次回賞味期限の日時
  final DateTime? nextExpirationDate;

  @override
  List<Object?> get props => [
        productCode,
        productName,
        specName,
        brandName,
        nextExpirationDate,
      ];
}
