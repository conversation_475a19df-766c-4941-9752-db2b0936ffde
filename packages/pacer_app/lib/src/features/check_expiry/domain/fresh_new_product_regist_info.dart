// ignore_for_file: public_member_api_docs

import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life_fresh.pb.dart';

import 'product_code.dart';

/// 生鮮新規商品登録情報
class FreshNewProductRegisterInfo with EquatableMixin {
  FreshNewProductRegisterInfo({
    required this.countNewInput,
    required this.checkPlanWeek,
    required this.limitDate,
  });

  factory FreshNewProductRegisterInfo.fromFetchFreshProdNewInputListRes(
    GetFreshNewInputAlreadySetSpeedResponse freshNewInputAlreadySetSpeedRes,
    GetDateLimitResponse getDateLimitRes,
  ) {
    final checkPlanWeek = freshNewInputAlreadySetSpeedRes.checkPlanWeek.isNotEmpty
        ? CheckPlanWeek.fromGetFreshNewInputAlreadySetSpeedRes(
            freshNewInputAlreadySetSpeedRes.checkPlanWeek.first,
          )
        : null;
    final countNewInput = freshNewInputAlreadySetSpeedRes.countNewInput.isNotEmpty
        ? CountNewInput.fromGetFreshNewInputAlreadySetSpeedRes(
            freshNewInputAlreadySetSpeedRes.countNewInput.first,
          )
        : null;

    return FreshNewProductRegisterInfo(
      countNewInput: countNewInput,
      checkPlanWeek: checkPlanWeek,
      limitDate: DateFormat('yyyy-MM-dd').tryParse(getDateLimitRes.limitDate),
    );
  }

  // 商品の件数、進捗情報
  final CountNewInput? countNewInput;
  // 商品のチェック情報
  final CheckPlanWeek? checkPlanWeek;

  /// ミッド・生鮮・ドラッグ次回賞味期限最大日付制御
  final DateTime? limitDate;

  @override
  List<Object?> get props => [
        countNewInput,
        checkPlanWeek,
        limitDate,
      ];
}

/// 商品の件数、進捗情報
class CountNewInput with EquatableMixin {
  CountNewInput({
    required this.branchCode,
    required this.zoneCode,
    required this.countNewInput,
    required this.countAlreadySet,
    required this.speed,
  });
  factory CountNewInput.fromGetFreshNewInputAlreadySetSpeedRes(
    GetFreshNewInputAlreadySetSpeedResponse_CountNewInput countNewInput,
  ) =>
      CountNewInput(
        branchCode: countNewInput.branchCode,
        zoneCode: countNewInput.zoneCode,
        countNewInput: countNewInput.countNewInput,
        countAlreadySet: countNewInput.countAlreadySet,
        speed: countNewInput.speed,
      );

  //店舗コード
  final int branchCode;
  //ゾーンコード
  final int zoneCode;
  //生鮮新規データ件数
  final String countNewInput;
  //生鮮新規登録完了件数
  final String countAlreadySet;
  //生鮮新規登録完了件数/生鮮新規データ件数
  final String speed;

  @override
  List<Object?> get props => [
        branchCode,
        zoneCode,
        countNewInput,
        countAlreadySet,
        speed,
      ];
}

/// 商品のチェック情報
class CheckPlanWeek with EquatableMixin {
  CheckPlanWeek({
    required this.productName,
    required this.productCode,
    required this.specName,
    required this.checkDay,
    required this.midCheckDay,
    required this.freshFlg,
    required this.brandName,
  });

  factory CheckPlanWeek.fromGetFreshNewInputAlreadySetSpeedRes(
    GetFreshNewInputAlreadySetSpeedResponse_CheckPlanWeek checkPlanWeek,
  ) =>
      CheckPlanWeek(
        productCode: ProductCode.parseProductCode(checkPlanWeek.productCode),
        productName: checkPlanWeek.productCode,
        specName: checkPlanWeek.specName,
        brandName: checkPlanWeek.brandName,
        checkDay: checkPlanWeek.checkDay,
        midCheckDay: checkPlanWeek.midCheckDay,
        freshFlg: checkPlanWeek.freshFlg,
      );

  //商品コード
  final ProductCode productCode;
  //商品名
  final String productName;
  //規格
  final String specName;
  //ブランド名
  final String brandName;
  //賞味期限の週の日付
  final String checkDay;
  //チェックサイクルによって、この商品のチェック日を取得
  final String midCheckDay;
  //生鮮フラグ　0：非生鮮　1：生鮮商品
  final String freshFlg;

  @override
  List<Object?> get props => [
        productName,
        specName,
        brandName,
        checkDay,
        midCheckDay,
        freshFlg,
        productCode,
      ];
}
