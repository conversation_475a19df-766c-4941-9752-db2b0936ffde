import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';

import 'product_code.dart';

/// 特殊商品情報
class SpecialProductSummary extends Equatable {
  /// constructor
  const SpecialProductSummary({
    required this.productCode,
    required this.productName,
    required this.brandName,
    required this.specName,
  });

  /// constructor
  factory SpecialProductSummary.fromResponse(
    GetSpecialListResponse_ProductInfo getSpecialListRes,
  ) {
    return SpecialProductSummary(
      productCode: ProductCode.parseProductCode(getSpecialListRes.productCode),
      productName: getSpecialListRes.productName,
      brandName: getSpecialListRes.brandName,
      specName: getSpecialListRes.specName,
    );
  }

  /// 商品コード
  final ProductCode productCode;

  /// 商品名
  final String productName;

  /// ブランド名
  final String brandName;

  ///  規格名
  final String specName;

  @override
  List<Object> get props => [
        productCode,
        productName,
        brandName,
        specName,
      ];
}
