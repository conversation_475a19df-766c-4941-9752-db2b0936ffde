// ignore_for_file: public_member_api_docs

import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/shelf_life/v1/shelf_life.pb.dart';

import 'enum/shelf_type.dart';

class ShelfListInfo with EquatableMixin {
  const ShelfListInfo({
    required this.shelfList,
    required this.limitDate,
  });

  factory ShelfListInfo.fromGetShelfListResponse(
    GetShelfListResponse shelfInfoRes,
    GetDateLimitResponse getDateLimitRes,
  ) =>
      ShelfListInfo(
        shelfList: shelfInfoRes.shelf.map(ShelfInfo.fromGetShelfListResponse).toList(),
        limitDate: DateFormat('yyyy-MM-dd').tryParse(getDateLimitRes.limitDate),
      );

  /// 棚番号
  final List<ShelfInfo> shelfList;
  // ミッド・生鮮・ドラッグ次回賞味期限最大日付制御
  final DateTime? limitDate;

  @override
  List<Object?> get props => [
        shelfList,
        limitDate,
      ];
}

class ShelfInfo with EquatableMixin {
  const ShelfInfo({
    required this.shelfNumberText,
    required this.shelfName,
    required this.zoneCode,
    required this.zoneName,
    required this.shelfType,
  });

  factory ShelfInfo.fromGetShelfListResponse(
    GetShelfListResponse_Shelf shelfInfoRes,
  ) =>
      ShelfInfo(
        shelfNumberText: parseShelfNumberText(shelfInfoRes.shelfNo),
        shelfName: shelfInfoRes.shelfName,
        shelfType: ShelfType.fromValue(shelfInfoRes.shelfNoFlag.value),
        zoneCode: shelfInfoRes.zoneCode,
        zoneName: shelfInfoRes.zoneName,
      );

  /// 棚番号
  final String shelfNumberText;
  // 棚名称
  final String shelfName;
  // ゾーンコード
  final int zoneCode;
  // ゾーン名称
  final String zoneName;
  // 棚タイプ
  final ShelfType shelfType;

  @override
  List<Object?> get props => [
        shelfNumberText,
        shelfName,
        zoneCode,
        zoneName,
        shelfType,
      ];

  static String parseShelfNumberText(String shelfNo) {
    // 正規表現で大文字または小文字のアルファベットに続く数字部分を抽出
    final regExp = RegExp(r'([a-zA-Z]\d+)|(\b\d+\b)');

    return regExp.stringMatch(shelfNo) ?? '';
  }
}
