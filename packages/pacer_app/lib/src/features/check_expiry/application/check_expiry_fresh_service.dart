import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/shelf_life_fresh_repository.dart';
import '../data/shelf_life_repository.dart';
import '../domain/enum/shelf_type.dart';
import '../domain/enum/work_status.dart';
import '../domain/fresh_discount_product.dart';
import '../domain/fresh_maintenance_list_product.dart';
import '../domain/fresh_maintenance_product.dart';
import '../domain/fresh_new_product.dart';
import '../domain/fresh_new_product_regist_info.dart';
import '../domain/product_check_info.dart';
import '../domain/shelf_work_info.dart';
import '../domain/zoon_shelf_info.dart';

part 'check_expiry_fresh_service.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
CheckExpiryFreshService checkExpiryFreshService(
  CheckExpiryFreshServiceRef ref,
) {
  return CheckExpiryFreshService(ref);
}

/// 生鮮の賞味期限チェックサービス
class CheckExpiryFreshService {
  /// contractor
  CheckExpiryFreshService(this.ref);

  /// ref
  final Ref ref;

  ShelfLifeFreshRepository get _shelfLifeFreshRepository => ref.read(shelfLifeFreshRepositoryProvider);

  /// 生鮮新規商品一覧取得
  Future<List<FreshNewProduct>> fetchFreshNewProductList(
    int? zoneCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.fetchFreshNewProductList(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
    );
  }

  /// 生鮮の棚一覧取得
  Future<List<ZoonShelfInfo>> fetchFreshShelfList(
    int? zoneCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    final shelfLifeRepository = ref.read(shelfLifeRepositoryProvider);

    return shelfLifeRepository.fetchShelfListByZone(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfTypeValue: ShelfType.fresh.value,
    );
  }

  /// 生鮮新規商品の登録を取得
  Future<FreshNewProductRegisterInfo> fetchFreshNewProductRegisterInfo(
    int? zoneCode,
    String? productCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.fetchFreshNewProductRegisterInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      productCode: productCode,
    );
  }

  /// 生鮮棚情チェック報取得
  Future<ProductCheckInfo> fetchFreshProductCheckInfo(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    if (shelfInfo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.fetchFreshProductCheckInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      shelfInfo: shelfInfo,
      caller: caller,
    );
  }

  /// 棚番設定画面、値下データをクリック時、作業中設定
  Future<void> setFreshDCTShelfWork(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.setFreshDCTShelfWork(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfInfo: shelfInfo,
    );
  }

  /// 新規商品を一覧から削除処理
  Future<List<FreshNewProduct>> deleteFreshNewProductList(
    int? zoneCode,
    FreshNewProduct product,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.deleteFreshNewProductList(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      product: product,
    );
  }

  /// 生鮮の商品の登録
  Future<void> setFreshNewProdInfo({
    required int? zoneCode,
    required DateTime nextExpiryDate,
    required String? shelfNumberText,
    required FreshNewProduct? product,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.setFreshNewProdInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      nextExpiryDate: nextExpiryDate,
      shelfNumberText: shelfNumberText,
      product: product,
    );
  }

  /// 生鮮新規商品を削除
  Future<void> deleteFreshNewProductInfo(
    int? zoneCode,
    String? productCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.deleteFreshNewProductInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      productCode: productCode,
    );
  }

  /// 棚一覧の更新（生鮮）
  Future<ProductCheckInfo?> updateFreshProductCheckInfo({
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
    required String? productCode,
    required DateTime nextExpiryDate,
    required WorkStatus? workStatus,
  }) async {
    if (shelfInfo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.updateFreshProductCheckInfo(
      shelfInfo: shelfInfo,
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      nextExpiryDate: nextExpiryDate,
      productCode: productCode,
      workStatusValue: workStatus?.value ?? WorkStatus.not.value,
    );
  }

  /// 賞味期限チェックの削除（生鮮）
  Future<void> deleteFreshProductCheckInfo({
    required String? shelfNo,
    required int? zoneCode,
    required String? productCode,
    required DateTime? expirationDate,
  }) async {
    if (shelfNo == null || zoneCode == null || productCode == null) {
      throw UnknownException('棚番号とゾーンコードと商品コードを指定してください');
    }
    if (expirationDate == null) {
      throw UnknownException('見切り日が存在しないため、削除できません');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.deleteFreshProductCheckInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      productCode: productCode,
      shelfNo: shelfNo,
      caller: caller,
      expirationDate: expirationDate,
    );
  }

  /// 値下賞味期限登録
  Future<void> registerExpiryDate({
    required int? zoneCode,
    required ProductCheckInfo productCheckInfo,
    required DateTime closeoutExpirationDate, //見切り商品の賞味期限
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    final shelfLifeFreshRepository = ref.read(shelfLifeFreshRepositoryProvider);

    return shelfLifeFreshRepository.registerExpiryDate(
      storeCode: store.code,
      zoneCode: zoneCode,
      productCheckInfo: productCheckInfo,
      caller: caller,
      closeoutExpirationDate: closeoutExpirationDate,
    );
  }

  /// 生鮮の値下一覧データ取得
  Future<List<FreshDiscountProduct>> fetchFreshDiscountList(
    String? shelfNo,
    int? zoneCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (shelfNo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.fetchFreshDiscountList(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfNo: shelfNo,
    );
  }

  /// 値下完了
  Future<void> completeDiscount({
    required String? shelfNo,
    required int? zoneCode,
    required List<String> productCodeList,
  }) async {
    if (shelfNo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    final shelfLifeFreshRepository = ref.read(shelfLifeFreshRepositoryProvider);

    return shelfLifeFreshRepository.completeDiscount(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfNo: shelfNo,
      productCodeList: productCodeList,
    );
  }

  /// 生鮮のメンテナンス商品取得
  Future<FreshMaintenanceProduct?> fetchFreshMaintenanceProduct(
    String productCode,
    String? shelfNo,
    int? zoneCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (shelfNo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.fetchFreshMaintenanceProduct(
      productCode: productCode,
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfNo: shelfNo,
    );
  }

  /// 生鮮商品の登録
  Future<void> registerFreshProduct({
    required String? shelfNo,
    required int? zoneCode,
    required String newProductCode,
    required FreshMaintenanceProduct freshProductInfo,
    required DateTime nextExpiryDate,
  }) async {
    if (shelfNo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    final shelfLifeFreshRepository = ref.read(shelfLifeFreshRepositoryProvider);

    return shelfLifeFreshRepository.registerFreshProduct(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfNo: shelfNo,
      freshProductInfo: freshProductInfo,
      nextExpiryDate: nextExpiryDate,
    );
  }

  /// 生鮮のメンテナンス一覧データ取得
  Future<List<FreshMaintenanceListProduct>> fetchFreshMaintenanceProductList(
    String? shelfNo,
    int? zoneCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (shelfNo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeFreshRepository.fetchFreshMaintenanceProductList(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfNo: shelfNo,
    );
  }
}
