import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/shelf_life_drug_repository.dart';
import '../domain/drug_history.dart';
import '../domain/drug_product_info.dart';
import '../domain/enum/work_status.dart';
import '../domain/product_check_info.dart';
import '../domain/shelf_work_info.dart';

part 'check_expiry_drug_service.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
CheckExpiryDrugService checkExpiryDrugService(CheckExpiryDrugServiceRef ref) {
  return CheckExpiryDrugService(ref);
}

///　賞味期限チェックドラッグサービス
class CheckExpiryDrugService {
  /// constructor
  CheckExpiryDrugService(this.ref);

  /// ref
  final Ref ref;

  ShelfLifeDrugRepository get _shelfLifeDragRepository => ref.read(shelfLifeDrugRepositoryProvider);

  /// 賞味期限チェック棚情報の取得（ドラッグ）
  Future<ProductCheckInfo> fetchDrugProductCheckInfo(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    if (shelfInfo == null || zoneCode == null) {
      throw UnknownException('棚情報が不正です　fetchDrugProductCheckInfo');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeDragRepository.fetchDrugProductCheckInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      shelfInfo: shelfInfo,
      caller: caller,
    );
  }

  /// 商品を取得（ドラッグ）
  Future<DrugProductInfo> fetchDrugProductInfo(
    String productCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeDragRepository.fetchDrugProductInfo(
      storeCode: store.code,
      productCode: productCode,
      caller: caller,
    );
  }

  /// 棚一覧の更新（ドラッグ）
  Future<ProductCheckInfo?> updateDrugProductCheckInfo({
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
    required bool? isMid,
    required bool? isEnd,
    required String? productCode,
    required DateTime nextExpiryDate,
    required WorkStatus? workStatus,
  }) async {
    if (shelfInfo == null || zoneCode == null) {
      throw UnknownException('棚情報が不正です');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeDragRepository.updateDrugProductCheckInfo(
      shelfInfo: shelfInfo,
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      isMid: isMid,
      isEnd: isEnd,
      nextExpiryDate: nextExpiryDate,
      productCode: productCode,
      workStatusValue: workStatus?.value ?? WorkStatus.not.value,
    );
  }

  /// 賞味期限チェックの削除（ドラッグ）
  Future<void> deleteDrugProductCheckInfo({
    required String? shelfNo,
    required int? zoneCode,
    required String? productCode,
  }) async {
    if (shelfNo == null || zoneCode == null || productCode == null) {
      throw UnknownException('商品情報が不正です');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeDragRepository.deleteDrugProductCheckInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      productCode: productCode,
      shelfNo: shelfNo,
      caller: caller,
    );
  }

  /// ドラッグ商品情報の登録
  Future<void> setDrugProdInfo({
    required DrugProductInfo drugProductInfo,
    required bool isEnd,
    required bool isMid,
    required DateTime nextExpiryDate,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeDragRepository.setDrugProdInfo(
      storeCode: store.code,
      drugProductInfo: drugProductInfo,
      caller: caller,
      isEnd: isEnd,
      isMid: isMid,
      nextExpiryDate: nextExpiryDate,
    );
  }

  /// ドラッグ商品情報の削除
  Future<void> deleteDrugProdInfo(
    DrugProductInfo drugProductInfo,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeDragRepository.deleteDrugProdInfo(
      storeCode: store.code,
      drugProductInfo: drugProductInfo,
      caller: caller,
    );
  }

  /// ドラッグ商品の連続スキャンの時、前の商品登録、次の商品情報取得
  Future<DrugProductInfo> getProcDrugProdInfo({
    required String productCode,
    required DrugProductInfo drugProductInfo,
    required bool isEnd,
    required bool isMid,
    required DateTime nextExpiryDate,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeDragRepository.getProcDrugProdInfo(
      newProductCode: productCode,
      storeCode: store.code,
      drugProductInfo: drugProductInfo,
      caller: caller,
      isEnd: isEnd,
      isMid: isMid,
      nextExpiryDate: nextExpiryDate,
    );
  }

  /// ドラッグ商品情報の作業完了取得
  Future<List<DrugHistory>> getDrugHistory({
    required String? productCode,
  }) async {
    if (productCode == null) {
      throw UnknownException('商品情報が不正です');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeDragRepository.getDrugHistory(
      storeCode: store.code,
      productCode: productCode,
      caller: caller,
    );
  }
}
