import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/shelf_life_special_repository.dart';
import '../domain/check_cycle.dart';
import '../domain/check_min_cycle.dart';
import '../domain/request_special_product_info.dart';
import '../domain/special_product.dart';

part 'check_expiry_special_service.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
CheckExpirySpecialService checkExpirySpecialService(
  CheckExpirySpecialServiceRef ref,
) {
  return CheckExpirySpecialService(ref);
}

/// 賞味期限チェック特殊のサービス
class CheckExpirySpecialService {
  /// contractor
  CheckExpirySpecialService(this.ref);

  /// ref
  final Ref ref;

  ShelfLifeSpecialRepository get _shelfLifeSpecialRepository => ref.read(shelfLifeSpecialRepositoryProvider);

  /// チェック頻度取得
  Future<List<CheckCycle>> fetchCheckCycle() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeSpecialRepository.fetchCheckCycle(store.code, caller);
  }

  /// 特殊商品の取得
  Future<SpecialProduct> fetchSpecialProduct({
    required String? productCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeSpecialRepository.fetchSpecialProduct(
      storeCode: store.code,
      productCode: productCode,
      caller: caller,
    );
  }

  /// 特殊商品の棚のチェック頻度取得
  Future<List<CheckMinCycle>> fetchMinCheckCycle({
    required String? productCode,
    required String? shelfNo1,
    required String? shelfNo2,
    required String? shelfNo3,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeSpecialRepository.fetchMinCheckCycle(
      storeCode: store.code,
      productCode: productCode,
      shelfNo1: shelfNo1,
      shelfNo2: shelfNo2,
      shelfNo3: shelfNo3,
      caller: caller,
    );
  }

  /// 特殊商品連続スキャンの時、前の商品登録、次の商品情報取得
  Future<SpecialProduct> scanRegisterSpecialProduct({
    required RequestSpecialProductInfo oldProductInfo,
    required RequestSpecialProductInfo newProductInfo,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeSpecialRepository.scanRegisterSpecialProduct(
      storeCode: store.code,
      oldProductInfo: oldProductInfo,
      newProductInfo: newProductInfo,
      caller: caller,
    );
  }

  /// 特殊商品情報登録
  Future<void> registerSpecialProduct({
    required RequestSpecialProductInfo oldProductInfo,
    required RequestSpecialProductInfo newProductInfo,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeSpecialRepository.registerSpecialProduct(
      storeCode: store.code,
      oldProductInfo: oldProductInfo,
      newProductInfo: newProductInfo,
      caller: caller,
    );
  }

  /// 特殊の棚情報を取得
  Future<void> deleteSpecialProduct(
    RequestSpecialProductInfo productInfo,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeSpecialRepository.deleteSpecialProduct(
      storeCode: store.code,
      productInfo: productInfo,
      caller: caller,
    );
  }
}
