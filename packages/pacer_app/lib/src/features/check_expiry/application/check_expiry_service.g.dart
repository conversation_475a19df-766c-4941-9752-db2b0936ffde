// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_expiry_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkExpiryServiceHash() => r'da388a6735393db9f18c13089758f5eb56bf2ea0';

/// provider生成コード
///
/// Copied from [checkExpiryService].
@ProviderFor(checkExpiryService)
final checkExpiryServiceProvider = Provider<CheckExpiryService>.internal(
  checkExpiryService,
  name: r'checkExpiryServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkExpiryServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CheckExpiryServiceRef = ProviderRef<CheckExpiryService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
