// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_expiry_mid_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkExpiryMidServiceHash() => r'7ce6e61d1b4107870ff930620c29f158ab2c11fb';

/// provider生成コード
///
/// Copied from [checkExpiryMidService].
@ProviderFor(checkExpiryMidService)
final checkExpiryMidServiceProvider = Provider<CheckExpiryMidService>.internal(
  checkExpiryMidService,
  name: r'checkExpiryMidServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkExpiryMidServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CheckExpiryMidServiceRef = ProviderRef<CheckExpiryMidService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
