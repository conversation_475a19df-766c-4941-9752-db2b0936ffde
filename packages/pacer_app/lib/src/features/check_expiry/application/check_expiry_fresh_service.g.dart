// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_expiry_fresh_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkExpiryFreshServiceHash() => r'eaebd8efc67a4daade824f1822f9a84c1d5ab2c3';

/// provider生成コード
///
/// Copied from [checkExpiryFreshService].
@ProviderFor(checkExpiryFreshService)
final checkExpiryFreshServiceProvider = Provider<CheckExpiryFreshService>.internal(
  checkExpiryFreshService,
  name: r'checkExpiryFreshServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkExpiryFreshServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CheckExpiryFreshServiceRef = ProviderRef<CheckExpiryFreshService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
