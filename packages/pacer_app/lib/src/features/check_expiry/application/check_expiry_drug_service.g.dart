// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_expiry_drug_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkExpiryDrugServiceHash() => r'c9eb3edefd2d6ff93bd5338fa2bfe667f5275ca6';

/// provider生成コード
///
/// Copied from [checkExpiryDrugService].
@ProviderFor(checkExpiryDrugService)
final checkExpiryDrugServiceProvider = Provider<CheckExpiryDrugService>.internal(
  checkExpiryDrugService,
  name: r'checkExpiryDrugServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkExpiryDrugServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CheckExpiryDrugServiceRef = ProviderRef<CheckExpiryDrugService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
