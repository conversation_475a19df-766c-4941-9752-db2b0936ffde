// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_expiry_special_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkExpirySpecialServiceHash() => r'c54ea4fba96dcbce5ce0de6225720b4171efa20e';

/// provider生成コード
///
/// Copied from [checkExpirySpecialService].
@ProviderFor(checkExpirySpecialService)
final checkExpirySpecialServiceProvider = Provider<CheckExpirySpecialService>.internal(
  checkExpirySpecialService,
  name: r'checkExpirySpecialServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkExpirySpecialServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CheckExpirySpecialServiceRef = ProviderRef<CheckExpirySpecialService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
