import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/shelf_life_repository.dart';
import '../domain/check_shelf_info.dart';
import '../domain/check_worker.dart';
import '../domain/end_return_product.dart';
import '../domain/enum/shelf_type.dart';
import '../domain/shelf_history.dart';
import '../domain/shelf_list_info.dart';
import '../domain/shelf_work_info.dart';
import '../domain/special_product_summary.dart';
import '../domain/zone_info.dart';
import '../presentation/common_widgets/work_stop_alert_dialog.dart';

part 'check_expiry_service.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
CheckExpiryService checkExpiryService(
  CheckExpiryServiceRef ref,
) {
  return CheckExpiryService(ref);
}

/// 賞味期限チェックサービス
class CheckExpiryService {
  /// contractor
  CheckExpiryService(this.ref);

  /// ref
  final Ref ref;

  ShelfLifeRepository get _shelfLifeRepository => ref.read(shelfLifeRepositoryProvider);

  /// ゾーン一覧を取得
  Future<List<Zone>> fetchZoneList() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchZoneList(
      storeCode: store.code,
      caller: caller,
    );
  }

  /// 棚の作業情報一覧を取得
  Future<List<ShelfWorkInfo>> fetchShelfNoList(
    int? zoneCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchShelfNoList(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
    );
  }

  /// ドラッグの棚一覧情報を取得
  Future<ShelfListInfo> fetchDrugShelfList() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchShelfList(
      storeCode: store.code,
      caller: caller,
      shelfTypeValue: ShelfType.drug.value,
    );
  }

  /// 生鮮の棚一覧情報を取得
  Future<ShelfListInfo> fetchFreshShelfList() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchShelfList(
      storeCode: store.code,
      caller: caller,
      shelfTypeValue: ShelfType.fresh.value,
    );
  }

  /// ミッドの棚一覧情報を取得
  Future<ShelfListInfo> fetchMidShelfList() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchShelfList(
      storeCode: store.code,
      caller: caller,
      shelfTypeValue: ShelfType.mid.value,
    );
  }

  /// 定番の棚一覧情報を取得
  Future<ShelfListInfo> fetchStandardShelfList() async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchShelfList(
      storeCode: store.code,
      caller: caller,
      shelfTypeValue: ShelfType.standard.value,
    );
  }

  /// その他メンテナンスの棚一覧情報を取得
  Future<List<ShelfInfo>> fetchOtherMaintenanceShelfList() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    //取得する棚タイプリスト
    final fetchShelfTypeList = [
      ShelfType.standard,
      ShelfType.end,
      ShelfType.mid,
      ShelfType.fresh,
    ];

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    final futureList = fetchShelfTypeList
        .map(
          (shelfType) => _shelfLifeRepository.fetchShelfInfoList(
            storeCode: store.code,
            caller: caller,
            shelfTypeValue: shelfType.value,
          ),
        )
        .toList();
    final result = await Future.wait(futureList);

    return result.expand((list) => list).toList();
  }

  /// 賞味期限作業している担当者取得
  Future<WorkerInfo> fetchWorkmanager(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchWorkmanager(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfInfo: shelfInfo,
    );
  }

  /// 棚ステータス設定処理
  Future<void> setShelfWork(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.setShelfWork(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfInfo: shelfInfo,
    );
  }

  /// 賞味期限チェックの中断、終了処理
  Future<void> workStopShelfCheck({
    required int? zoneCode,
    required String? shelfNumberText,
    required int? checkPlanWeek,
    required WorkStopType workStopType,
  }) async {
    if (shelfNumberText == null || checkPlanWeek == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードとチェック予定日時を指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.workStopShelfCheck(
      storeCode: store.code,
      zoneCode: zoneCode,
      shelfNumberText: shelfNumberText,
      checkPlanWeek: checkPlanWeek,
      workStopType: workStopType,
      caller: caller,
    );
  }

  /// 賞味期限チェックの中断、終了処理(値下げ)
  Future<void> workStopPriceReductionShelfCheck({
    required int? zoneCode,
    required String? shelfNumberText,
    required int? checkPlanWeek,
    required WorkStopType workStopType,
  }) async {
    if (shelfNumberText == null || checkPlanWeek == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードとチェック予定日時を指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.workStopPriceReductionShelfCheck(
      storeCode: store.code,
      zoneCode: zoneCode,
      shelfNumberText: shelfNumberText,
      checkPlanWeek: checkPlanWeek,
      workStopType: workStopType,
      caller: caller,
    );
  }

  /// 棚履歴情報を取得
  Future<ShelfHistory> fetchShelfHistory(
    String? shelfNo,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (shelfNo == null) {
      throw UnknownException('棚番号を指定してください');
    }

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchShelfHistory(
      shelfNo: shelfNo,
      storeCode: store.code,
      caller: caller,
    );
  }

  /// 定番の棚情報を取得
  Future<StandardCheckShelfInfo> fetchStandardShelfInfo(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchStandardShelfInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfInfo: shelfInfo,
    );
  }

  /// 賞味期限の作業完了登録
  Future<void> finishShelfWork(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.finishShelfWork(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfInfo: shelfInfo,
    );
  }

  /// エンド戻しの商品情報取得
  Future<EndReturnProduct> fetchEndReturnProduct(
    String? productCode,
  ) async {
    if (productCode == null) {
      throw UnknownException('商品コードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchEndReturnProduct(
      productCode: productCode,
      storeCode: store.code,
      caller: caller,
    );
  }

  /// 特殊の棚情報を取得
  Future<List<SpecialProductSummary>> fetchSpecialProductList(
    int? zoneCode,
    String? shelfNo,
  ) async {
    if (shelfNo == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeRepository.fetchSpecialProductList(
      storeCode: store.code,
      zoneCode: zoneCode,
      shelfNo: shelfNo,
      caller: caller,
    );
  }
}
