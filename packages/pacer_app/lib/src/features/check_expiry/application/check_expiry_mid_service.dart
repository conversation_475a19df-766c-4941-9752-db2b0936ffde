import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/shelf_life_mid_repository.dart';
import '../domain/enum/work_status.dart';
import '../domain/mid_maintenance_list_product.dart';
import '../domain/mid_maintenance_product.dart';
import '../domain/product_check_info.dart';
import '../domain/shelf_work_info.dart';

part 'check_expiry_mid_service.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
CheckExpiryMidService checkExpiryMidService(CheckExpiryMidServiceRef ref) {
  return CheckExpiryMidService(ref);
}

///　賞味期限チェックミッドサービス
class CheckExpiryMidService {
  /// constructor
  CheckExpiryMidService(this.ref);

  /// ref
  final Ref ref;

  ShelfLifeMidRepository get _shelfLifeMidRepository => ref.read(shelfLifeMidRepositoryProvider);

  /// 賞味期限チェック棚情報の取得（ミッド）
  Future<ProductCheckInfo> fetchMidProductCheckInfo(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    if (shelfInfo == null || zoneCode == null) {
      throw UnknownException('棚情報、ゾーンコードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeMidRepository.fetchMidProductCheckInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      shelfInfo: shelfInfo,
      caller: caller,
    );
  }

  /// 賞味期限チェックの更新（ミッド）
  Future<void> updateMidProductCheckInfo({
    required int? zoneCode,
    required ShelfWorkInfo? shelfInfo,
    required String? productCode,
    required DateTime nextExpiryDate,
    required WorkStatus? workStatus,
  }) async {
    if (shelfInfo == null || zoneCode == null) {
      throw UnknownException('棚情報、ゾーンコードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeMidRepository.updateMidProductCheckInfo(
      shelfInfo: shelfInfo,
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      nextExpiryDate: nextExpiryDate,
      productCode: productCode,
      workStatusValue: workStatus?.value ?? WorkStatus.not.value,
    );
  }

  /// 賞味期限チェックの削除（ミッド）
  Future<void> deleteMidProductCheckInfo({
    required String? shelfNo,
    required int? zoneCode,
    required String? productCode,
    required DateTime? nextExpiryDate,
  }) async {
    if (shelfNo == null || zoneCode == null || productCode == null) {
      throw UnknownException('棚番、ゾーンコード、商品コードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeMidRepository.deleteMidProductCheckInfo(
      storeCode: store.code,
      zoneCode: zoneCode,
      productCode: productCode,
      shelfNo: shelfNo,
      caller: caller,
      nextExpiryDate: nextExpiryDate,
    );
  }

  /// ミッドのメンテナンス商品取得
  Future<MidMaintenanceProduct?> fetchMidMaintenanceProduct(
    String productCode,
    String? shelfNo,
    int? zoneCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (shelfNo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeMidRepository.fetchMidMaintenanceProduct(
      productCode: productCode,
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfNo: shelfNo,
    );
  }

  /// 生鮮のメンテナンス一覧データ取得
  Future<List<MidMaintenanceListProduct>> fetchMidMaintenanceProductList(
    String? shelfNo,
    int? zoneCode,
  ) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (shelfNo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeMidRepository.fetchMidMaintenanceProductList(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfNo: shelfNo,
    );
  }

  /// ミッド商品の連続スキャンの時、前の商品登録
  Future<void> scanRegisterMidProduct({
    required String? shelfNo,
    required int? zoneCode,
    required String newProductCode,
    required MidMaintenanceProduct midProductInfo,
    required DateTime nextExpiryDate,
  }) async {
    if (shelfNo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeMidRepository.scanRegisterMidProduct(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfNo: shelfNo,
      newProductCode: newProductCode,
      midProductInfo: midProductInfo,
      nextExpiryDate: nextExpiryDate,
    );
  }

  /// ミッド商品の連続スキャンの時、前の商品登録
  Future<void> registerMidProduct({
    required String? shelfNo,
    required int? zoneCode,
    required String productCode,
    required DateTime nextExpiryDate,
  }) async {
    if (shelfNo == null || zoneCode == null) {
      throw UnknownException('棚番号とゾーンコードを指定してください');
    }
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return _shelfLifeMidRepository.registerMidProduct(
      storeCode: store.code,
      zoneCode: zoneCode,
      caller: caller,
      shelfNo: shelfNo,
      productCode: productCode,
      nextExpiryDate: nextExpiryDate,
    );
  }
}
