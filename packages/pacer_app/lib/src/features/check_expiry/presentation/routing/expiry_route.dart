import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../exceptions/app_exception.dart';
import '../../../../routing/app_router.dart';
import '../../domain/enum/shelf_product_type.dart';
import '../../domain/enum/shelf_type.dart';
import '../page/check_shelf_page/check_drug_shelf_page/check_drug_shelf_page.dart';
import '../page/check_shelf_page/check_fresh_shelf_page/check_fresh_shelf_page.dart';
import '../page/check_shelf_page/check_mid_shelf_page/check_mid_shelf_page.dart';
import '../page/check_shelf_page/check_standard_shelf_page/check_standard_shelf_page.dart';
import '../page/check_shelf_page/fresh_discount_page/fresh_discount_page.dart';
import '../page/check_shelf_page/fresh_new_product_page/fresh_new_product_list_page.dart';
import '../page/check_shelf_page/fresh_new_product_page/fresh_new_product_register_page.dart';
import '../page/end_return/end_return_page.dart';
import '../page/expiry_top_page/expiry_top_page.dart';
import '../page/history_page/history_drug_page/history_drug_page.dart';
import '../page/history_page/history_other_page/history_other_page.dart';
import '../page/maintenance_page/drug_page/maintenance_drug_page.dart';
import '../page/maintenance_page/fresh_page/maintenance_fresh_list_page.dart';
import '../page/maintenance_page/fresh_page/maintenance_fresh_page.dart';
import '../page/maintenance_page/mid_page/maintenance_mid_list_page.dart';
import '../page/maintenance_page/mid_page/maintenance_mid_page.dart';
import '../page/maintenance_page/special_page/maintenance_special_page.dart';
import '../page/shelf_list_page/shelf_list_page.dart';
import '../page/zone_list_page/zone_list_page.dart';

/// 賞味期限チェックTOP
class ExpiryTopRoute extends GoRouteData {
  /// constructor
  const ExpiryTopRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const ExpiryTopPage();
}

/// ゾーン一覧
class ExpiryZoneListRoute extends GoRouteData {
  /// constructor
  const ExpiryZoneListRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const ZoneListPage();
}

/// ドラッグのメンテナンス
class ExpiryMaintenanceDrugRoute extends GoRouteData {
  /// constructor
  const ExpiryMaintenanceDrugRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const MaintenanceDrugPage();
}

/// 生鮮のメンテナンス
class ExpiryMaintenanceFreshRoute extends GoRouteData {
  /// constructor
  const ExpiryMaintenanceFreshRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const MaintenanceFreshPage();
}

/// 生鮮のメンテナンスの一覧ページ
class ExpiryMaintenanceFreshListRoute extends GoRouteData {
  /// constructor
  const ExpiryMaintenanceFreshListRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const MaintenanceFreshListPage();
}

/// ミッドのメンテナンス
class ExpiryMaintenanceMidRoute extends GoRouteData {
  /// constructor
  const ExpiryMaintenanceMidRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const MaintenanceMidPage();
}

/// ミッドのメンテナンスの一覧ページ
class ExpiryMaintenanceMidListRoute extends GoRouteData {
  /// constructor
  const ExpiryMaintenanceMidListRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const MaintenanceMidListPage();
}

/// 棚一覧
class ExpiryShelfListRoute extends GoRouteData {
  /// constructor
  const ExpiryShelfListRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const ShelfListPage();
}

/// ドラッグ棚の賞味期限チェック
class ExpiryCheckDrugShelfRoute extends GoRouteData {
  /// constructor
  const ExpiryCheckDrugShelfRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const CheckDrugShelfPage();
}

/// 生鮮の新規登録
class ExpiryFreshNewProductListRoute extends GoRouteData {
  /// constructor
  const ExpiryFreshNewProductListRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const FreshNewProductListPage();
}

/// 生鮮の新規登録
class ExpiryFreshNewProductRegisterRoute extends GoRouteData {
  /// constructor
  const ExpiryFreshNewProductRegisterRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const FreshNewProductRegisterPage();
}

/// ドラッグ履歴
class ExpiryHistoryDrugRoute extends GoRouteData {
  /// constructor
  const ExpiryHistoryDrugRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const HistoryDrugPage();
}

/// その他の履歴
class ExpiryHistoryOtherRoute extends GoRouteData {
  /// constructor
  const ExpiryHistoryOtherRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const HistoryOtherPage();
}

/// 生鮮の賞味期限チェック
class ExpiryCheckFreshShelfRoute extends GoRouteData {
  /// constructor
  const ExpiryCheckFreshShelfRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const CheckFreshShelfPage();
}

/// 生鮮の値下げ
class ExpiryFreshDiscountRoute extends GoRouteData {
  /// constructor
  const ExpiryFreshDiscountRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const FreshDiscountPage();
}

/// 定番棚の賞味期限チェック
class ExpiryCheckStandardShelfRoute extends GoRouteData {
  /// constructor
  const ExpiryCheckStandardShelfRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const CheckStandardShelfPage();
}

/// エンド戻し
class ExpiryEndReturnRoute extends GoRouteData {
  /// constructor
  const ExpiryEndReturnRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const EndReturnPage();
}

/// ミッド棚の賞味期限チェック
class ExpiryCheckMidShelfRoute extends GoRouteData {
  /// constructor
  const ExpiryCheckMidShelfRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const CheckMidShelfPage();
}

/// 特殊棚のメンテナンス
class ExpiryMaintenanceSpecialRoute extends GoRouteData {
  /// constructor
  const ExpiryMaintenanceSpecialRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const MaintenanceSpecialPage();
}

/// 賞味期限チェックで棚タイプによって画面遷移を行う
void goExpiryCheckShelf({
  required BuildContext context,
  required ShelfType? shelfType,
  required ShelfProductType? shelfProductType,
}) {
  if (shelfType == null || shelfProductType == null) {
    throw UnknownException('棚情報が不正です');
  }
  if (shelfProductType != ShelfProductType.unspecified) {
    const ExpiryCheckStandardShelfRoute().go(context);
  }

  switch (shelfType) {
    case ShelfType.drug:
      const ExpiryCheckDrugShelfRoute().go(context);
    case ShelfType.standard:
      const ExpiryCheckStandardShelfRoute().go(context);
    case ShelfType.end:
      const ExpiryCheckStandardShelfRoute().go(context);
    case ShelfType.mid:
      const ExpiryCheckMidShelfRoute().go(context);
    case ShelfType.endmid:
      const ExpiryCheckStandardShelfRoute().go(context);
    case ShelfType.fresh:
      const ExpiryCheckFreshShelfRoute().go(context);
    case ShelfType.newData:
      const ExpiryFreshNewProductListRoute().go(context);
    case ShelfType.discount:
      const ExpiryFreshDiscountRoute().go(context);
  }
}
