import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../device/application/pacer_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../../domain/shelf_list_info.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/default_bottom_app_bar.dart';
import '../../../common_widgets/other_history_table.dart';
import 'history_other_state.dart';

/// その他のメンテナンスページ
/// 履歴取得のみ
/// 副作用なし
class HistoryOtherPage extends HookConsumerWidget {
  /// constructor
  const HistoryOtherPage({super.key});

  Future<void> _onScan(WidgetRef ref, String code) async {
    final shelfInfo = ref.read(shelfInfoListProvider.notifier).get(code);
    if (shelfInfo == null) {
      await showAlertDialog(
        context: ref.context,
        title: '正しい棚番をスキャンしてください',
      );
      return;
    }
    ref.read(selectShelfInfoProvider.notifier).update(shelfInfo);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const title = 'その他 履歴確認';

    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final selectShelfInfo = ref.watch(selectShelfInfoProvider);

    ref
      ..listen(
        shelfInfoListProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(scanCodeProvider, (_, code) {
        if (code case AsyncData(:final value)) {
          _onScan(ref, value);
        }
      });

    return Scaffold(
      appBar: const DefaultAppBar(title: title),
      bottomNavigationBar: const DefaultBottomAppBar(isNotched: true),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: ScanFloatingIconButton(
        onScan: (code) => _onScan(ref, code),
      ),
      resizeToAvoidBottomInset: false,
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const _ShelfNumberDropdown(),
            const Gap(15),
            if (selectShelfInfo != null) ...[
              Text(
                '履歴',
                style: texts.labelLarge?.copyWith(color: colors.subText),
              ),
              const Gap(4),
              const _HisotryListView(),
            ],
          ],
        ),
      ),
    );
  }
}

/// タイトル部分（ヘッダー）
class _ShelfNumberDropdown extends HookConsumerWidget {
  const _ShelfNumberDropdown();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final shelfInfoList = ref.watch(shelfInfoListProvider).valueOrNull ?? [];
    final selectShelfInfo = ref.watch(selectShelfInfoProvider);
    final textEditingController = useTextEditingController();

    useEffect(
      () {
        textEditingController.text = selectShelfInfo?.shelfNumberText ?? '';

        return null;
      },
      [selectShelfInfo],
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '棚番',
          style: texts.labelLarge?.copyWith(color: colors.subText),
        ),
        const Gap(4),
        Container(
          decoration: BoxDecoration(
            color: colors.button,
            border: Border.all(
              color: colors.primary.withOpacity(0.2),
            ),
            borderRadius: const BorderRadius.all(Radius.circular(4)),
          ),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: DropdownButton<ShelfInfo?>(
                  underline: const SizedBox.shrink(),
                  isExpanded: true,
                  items: shelfInfoList
                      .map(
                        (e) => DropdownMenuItem(
                          value: e,
                          child: Text(
                            '${e.shelfNumberText}  ${e.shelfName}',
                          ),
                        ),
                      )
                      .toList(),
                  onChanged: ref.read(selectShelfInfoProvider.notifier).update,
                  value: selectShelfInfo,
                  menuMaxHeight: 400,
                ),
              ),
              SizedBox(
                width: (MediaQuery.of(context).size.width - 16) * 0.9,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: TextFormField(
                    controller: textEditingController,
                    keyboardType: TextInputType.visiblePassword,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    decoration: InputDecoration(
                      filled: true,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 4,
                        vertical: 8,
                      ),
                      border: InputBorder.none,
                      fillColor: colors.button,
                      focusColor: colors.button,
                      hintText: '棚番を入力してください',
                      hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
                      counterText: '',
                    ),
                    onFieldSubmitted: (value) => onFieldSubmitted(ref, value),
                    onTapOutside: (_) => FocusScope.of(context).unfocus(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void onFieldSubmitted(WidgetRef ref, String value) {
    final shelfInfo = ref.read(shelfInfoListProvider.notifier).get(value);
    if (shelfInfo == null) {
      showAlertDialog(
        context: ref.context,
        title: '棚番を正しく入力してください',
      );

      return;
    }
    ref.read(selectShelfInfoProvider.notifier).update(shelfInfo);
  }
}

/// 履歴のリスト
class _HisotryListView extends ConsumerWidget {
  const _HisotryListView();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final shelfNumberText = ref.watch(selectShelfInfoProvider)?.shelfNumberText;
    if (shelfNumberText == null) {
      return const SizedBox.shrink();
    }
    final shelfHistoryList = ref.watch(shelfHistoryListProvider(shelfNumberText));

    return Expanded(
      child: switch (shelfHistoryList) {
        AsyncLoading() => const Center(child: CircularProgressIndicator()),
        AsyncError() => const SizedBox.shrink(),
        AsyncData(:final value) when value.productList.isEmpty => const _NoHistoryText(),
        AsyncData(:final value) => ListView.separated(
            itemCount: value.productList.length,
            separatorBuilder: (BuildContext context, int index) {
              return const Gap(8);
            },
            itemBuilder: (BuildContext context, int index) {
              final historyProduct = value.productList[index];

              return Column(
                children: [
                  OtherHistoryTable.fromHistoryProduct(historyProduct),
                  if (index == value.productList.length - 1) const Gap(40),
                ],
              );
            },
          ),
      },
    );
  }
}

/// 「履歴はまだありません」
class _NoHistoryText extends StatelessWidget {
  const _NoHistoryText();

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.all(32),
      child: Center(
        child: Text(
          '履歴はまだありません',
          style: texts.titleMedium?.copyWith(
            color: colors.subText,
            fontSize: 18,
          ),
        ),
      ),
    );
  }
}
