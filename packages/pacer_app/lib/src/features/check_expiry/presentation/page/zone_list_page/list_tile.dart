part of 'zone_list_page.dart';

/// ゾーン一覧のリストタイル
class _ZoneListTile extends StatelessWidget {
  const _ZoneListTile({
    required this.zone,
    this.onPressed,
  });

  final Zone zone;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final tileColor = zone.isDone ? colors.successContainer : Colors.transparent;

    return InkWell(
      onTap: onPressed,
      child: ColoredBox(
        color: tileColor,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16).copyWith(left: 18, right: 8),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  zone.zoneName,
                  style: texts.titleMedium,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (zone.isDone)
                Assets.commonIcon.iconCheckCircle.image(width: 24)
              else if (zone.isDanger)
                Assets.commonIcon.iconStatusCaution.image(width: 24),
              const Gap(17),
              Text(zone.checkedTextOfNumber, style: texts.titleMedium),
              const Gap(8),
              const Icon(Icons.arrow_right, size: 24),
            ],
          ),
        ),
      ),
    );
  }
}
