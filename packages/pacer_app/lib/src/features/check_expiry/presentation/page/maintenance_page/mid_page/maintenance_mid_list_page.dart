import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/mid_maintenance_list_product.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/empty_data_text.dart';
import '../../../common_widgets/maintenance_product_list_table.dart';
import 'maintenance_mid_state.dart';
import 'widgets/maintenance_mid_list_bottom_app_bar.dart';

/// 生鮮のメンテナンスの商品一覧ページ
class MaintenanceMidListPage extends HookConsumerWidget {
  /// constructor
  const MaintenanceMidListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectShelfInfo = ref.watch(selectShelfInfoProvider);
    final freshProducts = ref.watch(midProductListProvider);
    final selectProductList = ref.watch(selectProductListProvider);

    return Scaffold(
      appBar: const DefaultAppBar(),
      bottomNavigationBar: const MaintenanceMidListBottomAppBar(),
      body: switch (freshProducts) {
        AsyncError() => const SizedBox.shrink(),
        AsyncLoading() => const Center(child: CircularProgressIndicator()),
        AsyncData(:final value) when value.isEmpty => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Gap(24),
                _Title(
                  '${selectShelfInfo?.shelfNumberText}  '
                  '${selectShelfInfo?.shelfName}',
                ),
                _ProductCountText(value.length),
                const Gap(32),
                const EmptyDataText(title: '削除可能な商品はありません'),
              ],
            ),
          ),
        AsyncData(:final value) => ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            itemCount: value.length,
            separatorBuilder: (BuildContext context, int index) {
              return const Gap(6);
            },
            itemBuilder: (BuildContext context, int index) {
              final product = value[index];
              final isCheckd = selectProductList.contains(product);

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (index == 0) ...[
                    const Gap(24),
                    _Title(
                      '${selectShelfInfo?.shelfNumberText}  '
                      '${selectShelfInfo?.shelfName}',
                    ),
                    _ProductCountText(value.length),
                  ],
                  MaintenanceProductListTable.midMaintenanceListProduct(
                    product: product,
                    isCheckd: isCheckd,
                    onChanged: ({bool? isCheckd}) => onCheckeChanged(
                      ref: ref,
                      isCheckd: isCheckd,
                      product: product,
                    ),
                  ),
                  if (index == value.length - 1) ...[
                    const Gap(24),
                  ],
                ],
              );
            },
          ),
      },
    );
  }

  /// チェックボックスの変更イベント
  void onCheckeChanged({
    required WidgetRef ref,
    required bool? isCheckd,
    required MidMaintenanceListProduct product,
  }) {
    if (isCheckd == null) return;

    isCheckd
        ? ref.read(selectProductListProvider.notifier).add(product)
        : ref.read(selectProductListProvider.notifier).remove(product);
  }
}

/// タイトル部分（ヘッダー）
class _Title extends StatelessWidget {
  const _Title(this.shelfName);

  final String shelfName;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          shelfName,
          style: texts.titleMedium?.copyWith(color: colors.primary, fontSize: 18),
        ),
        const Gap(8),
        Text(
          '削除する商品を選択してください',
          style: texts.titleMedium,
        ),
      ],
    );
  }
}

/// 対象商品（0）の表示
class _ProductCountText extends StatelessWidget {
  const _ProductCountText(this.count);

  final int count;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Text(
        '対象商品 ($count)',
        style: texts.labelLarge?.copyWith(color: colors.subText),
      ),
    );
  }
}
