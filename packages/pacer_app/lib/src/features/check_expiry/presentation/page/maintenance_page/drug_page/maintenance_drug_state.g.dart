// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'maintenance_drug_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$drugShelfListInfoHash() => r'779652a49ba1bcd05d11f78a98c65cbbeaebb7e7';

/// ドラッグ棚一覧情報
///
/// Copied from [DrugShelfListInfo].
@ProviderFor(DrugShelfListInfo)
final drugShelfListInfoProvider = AutoDisposeAsyncNotifierProvider<DrugShelfListInfo, ShelfListInfo>.internal(
  DrugShelfListInfo.new,
  name: r'drugShelfListInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$drugShelfListInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DrugShelfListInfo = AutoDisposeAsyncNotifier<ShelfListInfo>;
String _$drugProductInfoStateHash() => r'618294d9b0ac0a39614d705b87b0a9dfef8f2f92';

/// ドラッグ商品情報
///
/// Copied from [DrugProductInfoState].
@ProviderFor(DrugProductInfoState)
final drugProductInfoStateProvider = AutoDisposeNotifierProvider<DrugProductInfoState, DrugProductInfo?>.internal(
  DrugProductInfoState.new,
  name: r'drugProductInfoStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$drugProductInfoStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DrugProductInfoState = AutoDisposeNotifier<DrugProductInfo?>;
String _$drugProductInfoControllerHash() => r'f80dffccaccbcf5f3b5d5b45b8d629aab54809e5';

/// See also [DrugProductInfoController].
@ProviderFor(DrugProductInfoController)
final drugProductInfoControllerProvider = AutoDisposeAsyncNotifierProvider<DrugProductInfoController, void>.internal(
  DrugProductInfoController.new,
  name: r'drugProductInfoControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$drugProductInfoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DrugProductInfoController = AutoDisposeAsyncNotifier<void>;
String _$endFlgHash() => r'3cab7bdce3601fde36ec9390abee907973f0b624';

/// エンドボタンのチェックマークの状態
///
/// Copied from [EndFlg].
@ProviderFor(EndFlg)
final endFlgProvider = AutoDisposeNotifierProvider<EndFlg, bool>.internal(
  EndFlg.new,
  name: r'endFlgProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$endFlgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EndFlg = AutoDisposeNotifier<bool>;
String _$midFlgHash() => r'4c5952d20d7ce79a14f989cc441a8888200f0f24';

/// ミッドボタンのチェックマークの状態
///
/// Copied from [MidFlg].
@ProviderFor(MidFlg)
final midFlgProvider = AutoDisposeNotifierProvider<MidFlg, bool>.internal(
  MidFlg.new,
  name: r'midFlgProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$midFlgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MidFlg = AutoDisposeNotifier<bool>;
String _$nextExpiryDateTextHash() => r'014707841960da0115e345febff23b89354860b0';

/// 次回の賞味期限の文字
///
/// Copied from [NextExpiryDateText].
@ProviderFor(NextExpiryDateText)
final nextExpiryDateTextProvider = AutoDisposeNotifierProvider<NextExpiryDateText, String>.internal(
  NextExpiryDateText.new,
  name: r'nextExpiryDateTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateText = AutoDisposeNotifier<String>;
String _$nextExpiryDateValidHash() => r'7d10dd7d4ab3c6ba95699f6ee6e652c4cb1b7b37';

/// See also [NextExpiryDateValid].
@ProviderFor(NextExpiryDateValid)
final nextExpiryDateValidProvider = AutoDisposeNotifierProvider<NextExpiryDateValid, bool>.internal(
  NextExpiryDateValid.new,
  name: r'nextExpiryDateValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateValid = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
