// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_other_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shelfInfoListHash() => r'e2af4993923ef4541f53047b98a9b9ba31a83ff4';

/// 棚一覧情報
///
/// Copied from [ShelfInfoList].
@ProviderFor(ShelfInfoList)
final shelfInfoListProvider = AutoDisposeAsyncNotifierProvider<ShelfInfoList, List<ShelfInfo>>.internal(
  ShelfInfoList.new,
  name: r'shelfInfoListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$shelfInfoListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ShelfInfoList = AutoDisposeAsyncNotifier<List<ShelfInfo>>;
String _$shelfHistoryListHash() => r'beb7146cbd4c26889d4c9b27c564bfb96f849262';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ShelfHistoryList extends BuildlessAutoDisposeAsyncNotifier<ShelfHistory> {
  late final String shelfNumberText;

  FutureOr<ShelfHistory> build(
    String shelfNumberText,
  );
}

/// 棚履歴情報
///
/// Copied from [ShelfHistoryList].
@ProviderFor(ShelfHistoryList)
const shelfHistoryListProvider = ShelfHistoryListFamily();

/// 棚履歴情報
///
/// Copied from [ShelfHistoryList].
class ShelfHistoryListFamily extends Family {
  /// 棚履歴情報
  ///
  /// Copied from [ShelfHistoryList].
  const ShelfHistoryListFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'shelfHistoryListProvider';

  /// 棚履歴情報
  ///
  /// Copied from [ShelfHistoryList].
  ShelfHistoryListProvider call(
    String shelfNumberText,
  ) {
    return ShelfHistoryListProvider(
      shelfNumberText,
    );
  }

  @visibleForOverriding
  @override
  ShelfHistoryListProvider getProviderOverride(
    covariant ShelfHistoryListProvider provider,
  ) {
    return call(
      provider.shelfNumberText,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(ShelfHistoryList Function() create) {
    return _$ShelfHistoryListFamilyOverride(this, create);
  }
}

class _$ShelfHistoryListFamilyOverride implements FamilyOverride {
  _$ShelfHistoryListFamilyOverride(this.overriddenFamily, this.create);

  final ShelfHistoryList Function() create;

  @override
  final ShelfHistoryListFamily overriddenFamily;

  @override
  ShelfHistoryListProvider getProviderOverride(
    covariant ShelfHistoryListProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// 棚履歴情報
///
/// Copied from [ShelfHistoryList].
class ShelfHistoryListProvider extends AutoDisposeAsyncNotifierProviderImpl<ShelfHistoryList, ShelfHistory> {
  /// 棚履歴情報
  ///
  /// Copied from [ShelfHistoryList].
  ShelfHistoryListProvider(
    String shelfNumberText,
  ) : this._internal(
          () => ShelfHistoryList()..shelfNumberText = shelfNumberText,
          from: shelfHistoryListProvider,
          name: r'shelfHistoryListProvider',
          debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$shelfHistoryListHash,
          dependencies: ShelfHistoryListFamily._dependencies,
          allTransitiveDependencies: ShelfHistoryListFamily._allTransitiveDependencies,
          shelfNumberText: shelfNumberText,
        );

  ShelfHistoryListProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.shelfNumberText,
  }) : super.internal();

  final String shelfNumberText;

  @override
  FutureOr<ShelfHistory> runNotifierBuild(
    covariant ShelfHistoryList notifier,
  ) {
    return notifier.build(
      shelfNumberText,
    );
  }

  @override
  Override overrideWith(ShelfHistoryList Function() create) {
    return ProviderOverride(
      origin: this,
      override: ShelfHistoryListProvider._internal(
        () => create()..shelfNumberText = shelfNumberText,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        shelfNumberText: shelfNumberText,
      ),
    );
  }

  @override
  (String,) get argument {
    return (shelfNumberText,);
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ShelfHistoryList, ShelfHistory> createElement() {
    return _ShelfHistoryListProviderElement(this);
  }

  ShelfHistoryListProvider _copyWith(
    ShelfHistoryList Function() create,
  ) {
    return ShelfHistoryListProvider._internal(
      () => create()..shelfNumberText = shelfNumberText,
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      shelfNumberText: shelfNumberText,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is ShelfHistoryListProvider && other.shelfNumberText == shelfNumberText;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, shelfNumberText.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ShelfHistoryListRef on AutoDisposeAsyncNotifierProviderRef<ShelfHistory> {
  /// The parameter `shelfNumberText` of this provider.
  String get shelfNumberText;
}

class _ShelfHistoryListProviderElement extends AutoDisposeAsyncNotifierProviderElement<ShelfHistoryList, ShelfHistory>
    with ShelfHistoryListRef {
  _ShelfHistoryListProviderElement(super.provider);

  @override
  String get shelfNumberText => (origin as ShelfHistoryListProvider).shelfNumberText;
}

String _$selectShelfInfoHash() => r'56f6397bfa602448d13524cee4a1c27b0ee212da';

/// 選択された棚番情報
///
/// Copied from [SelectShelfInfo].
@ProviderFor(SelectShelfInfo)
final selectShelfInfoProvider = AutoDisposeNotifierProvider<SelectShelfInfo, ShelfInfo?>.internal(
  SelectShelfInfo.new,
  name: r'selectShelfInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectShelfInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectShelfInfo = AutoDisposeNotifier<ShelfInfo?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
