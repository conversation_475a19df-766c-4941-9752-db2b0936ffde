import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../application/check_expiry_fresh_service.dart';
import '../../../../application/check_expiry_service.dart';
import '../../../../domain/enum/work_status.dart';
import '../../../../domain/next_expiry_date.dart';
import '../../../../domain/product_check_info.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/work_stop_alert_dialog.dart';
import '../check_shelf_page_props_controller.dart';

part 'check_fresh_shelf_state.g.dart';

////ドラッグの棚情報
@riverpod
class CheckFreshShelfInfo extends _$CheckFreshShelfInfo {
  @override
  FutureOr<ProductCheckInfo> build() async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);

    return ref.read(checkExpiryFreshServiceProvider).fetchFreshProductCheckInfo(props?.zoneCode, props?.shelfInfo);
  }

  /// 棚チェック情報を更新する
  void updateCheckInfo(ProductCheckInfo checkInfo) {
    state = AsyncData(checkInfo);
  }
}

///賞味期限チェックの完了処理
@riverpod
class UpdateCheckFreshShelfInfoController extends _$UpdateCheckFreshShelfInfoController {
  @override
  FutureOr<void> build() {}

  ///「次へ」もくは「完了」の場合
  Future<void> updateComplete(
    VoidCallback onSuccess,
  ) async {
    final nextExpiryDateText = ref.watch(nextExpiryDateTextProvider);
    final freshProductCheckInfo = ref.watch(checkFreshShelfInfoProvider).value;

    if (freshProductCheckInfo == null) {
      return;
    }

    try {
      final errorMeesage = getNextExpiryErrorMessage(
        nextExpiryDateText,
        jobDate: freshProductCheckInfo.productInfo.jobDate,
        limitDate: freshProductCheckInfo.limitDate,
        isCheckDiscard: false,
      );

      // エラーメッセージがある場合はエラーをスローする
      if (errorMeesage != null) {
        throw UnknownException(errorMeesage);
      }

      final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(nextExpiryDateText);

      final props = ref.watch(checkShelfPagePropsControllerProvider);
      state = const AsyncValue.loading();
      final productCheckInfo = await AsyncValue.guard(
        () => ref.read(checkExpiryFreshServiceProvider).updateFreshProductCheckInfo(
              zoneCode: props?.zoneCode,
              shelfInfo: props?.shelfInfo,
              productCode: freshProductCheckInfo.productInfo.productCode.value,
              nextExpiryDate: nextExpiryDate,
              workStatus: WorkStatus.normalCompletion,
            ),
      );

      state = const AsyncValue.data(null);
      final newProductCheckInfo = productCheckInfo.valueOrNull;
      if (newProductCheckInfo != null) {
        ref.read(checkFreshShelfInfoProvider.notifier).updateCheckInfo(newProductCheckInfo);
      }
    } catch (err, stack) {
      state = AsyncValue.error(err, stack);
    }
    if (!state.hasError) {
      onSuccess();
    }
  }
}

///値引き確定のコントローラー
@riverpod
class DiscountConfirmController extends _$DiscountConfirmController {
  @override
  FutureOr<void> build() {}

  ///値引き確定処理
  Future<void> discountConfirm(VoidCallback? onSuccess) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final closeoutProductExpiryDateText = ref.watch(closeoutProductExpiryDateTextProvider);

    try {
      final errorMeesage = getNextExpiryErrorMessage(
        closeoutProductExpiryDateText,
        isCheckDiscard: false,
        isTodayPast: false,
      );

      // エラーメッセージがある場合はエラーをスローする
      if (errorMeesage != null) {
        throw UnknownException(errorMeesage);
      }

      final closeoutProductExpiryDate = DateFormat('yyyy年MM月dd日').parse(closeoutProductExpiryDateText);

      final freshProductCheckInfo = ref.watch(checkFreshShelfInfoProvider).value;

      if (freshProductCheckInfo == null) {
        return;
      }
      state = const AsyncValue.loading();
      state = await AsyncValue.guard(
        () => ref.read(checkExpiryFreshServiceProvider).registerExpiryDate(
              zoneCode: props?.zoneCode,
              productCheckInfo: freshProductCheckInfo,
              closeoutExpirationDate: closeoutProductExpiryDate,
            ),
      );
      if (!state.hasError) {
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncValue.error(err, stack);
    }
  }
}

///作業中断、一時停止の場合の処理
@riverpod
class WorkStopShelfCheckController extends _$WorkStopShelfCheckController {
  @override
  FutureOr<void> build() {}

  ///作業中断、一時停止の更新
  Future<void> workStop({
    required WorkStopType workStopType,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryServiceProvider).workStopShelfCheck(
            zoneCode: props?.zoneCode,
            shelfNumberText: props?.shelfInfo.shelfNumberText,
            checkPlanWeek: props?.shelfInfo.checkPlanWeek,
            workStopType: workStopType,
          ),
    );
  }
}

///賞味期限チェック削除用の処理
@riverpod
class DeleteFreshProductCheckInfoController extends _$DeleteFreshProductCheckInfoController {
  @override
  FutureOr<void> build() {}

  ///データの削除
  Future<void> delete({
    required String? productCode,
    required DateTime? expirationDate,
    VoidCallback? onSuccess,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryFreshServiceProvider).deleteFreshProductCheckInfo(
            zoneCode: props?.zoneCode,
            shelfNo: props?.shelfInfo.shelfNumberText,
            productCode: productCode,
            expirationDate: expirationDate,
          ),
    );
    if (!state.hasError) {
      onSuccess?.call();
    }
  }
}

/// 次回の賞味期限の文字
@riverpod
class NextExpiryDateText extends _$NextExpiryDateText {
  @override
  String build() => '';

  ///データの更新
  void update(String text) {
    debugPrint('update nextExpiryDateText');
    state = text;
  }

  ///データのクリア
  void clear() {
    state = '';
  }
}

/// 次回の賞味期限が有効であるかのフラグ
@riverpod
class NextExpiryDateValid extends _$NextExpiryDateValid {
  @override
  bool build() {
    final dateText = ref.watch(nextExpiryDateTextProvider);

    return isValidNextExpiryDate(dateText);
  }
}

/// 見切り商品の賞味期限の文字
@riverpod
class CloseoutProductExpiryDateText extends _$CloseoutProductExpiryDateText {
  @override
  String build() => '';

  ///データの更新
  void update(String text) {
    state = formatNextExpiryDate(text);
  }

  ///データのクリア
  void clear() {
    state = '';
  }
}

/// 見切り商品の賞味期限が有効であるかのフラグ
@riverpod
class CloseoutProductExpiryDateValid extends _$CloseoutProductExpiryDateValid {
  @override
  bool build() {
    final dateText = ref.watch(closeoutProductExpiryDateTextProvider);

    return isValidNextExpiryDate(dateText);
  }
}
