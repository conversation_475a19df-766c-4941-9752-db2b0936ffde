import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../device/application/pacer_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/maintenance_product_table.dart';
import '../../../common_widgets/next_expiry_date_text_field.dart';
import 'maintenance_fresh_state.dart';
import 'widgets/maintenance_fresh_bottom_app_bar.dart';

/// 生鮮のメンテナンスページ
class MaintenanceFreshPage extends HookConsumerWidget {
  /// constructor
  const MaintenanceFreshPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const title = 'メンテナンス（生鮮）登録';

    final focusNode = useFocusNode();

    final freshShelfListInfo = ref.watch(freshShelfListInfoProvider).valueOrNull;
    final selectShelfInfo = ref.watch(selectShelfInfoProvider);
    final freshProductInfo = ref.watch(freshProductInfoStateProvider);

    ref
      ..listen(
        freshProductInfoControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        freshShelfListInfoProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(scanCodeProvider, (_, code) {
        if (code case AsyncData(:final value)) {
          _scan(ref, value);
        }
      });

    return Focus(
      focusNode: focusNode,
      child: GestureDetector(
        onTap: focusNode.requestFocus,
        child: Scaffold(
          appBar: const DefaultAppBar(title: title),
          bottomNavigationBar: const MaintenanceFreshBottomAppBar(),
          floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
          floatingActionButton: ScanFloatingIconButton(
            onScan: (jan) => _scan(ref, jan),
          ),
          resizeToAvoidBottomInset: false,
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MaintenanceProductTable.freshMaintenanceProduct(
                    product: freshProductInfo,
                    shelfInfoList: freshShelfListInfo?.shelfList ?? [],
                    selectShelfInfo: selectShelfInfo,
                    onShelfInfoChanged: ref.read(selectShelfInfoProvider.notifier).update,
                    onProductCodeChanged: (jan) => _scan(ref, jan),
                  ),
                  const Gap(12),
                  const _NextExpiryDateTextField(),
                  const Gap(16),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _scan(WidgetRef ref, String jan) {
    final context = ref.context;
    ref.read(freshProductInfoControllerProvider.notifier).scan(
      jan,
      onFetchProductSuccess: (freshMaintenanceProduct) async {
        // 非生鮮商品の場合はダイアログを表示して商品情報取得をキャンセル
        if (!freshMaintenanceProduct.isFreshProduct) {
          await showAlertDialog(
            context: context,
            title: '生鮮商品以外は登録できません',
          );
          return;
        }
        //登録がないの場合
        if (!freshMaintenanceProduct.isRegistered) {
          ref.read(freshProductInfoStateProvider.notifier).update(freshMaintenanceProduct);

          return;
        }
        final nextExpirationDate = freshMaintenanceProduct.nextExpirationDate;
        if (nextExpirationDate == null) {
          return;
        }
        //登録がある場合は、ダイアログを表示する
        final nextExpirationDateText = DateFormat('yyyy年MM月dd日').format(nextExpirationDate);
        final ok = await showAlertDialog(
          context: context,
          title: '$nextExpirationDateTextの賞味期限で既に登録されています。'
              'この賞味期限より期限の短い商品が売場にありますか？',
          cancelActionText: 'いいえ',
          defaultActionText: 'はい',
        );

        if (!context.mounted) return;
        //登録情報を表示する
        if (ok ?? false) {
          showSnackBar(context, '登録済みの情報を表示しました');
          ref.read(freshProductInfoStateProvider.notifier).update(freshMaintenanceProduct);
        } else {
          //登録情報を見せずリセットする
          showSnackBar(context, '今回のメンテナンス対象ではありません');
          _reset(context, ref);
        }
      },
      //登録完了
      onRegisterSuccess: () {
        showSnackBar(context, '登録しました。');
        ref.read(nextExpiryDateTextProvider.notifier).clear();
      },
    );
  }

  void _reset(BuildContext context, WidgetRef ref) {
    //画面の初期化
    ref.read(nextExpiryDateTextProvider.notifier).clear();
    ref.read(freshProductInfoControllerProvider.notifier).clear();
    ref.read(selectShelfInfoProvider.notifier).clear();
  }
}

/// 次回賞味期限
class _NextExpiryDateTextField extends HookConsumerWidget {
  const _NextExpiryDateTextField();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final text = ref.watch(nextExpiryDateTextProvider);
    final isValid = ref.watch(nextExpiryDateValidProvider);
    final productInfo = ref.watch(freshProductInfoStateProvider);

    final freshShelfListInfo = ref.watch(freshShelfListInfoProvider).valueOrNull;

    return NextExpiryDateTextField(
      inputText: text,
      onChanged: ref.read(nextExpiryDateTextProvider.notifier).update,
      onFieldSubmitted: (text) async {
        final isShowDialog = await showNextExpiryErrorAlertDialog(
          context,
          text,
          jobDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo?.checkDay ?? ''),
          limitDate: freshShelfListInfo?.limitDate,
        );
        if (isShowDialog) {
          ref.read(nextExpiryDateTextProvider.notifier).clear();
        }
      },
      isValid: isValid,
    );
  }
}
