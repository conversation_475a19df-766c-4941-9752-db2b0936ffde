import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../common_widgets/default_bottom_app_bar.dart';
import '../../../../common_widgets/outline_round_text_button.dart';
import '../../../../common_widgets/round_text_button.dart';
import '../../../../routing/expiry_route.dart';
import '../maintenance_mid_state.dart';

/// フッターのボトムバー
class MaintenanceMidBottomAppBar extends ConsumerWidget {
  /// constructor
  const MaintenanceMidBottomAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDataExsits = ref.watch(midProductInfoStateProvider) != null;
    final isSelectedShelf = ref.watch(selectShelfInfoProvider) != null;

    return DefaultBottomAppBar(
      isNotched: true,
      height: 100,
      actions: [
        OutlineRoundTextButton(
          '取消',
          onPressed: () => _onResetPressed(context, ref),
        ),
        const SizedBox(width: 8),
        OutlineRoundTextButton(
          '一覧',
          onPressed: isSelectedShelf ? () => const ExpiryMaintenanceMidListRoute().go(context) : null,
        ),
        const SizedBox(width: 8),
        RoundTextButton(
          '登録',
          onPressed: isDataExsits
              ? () => _onRegistPressed(
                    context,
                    ref,
                  )
              : null,
        ),
      ],
    );
  }

  void _reset(BuildContext context, WidgetRef ref) {
    //画面の初期化
    ref.read(nextExpiryDateTextProvider.notifier).clear();
    ref.read(midProductInfoControllerProvider.notifier).clear();
    ref.read(selectShelfInfoProvider.notifier).clear();
  }

  Future<void> _onResetPressed(BuildContext context, WidgetRef ref) async {
    _reset(context, ref);
    showSnackBar(context, '入力内容を取り消しました');
  }

  Future<void> _onRegistPressed(BuildContext context, WidgetRef ref) async {
    final ok = await showAlertDialog(
      context: ref.context,
      title: 'データを登録します。よろしいですか？',
      cancelActionText: 'キャンセル',
      defaultActionText: '登録する',
    );
    if (ok ?? false) {
      await ref.watch(midProductInfoControllerProvider.notifier).register(
        onSuccess: () {
          // 棚番以外の情報をリセット
          ref.read(nextExpiryDateTextProvider.notifier).clear();
          ref.read(midProductInfoControllerProvider.notifier).clear();
          showSnackBar(context, '登録しました。');
        },
      );
    }
  }
}
