import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../application/check_expiry_mid_service.dart';
import '../../../../application/check_expiry_service.dart';
import '../../../../domain/enum/work_status.dart';
import '../../../../domain/next_expiry_date.dart';
import '../../../../domain/product_check_info.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/work_stop_alert_dialog.dart';
import '../check_shelf_page_props_controller.dart';

part 'check_mid_shelf_state.g.dart';

///ミッドの棚情報
@riverpod
class CheckMidShelfInfo extends _$CheckMidShelfInfo {
  @override
  FutureOr<ProductCheckInfo> build() async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);

    return ref.read(checkExpiryMidServiceProvider).fetchMidProductCheckInfo(props?.zoneCode, props?.shelfInfo);
  }

  /// 棚チェック情報を更新する
  void updateCheckInfo(ProductCheckInfo checkInfo) {
    state = AsyncData(checkInfo);
  }
}

///賞味期限チェックの完了処理
@riverpod
class UpdateCheckMidShelfInfoController extends _$UpdateCheckMidShelfInfoController {
  @override
  FutureOr<void> build() {}

  ///「次へ」もくは「完了」の場合
  Future<void> updateComplete(
    VoidCallback onSuccess,
  ) async {
    final nextExpiryDateText = ref.watch(nextExpiryDateTextProvider);
    final midProductCheckInfo = ref.watch(checkMidShelfInfoProvider).value;

    if (midProductCheckInfo == null) {
      return;
    }

    try {
      final errorMeesage = getNextExpiryErrorMessage(
        nextExpiryDateText,
        jobDate: midProductCheckInfo.productInfo.jobDate,
        limitDate: midProductCheckInfo.limitDate,
        isCheckDiscard: false,
      );

      // エラーメッセージがある場合はエラーをスローする
      if (errorMeesage != null) {
        throw UnknownException(errorMeesage);
      }

      final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(nextExpiryDateText);

      final props = ref.watch(checkShelfPagePropsControllerProvider);
      state = const AsyncValue.loading();
      state = await AsyncValue.guard(
        () => ref.read(checkExpiryMidServiceProvider).updateMidProductCheckInfo(
              zoneCode: props?.zoneCode,
              shelfInfo: props?.shelfInfo,
              productCode: midProductCheckInfo.productInfo.productCode.value,
              nextExpiryDate: nextExpiryDate,
              workStatus: WorkStatus.normalCompletion,
            ),
      );
      if (!state.hasError) {
        onSuccess();
      }

      state = const AsyncValue.data(null);
    } catch (err, stack) {
      state = AsyncValue.error(err, stack);
    }
  }
}

///作業中断、一時停止の場合の処理
@riverpod
class WorkStopShelfCheckController extends _$WorkStopShelfCheckController {
  @override
  FutureOr<void> build() {}

  ///作業中断、一時停止の更新
  Future<void> workStop({
    required WorkStopType workStopType,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryServiceProvider).workStopShelfCheck(
            zoneCode: props?.zoneCode,
            shelfNumberText: props?.shelfInfo.shelfNumberText,
            checkPlanWeek: props?.shelfInfo.checkPlanWeek,
            workStopType: workStopType,
          ),
    );
  }
}

///賞味期限チェック削除用の処理
@riverpod
class DeleteMidProductCheckInfoController extends _$DeleteMidProductCheckInfoController {
  @override
  FutureOr<void> build() {}

  ///データの削除
  Future<void> delete({
    required String? productCode,
    VoidCallback? onSuccess,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final drugProductCheckInfo = ref.watch(checkMidShelfInfoProvider).value;

    if (drugProductCheckInfo == null) {
      return;
    }
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryMidServiceProvider).deleteMidProductCheckInfo(
            zoneCode: props?.zoneCode,
            shelfNo: props?.shelfInfo.shelfNumberText,
            productCode: productCode,
            nextExpiryDate: drugProductCheckInfo.productInfo.expirationDate,
          ),
    );
    if (!state.hasError) {
      onSuccess?.call();
    }
  }
}

/// 次回の賞味期限の文字
@riverpod
class NextExpiryDateText extends _$NextExpiryDateText {
  @override
  String build() => '';

  ///データの更新
  void update(String text) {
    debugPrint('update nextExpiryDateText');
    state = text;
  }

  ///データのクリア
  void clear() {
    state = '';
  }
}

/// 次回の賞味期限が有効であるかのフラグ
@riverpod
class NextExpiryDateValid extends _$NextExpiryDateValid {
  @override
  bool build() {
    final dateText = ref.watch(nextExpiryDateTextProvider);

    return isValidNextExpiryDate(dateText);
  }
}
