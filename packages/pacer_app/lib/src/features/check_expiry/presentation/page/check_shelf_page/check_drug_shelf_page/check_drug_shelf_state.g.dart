// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_drug_shelf_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkDrugShelfInfoHash() => r'186ec383f37ad3f4fda8429bb71abb48d9fbc5ab';

///ドラッグの棚情報
///
/// Copied from [CheckDrugShelfInfo].
@ProviderFor(CheckDrugShelfInfo)
final checkDrugShelfInfoProvider = AutoDisposeAsyncNotifierProvider<CheckDrugShelfInfo, ProductCheckInfo>.internal(
  CheckDrugShelfInfo.new,
  name: r'checkDrugShelfInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkDrugShelfInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckDrugShelfInfo = AutoDisposeAsyncNotifier<ProductCheckInfo>;
String _$updateCheckDrugShelfInfoControllerHash() => r'd8d5d85109c3c8709e488c8d43af08d9f3694ef3';

///賞味期限チェックの完了処理
///
/// Copied from [UpdateCheckDrugShelfInfoController].
@ProviderFor(UpdateCheckDrugShelfInfoController)
final updateCheckDrugShelfInfoControllerProvider =
    AutoDisposeAsyncNotifierProvider<UpdateCheckDrugShelfInfoController, void>.internal(
  UpdateCheckDrugShelfInfoController.new,
  name: r'updateCheckDrugShelfInfoControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$updateCheckDrugShelfInfoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateCheckDrugShelfInfoController = AutoDisposeAsyncNotifier<void>;
String _$workStopShelfCheckControllerHash() => r'0de2c8723024bcbf6ccba1ecd9bc87bb09d308a8';

///作業中断、一時停止の場合の処理
///
/// Copied from [WorkStopShelfCheckController].
@ProviderFor(WorkStopShelfCheckController)
final workStopShelfCheckControllerProvider =
    AutoDisposeAsyncNotifierProvider<WorkStopShelfCheckController, void>.internal(
  WorkStopShelfCheckController.new,
  name: r'workStopShelfCheckControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$workStopShelfCheckControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WorkStopShelfCheckController = AutoDisposeAsyncNotifier<void>;
String _$deleteDrugProductCheckInfoControllerHash() => r'6f6bdf7d8ed9a5eed7f366fa4d49d75675ab18cb';

///賞味期限チェック削除用の処理
///
/// Copied from [DeleteDrugProductCheckInfoController].
@ProviderFor(DeleteDrugProductCheckInfoController)
final deleteDrugProductCheckInfoControllerProvider =
    AutoDisposeAsyncNotifierProvider<DeleteDrugProductCheckInfoController, void>.internal(
  DeleteDrugProductCheckInfoController.new,
  name: r'deleteDrugProductCheckInfoControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$deleteDrugProductCheckInfoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteDrugProductCheckInfoController = AutoDisposeAsyncNotifier<void>;
String _$endFlgHash() => r'3cab7bdce3601fde36ec9390abee907973f0b624';

/// エンドボタンのチェックマークの状態
///
/// Copied from [EndFlg].
@ProviderFor(EndFlg)
final endFlgProvider = AutoDisposeNotifierProvider<EndFlg, bool>.internal(
  EndFlg.new,
  name: r'endFlgProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$endFlgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EndFlg = AutoDisposeNotifier<bool>;
String _$midFlgHash() => r'4c5952d20d7ce79a14f989cc441a8888200f0f24';

/// ミッドボタンのチェックマークの状態
///
/// Copied from [MidFlg].
@ProviderFor(MidFlg)
final midFlgProvider = AutoDisposeNotifierProvider<MidFlg, bool>.internal(
  MidFlg.new,
  name: r'midFlgProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$midFlgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MidFlg = AutoDisposeNotifier<bool>;
String _$nextExpiryDateTextHash() => r'014707841960da0115e345febff23b89354860b0';

/// 次回の賞味期限の文字
///
/// Copied from [NextExpiryDateText].
@ProviderFor(NextExpiryDateText)
final nextExpiryDateTextProvider = AutoDisposeNotifierProvider<NextExpiryDateText, String>.internal(
  NextExpiryDateText.new,
  name: r'nextExpiryDateTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateText = AutoDisposeNotifier<String>;
String _$nextExpiryDateValidHash() => r'7d10dd7d4ab3c6ba95699f6ee6e652c4cb1b7b37';

/// 次回の賞味期限が有効であるかのフラグ
///
/// Copied from [NextExpiryDateValid].
@ProviderFor(NextExpiryDateValid)
final nextExpiryDateValidProvider = AutoDisposeNotifierProvider<NextExpiryDateValid, bool>.internal(
  NextExpiryDateValid.new,
  name: r'nextExpiryDateValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateValid = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
