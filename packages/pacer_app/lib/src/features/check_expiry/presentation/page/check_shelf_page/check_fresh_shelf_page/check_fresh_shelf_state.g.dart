// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_fresh_shelf_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkFreshShelfInfoHash() => r'7701aab39be8ba55ce5e3e69acb66869c2b8b644'; ////ドラッグの棚情報
///
/// Copied from [CheckFreshShelfInfo].
@ProviderFor(CheckFreshShelfInfo)
final checkFreshShelfInfoProvider = AutoDisposeAsyncNotifierProvider<CheckFreshShelfInfo, ProductCheckInfo>.internal(
  CheckFreshShelfInfo.new,
  name: r'checkFreshShelfInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkFreshShelfInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckFreshShelfInfo = AutoDisposeAsyncNotifier<ProductCheckInfo>;
String _$updateCheckFreshShelfInfoControllerHash() => r'63438bc5872d30ff5983b2f66bfc97eb91f71d54';

///賞味期限チェックの完了処理
///
/// Copied from [UpdateCheckFreshShelfInfoController].
@ProviderFor(UpdateCheckFreshShelfInfoController)
final updateCheckFreshShelfInfoControllerProvider =
    AutoDisposeAsyncNotifierProvider<UpdateCheckFreshShelfInfoController, void>.internal(
  UpdateCheckFreshShelfInfoController.new,
  name: r'updateCheckFreshShelfInfoControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$updateCheckFreshShelfInfoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateCheckFreshShelfInfoController = AutoDisposeAsyncNotifier<void>;
String _$discountConfirmControllerHash() => r'3675cbd735c02f516b915cb432cb4388f291365a';

///値引き確定のコントローラー
///
/// Copied from [DiscountConfirmController].
@ProviderFor(DiscountConfirmController)
final discountConfirmControllerProvider = AutoDisposeAsyncNotifierProvider<DiscountConfirmController, void>.internal(
  DiscountConfirmController.new,
  name: r'discountConfirmControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$discountConfirmControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DiscountConfirmController = AutoDisposeAsyncNotifier<void>;
String _$workStopShelfCheckControllerHash() => r'0de2c8723024bcbf6ccba1ecd9bc87bb09d308a8';

///作業中断、一時停止の場合の処理
///
/// Copied from [WorkStopShelfCheckController].
@ProviderFor(WorkStopShelfCheckController)
final workStopShelfCheckControllerProvider =
    AutoDisposeAsyncNotifierProvider<WorkStopShelfCheckController, void>.internal(
  WorkStopShelfCheckController.new,
  name: r'workStopShelfCheckControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$workStopShelfCheckControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WorkStopShelfCheckController = AutoDisposeAsyncNotifier<void>;
String _$deleteFreshProductCheckInfoControllerHash() => r'ad0302799168ef42037247fd66943d1340c651dd';

///賞味期限チェック削除用の処理
///
/// Copied from [DeleteFreshProductCheckInfoController].
@ProviderFor(DeleteFreshProductCheckInfoController)
final deleteFreshProductCheckInfoControllerProvider =
    AutoDisposeAsyncNotifierProvider<DeleteFreshProductCheckInfoController, void>.internal(
  DeleteFreshProductCheckInfoController.new,
  name: r'deleteFreshProductCheckInfoControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$deleteFreshProductCheckInfoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteFreshProductCheckInfoController = AutoDisposeAsyncNotifier<void>;
String _$nextExpiryDateTextHash() => r'014707841960da0115e345febff23b89354860b0';

/// 次回の賞味期限の文字
///
/// Copied from [NextExpiryDateText].
@ProviderFor(NextExpiryDateText)
final nextExpiryDateTextProvider = AutoDisposeNotifierProvider<NextExpiryDateText, String>.internal(
  NextExpiryDateText.new,
  name: r'nextExpiryDateTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateText = AutoDisposeNotifier<String>;
String _$nextExpiryDateValidHash() => r'7d10dd7d4ab3c6ba95699f6ee6e652c4cb1b7b37';

/// 次回の賞味期限が有効であるかのフラグ
///
/// Copied from [NextExpiryDateValid].
@ProviderFor(NextExpiryDateValid)
final nextExpiryDateValidProvider = AutoDisposeNotifierProvider<NextExpiryDateValid, bool>.internal(
  NextExpiryDateValid.new,
  name: r'nextExpiryDateValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateValid = AutoDisposeNotifier<bool>;
String _$closeoutProductExpiryDateTextHash() => r'7ffc169033a63889c5b1ceeaed0763fd89af7450';

/// 見切り商品の賞味期限の文字
///
/// Copied from [CloseoutProductExpiryDateText].
@ProviderFor(CloseoutProductExpiryDateText)
final closeoutProductExpiryDateTextProvider =
    AutoDisposeNotifierProvider<CloseoutProductExpiryDateText, String>.internal(
  CloseoutProductExpiryDateText.new,
  name: r'closeoutProductExpiryDateTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$closeoutProductExpiryDateTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CloseoutProductExpiryDateText = AutoDisposeNotifier<String>;
String _$closeoutProductExpiryDateValidHash() => r'81332e8f513f3986806d319345bbe08b5110a3eb';

/// 見切り商品の賞味期限が有効であるかのフラグ
///
/// Copied from [CloseoutProductExpiryDateValid].
@ProviderFor(CloseoutProductExpiryDateValid)
final closeoutProductExpiryDateValidProvider =
    AutoDisposeNotifierProvider<CloseoutProductExpiryDateValid, bool>.internal(
  CloseoutProductExpiryDateValid.new,
  name: r'closeoutProductExpiryDateValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$closeoutProductExpiryDateValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CloseoutProductExpiryDateValid = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
