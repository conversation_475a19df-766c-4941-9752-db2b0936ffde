// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'maintenance_special_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchMinCheckCycleStateHash() => r'7d6c38ff013c3c45ff939b42fa4e2c4984b3c87b';

/// 特殊商品の棚のチェック頻度取得
///
/// Copied from [fetchMinCheckCycleState].
@ProviderFor(fetchMinCheckCycleState)
final fetchMinCheckCycleStateProvider = AutoDisposeFutureProvider<List<CheckMinCycle>?>.internal(
  fetchMinCheckCycleState,
  name: r'fetchMinCheckCycleStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fetchMinCheckCycleStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchMinCheckCycleStateRef = AutoDisposeFutureProviderRef<List<CheckMinCycle>?>;
String _$standardShelfListHash() => r'a819b5be17049d756a07424394b2ac7ba4e780c6';

/// 定番棚一覧情報
///
/// Copied from [StandardShelfList].
@ProviderFor(StandardShelfList)
final standardShelfListProvider = AutoDisposeAsyncNotifierProvider<StandardShelfList, ShelfListInfo>.internal(
  StandardShelfList.new,
  name: r'standardShelfListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$standardShelfListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$StandardShelfList = AutoDisposeAsyncNotifier<ShelfListInfo>;
String _$checkCycleListHash() => r'914f7d55bceeed140578b4755dd223e742097e2c';

/// チェック頻度リスト
///
/// Copied from [CheckCycleList].
@ProviderFor(CheckCycleList)
final checkCycleListProvider = AutoDisposeAsyncNotifierProvider<CheckCycleList, List<CheckCycle>>.internal(
  CheckCycleList.new,
  name: r'checkCycleListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkCycleListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckCycleList = AutoDisposeAsyncNotifier<List<CheckCycle>>;
String _$selectCheckCycleHash() => r'597da5d8ac2c80ad0d55fa6b411e67bf4ff35bd9';

/// 選択されたチェック頻度
///
/// Copied from [SelectCheckCycle].
@ProviderFor(SelectCheckCycle)
final selectCheckCycleProvider = AutoDisposeNotifierProvider<SelectCheckCycle, CheckCycle?>.internal(
  SelectCheckCycle.new,
  name: r'selectCheckCycleProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectCheckCycleHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectCheckCycle = AutoDisposeNotifier<CheckCycle?>;
String _$specialShelfInfoListHash() => r'206ca06ebde5971841c4c06e9c08bf69e2798ec1';

/// 棚情報
///
/// Copied from [SpecialShelfInfoList].
@ProviderFor(SpecialShelfInfoList)
final specialShelfInfoListProvider = AutoDisposeNotifierProvider<SpecialShelfInfoList, List<SpecialShelfInfo>>.internal(
  SpecialShelfInfoList.new,
  name: r'specialShelfInfoListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$specialShelfInfoListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SpecialShelfInfoList = AutoDisposeNotifier<List<SpecialShelfInfo>>;
String _$specialProductStateHash() => r'e19ed90c8e068d9fb11dc63353b5833510b95b5e';

/// 特殊商品情報
///
/// Copied from [SpecialProductState].
@ProviderFor(SpecialProductState)
final specialProductStateProvider = AutoDisposeNotifierProvider<SpecialProductState, SpecialProduct?>.internal(
  SpecialProductState.new,
  name: r'specialProductStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$specialProductStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SpecialProductState = AutoDisposeNotifier<SpecialProduct?>;
String _$specialProductControllerHash() => r'72d55159cdc1a0c962613b265cce1fbac73eeb77';

/// 特殊商品情報の登録・削除・取得
///
/// Copied from [SpecialProductController].
@ProviderFor(SpecialProductController)
final specialProductControllerProvider = AutoDisposeAsyncNotifierProvider<SpecialProductController, void>.internal(
  SpecialProductController.new,
  name: r'specialProductControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$specialProductControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SpecialProductController = AutoDisposeAsyncNotifier<void>;
String _$checkWeekTextHash() => r'b15d7a464729859ec51dd624ab231d634c04c416';

/// チェック週の文字
///
/// Copied from [CheckWeekText].
@ProviderFor(CheckWeekText)
final checkWeekTextProvider = AutoDisposeNotifierProvider<CheckWeekText, String>.internal(
  CheckWeekText.new,
  name: r'checkWeekTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkWeekTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckWeekText = AutoDisposeNotifier<String>;
String _$checkWeekTextValidHash() => r'5fd2b36eea8230d4cd4519fa87ec232671433ef5';

/// チェック週が有効であるかのフラグ
///
/// Copied from [CheckWeekTextValid].
@ProviderFor(CheckWeekTextValid)
final checkWeekTextValidProvider = AutoDisposeNotifierProvider<CheckWeekTextValid, bool>.internal(
  CheckWeekTextValid.new,
  name: r'checkWeekTextValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkWeekTextValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckWeekTextValid = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
