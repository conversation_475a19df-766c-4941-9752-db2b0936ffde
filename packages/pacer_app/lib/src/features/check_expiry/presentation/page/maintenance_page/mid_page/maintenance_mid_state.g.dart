// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'maintenance_mid_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$midShelfListInfoHash() => r'a5628df4c2f007fc7ba4228ec6b31173a5e6e122';

/// ミッド棚一覧情報
///
/// Copied from [MidShelfListInfo].
@ProviderFor(MidShelfListInfo)
final midShelfListInfoProvider = AutoDisposeAsyncNotifierProvider<MidShelfListInfo, ShelfListInfo>.internal(
  MidShelfListInfo.new,
  name: r'midShelfListInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$midShelfListInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MidShelfListInfo = AutoDisposeAsyncNotifier<ShelfListInfo>;
String _$selectShelfInfoHash() => r'56f6397bfa602448d13524cee4a1c27b0ee212da';

/// 選択された棚番情報
///
/// Copied from [SelectShelfInfo].
@ProviderFor(SelectShelfInfo)
final selectShelfInfoProvider = AutoDisposeNotifierProvider<SelectShelfInfo, ShelfInfo?>.internal(
  SelectShelfInfo.new,
  name: r'selectShelfInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectShelfInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectShelfInfo = AutoDisposeNotifier<ShelfInfo?>;
String _$nextExpiryDateTextHash() => r'014707841960da0115e345febff23b89354860b0';

/// 次回の賞味期限の文字
///
/// Copied from [NextExpiryDateText].
@ProviderFor(NextExpiryDateText)
final nextExpiryDateTextProvider = AutoDisposeNotifierProvider<NextExpiryDateText, String>.internal(
  NextExpiryDateText.new,
  name: r'nextExpiryDateTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateText = AutoDisposeNotifier<String>;
String _$nextExpiryDateValidHash() => r'7d10dd7d4ab3c6ba95699f6ee6e652c4cb1b7b37';

/// 次回の賞味期限が有効であるかのフラグ
///
/// Copied from [NextExpiryDateValid].
@ProviderFor(NextExpiryDateValid)
final nextExpiryDateValidProvider = AutoDisposeNotifierProvider<NextExpiryDateValid, bool>.internal(
  NextExpiryDateValid.new,
  name: r'nextExpiryDateValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateValid = AutoDisposeNotifier<bool>;
String _$midProductInfoStateHash() => r'80058a9c5a213b7e25cb0a0915295a2dad04324b';

/// メンテナンス商品情報
///
/// Copied from [MidProductInfoState].
@ProviderFor(MidProductInfoState)
final midProductInfoStateProvider = AutoDisposeNotifierProvider<MidProductInfoState, MidMaintenanceProduct?>.internal(
  MidProductInfoState.new,
  name: r'midProductInfoStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$midProductInfoStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MidProductInfoState = AutoDisposeNotifier<MidMaintenanceProduct?>;
String _$midProductInfoControllerHash() => r'90320141146e28e5725e894662e26abeea24c06d';

/// メンテナンス商品情報のコントローラー
///
/// Copied from [MidProductInfoController].
@ProviderFor(MidProductInfoController)
final midProductInfoControllerProvider = AutoDisposeAsyncNotifierProvider<MidProductInfoController, void>.internal(
  MidProductInfoController.new,
  name: r'midProductInfoControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$midProductInfoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MidProductInfoController = AutoDisposeAsyncNotifier<void>;
String _$midProductListHash() => r'2251d93bd9de5599fe543100357a37064bff72f4';

/// ミッド商品リスト（削除用）
///
/// Copied from [MidProductList].
@ProviderFor(MidProductList)
final midProductListProvider =
    AutoDisposeAsyncNotifierProvider<MidProductList, List<MidMaintenanceListProduct>>.internal(
  MidProductList.new,
  name: r'midProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$midProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MidProductList = AutoDisposeAsyncNotifier<List<MidMaintenanceListProduct>>;
String _$selectProductListHash() => r'a237016fada6df85a17d5956f923fb376c27491b';

/// 選択された商品リスト
///
/// Copied from [SelectProductList].
@ProviderFor(SelectProductList)
final selectProductListProvider =
    AutoDisposeNotifierProvider<SelectProductList, List<MidMaintenanceListProduct>>.internal(
  SelectProductList.new,
  name: r'selectProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectProductList = AutoDisposeNotifier<List<MidMaintenanceListProduct>>;
String _$deleteMidProductListControllerHash() => r'f9e1102d88710842c96ba4688c2c71d2af7b0365';

/// ミッド商品削除
///
/// Copied from [DeleteMidProductListController].
@ProviderFor(DeleteMidProductListController)
final deleteMidProductListControllerProvider =
    AutoDisposeAsyncNotifierProvider<DeleteMidProductListController, void>.internal(
  DeleteMidProductListController.new,
  name: r'deleteMidProductListControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deleteMidProductListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteMidProductListController = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
