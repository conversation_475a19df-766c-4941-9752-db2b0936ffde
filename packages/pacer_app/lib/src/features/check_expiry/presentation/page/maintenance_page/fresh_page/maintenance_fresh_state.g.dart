// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'maintenance_fresh_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freshShelfListInfoHash() => r'66bcb0b7df31f6da7930f4e674ccc586a401d1ae';

/// 生鮮棚一覧情報
///
/// Copied from [FreshShelfListInfo].
@ProviderFor(FreshShelfListInfo)
final freshShelfListInfoProvider = AutoDisposeAsyncNotifierProvider<FreshShelfListInfo, ShelfListInfo>.internal(
  FreshShelfListInfo.new,
  name: r'freshShelfListInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshShelfListInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshShelfListInfo = AutoDisposeAsyncNotifier<ShelfListInfo>;
String _$selectShelfInfoHash() => r'56f6397bfa602448d13524cee4a1c27b0ee212da';

/// 選択された棚番情報
///
/// Copied from [SelectShelfInfo].
@ProviderFor(SelectShelfInfo)
final selectShelfInfoProvider = AutoDisposeNotifierProvider<SelectShelfInfo, ShelfInfo?>.internal(
  SelectShelfInfo.new,
  name: r'selectShelfInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectShelfInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectShelfInfo = AutoDisposeNotifier<ShelfInfo?>;
String _$nextExpiryDateTextHash() => r'014707841960da0115e345febff23b89354860b0';

/// 次回の賞味期限の文字
///
/// Copied from [NextExpiryDateText].
@ProviderFor(NextExpiryDateText)
final nextExpiryDateTextProvider = AutoDisposeNotifierProvider<NextExpiryDateText, String>.internal(
  NextExpiryDateText.new,
  name: r'nextExpiryDateTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateText = AutoDisposeNotifier<String>;
String _$nextExpiryDateValidHash() => r'7d10dd7d4ab3c6ba95699f6ee6e652c4cb1b7b37';

/// 次回の賞味期限が有効であるかのフラグ
///
/// Copied from [NextExpiryDateValid].
@ProviderFor(NextExpiryDateValid)
final nextExpiryDateValidProvider = AutoDisposeNotifierProvider<NextExpiryDateValid, bool>.internal(
  NextExpiryDateValid.new,
  name: r'nextExpiryDateValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateValid = AutoDisposeNotifier<bool>;
String _$freshProductInfoStateHash() => r'a3df5c502acf095c2db94e4a5ca09bc46d8c8c6d';

/// メンテナンス商品情報
///
/// Copied from [FreshProductInfoState].
@ProviderFor(FreshProductInfoState)
final freshProductInfoStateProvider =
    AutoDisposeNotifierProvider<FreshProductInfoState, FreshMaintenanceProduct?>.internal(
  FreshProductInfoState.new,
  name: r'freshProductInfoStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshProductInfoStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshProductInfoState = AutoDisposeNotifier<FreshMaintenanceProduct?>;
String _$freshProductInfoControllerHash() => r'54b2d3e211b725270b559c537ec16b15426dfcda';

/// メンテナンス商品情報の取得処理の状態
/// 登録処理の状態
///
/// Copied from [FreshProductInfoController].
@ProviderFor(FreshProductInfoController)
final freshProductInfoControllerProvider =
    AutoDisposeAsyncNotifierProvider<FreshProductInfoController, FreshMaintenanceProduct?>.internal(
  FreshProductInfoController.new,
  name: r'freshProductInfoControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshProductInfoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshProductInfoController = AutoDisposeAsyncNotifier<FreshMaintenanceProduct?>;
String _$freshProductListHash() => r'dee752f51b19c0ee0bf52f9a6df4c2fbcb877fef';

/// 生鮮商品リスト（削除用）
///
/// Copied from [FreshProductList].
@ProviderFor(FreshProductList)
final freshProductListProvider =
    AutoDisposeAsyncNotifierProvider<FreshProductList, List<FreshMaintenanceListProduct>>.internal(
  FreshProductList.new,
  name: r'freshProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshProductList = AutoDisposeAsyncNotifier<List<FreshMaintenanceListProduct>>;
String _$selectProductListHash() => r'aec53159cd918674094af7b314538ed189bf0df8';

/// 選択された商品リスト
///
/// Copied from [SelectProductList].
@ProviderFor(SelectProductList)
final selectProductListProvider =
    AutoDisposeNotifierProvider<SelectProductList, List<FreshMaintenanceListProduct>>.internal(
  SelectProductList.new,
  name: r'selectProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectProductList = AutoDisposeNotifier<List<FreshMaintenanceListProduct>>;
String _$deleteFreshProductListControllerHash() => r'c6102102434da4cf62522c906233781f5ae3354a';

/// 生鮮新規削除
///
/// Copied from [DeleteFreshProductListController].
@ProviderFor(DeleteFreshProductListController)
final deleteFreshProductListControllerProvider =
    AutoDisposeAsyncNotifierProvider<DeleteFreshProductListController, void>.internal(
  DeleteFreshProductListController.new,
  name: r'deleteFreshProductListControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$deleteFreshProductListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteFreshProductListController = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
