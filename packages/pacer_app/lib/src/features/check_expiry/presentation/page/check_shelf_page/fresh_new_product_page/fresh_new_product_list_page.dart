import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/fresh_new_product.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/empty_data_text.dart';
import '../../../common_widgets/new_product_table.dart';
import '../check_shelf_page_props_controller.dart';
import 'fresh_new_product_state.dart';
import 'widgets/fresh_new_product_list_bottom_app_bar.dart';

/// 生鮮新規商品一覧ページ
class FreshNewProductListPage extends HookConsumerWidget {
  /// constructor
  const FreshNewProductListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final newProductList = ref.watch(freshNewProductListProvider);
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final selectProductList = ref.watch(selectProductListProvider);

    return Scaffold(
      appBar: const DefaultAppBar(),
      bottomNavigationBar: FreshNewProductListBottomAppBar(selectProductList),
      body: switch (newProductList) {
        AsyncLoading() => const Center(child: CircularProgressIndicator()),
        AsyncError() => const SizedBox.shrink(),
        AsyncData(:final value) when value.isEmpty => const EmptyDataText(),
        AsyncData(:final value) => ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            itemCount: _sortProductsByShelfNumber(value).length,
            separatorBuilder: (BuildContext context, int index) {
              return const Gap(6);
            },
            itemBuilder: (BuildContext context, int index) {
              final product = _sortProductsByShelfNumber(value)[index];
              final isCheckd = selectProductList.contains(product);

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (index == 0) ...[
                    const Gap(24),
                    _Title(props?.shelfInfo.shelfName ?? ''),
                    _ProductCountText(
                      _sortProductsByShelfNumber(value).length,
                    ),
                  ],
                  NewProductTable.freshNewProduct(
                    product: product,
                    isCheckd: isCheckd,
                    onChanged: ({bool? isCheckd}) => onCheckeChanged(
                      ref: ref,
                      isCheckd: isCheckd,
                      product: product,
                    ),
                  ),
                  if (index == _sortProductsByShelfNumber(value).length - 1) ...[
                    const Gap(24),
                  ],
                ],
              );
            },
          ),
      },
    );
  }

  /// チェックボックスの変更イベント
  void onCheckeChanged({
    required WidgetRef ref,
    required bool? isCheckd,
    required FreshNewProduct product,
  }) {
    if (isCheckd == null) {
      return;
    }
    isCheckd
        ? ref.read(selectProductListProvider.notifier).add(product)
        : ref.read(selectProductListProvider.notifier).remove(product);
  }
}

/// タイトル部分（ヘッダー）
class _Title extends StatelessWidget {
  const _Title(this.shelfName);

  final String shelfName;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          shelfName,
          style: texts.titleMedium?.copyWith(color: colors.primary, fontSize: 18),
        ),
        const Gap(6),
        Text(
          'チェックをする商品を選択してください',
          style: texts.titleMedium,
        ),
      ],
    );
  }
}

/// 対象商品（0/10）の表示
class _ProductCountText extends StatelessWidget {
  const _ProductCountText(this.count);

  final int count;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Text(
        '対象商品 ($count)',
        style: texts.labelLarge?.copyWith(color: colors.subText),
      ),
    );
  }
}

List<FreshNewProduct> _sortProductsByShelfNumber(
  List<FreshNewProduct> productList,
) {
  return [
    ...productList.where((product) => product.shelfNumberText == '0'),
    ...productList.where((product) => product.shelfNumberText != '0'),
  ];
}
