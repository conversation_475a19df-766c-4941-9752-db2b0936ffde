// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../../utils/async_value_ui.dart';
import '../../../../common_widgets/default_bottom_app_bar.dart';
import '../../../../common_widgets/outline_round_text_button.dart';
import '../../../../common_widgets/round_text_button.dart';
import '../fresh_new_product_state.dart';

/// フッターのボトムバー
class FreshNewProductRegistBottomAppBar extends ConsumerWidget {
  /// constructor
  const FreshNewProductRegistBottomAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentRegistProduct = ref.watch(currentRegistProductProvider);
    final isLastProduct = ref.watch(selectProductListProvider).last == currentRegistProduct;
    ref.listen(
      currentRegistProductControllerProvider,
      (_, state) => state.showAlertDialogOnError(context),
    );

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        await _onBackPressed(context: context, ref: ref);
      },
      child: DefaultBottomAppBar(
        leading: BackButton(
          onPressed: () => _onBackPressed(
            context: context,
            ref: ref,
          ),
        ),
        actions: [
          OutlineRoundTextButton(
            '欠品',
            onPressed: () => _onDeletePressed(
              ref: ref,
              context: context,
              isLastProduct: isLastProduct,
            ),
          ),
          const SizedBox(width: 8),
          OutlineRoundTextButton(
            '終売',
            onPressed: () => _onDeletePressed(
              ref: ref,
              context: context,
              isLastProduct: isLastProduct,
            ),
          ),
          const SizedBox(width: 8),
          RoundTextButton(
            isLastProduct ? '登録' : '次へ',
            onPressed: () => _onNextPressed(
              ref: ref,
              context: context,
              isLastProduct: isLastProduct,
            ),
          ),
        ],
      ),
    );
  }

  /// 戻るボタンの処理
  Future<bool> _onBackPressed({
    required BuildContext context,
    required WidgetRef ref,
  }) async {
    // 新規商品一覧を更新してあげる
    ref.read(freshNewProductListProvider.notifier).refresh();
    context.pop();

    return false;
  }

  /// 終売ボタンの処理
  Future<void> _onDeletePressed({
    required BuildContext context,
    required WidgetRef ref,
    required bool isLastProduct,
  }) async {
    final ok = await showAlertDialog(
      context: ref.context,
      title: 'この商品を削除してもよろしいですか？',
      cancelActionText: 'キャンセル',
      defaultActionText: '削除する',
    );
    if (ok ?? false) {
      //削除処理
      await ref.watch(currentRegistProductControllerProvider.notifier).deleteProduct(
        onSuccess: () {
          if (isLastProduct) {
            // 完了の場合は戻る
            showSnackBar(context, 'すべての商品がチェックされました');
            ref.read(freshNewProductListProvider.notifier).refresh();
            context.pop();
          } else {
            //データ次のデータへ
            showSnackBar(context, '削除しました');
            ref.read(currentRegistProductProvider.notifier).nextProduct();
            ref.read(nextExpiryDateTextProvider.notifier).clear();
          }
        },
      );
    }
  }

  /// 次へボタンの処理
  Future<void> _onNextPressed({
    required BuildContext context,
    required WidgetRef ref,
    required bool isLastProduct,
  }) async {
    //完了処理
    await ref.watch(currentRegistProductControllerProvider.notifier).registerProduct(
      onSuccess: () {
        if (isLastProduct) {
          showSnackBar(context, 'すべての商品がチェックされました。');
          ref.read(freshNewProductListProvider.notifier).refresh();
          context.pop();
        } else {
          showSnackBar(context, '登録が完了しました。');
          //次の商品に更新
          ref.read(currentRegistProductProvider.notifier).nextProduct();
          ref.read(nextExpiryDateTextProvider.notifier).clear();
        }
      },
    );
  }
}
