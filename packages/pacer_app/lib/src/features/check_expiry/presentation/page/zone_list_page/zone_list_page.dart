import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../gen/assets.gen.dart';
import '../../../../../routing/app_router.dart';
import '../../../../../themes/app_color_scheme.dart';
import '../../../domain/zone_info.dart';
import '../../common_widgets/default_app_bar.dart';
import '../../common_widgets/default_bottom_app_bar.dart';
import '../../common_widgets/empty_data_text.dart';
import '../../routing/expiry_route.dart';
import '../shelf_list_page/shelf_list_page_state.dart';

part 'list_tile.dart';

/// 賞味チェック　ゾーン選択ページ
class ZoneListPage extends ConsumerWidget {
  /// init
  const ZoneListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const title = 'ゾーン一覧 選択';

    ref.watch(selectingZoneProvider);

    return const Scaffold(
      appBar: DefaultAppBar(title: title),
      bottomNavigationBar: DefaultBottomAppBar(),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(child: _Title()),
              Flexible(child: _ListItemHeader()),
              Expanded(flex: 10, child: _ZoneListView()),
            ],
          ),
        ),
      ),
    );
  }
}

class _Title extends StatelessWidget {
  const _Title();

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('チェックするゾーンを選択してください', style: texts.titleMedium),
      ],
    );
  }
}

class _ListItemHeader extends StatelessWidget {
  const _ListItemHeader();

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final textStyle = Theme.of(context).textTheme.titleSmall?.copyWith(color: colors.secondary);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('ゾーン名称', style: textStyle),
          Text('作業進捗', style: textStyle),
        ],
      ),
    );
  }
}

/// ゾーン一覧
class _ZoneListView extends ConsumerWidget {
  const _ZoneListView();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final zoneList = ref.watch(zoneListProvider);

    return switch (zoneList) {
      AsyncLoading() => const Center(child: CircularProgressIndicator()),
      AsyncError() => const SizedBox.shrink(),
      AsyncData(:final value) when value.isEmpty => const EmptyDataText(),
      AsyncData(:final value) => ListView.separated(
          itemCount: value.length,
          separatorBuilder: (BuildContext context, int index) => Divider(height: 1, color: colors.sub),
          itemBuilder: (BuildContext context, int index) {
            final zoneInfo = value[index];

            return Column(
              children: [
                _ZoneListTile(
                  zone: zoneInfo,
                  onPressed: () => _selectZoneAndGoShelfListPage(ref, zoneInfo),
                ),
                if (index == value.length - 1) Divider(height: 1, color: colors.sub),
              ],
            );
          },
        ),
    };
  }

  void _selectZoneAndGoShelfListPage(WidgetRef ref, Zone zoneInfo) {
    log('_onZoneListTilePressed zoneInfo: $zoneInfo');

    ref.read(selectingZoneProvider.notifier).update(zoneInfo);
    const ExpiryShelfListRoute().go(ref.context);
  }
}
