import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../themes/app_color_scheme.dart';
import '../../../../../utils/async_value_ui.dart';
import '../../../../device/application/pacer_service.dart';
import '../../../../device/presentation/scan_window.dart';
import '../../common_widgets/default_app_bar.dart';
import '../../common_widgets/default_bottom_app_bar.dart';
import '../../common_widgets/product_info_table.dart';
import 'end_return_state.dart';

/// エンド戻しページ
class EndReturnPage extends ConsumerWidget {
  /// init
  const EndReturnPage({super.key});

  Future<void> _onScan(WidgetRef ref, String code) => ref.read(endReturnProductControllerProvider.notifier).scan(code);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final endReturnProduct = ref.watch(endReturnProductControllerProvider).valueOrNull;

    ref
      ..listen(
        endReturnProductControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(scanCodeProvider, (_, code) {
        if (code case AsyncData(:final value)) {
          _onScan(ref, value);
        }
      });

    return Scaffold(
      appBar: const DefaultAppBar(),
      bottomNavigationBar: const DefaultBottomAppBar(isNotched: true),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: ScanFloatingIconButton(onScan: (code) => _onScan(ref, code)),
      resizeToAvoidBottomInset: false,
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(24),
            const _Title(),
            const Gap(8),
            _ReturnProductDate(endReturnProduct?.midCheckDate),
            const Gap(8),
            Text(
              '対象商品',
              style: texts.labelLarge?.copyWith(color: colors.subText),
            ),
            const Gap(4),
            ProductInfoTable.fromEndReturnProduct(
              endReturnProduct: endReturnProduct,
              onProductCodeChanged: (code) => _onScan(ref, code),
            ),
          ],
        ),
      ),
    );
  }
}

/// タイトル部分（ヘッダー）
class _Title extends StatelessWidget {
  const _Title();

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Text(
      'エンドから定番へ商品を戻す',
      style: texts.titleMedium?.copyWith(color: colors.primary, fontSize: 18),
    );
  }
}

/// 戻す商品の日時表示
class _ReturnProductDate extends StatelessWidget {
  const _ReturnProductDate(this.limitDate);

  final DateTime? limitDate;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final limitDate = this.limitDate;

    if (limitDate == null) {
      return Text(
        'エンドから定番へ戻す商品を入力してください',
        style: texts.titleLarge?.copyWith(fontSize: 18),
      );
    }

    return RichText(
      text: TextSpan(
        style: Theme.of(context).textTheme.bodyMedium,
        children: [
          TextSpan(
            text: DateFormat('yyyy年').format(limitDate),
            style: texts.titleSmall,
          ),
          WidgetSpan(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: colors.accent,
                borderRadius: const BorderRadius.all(Radius.circular(4)),
              ),
              child: Text(
                DateFormat('MM月dd日').format(limitDate),
                style: texts.titleLarge?.copyWith(fontSize: 20),
              ),
            ),
          ),
          TextSpan(text: '以降の商品のみ戻してください', style: texts.titleSmall),
        ],
      ),
    );
  }
}
