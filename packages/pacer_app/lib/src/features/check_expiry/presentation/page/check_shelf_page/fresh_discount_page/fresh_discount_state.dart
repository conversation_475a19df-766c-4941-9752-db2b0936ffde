import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/check_expiry_fresh_service.dart';
import '../../../../application/check_expiry_service.dart';
import '../../../../domain/fresh_discount_product.dart';
import '../../../common_widgets/work_stop_alert_dialog.dart';
import '../check_shelf_page_props_controller.dart';

part 'fresh_discount_state.g.dart';

/// 取得した商品リスト
@riverpod
class FreshDiscountProductList extends _$FreshDiscountProductList {
  @override
  FutureOr<List<FreshDiscountProduct>> build() async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);

    return ref.read(checkExpiryFreshServiceProvider).fetchFreshDiscountList(
          props?.shelfInfo.shelfNumberText,
          props?.zoneCode,
        );
  }

  ///再取得
  void refresh() {
    //選択したものをクリア
    ref.read(selectProductListProvider).clear();
    //更新
    ref.invalidateSelf();
  }
}

/// 選択された商品リスト
@riverpod
class SelectProductList extends _$SelectProductList {
  @override
  List<FreshDiscountProduct> build() => [];

  /// 追加
  void add(FreshDiscountProduct product) {
    state = [...state, product];
  }

  /// 削除
  void remove(FreshDiscountProduct product) {
    state = [...state]..remove(product);
  }

  /// クリア
  void clear() {
    state = [];
  }
}

///作業中断、一時停止の場合の処理
@riverpod
class WorkStopShelfCheckController extends _$WorkStopShelfCheckController {
  @override
  FutureOr<void> build() {}

  ///作業中断、一時停止の更新
  Future<void> workStop({
    required WorkStopType workStopType,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryServiceProvider).workStopPriceReductionShelfCheck(
            zoneCode: props?.zoneCode,
            shelfNumberText: props?.shelfInfo.shelfNumberText,
            checkPlanWeek: props?.shelfInfo.checkPlanWeek,
            workStopType: workStopType,
          ),
    );
  }
}

/// 値下げ完了処理
@riverpod
class CompleteDiscountController extends _$CompleteDiscountController {
  @override
  FutureOr<void> build() {}

  ///作業中断、一時停止の更新
  Future<void> complete(VoidCallback? onSuccess) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final selectProductList = ref.watch(selectProductListProvider);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryFreshServiceProvider).completeDiscount(
            zoneCode: props?.zoneCode,
            shelfNo: props?.shelfInfo.shelfNumberText,
            productCodeList: selectProductList.map((product) => product.productCode.value).toList(),
          ),
    );
    if (!state.hasError) {
      onSuccess?.call();
    }
  }
}
