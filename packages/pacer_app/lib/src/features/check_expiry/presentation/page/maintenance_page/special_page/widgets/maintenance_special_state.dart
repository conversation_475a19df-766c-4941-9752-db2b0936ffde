import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../../exceptions/app_exception.dart';
import '../../../../../../app/application/global_loading_service.dart';
import '../../../../../application/check_expiry_service.dart';
import '../../../../../application/check_expiry_special_service.dart';
import '../../../../../domain/check_cycle.dart';
import '../../../../../domain/check_min_cycle.dart';
import '../../../../../domain/check_week.dart';
import '../../../../../domain/product_code.dart';
import '../../../../../domain/request_special_product_info.dart';
import '../../../../../domain/shelf_list_info.dart';
import '../../../../../domain/special_product.dart';

part 'maintenance_special_state.g.dart';

/// 定番棚一覧情報
@riverpod
class StandardShelfList extends _$StandardShelfList {
  @override
  FutureOr<ShelfListInfo> build() async {
    return ref.read(checkExpiryServiceProvider).fetchStandardShelfList();
  }

  ///　存在チェック
  bool isExist(String shelfNumberText) {
    final shelfInfo = state.valueOrNull?.shelfList.where((e) => e.shelfNumberText == shelfNumberText).firstOrNull;

    return shelfInfo != null;
  }

  ///　存在チェック
  ShelfInfo? getShelfInfo(String shelfNumberText) {
    return state.valueOrNull?.shelfList.where((e) => e.shelfNumberText == shelfNumberText).firstOrNull;
  }
}

/// チェック頻度リスト
@riverpod
class CheckCycleList extends _$CheckCycleList {
  @override
  FutureOr<List<CheckCycle>> build() async {
    final result = await ref.read(checkExpirySpecialServiceProvider).fetchCheckCycle();
    final currentWeek = result.firstOrNull?.currentWeek;
    if (currentWeek != null) {
      ref.read(checkWeekTextProvider.notifier).update(formatWeekText(currentWeek.toString()));
    }

    return result;
  }
}

/// 選択されたチェック頻度
@riverpod
class SelectCheckCycle extends _$SelectCheckCycle {
  @override
  CheckCycle? build() => null;

  /// 更新
  void update(CheckCycle? selectCheckCycle) {
    debugPrint('update selectCheckCycle');
    state = selectCheckCycle;
  }

  /// コード番号で更新
  void updateCheckCycleCode(int code) {
    final checkCycleList = ref.watch(checkCycleListProvider).valueOrNull;
    if (checkCycleList != null) {
      final checkCycle = checkCycleList.where((e) => e.checkCycleCode == code).firstOrNull;
      update(checkCycle);
    }
  }

  /// クリア
  void clear() {
    state = null;
  }
}

/// 棚情報
@riverpod
class SpecialShelfInfoList extends _$SpecialShelfInfoList {
  @override
  List<SpecialShelfInfo> build() => List.generate(
        maxSpecialShelfInfoCount,
        (index) => SpecialShelfInfo.empty(),
      );

  /// データ更新
  void update(List<SpecialShelfInfo> specialShelfInfoList) {
    debugPrint('update specialShelfInfoList');
    final list = [...specialShelfInfoList];

    // リストが3つ未満の場合、空の SpecialShelfInfo オブジェクトで補充する
    while (list.length < maxSpecialShelfInfoCount) {
      list.add(SpecialShelfInfo.empty());
    }

    state = list;
  }

  /// 棚番の更新
  void updateShelfNumberText({
    required int index,
    required String shelfNumberText,
  }) {
    if (index >= maxSpecialShelfInfoCount) return;

    final list = [...state];
    if (shelfNumberText.isEmpty) {
      list[index] = SpecialShelfInfo.empty();
      state = list;

      return;
    }
    final shelfInfo = ref.read(standardShelfListProvider.notifier).getShelfInfo(shelfNumberText);

    if (shelfInfo == null) return;

    list[index] = SpecialShelfInfo(
      shelfNumberText: shelfInfo.shelfNumberText,
      shelfName: shelfInfo.shelfName,
      zoneCode: shelfInfo.zoneCode,
      zoneName: shelfInfo.zoneName,
    );

    state = list;
  }

  /// クリア
  void clear() {
    state = List.generate(
      maxSpecialShelfInfoCount,
      (index) => SpecialShelfInfo.empty(),
    );
  }
}

/// 特殊商品情報
@riverpod
class SpecialProductState extends _$SpecialProductState {
  @override
  SpecialProduct? build() => null;

  /// 更新
  void update(SpecialProduct? productInfo) {
    debugPrint('update productInfo');
    state = productInfo;

    if (productInfo == null) return;

    //各項目の初期化
    final checkPlanWeek = productInfo.checkPlanWeek;
    if (checkPlanWeek != 0) {
      ref.read(checkWeekTextProvider.notifier).update(formatWeekText(checkPlanWeek.toString()));
    } else {
      ref.read(checkWeekTextProvider.notifier).clear();
    }
    if (productInfo.checkCycle != 0) {
      ref.read(selectCheckCycleProvider.notifier).updateCheckCycleCode(productInfo.checkCycle);
    }
    ref.read(specialShelfInfoListProvider.notifier).update(productInfo.shelfInfoList);
  }

  /// クリア
  void clear() {
    state = null;
    ref.read(checkWeekTextProvider.notifier).clear();
    ref.read(selectCheckCycleProvider.notifier).clear();
    ref.read(specialShelfInfoListProvider.notifier).clear();
  }
}

/// 特殊商品の棚のチェック頻度取得
@riverpod
Future<List<CheckMinCycle>?> fetchMinCheckCycleState(
  FetchMinCheckCycleStateRef ref,
) async {
  final specialProduct = ref.watch(specialProductStateProvider);
  final specialShelfList = ref.watch(specialShelfInfoListProvider);

  return ref.read(checkExpirySpecialServiceProvider).fetchMinCheckCycle(
        productCode: specialProduct?.productCode.value,
        shelfNo1: specialShelfList.elementAtOrNull(0)?.shelfNumberText,
        shelfNo2: specialShelfList.elementAtOrNull(1)?.shelfNumberText,
        shelfNo3: specialShelfList.elementAtOrNull(2)?.shelfNumberText,
      );
}

/// 特殊商品情報の登録・削除・取得
@riverpod
class SpecialProductController extends _$SpecialProductController {
  @override
  FutureOr<void> build() async {
    ref.showGlobalLoading();
  }

  /// スキャン取得、登録
  Future<void> scan(
    String janCode, {
    VoidCallback? onRegisterSuccess,
  }) async {
    final productCode = ProductCode.parseProductCode(janCode);
    final specialProduct = ref.watch(specialProductStateProvider);

    if (specialProduct == null) {
      await _fetch(productCode.value);
    } else {
      await _registerAndFetchNewData(productCode.value, onRegisterSuccess);
    }
  }

  /// 商品情報を取得
  Future<void> _fetch(String janCode) async {
    if (janCode.isEmpty) return;

    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final item = await ref.read(checkExpirySpecialServiceProvider).fetchSpecialProduct(productCode: janCode);

      return ref.read(specialProductStateProvider.notifier).update(item);
    });
  }

  /// 商品情報を登録して別の商品データ取得
  Future<void> _registerAndFetchNewData(
    String janCode,
    VoidCallback? onSuccess,
  ) async {
    if (janCode.isEmpty) return;

    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final specialProduct = ref.read(specialProductStateProvider);
      if (specialProduct == null) {
        throw UnknownException('商品をスキャンしてください。');
      }
      final checkCycleList = ref.read(checkCycleListProvider).valueOrNull;
      if (checkCycleList == null) {
        throw UnknownException('チェック頻度が取得できません。');
      }

      final oldCheckCycleCode =
          checkCycleList.where((e) => e.checkCycleName == specialProduct.checkCycleName).firstOrNull?.checkCycleCode;

      final selectCheckCycle = ref.read(selectCheckCycleProvider);

      if (selectCheckCycle == null) throw UnknownException('チェック頻度が選択されていません。');

      _checkCheckWeekText();
      final checkWeekText = ref.read(checkWeekTextProvider);
      final checkWeekNumberText = checkWeekText.replaceAll(RegExp(r'\D'), '');
      final specialShelfList = ref.read(specialShelfInfoListProvider);

      final isShelfEmpty = specialShelfList.where((e) => e.zoneCode != null).toList().isEmpty;
      if (isShelfEmpty) throw UnknownException('棚番を入力してください。');

      final newSpecialProductInfo = await ref.read(checkExpirySpecialServiceProvider).scanRegisterSpecialProduct(
            oldProductInfo: RequestSpecialProductInfo.fromSpecialProduct(
              specialProduct,
              oldCheckCycleCode ?? 0,
            ),
            newProductInfo: RequestSpecialProductInfo.fromShelfInfoList(
              productCode: janCode,
              shelfInfoList: specialShelfList,
              checkPlanWeek: int.tryParse(checkWeekNumberText) ?? 0,
              checkCycleCode: selectCheckCycle.checkCycleCode,
            ),
          );
      return ref.read(specialProductStateProvider.notifier).update(newSpecialProductInfo);
    });

    if (!state.hasError) onSuccess?.call();
  }

  /// 特殊商品情報を登録
  Future<void> register(VoidCallback? onSuccess) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final specialProduct = ref.watch(specialProductStateProvider);
      if (specialProduct == null) throw UnknownException('商品をスキャンしてください。');
      final checkCycleList = ref.watch(checkCycleListProvider).valueOrNull;
      if (checkCycleList == null) throw UnknownException('チェック頻度が取得できません。');

      final oldCheckCycleCode =
          checkCycleList.where((e) => e.checkCycleName == specialProduct.checkCycleName).firstOrNull?.checkCycleCode;

      final selectCheckCycle = ref.watch(selectCheckCycleProvider);

      if (selectCheckCycle == null) throw UnknownException('チェック頻度が選択されていません。');

      _checkCheckWeekText();

      final checkWeekText = ref.watch(checkWeekTextProvider);
      final checkWeekNumberText = checkWeekText.replaceAll(RegExp(r'\D'), '');
      final specialShelfList = ref.watch(specialShelfInfoListProvider);
      final isShelfEmpty = specialShelfList.where((e) => e.zoneCode != null).toList().isEmpty;
      if (isShelfEmpty) throw UnknownException('棚番を入力してください。');

      if (!isChangeData()) throw UnknownException('変更がありません。');

      return ref.read(checkExpirySpecialServiceProvider).registerSpecialProduct(
            oldProductInfo: RequestSpecialProductInfo.fromSpecialProduct(
              specialProduct,
              oldCheckCycleCode ?? 0,
            ),
            newProductInfo: RequestSpecialProductInfo.fromShelfInfoList(
              productCode: specialProduct.productCode.value,
              shelfInfoList: specialShelfList,
              checkPlanWeek: int.tryParse(checkWeekNumberText) ?? 0,
              checkCycleCode: selectCheckCycle.checkCycleCode,
            ),
          );
    });

    if (!state.hasError) onSuccess?.call();
  }

  /// 特殊商品情報の削除
  Future<void> delete(VoidCallback? onSuccess) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final item = ref.watch(specialProductStateProvider);
      if (item == null) throw UnknownException('商品をスキャンしてください。');

      final checkCycleList = ref.watch(checkCycleListProvider).valueOrNull;
      if (checkCycleList == null) throw UnknownException('チェック頻度が取得できません。');

      final preCheckCycle =
          checkCycleList.where((e) => e.checkCycleName == item.checkCycleName).firstOrNull?.checkCycleCode;

      if (preCheckCycle == null) return;

      return ref.read(checkExpirySpecialServiceProvider).deleteSpecialProduct(
            RequestSpecialProductInfo.fromSpecialProduct(item, preCheckCycle),
          );
    });

    if (!state.hasError) onSuccess?.call();
  }

  void _checkCheckWeekText() {
    final checkWeekText = ref.watch(checkWeekTextProvider);
    final isValid = ref.watch(checkWeekTextValidProvider);
    if (!isValid) throw UnknownException('初回チェック週が不正です。6桁の数字を入力してください。');

    final checkWeekNumberText = checkWeekText.replaceAll(RegExp(r'\D'), '');
    final checkWeek = int.parse(checkWeekNumberText);
    final checkCycleList = ref.watch(checkCycleListProvider).valueOrNull;

    final checkCycle = checkCycleList?.firstOrNull;
    if (checkCycle case CheckCycle(currentWeek: final week) when week > checkWeek) {
      throw UnknownException(' 直近チェックの入力内容が正しくありません。');
    }
  }

  /// 入力に変更があったかどうか
  bool isChangeData() {
    final specialProduct = ref.read(specialProductStateProvider);
    final checkWeekText = ref.read(checkWeekTextProvider);
    final checkWeekNumberText = checkWeekText.replaceAll(RegExp(r'\D'), '');
    final checkWeek = int.parse(checkWeekNumberText);
    final selectCheckCycle = ref.read(selectCheckCycleProvider);
    final currentShelfNumberList = specialProduct?.shelfInfoList.map((e) => e.shelfNumberText).toList() ?? [];
    final enterShelfNumberList =
        ref.read(specialShelfInfoListProvider).map((e) => e.shelfNumberText?.toString()).nonNulls.toList();

    return !(specialProduct?.checkPlanWeek == checkWeek &&
        specialProduct?.checkCycle == selectCheckCycle?.checkCycleCode &&
        listEquals(currentShelfNumberList, enterShelfNumberList));
  }
}

/// チェック週の文字
@riverpod
class CheckWeekText extends _$CheckWeekText {
  @override
  String build() => '';

  /// 更新
  void update(String text) {
    debugPrint('update checkWeekText');
    state = text;
  }

  ///  クリア
  void clear() {
    final checkCycleList = ref.watch(checkCycleListProvider).valueOrNull;

    if (checkCycleList != null) {
      final text = checkCycleList.firstOrNull?.currentWeek.toString() ?? '';
      update(formatWeekText(text));
    }
  }
}

/// チェック週が有効であるかのフラグ
@riverpod
class CheckWeekTextValid extends _$CheckWeekTextValid {
  @override
  bool build() {
    final dateText = ref.watch(checkWeekTextProvider);

    return isValidCheckWeekText(dateText);
  }
}
