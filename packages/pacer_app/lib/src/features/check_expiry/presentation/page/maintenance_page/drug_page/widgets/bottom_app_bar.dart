// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/snack_bars.dart';
import '../../../../common_widgets/default_bottom_app_bar.dart';
import '../../../../common_widgets/outline_round_text_button.dart';
import '../../../../common_widgets/round_text_button.dart';
import '../maintenance_drug_state.dart';

/// フッターのボトムバー
class MaintenanceDrugBottomAppBar extends ConsumerWidget {
  const MaintenanceDrugBottomAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDataExsits = ref.watch(drugProductInfoStateProvider) != null;

    return DefaultBottomAppBar(
      isNotched: true,
      height: 100,
      actions: [
        OutlineRoundTextButton(
          '取消',
          onPressed: () => _onResetPressed(context, ref),
        ),
        const SizedBox(width: 8),
        OutlineRoundTextButton(
          '削除',
          onPressed: isDataExsits ? () => _onDeletePressed(context, ref) : null,
        ),
        const SizedBox(width: 8),
        RoundTextButton(
          '登録',
          onPressed: isDataExsits ? () => _onRegistPressed(context, ref) : null,
        ),
      ],
    );
  }

  void _reset(BuildContext context, WidgetRef ref) {
    //画面の初期化
    ref.read(nextExpiryDateTextProvider.notifier).clear();
    ref.read(drugProductInfoStateProvider.notifier).clear();
    ref.read(endFlgProvider.notifier).update(isEnd: false);
    ref.read(midFlgProvider.notifier).update(isMid: false);
  }

  Future<void> _onResetPressed(BuildContext context, WidgetRef ref) async {
    _reset(context, ref);
    showSnackBar(context, '入力内容を取り消しました');
  }

  Future<void> _onDeletePressed(BuildContext context, WidgetRef ref) async {
    final ok = await showAlertDialog(
      context: ref.context,
      title: '削除します。よろしいですか？',
      cancelActionText: 'キャンセル',
      defaultActionText: '削除する',
    );
    if (ok ?? false) {
      await ref.watch(drugProductInfoControllerProvider.notifier).delete(
        () {
          _reset(context, ref);
          showSnackBar(context, '削除しました。');
        },
      );
    }
  }

  Future<void> _onRegistPressed(BuildContext context, WidgetRef ref) async {
    final isChangeData = ref.read(drugProductInfoControllerProvider.notifier).isChangeData();
    if (!isChangeData) {
      await showAlertDialog(context: context, title: '変更内容がありません。');
      return;
    }
    final ok = await showAlertDialog(
      context: ref.context,
      title: 'データを登録します。よろしいですか？',
      cancelActionText: 'キャンセル',
      defaultActionText: '登録する',
    );
    if (ok ?? false) {
      await ref.watch(drugProductInfoControllerProvider.notifier).register(
        () {
          _reset(context, ref);
          showSnackBar(context, '登録しました。');
        },
      );
    }
  }
}
