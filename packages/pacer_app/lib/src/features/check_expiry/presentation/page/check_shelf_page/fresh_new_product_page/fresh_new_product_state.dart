import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../application/check_expiry_fresh_service.dart';
import '../../../../domain/fresh_new_product.dart';
import '../../../../domain/fresh_new_product_regist_info.dart';
import '../../../../domain/next_expiry_date.dart';
import '../../../../domain/zoon_shelf_info.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../check_shelf_page_props_controller.dart';

part 'fresh_new_product_state.g.dart';

/// 取得した商品リスト
@riverpod
class FreshNewProductList extends _$FreshNewProductList {
  @override
  FutureOr<List<FreshNewProduct>> build() async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);

    return ref.read(checkExpiryFreshServiceProvider).fetchFreshNewProductList(props?.zoneCode);
  }

  ///再取得
  void refresh() {
    //選択したものをクリア
    ref.read(selectProductListProvider).clear();
    //更新
    ref.invalidateSelf();
  }
}

/// 生鮮新規削除
@riverpod
class DeleteFreshNewProductListController extends _$DeleteFreshNewProductListController {
  @override
  FutureOr<void> build() {}

  /// 生鮮新規商品を削除
  @riverpod
  Future<void> deleteList() async {
    final deleteProductList = ref.watch(selectProductListProvider);
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final futureList = deleteProductList
        .map(
          (product) => ref.read(checkExpiryFreshServiceProvider).deleteFreshNewProductList(props?.zoneCode, product),
        )
        .toList();

    state = const AsyncLoading();

    state = await AsyncValue.guard(
      () => Future.wait(futureList),
    );
    //選択したリストをクリアする
    ref.read(selectProductListProvider.notifier).clear();
    ref.read(freshNewProductListProvider.notifier).refresh();
  }
}

/// 生鮮棚一覧
@riverpod
class FreshShelfList extends _$FreshShelfList {
  @override
  FutureOr<List<ZoonShelfInfo>> build() async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);

    return ref.read(checkExpiryFreshServiceProvider).fetchFreshShelfList(props?.zoneCode);
  }
}

/// 生鮮の登録情報
@riverpod
class FreshResistInfo extends _$FreshResistInfo {
  @override
  FutureOr<FreshNewProductRegisterInfo> build() async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final currentProduct = ref.watch(currentRegistProductProvider);

    return ref.read(checkExpiryFreshServiceProvider).fetchFreshNewProductRegisterInfo(
          props?.zoneCode,
          currentProduct?.productCode.value,
        );
  }
}

/// 選択された商品リスト
@riverpod
class SelectProductList extends _$SelectProductList {
  @override
  List<FreshNewProduct> build() => [];

  /// 追加
  void add(FreshNewProduct product) {
    state = [...state, product];
  }

  /// 削除
  void remove(FreshNewProduct product) {
    state = [...state]..remove(product);
  }

  /// クリア
  void clear() {
    state = [];
  }
}

/// 選択された棚番情報
@riverpod
class SelectZoonShelfInfo extends _$SelectZoonShelfInfo {
  @override
  ZoonShelfInfo? build() => null;

  /// 更新
  void update(ZoonShelfInfo? shelfInfo) {
    debugPrint('update currentRegistProduct');
    state = shelfInfo;
  }

  /// クリア
  void clear() {
    state = null;
  }
}

/// 現在登録中の商品
@riverpod
class CurrentRegistProduct extends _$CurrentRegistProduct {
  @override
  FreshNewProduct? build() {
    final selectProductList = ref.watch(selectProductListProvider);

    return selectProductList.isNotEmpty ? selectProductList.first : null;
  }

  /// 現在の登録中の商品は選択した商品リストの何番目か
  int getIndex() {
    final currentProduct = state;
    final selectProductList = ref.watch(selectProductListProvider);
    if (currentProduct == null) {
      return 0;
    }

    return selectProductList.indexOf(currentProduct);
  }

  /// 最後の商品かどうか
  bool isLastProduct() {
    final selectProductList = ref.watch(selectProductListProvider);
    return selectProductList.last == state;
  }

  /// 更新
  void update(FreshNewProduct product) {
    debugPrint('update currentRegistProduct');
    state = product;
  }

  /// 次の商品へ
  void nextProduct() {
    final selectProductList = ref.watch(selectProductListProvider);
    //次の商品に更新
    if (!isLastProduct()) {
      update(selectProductList[getIndex() + 1]);
    }
  }
}

/// 生鮮の登録情報
@riverpod
class CurrentRegistProductController extends _$CurrentRegistProductController {
  @override
  FutureOr<void> build() async {}

  /// 現在の登録中の商品の登録処理
  Future<void> registerProduct({
    required void Function() onSuccess,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final nextExpiryDateText = ref.watch(nextExpiryDateTextProvider);
    final selectZoonShelfInfo = ref.watch(selectZoonShelfInfoProvider);
    final currentRegistProduct = ref.watch(currentRegistProductProvider);
    final registInfo = ref.watch(freshResistInfoProvider);
    try {
      if (selectZoonShelfInfo == null) {
        throw UnknownException('棚番を選択してください。');
      }
      final oldNextExpirationDate = currentRegistProduct?.nextBestBeforeDate;
      final errorMeesage = getNextExpiryErrorMessage(
        nextExpiryDateText,
        jobDate: oldNextExpirationDate,
        limitDate: registInfo.valueOrNull?.limitDate,
      );

      // エラーメッセージがある場合はエラーをスローする
      if (errorMeesage != null) {
        throw UnknownException(errorMeesage);
      }
      final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(nextExpiryDateText);

      state = const AsyncValue.loading();
      state = await AsyncValue.guard(
        () => ref.read(checkExpiryFreshServiceProvider).setFreshNewProdInfo(
              zoneCode: props?.zoneCode,
              product: currentRegistProduct,
              nextExpiryDate: nextExpiryDate,
              shelfNumberText: selectZoonShelfInfo.shelfNumberText,
            ),
      );
    } catch (err, stack) {
      state = AsyncValue.error(err, stack);
    }
    if (!state.hasError) {
      onSuccess();
    }
  }

  ///現在の登録中の商品の削除処理
  Future<void> deleteProduct({
    required void Function() onSuccess,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final currentRegistProduct = ref.watch(currentRegistProductProvider);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryFreshServiceProvider).deleteFreshNewProductInfo(
            props?.zoneCode,
            currentRegistProduct?.productCode.value,
          ),
    );
    if (!state.hasError) {
      onSuccess();
    }
  }
}

/// 次回の賞味期限の文字
@riverpod
class NextExpiryDateText extends _$NextExpiryDateText {
  @override
  String build() => '';

  /// 更新
  void update(String text) {
    debugPrint('update nextExpiryDateText');
    state = text;
  }

  /// クリア
  void clear() {
    state = '';
  }
}

/// 次回の賞味期限が有効であるかのフラグ
@riverpod
class NextExpiryDateValid extends _$NextExpiryDateValid {
  @override
  bool build() {
    final dateText = ref.watch(nextExpiryDateTextProvider);

    return isValidNextExpiryDate(dateText);
  }
}
