import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/check_expiry_fresh_service.dart';
import '../../../../application/check_expiry_service.dart';
import '../../../../domain/fresh_maintenance_list_product.dart';
import '../../../../domain/fresh_maintenance_product.dart';
import '../../../../domain/next_expiry_date.dart';
import '../../../../domain/product_code.dart';
import '../../../../domain/shelf_list_info.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';

part 'maintenance_fresh_state.g.dart';

/// 生鮮棚一覧情報
@riverpod
class FreshShelfListInfo extends _$FreshShelfListInfo {
  @override
  FutureOr<ShelfListInfo> build() async {
    return ref.read(checkExpiryServiceProvider).fetchFreshShelfList();
  }
}

/// 選択された棚番情報
@riverpod
class SelectShelfInfo extends _$SelectShelfInfo {
  @override
  ShelfInfo? build() => null;

  /// 更新
  void update(ShelfInfo? shelfInfo) {
    debugPrint('update selectShelfInfo');
    state = shelfInfo;
  }

  /// クリア
  void clear() {
    state = null;
  }
}

/// 次回の賞味期限の文字
@riverpod
class NextExpiryDateText extends _$NextExpiryDateText {
  @override
  String build() => '';

  /// 更新
  void update(String text) {
    debugPrint('update nextExpiryDateText');
    state = text;
  }

  ///クリア
  void clear() {
    state = '';
  }
}

/// 次回の賞味期限が有効であるかのフラグ
@riverpod
class NextExpiryDateValid extends _$NextExpiryDateValid {
  @override
  bool build() {
    final dateText = ref.watch(nextExpiryDateTextProvider);

    return isValidNextExpiryDate(dateText);
  }
}

/// メンテナンス商品情報
@riverpod
class FreshProductInfoState extends _$FreshProductInfoState {
  @override
  FreshMaintenanceProduct? build() => null;

  /// 更新
  void update(FreshMaintenanceProduct? productInfo) {
    debugPrint('update productInfo');
    final nextExpirationDate = productInfo?.nextExpirationDate;
    if (nextExpirationDate != null) {
      ref.read(nextExpiryDateTextProvider.notifier).update(
            formatNextExpiryDate(
              DateFormat('yyyyMMdd').format(nextExpirationDate),
            ),
          );
    }
    state = productInfo;
  }

  /// クリア
  void clear() {
    state = null;
  }
}

/// メンテナンス商品情報の取得処理の状態
/// 登録処理の状態
@riverpod
class FreshProductInfoController extends _$FreshProductInfoController {
  @override
  FutureOr<FreshMaintenanceProduct?> build() {
    ref.showGlobalLoading();
    return null;
  }

  /// スキャン
  /// 商品検索済みなら、今の商品を登録して、新しい商品を取得する
  /// 商品検索済みでないなら、商品を取得する
  void scan(
    String janCode, {
    void Function(FreshMaintenanceProduct)? onFetchProductSuccess,
    VoidCallback? onRegisterSuccess,
  }) {
    final productCode = ProductCode.parseProductCode(janCode);
    final item = ref.watch(freshProductInfoStateProvider);

    switch (item) {
      case null:
        _fetch(
          productCode.value,
          onFetchProductSuccess: onFetchProductSuccess,
        );
      case _:
        _registerAndFetchNewData(
          productCode.value,
          onFetchProductSuccess: onFetchProductSuccess,
          onRegisterSuccess: onRegisterSuccess,
        );
    }
  }

  Future<void> _fetch(
    String janCode, {
    void Function(FreshMaintenanceProduct)? onFetchProductSuccess,
  }) async {
    if (janCode.isEmpty) {
      return;
    }

    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final selectShelfInfo = ref.watch(selectShelfInfoProvider);
      if (selectShelfInfo == null) throw UnknownException('棚番が選択されていません。');

      //取得する
      final productInfo = await ref.read(checkExpiryFreshServiceProvider).fetchFreshMaintenanceProduct(
            janCode,
            selectShelfInfo.shelfNumberText,
            selectShelfInfo.zoneCode,
          );

      if (!state.hasError && productInfo != null) {
        onFetchProductSuccess?.call(productInfo);
      }
      return null;
    });
  }

  /// 商品情報を登録して別の商品データ取得
  Future<void> _registerAndFetchNewData(
    String janCode, {
    void Function(FreshMaintenanceProduct)? onFetchProductSuccess,
    VoidCallback? onRegisterSuccess,
  }) async {
    if (janCode.isEmpty) {
      return;
    }
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      await register(() async {
        await _fetch(
          janCode,
          onFetchProductSuccess: onFetchProductSuccess,
        );
        onRegisterSuccess?.call();
      });
      return null;
    });
  }

  /// ドラッグ商品情報を登録
  Future<void> register(VoidCallback? onSuccess) async {
    final productInfo = ref.read(freshProductInfoStateProvider);
    if (productInfo == null) return;

    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      _checknextExpiryDate();
      final nextExpiryDateText = ref.read(nextExpiryDateTextProvider);
      final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(nextExpiryDateText);

      final selectShelfInfo = ref.read(selectShelfInfoProvider);
      if (selectShelfInfo == null) throw UnknownException('棚番が選択されていません。');

      await ref.read(checkExpiryFreshServiceProvider).registerFreshProduct(
            freshProductInfo: productInfo,
            nextExpiryDate: nextExpiryDate,
            shelfNo: selectShelfInfo.shelfNumberText,
            zoneCode: selectShelfInfo.zoneCode,
            newProductCode: '',
          );
      return null;
    });

    if (!state.hasError) onSuccess?.call();
  }

  void _checknextExpiryDate() {
    final productInfo = ref.read(freshProductInfoStateProvider);
    final nextExpiryDateText = ref.read(nextExpiryDateTextProvider);
    final freshShelfListInfo = ref.read(freshShelfListInfoProvider).valueOrNull;
    if (productInfo == null) return;

    final errorMeesage = getNextExpiryErrorMessage(
      nextExpiryDateText,
      jobDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo.checkDay),
      limitDate: freshShelfListInfo?.limitDate,
    );

    // エラーメッセージがある場合はエラーをスローする
    if (errorMeesage != null) throw UnknownException(errorMeesage);
  }

  /// 商品情報をクリア
  void clear() {
    ref.read(freshProductInfoStateProvider.notifier).update(null);
  }
}

/// 生鮮商品リスト（削除用）
@riverpod
class FreshProductList extends _$FreshProductList {
  @override
  FutureOr<List<FreshMaintenanceListProduct>> build() async {
    final selectShelfInfo = ref.watch(selectShelfInfoProvider);

    return ref.read(checkExpiryFreshServiceProvider).fetchFreshMaintenanceProductList(
          selectShelfInfo?.shelfNumberText,
          selectShelfInfo?.zoneCode,
        );
  }
}

/// 選択された商品リスト
@riverpod
class SelectProductList extends _$SelectProductList {
  @override
  List<FreshMaintenanceListProduct> build() => [];

  /// 追加
  void add(FreshMaintenanceListProduct product) {
    state = [...state, product];
  }

  /// 削除
  void remove(FreshMaintenanceListProduct product) {
    state = [...state]..remove(product);
  }

  /// クリア
  void clear() {
    state = [];
  }
}

/// 生鮮新規削除
@riverpod
class DeleteFreshProductListController extends _$DeleteFreshProductListController {
  @override
  FutureOr<void> build() {}

  /// 生鮮新規商品を削除
  @riverpod
  Future<void> deleteList(VoidCallback? onSuccess) async {
    final selectShelfInfo = ref.watch(selectShelfInfoProvider);
    final deleteProductList = ref.watch(selectProductListProvider);
    try {
      final futureList = deleteProductList
          .map(
            (product) => ref.read(checkExpiryFreshServiceProvider).deleteFreshProductCheckInfo(
                  shelfNo: selectShelfInfo?.shelfNumberText,
                  zoneCode: selectShelfInfo?.zoneCode,
                  productCode: product.productCode.value,
                  expirationDate: product.nextExpirationDate,
                ),
          )
          .toList();

      state = const AsyncLoading();

      state = await AsyncValue.guard(
        () => Future.wait(futureList),
      );
      if (!state.hasError) {
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncValue.error(err, stack);
    }
    //選択したリストをクリアする
    ref.read(selectProductListProvider.notifier).clear();
    ref.invalidate(freshProductListProvider);
  }
}
