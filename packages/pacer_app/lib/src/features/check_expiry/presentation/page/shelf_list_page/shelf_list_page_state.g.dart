// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shelf_list_page_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectingZoneHash() => r'896481ba903bcd645309d8b19be932f3f9ae0edb';

/// ページ間の値受け渡し用
///
/// Copied from [SelectingZone].
@ProviderFor(SelectingZone)
final selectingZoneProvider = AutoDisposeNotifierProvider<SelectingZone, Zone?>.internal(
  SelectingZone.new,
  name: r'selectingZoneProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectingZoneHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectingZone = AutoDisposeNotifier<Zone?>;
String _$shelfWorkInfoListHash() => r'f905f2de10c759481aa2cfca0061d878dca8f3da';

///棚の作業情報
///
/// Copied from [ShelfWorkInfoList].
@ProviderFor(ShelfWorkInfoList)
final shelfWorkInfoListProvider = AutoDisposeAsyncNotifierProvider<ShelfWorkInfoList, List<ShelfWorkInfo>>.internal(
  ShelfWorkInfoList.new,
  name: r'shelfWorkInfoListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$shelfWorkInfoListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ShelfWorkInfoList = AutoDisposeAsyncNotifier<List<ShelfWorkInfo>>;
String _$zoneListHash() => r'345245f47bf82197db72c0670633bf3bafec59a2';

///ゾーンリスト
///
/// Copied from [ZoneList].
@ProviderFor(ZoneList)
final zoneListProvider = AutoDisposeAsyncNotifierProvider<ZoneList, List<Zone>>.internal(
  ZoneList.new,
  name: r'zoneListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$zoneListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZoneList = AutoDisposeAsyncNotifier<List<Zone>>;
String _$updateShelfWorkControllerHash() => r'6cd09a4be659fc4fbd8a69ae7e738af40248e2c1';

/// 棚番設定画面、値下データをクリック時、作業中設定
///
/// Copied from [UpdateShelfWorkController].
@ProviderFor(UpdateShelfWorkController)
final updateShelfWorkControllerProvider = AutoDisposeAsyncNotifierProvider<UpdateShelfWorkController, void>.internal(
  UpdateShelfWorkController.new,
  name: r'updateShelfWorkControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$updateShelfWorkControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateShelfWorkController = AutoDisposeAsyncNotifier<void>;
String _$updateFreshDCTShelfWorkControllerHash() => r'6fdd6a8d8d550e88ca304cec0779c1a3c9d2a62f';

/// 棚番設定画面、値下データをクリック時、作業中設定
///
/// Copied from [UpdateFreshDCTShelfWorkController].
@ProviderFor(UpdateFreshDCTShelfWorkController)
final updateFreshDCTShelfWorkControllerProvider =
    AutoDisposeAsyncNotifierProvider<UpdateFreshDCTShelfWorkController, void>.internal(
  UpdateFreshDCTShelfWorkController.new,
  name: r'updateFreshDCTShelfWorkControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$updateFreshDCTShelfWorkControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateFreshDCTShelfWorkController = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
