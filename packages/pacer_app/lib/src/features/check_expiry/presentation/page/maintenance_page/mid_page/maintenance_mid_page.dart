import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../device/application/pacer_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/maintenance_product_table.dart';
import '../../../common_widgets/next_expiry_date_text_field.dart';
import 'maintenance_mid_state.dart';
import 'widgets/maintenance_mid_bottom_app_bar.dart';

/// ミッドのメンテナンスページ
class MaintenanceMidPage extends HookConsumerWidget {
  /// constructor
  const MaintenanceMidPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const title = 'メンテナンス（ミッド）登録';
    final focusNode = useFocusNode();

    final midShelfListInfo = ref.watch(midShelfListInfoProvider).valueOrNull;
    final selectShelfInfo = ref.watch(selectShelfInfoProvider);
    final midProductInfo = ref.watch(midProductInfoStateProvider);

    ref
      ..listen(
        midProductInfoControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        midShelfListInfoProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(scanCodeProvider, (_, code) {
        if (code case AsyncData(:final value)) {
          _scan(ref, value);
        }
      });

    return Focus(
      focusNode: focusNode,
      child: GestureDetector(
        onTap: focusNode.requestFocus,
        child: Scaffold(
          appBar: const DefaultAppBar(
            title: title,
          ),
          bottomNavigationBar: const MaintenanceMidBottomAppBar(),
          floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
          floatingActionButton: ScanFloatingIconButton(onScan: (jan) => _scan(ref, jan)),
          resizeToAvoidBottomInset: false,
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MaintenanceProductTable.midMaintenanceProduct(
                    product: midProductInfo,
                    shelfInfoList: midShelfListInfo?.shelfList ?? [],
                    selectShelfInfo: selectShelfInfo,
                    onShelfInfoChanged: ref.read(selectShelfInfoProvider.notifier).update,
                    onProductCodeChanged: (jan) => _scan(ref, jan),
                  ),
                  const Gap(12),
                  const _NextExpiryDateTextField(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _scan(WidgetRef ref, String jan) {
    final context = ref.context;
    ref.read(midProductInfoControllerProvider.notifier).scan(
      jan,
      onFetchProductSuccess: (midMaintenanceProduct) async {
        ref.read(midProductInfoStateProvider.notifier).update(midMaintenanceProduct);
      },
      //登録完了
      onRegisterSuccess: () {
        showSnackBar(context, '登録しました。');
        ref.read(nextExpiryDateTextProvider.notifier).clear();
      },
    );
  }
}

/// 次回賞味期限
class _NextExpiryDateTextField extends HookConsumerWidget {
  const _NextExpiryDateTextField();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final text = ref.watch(nextExpiryDateTextProvider);
    final isValid = ref.watch(nextExpiryDateValidProvider);
    final productInfo = ref.watch(midProductInfoStateProvider);
    final midShelfListInfo = ref.watch(midShelfListInfoProvider).valueOrNull;

    return NextExpiryDateTextField(
      inputText: text,
      onChanged: ref.read(nextExpiryDateTextProvider.notifier).update,
      onFieldSubmitted: (text) async {
        final isShowDialog = await showNextExpiryErrorAlertDialog(
          context,
          text,
          jobDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo?.checkDay ?? ''),
          limitDate: midShelfListInfo?.limitDate,
        );
        if (isShowDialog) {
          ref.read(nextExpiryDateTextProvider.notifier).clear();
        }
      },
      isValid: isValid,
    );
  }
}
