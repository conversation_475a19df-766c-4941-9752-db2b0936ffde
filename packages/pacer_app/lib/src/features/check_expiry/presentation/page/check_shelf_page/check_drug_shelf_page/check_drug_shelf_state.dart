import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../application/check_expiry_drug_service.dart';
import '../../../../application/check_expiry_service.dart';
import '../../../../domain/enum/work_status.dart';
import '../../../../domain/next_expiry_date.dart';
import '../../../../domain/product_check_info.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/work_stop_alert_dialog.dart';
import '../check_shelf_page_props_controller.dart';

part 'check_drug_shelf_state.g.dart';

///ドラッグの棚情報
@riverpod
class CheckDrugShelfInfo extends _$CheckDrugShelfInfo {
  @override
  FutureOr<ProductCheckInfo> build() async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    log('props is $props');

    return ref.read(checkExpiryDrugServiceProvider).fetchDrugProductCheckInfo(props?.zoneCode, props?.shelfInfo);
  }

  /// 棚チェック情報を更新する
  void updateCheckInfo(ProductCheckInfo checkInfo) {
    state = AsyncData(checkInfo);
  }
}

///賞味期限チェックの完了処理
@riverpod
class UpdateCheckDrugShelfInfoController extends _$UpdateCheckDrugShelfInfoController {
  @override
  FutureOr<void> build() {}

  ///「次へ」もくは「完了」の場合
  Future<void> updateComplete(
    VoidCallback onSuccess,
  ) async {
    final isMid = ref.watch(midFlgProvider);
    final isEnd = ref.watch(endFlgProvider);
    final nextExpiryDateText = ref.watch(nextExpiryDateTextProvider);
    final drugProductCheckInfo = ref.watch(checkDrugShelfInfoProvider).value;

    if (drugProductCheckInfo == null) {
      return;
    }

    try {
      final errorMeesage = getNextExpiryErrorMessage(
        nextExpiryDateText,
        jobDate: drugProductCheckInfo.productInfo.jobDate,
        limitDate: drugProductCheckInfo.limitDate,
        isCheckDiscard: false,
      );

      // エラーメッセージがある場合はエラーをスローする
      if (errorMeesage != null) {
        throw UnknownException(errorMeesage);
      }

      final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(nextExpiryDateText);

      final props = ref.watch(checkShelfPagePropsControllerProvider);
      state = const AsyncValue.loading();
      final productCheckInfo = await AsyncValue.guard(
        () => ref.read(checkExpiryDrugServiceProvider).updateDrugProductCheckInfo(
              zoneCode: props?.zoneCode,
              shelfInfo: props?.shelfInfo,
              isMid: isMid,
              isEnd: isEnd,
              productCode: drugProductCheckInfo.productInfo.productCode.value,
              nextExpiryDate: nextExpiryDate,
              workStatus: WorkStatus.normalCompletion,
            ),
      );
      state = const AsyncValue.data(null);
      final newProductCheckInfo = productCheckInfo.valueOrNull;
      if (newProductCheckInfo != null) {
        ref.read(checkDrugShelfInfoProvider.notifier).updateCheckInfo(newProductCheckInfo);
      }
    } catch (err, stack) {
      state = AsyncValue.error(err, stack);
    }
    if (!state.hasError) {
      onSuccess();
    }
  }
}

///作業中断、一時停止の場合の処理
@riverpod
class WorkStopShelfCheckController extends _$WorkStopShelfCheckController {
  @override
  FutureOr<void> build() {}

  ///作業中断、一時停止の更新
  Future<void> workStop({
    required WorkStopType workStopType,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryServiceProvider).workStopShelfCheck(
            zoneCode: props?.zoneCode,
            shelfNumberText: props?.shelfInfo.shelfNumberText,
            checkPlanWeek: props?.shelfInfo.checkPlanWeek,
            workStopType: workStopType,
          ),
    );
  }
}

///賞味期限チェック削除用の処理
@riverpod
class DeleteDrugProductCheckInfoController extends _$DeleteDrugProductCheckInfoController {
  @override
  FutureOr<void> build() {}

  ///データの削除
  Future<void> delete({
    required String? productCode,
    VoidCallback? onSuccess,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryDrugServiceProvider).deleteDrugProductCheckInfo(
            zoneCode: props?.zoneCode,
            shelfNo: props?.shelfInfo.shelfNumberText,
            productCode: productCode,
          ),
    );
    if (!state.hasError) {
      onSuccess?.call();
    }
  }
}

/// エンドボタンのチェックマークの状態
@riverpod
class EndFlg extends _$EndFlg {
  @override
  bool build() => false;

  ///データの更新
  void update({required bool isEnd}) {
    debugPrint('update isEnd');
    state = isEnd;
  }
}

/// ミッドボタンのチェックマークの状態
@riverpod
class MidFlg extends _$MidFlg {
  @override
  bool build() => false;

  ///データの更新
  void update({required bool isMid}) {
    debugPrint('update isMid');
    state = isMid;
  }
}

/// 次回の賞味期限の文字
@riverpod
class NextExpiryDateText extends _$NextExpiryDateText {
  @override
  String build() => '';

  ///データの更新
  void update(String text) {
    debugPrint('update nextExpiryDateText');
    state = text;
  }

  ///データのクリア
  void clear() {
    state = '';
  }
}

/// 次回の賞味期限が有効であるかのフラグ
@riverpod
class NextExpiryDateValid extends _$NextExpiryDateValid {
  @override
  bool build() {
    final dateText = ref.watch(nextExpiryDateTextProvider);

    return isValidNextExpiryDate(dateText);
  }
}
