// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fresh_new_product_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freshNewProductListHash() => r'04ce1196a9f8551fffe2224543140df29f8f3ba1';

/// 取得した商品リスト
///
/// Copied from [FreshNewProductList].
@ProviderFor(FreshNewProductList)
final freshNewProductListProvider =
    AutoDisposeAsyncNotifierProvider<FreshNewProductList, List<FreshNewProduct>>.internal(
  FreshNewProductList.new,
  name: r'freshNewProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshNewProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshNewProductList = AutoDisposeAsyncNotifier<List<FreshNewProduct>>;
String _$deleteFreshNewProductListControllerHash() => r'f3b63022700128e155d022cdc6b5ff2e820dbfcc';

/// 生鮮新規削除
///
/// Copied from [DeleteFreshNewProductListController].
@ProviderFor(DeleteFreshNewProductListController)
final deleteFreshNewProductListControllerProvider =
    AutoDisposeAsyncNotifierProvider<DeleteFreshNewProductListController, void>.internal(
  DeleteFreshNewProductListController.new,
  name: r'deleteFreshNewProductListControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$deleteFreshNewProductListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteFreshNewProductListController = AutoDisposeAsyncNotifier<void>;
String _$freshShelfListHash() => r'97e32d0902a4d3771cb00952c9aaa66e106529f5';

/// 生鮮棚一覧
///
/// Copied from [FreshShelfList].
@ProviderFor(FreshShelfList)
final freshShelfListProvider = AutoDisposeAsyncNotifierProvider<FreshShelfList, List<ZoonShelfInfo>>.internal(
  FreshShelfList.new,
  name: r'freshShelfListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshShelfListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshShelfList = AutoDisposeAsyncNotifier<List<ZoonShelfInfo>>;
String _$freshResistInfoHash() => r'3e10f5a536c0f9316ea275645ba1b9ae9b29fcae';

/// 生鮮の登録情報
///
/// Copied from [FreshResistInfo].
@ProviderFor(FreshResistInfo)
final freshResistInfoProvider = AutoDisposeAsyncNotifierProvider<FreshResistInfo, FreshNewProductRegisterInfo>.internal(
  FreshResistInfo.new,
  name: r'freshResistInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshResistInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshResistInfo = AutoDisposeAsyncNotifier<FreshNewProductRegisterInfo>;
String _$selectProductListHash() => r'1344a7ee24ba70b0e09341bd3f82596c3371b704';

/// 選択された商品リスト
///
/// Copied from [SelectProductList].
@ProviderFor(SelectProductList)
final selectProductListProvider = AutoDisposeNotifierProvider<SelectProductList, List<FreshNewProduct>>.internal(
  SelectProductList.new,
  name: r'selectProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectProductList = AutoDisposeNotifier<List<FreshNewProduct>>;
String _$selectZoonShelfInfoHash() => r'076b9dfb059b82ac8a72e1eb5b07561b907f3ef4';

/// 選択された棚番情報
///
/// Copied from [SelectZoonShelfInfo].
@ProviderFor(SelectZoonShelfInfo)
final selectZoonShelfInfoProvider = AutoDisposeNotifierProvider<SelectZoonShelfInfo, ZoonShelfInfo?>.internal(
  SelectZoonShelfInfo.new,
  name: r'selectZoonShelfInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectZoonShelfInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectZoonShelfInfo = AutoDisposeNotifier<ZoonShelfInfo?>;
String _$currentRegistProductHash() => r'96eb7e08577b3bc651eed19d8ac31aae5a7e080c';

/// 現在登録中の商品
///
/// Copied from [CurrentRegistProduct].
@ProviderFor(CurrentRegistProduct)
final currentRegistProductProvider = AutoDisposeNotifierProvider<CurrentRegistProduct, FreshNewProduct?>.internal(
  CurrentRegistProduct.new,
  name: r'currentRegistProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$currentRegistProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentRegistProduct = AutoDisposeNotifier<FreshNewProduct?>;
String _$currentRegistProductControllerHash() => r'fe79156de115ed25493b010dda6a37ddfaa19579';

/// 生鮮の登録情報
///
/// Copied from [CurrentRegistProductController].
@ProviderFor(CurrentRegistProductController)
final currentRegistProductControllerProvider =
    AutoDisposeAsyncNotifierProvider<CurrentRegistProductController, void>.internal(
  CurrentRegistProductController.new,
  name: r'currentRegistProductControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$currentRegistProductControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentRegistProductController = AutoDisposeAsyncNotifier<void>;
String _$nextExpiryDateTextHash() => r'014707841960da0115e345febff23b89354860b0';

/// 次回の賞味期限の文字
///
/// Copied from [NextExpiryDateText].
@ProviderFor(NextExpiryDateText)
final nextExpiryDateTextProvider = AutoDisposeNotifierProvider<NextExpiryDateText, String>.internal(
  NextExpiryDateText.new,
  name: r'nextExpiryDateTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateText = AutoDisposeNotifier<String>;
String _$nextExpiryDateValidHash() => r'7d10dd7d4ab3c6ba95699f6ee6e652c4cb1b7b37';

/// 次回の賞味期限が有効であるかのフラグ
///
/// Copied from [NextExpiryDateValid].
@ProviderFor(NextExpiryDateValid)
final nextExpiryDateValidProvider = AutoDisposeNotifierProvider<NextExpiryDateValid, bool>.internal(
  NextExpiryDateValid.new,
  name: r'nextExpiryDateValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateValid = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
