import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../../utils/async_value_ui.dart';
import '../../../../../domain/next_expiry_date.dart';
import '../../../../common_widgets/default_bottom_app_bar.dart';
import '../../../../common_widgets/outline_round_text_button.dart';
import '../../../../common_widgets/round_text_button.dart';
import '../../../../common_widgets/work_stop_alert_dialog.dart';
import '../../../shelf_list_page/shelf_list_page_state.dart';
import '../check_mid_shelf_state.dart';

/// フッターのボトムバー
class CheckMidShelfBottomAppBar extends ConsumerWidget {
  /// constructor
  const CheckMidShelfBottomAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final drugProductCheckInfo = ref.watch(checkMidShelfInfoProvider);

    ref
      ..listen(
        updateCheckMidShelfInfoControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        workStopShelfCheckControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        deleteMidProductCheckInfoControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      );

    if (drugProductCheckInfo.hasError) {
      return const DefaultBottomAppBar();
    }
    final isLastCheckProduct = drugProductCheckInfo.valueOrNull?.productInfo.isLastCheckProduct ?? false;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        await _onBackPressed(context: context, ref: ref);
      },
      child: DefaultBottomAppBar(
        leading: BackButton(
          onPressed: () => _onBackPressed(
            context: context,
            ref: ref,
          ),
        ),
        actions: drugProductCheckInfo.value != null
            ? [
                OutlineRoundTextButton(
                  '欠品',
                  onPressed: () => _onShortagePressed(
                    ref: ref,
                    expirationDate: drugProductCheckInfo.value?.productInfo.expirationDate,
                  ),
                ),
                const SizedBox(width: 8),
                OutlineRoundTextButton(
                  '終売',
                  onPressed: () => _onDeletePressed(
                    context: context,
                    ref: ref,
                    productCode: drugProductCheckInfo.value?.productInfo.productCode.value,
                    isLastCheckProduct: isLastCheckProduct,
                  ),
                ),
                const SizedBox(width: 8),
                RoundTextButton(
                  isLastCheckProduct ? '完了' : '次へ',
                  onPressed: () => _onNextPressed(
                    context: context,
                    ref: ref,
                    isLastCheckProduct: isLastCheckProduct,
                  ),
                ),
              ]
            : [],
      ),
    );
  }

  /// 戻るボタンの処理
  Future<bool> _onBackPressed({
    required BuildContext context,
    required WidgetRef ref,
  }) async {
    final workStopType = await showWorkStopAlertAlertDialog(
      context: ref.context,
    );

    //キャンセルなら何もしない
    if (workStopType == null) {
      return false;
    }

    await ref.read(workStopShelfCheckControllerProvider.notifier).workStop(workStopType: workStopType);

    if (!context.mounted) return false;

    // 棚一覧を更新してあげる
    ref.invalidate(shelfWorkInfoListProvider);
    context.pop();

    return false;
  }

  /// 欠品ボタンの処理
  Future<void> _onShortagePressed({
    required WidgetRef ref,
    required DateTime? expirationDate,
  }) async {
    if (expirationDate == null) {
      return;
    }
    //欠品ボタンを押した場合、次回賞味期限に　見切り日＋1で表示
    final targetDate = expirationDate.add(const Duration(days: 1));
    ref.read(nextExpiryDateTextProvider.notifier).update(
          formatNextExpiryDate(DateFormat('yyyyMMdd').format(targetDate)),
        );
  }

  /// 終売ボタンの処理
  Future<void> _onDeletePressed({
    required BuildContext context,
    required WidgetRef ref,
    required String? productCode,
    required bool isLastCheckProduct,
  }) async {
    if (productCode == null) {
      return;
    }
    final ok = await showAlertDialog(
      context: ref.context,
      title: 'この商品を賞味期限チェックから削除します。',
      cancelActionText: 'キャンセル',
      defaultActionText: '削除する',
    );
    if (ok ?? false) {
      await ref
          .watch(
            deleteMidProductCheckInfoControllerProvider.notifier,
          )
          .delete(
            productCode: productCode,
            onSuccess: switch (isLastCheckProduct) {
              true => () {
                  showSnackBar(context, 'すべての商品がチェックされました。');
                  // 棚一覧を更新してあげる
                  ref.invalidate(shelfWorkInfoListProvider);
                  // 完了の場合は戻る
                  context.pop();
                },
              false => () {
                  showSnackBar(context, '削除しました。');
                  //データを再取得
                  ref.invalidate(checkMidShelfInfoProvider);
                },
            },
          );
    }
  }

  /// 次へボタンの処理
  Future<void> _onNextPressed({
    required BuildContext context,
    required WidgetRef ref,
    required bool isLastCheckProduct,
  }) async {
    await ref.watch(updateCheckMidShelfInfoControllerProvider.notifier).updateComplete(
          switch (isLastCheckProduct) {
            true => () {
                showSnackBar(context, 'すべての商品がチェックされました。');
                // 棚一覧を更新してあげる
                ref.invalidate(shelfWorkInfoListProvider);
                // 完了の場合は戻る
                context.pop();
              },
            false => () {
                showSnackBar(context, '完了しました。');
                //更新
                ref.invalidate(checkMidShelfInfoProvider);
                ref.watch(nextExpiryDateTextProvider.notifier).clear();
              },
          },
        );
  }
}
