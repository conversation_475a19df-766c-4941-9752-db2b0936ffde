// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_drug_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$drugHistoryListControllerHash() => r'd1af6815f66014f9e80899af313f293bd8d2c58d';

/// ドラッグ履歴一覧取得の状態
///
/// Copied from [DrugHistoryListController].
@ProviderFor(DrugHistoryListController)
final drugHistoryListControllerProvider =
    AutoDisposeAsyncNotifierProvider<DrugHistoryListController, List<DrugHistory>?>.internal(
  DrugHistoryListController.new,
  name: r'drugHistoryListControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$drugHistoryListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DrugHistoryListController = AutoDisposeAsyncNotifier<List<DrugHistory>?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
