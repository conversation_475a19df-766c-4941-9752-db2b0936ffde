// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_shelf_page_props_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkShelfPagePropsControllerHash() => r'ab44ea9afdfd2e910e94f2bdae412e6a29e94621';

/// ページ間の値受け渡し
///
/// Copied from [CheckShelfPagePropsController].
@ProviderFor(CheckShelfPagePropsController)
final checkShelfPagePropsControllerProvider =
    AutoDisposeNotifierProvider<CheckShelfPagePropsController, CheckShelfPageProps?>.internal(
  CheckShelfPagePropsController.new,
  name: r'checkShelfPagePropsControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkShelfPagePropsControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckShelfPagePropsController = AutoDisposeNotifier<CheckShelfPageProps?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
