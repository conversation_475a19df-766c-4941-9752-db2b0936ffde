import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../application/check_expiry_service.dart';
import '../../../domain/end_return_product.dart';
import '../../../domain/product_code.dart';

part 'end_return_state.g.dart';

/// エンド戻し商品の情報取得の状態
@riverpod
class EndReturnProductController extends _$EndReturnProductController {
  @override
  FutureOr<EndReturnProduct?> build() async {
    return null;
  }

  /// スキャン処理
  Future<void> scan(String janCode) async {
    final productCode = ProductCode.parseProductCode(janCode);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryServiceProvider).fetchEndReturnProduct(productCode.value),
    );
  }
}
