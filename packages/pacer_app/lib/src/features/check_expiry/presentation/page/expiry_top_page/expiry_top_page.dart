import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import '../../../../../routing/app_router.dart';
import '../../common_widgets/default_app_bar.dart';
import '../../common_widgets/default_bottom_app_bar.dart';
import '../../common_widgets/outline_round_text_button.dart';
import '../../common_widgets/round_text_button.dart';
import '../../routing/expiry_route.dart';

/// 賞味期限チェックトップメニューページ
class ExpiryTopPage extends StatelessWidget {
  /// init
  const ExpiryTopPage({super.key});
  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Scaffold(
      appBar: const DefaultAppBar(),
      bottomNavigationBar: const DefaultBottomAppBar(),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Gap(69),
                  RoundTextButton(
                    '賞味期限チェックをはじめる',
                    width: double.infinity,
                    height: 70,
                    onPressed: () => const ExpiryZoneListRoute().go(context),
                  ),
                  const Gap(15),
                  OutlineRoundTextButton(
                    'エンド戻し',
                    width: double.infinity,
                    height: 70,
                    onPressed: () => const ExpiryEndReturnRoute().go(context),
                  ),
                  const Gap(40),
                  Text(
                    'メンテナンス',
                    style: texts.titleSmall,
                  ),
                  const Gap(10),
                  GridView.count(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    crossAxisCount: 2,
                    crossAxisSpacing: 14,
                    mainAxisSpacing: 16,
                    childAspectRatio: 3 / 1,
                    children: [
                      OutlineRoundTextButton(
                        'ミッド',
                        onPressed: () => const ExpiryMaintenanceMidRoute().go(context),
                      ),
                      OutlineRoundTextButton(
                        '生鮮',
                        onPressed: () => const ExpiryMaintenanceFreshRoute().go(context),
                      ),
                      OutlineRoundTextButton(
                        'ドラッグ',
                        onPressed: () => const ExpiryMaintenanceDrugRoute().go(context),
                      ),
                      OutlineRoundTextButton(
                        '特殊',
                        onPressed: () => const ExpiryMaintenanceSpecialRoute().go(context),
                      ),
                    ],
                  ),
                  const Gap(40),
                  Text('履歴確認', style: texts.titleSmall),
                  const Gap(10),
                  GridView.count(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    crossAxisCount: 2,
                    crossAxisSpacing: 14,
                    mainAxisSpacing: 16,
                    childAspectRatio: 3 / 1,
                    children: [
                      OutlineRoundTextButton(
                        'ドラッグ',
                        onPressed: () => const ExpiryHistoryDrugRoute().go(context),
                      ),
                      OutlineRoundTextButton(
                        'その他',
                        onPressed: () => const ExpiryHistoryOtherRoute().go(context),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
