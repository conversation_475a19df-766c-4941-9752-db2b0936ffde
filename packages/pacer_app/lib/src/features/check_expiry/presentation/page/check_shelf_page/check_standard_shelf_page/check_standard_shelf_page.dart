import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../device/application/pacer_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../../domain/check_shelf_info.dart';
import '../../../../domain/enum/shelf_product_type.dart';
import '../../../../domain/shelf_work_info.dart';
import '../../../common_widgets/deadline.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/default_bottom_app_bar.dart';
import '../../../common_widgets/round_text_button.dart';
import '../../../common_widgets/work_stop_alert_dialog.dart';
import '../../shelf_list_page/shelf_list_page_state.dart';
import '../check_shelf_page_props_controller.dart';
import 'check_standard_shelf_state.dart';

part 'widgets/bottom_app_bar.dart';

/// 賞味期限チェックページ（定番）
class CheckStandardShelfPage extends HookConsumerWidget {
  /// constructor
  const CheckStandardShelfPage({super.key});

  /// スキャン時の処理
  void _onScan(WidgetRef ref, String code, bool isScanEnable) {
    //スキャンのダイアログが出てない場合は何もしない
    if (!isScanEnable) {
      return;
    }

    ref.read(completeShelfCheckControllerProvider.notifier).complete(
      code,
      onSuccess: () {
        ref.context
          ..pop()
          ..pop();
        showSnackBar(ref.context, '完了しました。');
        // 棚一覧を更新してあげる
        ref.invalidate(shelfWorkInfoListProvider);
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final isScanEnable = ref.watch(isScanEnableProvider);
    final checkStandardShelfInfo = ref.watch(checkStandardShelfInfoProvider);

    ref
      ..listen(
        scanCodeProvider,
        (_, state) {
          if (state case AsyncData(:final value) when value.isNotEmpty) {
            _onScan(ref, value, isScanEnable);
          }
        },
      )
      ..listen(
        completeShelfCheckControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        checkStandardShelfInfoProvider,
        (_, state) => state.showAlertDialogOnError(context),
      );

    return Scaffold(
      appBar: const DefaultAppBar(),
      bottomNavigationBar: CheckStandardShelfBottomAppBar(onScan: _onScan),
      body: SafeArea(
        child: switch (checkStandardShelfInfo) {
          AsyncError() => const SizedBox.shrink(),
          AsyncLoading() => const Center(child: CircularProgressIndicator()),
          AsyncData(:final value) => Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Gap(24),
                    _Title(
                      numberText: props?.shelfInfo.shelfNumberText,
                      name: props?.shelfInfo.shelfName,
                    ),
                    const Gap(10),
                    if (value.groupedProductInfoList.isNotEmpty) ...[
                      _StandardProducts(value.groupedProductInfoList, props),
                    ],
                    _TargetTime(value.endTime),
                  ],
                ),
              ),
            ),
        },
      ),
    );
  }
}

/// タイトル（番号 商品名）
class _Title extends StatelessWidget {
  const _Title({
    required this.numberText,
    required this.name,
  });

  final String? numberText;
  final String? name;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (numberText != null)
          Text(
            numberText ?? '',
            style: texts.titleMedium?.copyWith(color: colors.primary, fontSize: 18),
          ),
        const Gap(8),
        Expanded(
          child: Text(
            name ?? '',
            style: texts.titleMedium?.copyWith(color: colors.primary, fontSize: 18),
          ),
        ),
      ],
    );
  }
}

///商品情報
class _StandardProducts extends StatelessWidget {
  const _StandardProducts(this.productInfoList, this.props);

  final List<GroupedProductInfo> productInfoList;

  final CheckShelfPageProps? props;

  @override
  Widget build(BuildContext context) {
    final shelfProductType = switch (props?.shelfInfo) {
      null => ShelfProductType.other,
      final ShelfWorkInfo shelfWorkInfo => shelfWorkInfo.shelfProductType,
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: productInfoList.map(
        (e) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (e.specialProductInfoList.isNotEmpty) ...[
                const Gap(8),
                const _CheckTitle('特殊賞味期限'),
                Deadline(
                  limitDate: e.bestBefore,
                  shelfProductType: shelfProductType,
                  isNewproduct: false,
                ),
                const Gap(4),
                _SpecialProductTable(e.specialProductInfoList),
                const Gap(12),
              ],
              if (e.hasProducedDate) ...[
                const Gap(8),
                const _CheckTitle('製造期限'),
                Deadline(
                  limitDate: e.producedDate,
                  shelfProductType: shelfProductType,
                  isNewproduct: false,
                ),
                const Gap(4),
              ],
              if (e.standardProductInfoList.isNotEmpty) ...[
                const Gap(8),
                const _CheckTitle('通常賞味期限'),
                Deadline(
                  limitDate: e.bestBefore,
                  shelfProductType: shelfProductType,
                  isNewproduct: false,
                ),
                const Gap(4),
                _NormalProducts(e.standardProductInfoList),
                const Gap(12),
              ],
            ],
          );
        },
      ).toList(),
    );
  }
}

/// 定番商品表示
class _NormalProducts extends StatelessWidget {
  const _NormalProducts(this.productInfoList);

  final List<StandardProductInfo> productInfoList;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: productInfoList
          .map(
            (e) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '対象商品',
                  style: texts.labelLarge?.copyWith(color: colors.subText),
                ),
                const Gap(4),
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.fromBorderSide(BorderSide(color: colors.line)),
                    borderRadius: const BorderRadius.all(Radius.circular(4)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                    child: Text('通常賞味期限の商品', style: texts.titleMedium),
                  ),
                ),
              ],
            ),
          )
          .toList(),
    );
  }
}

/// 特殊商品テーブル
class _SpecialProductTable extends HookConsumerWidget {
  const _SpecialProductTable(
    this.specialProducts,
  );

  final List<StandardProductInfo> specialProducts;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    const edgeInsets = EdgeInsets.symmetric(horizontal: 8, vertical: 6);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('対象商品', style: texts.labelLarge?.copyWith(color: colors.subText)),
        const Gap(4),
        Table(
          columnWidths: const {
            0: FractionColumnWidth(0.48),
            1: FractionColumnWidth(0.52),
          },
          border: TableBorder.all(
            color: colors.line,
            borderRadius: const BorderRadius.all(Radius.circular(4)),
          ),
          children: specialProducts
              .map(
                (e) => TableRow(
                  children: [
                    Padding(
                      padding: edgeInsets,
                      child: Text(e.productCode.value, style: texts.titleMedium),
                    ),
                    Padding(
                      padding: edgeInsets,
                      child: Text(
                        e.productName,
                        style: texts.titleMedium,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              )
              .toList(),
        ),
      ],
    );
  }
}

/// 完了目標
class _TargetTime extends StatelessWidget {
  const _TargetTime(this.targetTime);

  final DateTime? targetTime;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final targetTime = this.targetTime;
    if (targetTime == null) return const SizedBox.shrink();

    return Align(
      alignment: Alignment.topRight,
      child: Text(
        '完了目標 ${DateFormat('HH:mm').format(targetTime)}',
        style: texts.titleMedium,
      ),
    );
  }
}

/// 賞味期限タイトル
class _CheckTitle extends StatelessWidget {
  const _CheckTitle(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(text, style: texts.titleMedium?.copyWith(color: colors.primary)),
    );
  }
}
