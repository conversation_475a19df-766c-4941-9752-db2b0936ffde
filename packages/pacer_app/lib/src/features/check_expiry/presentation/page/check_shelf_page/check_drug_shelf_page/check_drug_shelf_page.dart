import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/async_util.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../domain/product_check_info.dart';
import '../../../common_widgets/check_box_button.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/deadline.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/next_expiry_date_text_field.dart';
import '../../../common_widgets/product_info_table.dart';
import '../check_shelf_page_props_controller.dart';
import 'check_drug_shelf_state.dart';
import 'widgets/bottom_app_bar.dart';

/// ドラッグの棚チェックページ
class CheckDrugShelfPage extends HookConsumerWidget {
  /// constructor
  const CheckDrugShelfPage({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final props = ref.watch(checkShelfPagePropsControllerProvider);

    final drugProductCheckInfo = ref.watch(checkDrugShelfInfoProvider);

    final focusNode = useFocusNode();

    ref.listen(
      checkDrugShelfInfoProvider,
      (_, state) => state.showAlertDialogOnError(context),
    );

    return Focus(
      focusNode: focusNode,
      child: GestureDetector(
        onTap: focusNode.requestFocus,
        child: Scaffold(
          appBar: const DefaultAppBar(),
          bottomNavigationBar: const CheckDrugShelfBottomAppBar(),
          body: SafeArea(
            child: switch (drugProductCheckInfo) {
              AsyncLoading() => const Center(child: CircularProgressIndicator()),
              AsyncError() => const SizedBox.shrink(),
              AsyncData(value: final value && ProductCheckInfo(:final productInfo)) => SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 24,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _Title(
                        numberText: props?.shelfInfo.shelfNumberText,
                        name: props?.shelfInfo.shelfName,
                      ),
                      const SizedBox(height: 10),
                      Deadline(
                        limitDate: productInfo.expirationDate,
                        isNewproduct: productInfo.isNewproduct,
                      ),
                      const SizedBox(height: 12),
                      _SelectedProductText(progress: productInfo.taskCount),
                      const SizedBox(height: 4),
                      ProductInfoTable.fromProductCheckInfo(value),
                      const SizedBox(height: 12),
                      _AlignmentCheck(
                        isEnd: productInfo.isEnd,
                        isMid: productInfo.isMid,
                      ),
                      const SizedBox(height: 12),
                      const _NextExpiryDateTextField(),
                      const SizedBox(height: 24),
                      _WorkProgress(progress: productInfo.taskCount),
                      const SizedBox(height: 6),
                    ],
                  ),
                ),
            },
          ),
        ),
      ),
    );
  }
}

/// タイトル（番号 商品名）
class _Title extends StatelessWidget {
  const _Title({
    required this.numberText,
    required this.name,
  });

  final String? numberText;
  final String? name;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (numberText != null)
          Text(
            numberText ?? '',
            style: texts.titleMedium?.copyWith(
              color: colors.primary,
              fontSize: 18,
            ),
          ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            name ?? '',
            style: texts.titleMedium?.copyWith(
              color: colors.primary,
              fontSize: 18,
            ),
          ),
        ),
      ],
    );
  }
}

/// 対象商品（0/10）の表示
class _SelectedProductText extends StatelessWidget {
  const _SelectedProductText({
    required this.progress,
  });

  final String progress;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Text(
      '対象商品 $progress',
      style: texts.labelLarge?.copyWith(color: colors.subText),
    );
  }
}

// 定番棚以外の配置
class _AlignmentCheck extends HookConsumerWidget {
  const _AlignmentCheck({
    required this.isEnd,
    required this.isMid,
  });

  final bool isEnd;
  final bool isMid;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final endFlgState = ref.watch(endFlgProvider);
    final midFlgState = ref.watch(midFlgProvider);

    useEffect(
      () {
        delayed(() {
          //初期化
          ref.read(endFlgProvider.notifier).update(isEnd: isEnd);
          ref.read(midFlgProvider.notifier).update(isMid: isMid);
        });

        return;
      },
      [],
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '定番棚以外の配置',
          style: texts.titleSmall?.copyWith(color: colors.subText),
        ),
        const SizedBox(width: 4),
        Row(
          children: [
            CheckBoxButton(
              'エンド',
              isCheckd: endFlgState,
              onChanged: ({bool? isCheckd}) => ref.read(endFlgProvider.notifier).update(isEnd: isCheckd ?? false),
            ),
            const SizedBox(width: 8),
            CheckBoxButton(
              'ミッド',
              isCheckd: midFlgState,
              onChanged: ({bool? isCheckd}) => ref.read(midFlgProvider.notifier).update(isMid: isCheckd ?? false),
            ),
          ],
        ),
      ],
    );
  }
}

class _NextExpiryDateTextField extends HookConsumerWidget {
  const _NextExpiryDateTextField();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final text = ref.watch(nextExpiryDateTextProvider);
    final isValid = ref.watch(nextExpiryDateValidProvider);
    final drugProductCheckInfo = ref.watch(checkDrugShelfInfoProvider).value;

    return NextExpiryDateTextField(
      inputText: text,
      onChanged: ref.read(nextExpiryDateTextProvider.notifier).update,
      onFieldSubmitted: (text) async {
        final isShowDialog = await showNextExpiryErrorAlertDialog(
          context,
          text,
          jobDate: drugProductCheckInfo?.productInfo.jobDate,
          limitDate: drugProductCheckInfo?.limitDate,
        );
        if (isShowDialog) {
          ref.read(nextExpiryDateTextProvider.notifier).clear();
        }
      },
      isValid: isValid,
    );
  }
}

///下部の作業進捗
class _WorkProgress extends StatelessWidget {
  const _WorkProgress({
    required this.progress,
  });

  final String progress;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Align(
      alignment: Alignment.centerRight,
      child: Text(
        '作業進捗 ${removeParentheses(progress)}',
        style: texts.titleMedium,
      ),
    );
  }

  String removeParentheses(String input) {
    // 括弧()を取り除く
    return input.replaceAll(RegExp('[()]'), '');
  }
}
