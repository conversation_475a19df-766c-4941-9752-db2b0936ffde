import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../routing/app_router.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../print_discount/presentation/routing/discount_route.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/deadline.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/next_expiry_date_text_field.dart';
import '../../../common_widgets/outline_round_text_button.dart';
import '../../../common_widgets/product_info_table.dart';
import '../check_shelf_page_props_controller.dart';
import 'check_fresh_shelf_state.dart';
import 'widgets/bottom_app_bar.dart';

/// 賞味期限チェックページ（生鮮）
class CheckFreshShelfPage extends HookConsumerWidget {
  /// constructor
  const CheckFreshShelfPage({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final nextExpiryDateFocusNode = useFocusNode();
    final discountDateFocusNode = useFocusNode();
    final freshProductCheckInfo = ref.watch(checkFreshShelfInfoProvider);
    ref
      ..listen(
        checkFreshShelfInfoProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        discountConfirmControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      );

    return GestureDetector(
      onTap: () {
        nextExpiryDateFocusNode.unfocus();
        discountDateFocusNode.unfocus();
      },
      child: Scaffold(
        appBar: const DefaultAppBar(),
        bottomNavigationBar: const CheckFreshShelfBottomAppBar(),
        body: SafeArea(
          child: switch (freshProductCheckInfo) {
            AsyncError() => const SizedBox.shrink(),
            AsyncLoading() => const Center(child: CircularProgressIndicator()),
            AsyncData(:final value) => SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _Title(
                      numberText: props?.shelfInfo.shelfNumberText,
                      name: props?.shelfInfo.shelfName,
                    ),
                    const SizedBox(height: 10),
                    Deadline(
                      limitDate: value.productInfo.expirationDate,
                      isNewproduct: value.productInfo.isNewproduct,
                    ),
                    const SizedBox(height: 12),
                    _SelectedProductText(progress: value.productInfo.taskCount),
                    const SizedBox(height: 4),
                    ProductInfoTable.fromProductCheckInfo(value),
                    const SizedBox(height: 12),
                    _NextExpiryDateTextField(
                      nextExpiryDateFocusNode,
                      discountDateFocusNode,
                    ),
                    const SizedBox(height: 24),
                    _WorkProgress(progress: value.productInfo.taskCount),
                    const SizedBox(height: 6),
                  ],
                ),
              ),
          },
        ),
      ),
    );
  }
}

/// タイトル（番号 商品名）
class _Title extends StatelessWidget {
  const _Title({
    required this.numberText,
    required this.name,
  });

  final String? numberText;
  final String? name;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (numberText != null)
          Text(
            numberText ?? '',
            style: texts.titleMedium?.copyWith(
              color: colors.primary,
              fontSize: 18,
            ),
          ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            name ?? '',
            style: texts.titleMedium?.copyWith(
              color: colors.primary,
              fontSize: 18,
            ),
          ),
        ),
      ],
    );
  }
}

/// 対象商品（0/10）の表示
class _SelectedProductText extends StatelessWidget {
  const _SelectedProductText({
    required this.progress,
  });

  final String progress;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Text(
      '対象商品 $progress',
      style: texts.labelLarge?.copyWith(color: colors.subText),
    );
  }
}

/// 賞味期限テキストフォーム
class _NextExpiryDateTextField extends HookConsumerWidget {
  const _NextExpiryDateTextField(
    this.nextExpiryDateFocusNode,
    this.discountDateFocusNode,
  );

  final FocusNode nextExpiryDateFocusNode;
  final FocusNode discountDateFocusNode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final text = ref.watch(nextExpiryDateTextProvider);
    final isValid = ref.watch(nextExpiryDateValidProvider);
    final isTapOutsideEnabled = useState(false);
    final freshProductCheckInfo = ref.watch(checkFreshShelfInfoProvider).value;

    return NextExpiryDateTextField(
      inputText: text,
      onChanged: ref.read(nextExpiryDateTextProvider.notifier).update,
      isValid: isValid,
      onPressed: () => onTextFieldPressed(
        ref,
        context,
        nextExpiryDateFocusNode,
        discountDateFocusNode,
        isTapOutsideEnabled,
      ),
      focusNode: nextExpiryDateFocusNode,
      onFieldSubmitted: (text) async {
        final isShowDialog = await showNextExpiryErrorAlertDialog(
          context,
          text,
          jobDate: freshProductCheckInfo?.productInfo.jobDate,
          limitDate: freshProductCheckInfo?.limitDate,
        );
        if (isShowDialog) {
          ref.read(nextExpiryDateTextProvider.notifier).clear();
        }
      },
    );
  }

  // テキストフィールドを押したときの処理
  Future<void> onTextFieldPressed(
    WidgetRef ref,
    BuildContext context,
    FocusNode nextExpiryDateFocusNode,
    FocusNode discountDateFocusNode,
    ValueNotifier<bool> isTapOutsideEnabled,
  ) async {
    nextExpiryDateFocusNode.unfocus();
    isTapOutsideEnabled.value = false;
    final ok = await showAlertDialog(
      context: context,
      title: '見切り商品はありますか？',
      cancelActionText: 'いいえ',
      defaultActionText: 'はい',
    );
    if (!context.mounted) return;

    if (ok ?? false) {
      await showModalBottomSheet<void>(
        isScrollControlled: true,
        context: context,
        builder: (_) {
          return _DiscountBottomSheet(
            discountDateFocusNode,
            onConfirmPressed: () => onConfirmPressed(ref, context),
            onCancelPressed: () => onCancelPressed(ref, context),
          );
        },
      );
    } else {
      nextExpiryDateFocusNode.requestFocus();
    }
    isTapOutsideEnabled.value = true;
  }

  /// 確定ボタン押下時の処理
  Future<void> onConfirmPressed(WidgetRef ref, BuildContext context) async {
    context.pop();

    await ref.watch(discountConfirmControllerProvider.notifier).discountConfirm(() async {
      // 印刷確認
      final ok = await showPrintConfirmDialog(context);
      if (!context.mounted) return;
      if (ok ?? false) {
        await const SetPrinterRoute().push<void>(context);
      } else {
        nextExpiryDateFocusNode.requestFocus();
      }
      ref.read(closeoutProductExpiryDateTextProvider.notifier).clear();
    });
  }

  /// 印刷確認ダイアログ
  Future<bool?> showPrintConfirmDialog(BuildContext context) async {
    return showAlertDialog(
      context: context,
      title: '値引シールを出しますか？',
      cancelActionText: 'いいえ',
      defaultActionText: 'はい',
    );
  }

  //キャンセル押下時の処理
  void onCancelPressed(WidgetRef ref, BuildContext context) {
    ref.read(closeoutProductExpiryDateTextProvider.notifier).clear();
    Navigator.pop(context);
  }
}

/// 値引き
class _DiscountBottomSheet extends HookConsumerWidget {
  const _DiscountBottomSheet(
    this.focusNode, {
    required this.onConfirmPressed,
    required this.onCancelPressed,
  });

  final FocusNode focusNode;
  final VoidCallback? onConfirmPressed;
  final VoidCallback? onCancelPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final text = ref.watch(closeoutProductExpiryDateTextProvider);
    final isValid = ref.watch(closeoutProductExpiryDateValidProvider);
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return SafeArea(
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: keyboardHeight + 255,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              Center(
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: colors.line,
                    borderRadius: const BorderRadius.all(Radius.circular(10)),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '見切り商品の値下作業を行ってください',
                style: texts.titleMedium?.copyWith(color: colors.text),
              ),
              const SizedBox(height: 16),
              NextExpiryDateTextField(
                title: '見切り商品の賞味期限',
                hintText: '見切り商品の賞味期限を入力してください',
                inputText: text,
                onChanged: ref.read(closeoutProductExpiryDateTextProvider.notifier).update,
                isValid: isValid,
                autofocus: true,
                focusNode: focusNode,
              ),
              const SizedBox(height: 18.5),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlineRoundTextButton(
                    'キャンセル',
                    onPressed: onCancelPressed,
                  ),
                  const SizedBox(width: 8),
                  OutlineRoundTextButton(
                    '確定',
                    width: 110,
                    onPressed: onConfirmPressed,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

///下部の作業進捗
class _WorkProgress extends StatelessWidget {
  const _WorkProgress({
    required this.progress,
  });

  final String progress;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Align(
      alignment: Alignment.centerRight,
      child: Text(
        '作業進捗 ${removeParentheses(progress)}',
        style: texts.titleMedium,
      ),
    );
  }

  String removeParentheses(String input) {
    // 括弧()を取り除く
    return input.replaceAll(RegExp('[()]'), '');
  }
}
