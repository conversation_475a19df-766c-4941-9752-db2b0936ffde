import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/check_expiry_mid_service.dart';
import '../../../../application/check_expiry_service.dart';
import '../../../../domain/mid_maintenance_list_product.dart';
import '../../../../domain/mid_maintenance_product.dart';
import '../../../../domain/next_expiry_date.dart';
import '../../../../domain/product_code.dart';
import '../../../../domain/shelf_list_info.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';

part 'maintenance_mid_state.g.dart';

/// ミッド棚一覧情報
@riverpod
class MidShelfListInfo extends _$MidShelfListInfo {
  @override
  FutureOr<ShelfListInfo> build() async {
    return ref.read(checkExpiryServiceProvider).fetchMidShelfList();
  }
}

/// 選択された棚番情報
@riverpod
class SelectShelfInfo extends _$SelectShelfInfo {
  @override
  ShelfInfo? build() => null;

  /// 更新
  void update(ShelfInfo? shelfInfo) {
    debugPrint('update selectShelfInfo');
    state = shelfInfo;
  }

  /// クリア
  void clear() {
    state = null;
  }
}

/// 次回の賞味期限の文字
@riverpod
class NextExpiryDateText extends _$NextExpiryDateText {
  @override
  String build() => '';

  /// 更新
  void update(String text) {
    debugPrint('update nextExpiryDateText');
    state = text;
  }

  ///クリア
  void clear() {
    state = '';
  }
}

/// 次回の賞味期限が有効であるかのフラグ
@riverpod
class NextExpiryDateValid extends _$NextExpiryDateValid {
  @override
  bool build() {
    final dateText = ref.watch(nextExpiryDateTextProvider);

    return isValidNextExpiryDate(dateText);
  }
}

/// メンテナンス商品情報
@riverpod
class MidProductInfoState extends _$MidProductInfoState {
  @override
  MidMaintenanceProduct? build() => null;

  /// 更新
  void update(MidMaintenanceProduct? productInfo) {
    debugPrint('update productInfo');
    state = productInfo;
  }

  /// クリア
  void clear() {
    state = null;
  }
}

/// メンテナンス商品情報のコントローラー
@riverpod
class MidProductInfoController extends _$MidProductInfoController {
  @override
  FutureOr<void> build() {
    ref.showGlobalLoading();
  }

  /// スキャン
  void scan(
    String janCode, {
    void Function(MidMaintenanceProduct)? onFetchProductSuccess,
    VoidCallback? onRegisterSuccess,
  }) {
    final productCode = ProductCode.parseProductCode(janCode);
    final productInfo = ref.watch(midProductInfoStateProvider);
    if (productInfo == null) {
      _fetch(
        productCode.value,
        onFetchProductSuccess: onFetchProductSuccess,
      );
    } else {
      scanRegisterAndFetchNewData(
        productCode.value,
        onFetchProductSuccess: onFetchProductSuccess,
        onRegisterSuccess: onRegisterSuccess,
      );
    }
  }

  Future<void> _fetch(
    String janCode, {
    void Function(MidMaintenanceProduct)? onFetchProductSuccess,
  }) async {
    if (janCode.isEmpty) {
      return;
    }

    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final selectShelfInfo = ref.read(selectShelfInfoProvider);
      if (selectShelfInfo == null) {
        throw UnknownException('棚番が選択されていません。');
      }

      //取得する
      final productInfo = await ref.read(checkExpiryMidServiceProvider).fetchMidMaintenanceProduct(
            janCode,
            selectShelfInfo.shelfNumberText,
            selectShelfInfo.zoneCode,
          );

      if (productInfo == null) {
        return;
      }
      if (productInfo.amount > 0) {
        throw UnknownException('すでに登録済みの商品です。');
      }

      onFetchProductSuccess?.call(productInfo);
    });
  }

  /// 商品情報を登録して別の商品データ取得
  Future<void> scanRegisterAndFetchNewData(
    String janCode, {
    void Function(MidMaintenanceProduct)? onFetchProductSuccess,
    VoidCallback? onRegisterSuccess,
  }) async {
    if (janCode.isEmpty) {
      return;
    }

    await register(
      nextProductCode: janCode,
      onSuccess: () async {
        await _fetch(janCode, onFetchProductSuccess: onFetchProductSuccess);
        onRegisterSuccess?.call();
      },
    );
  }

  /// ミッド商品情報を登録
  Future<void> register({
    VoidCallback? onSuccess,
    String nextProductCode = '',
  }) async {
    final productInfo = ref.watch(midProductInfoStateProvider);
    final isScan = nextProductCode.isNotEmpty;
    if (productInfo == null) {
      return;
    }
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      _checknextExpiryDate();
      final nextExpiryDateText = ref.read(nextExpiryDateTextProvider);
      final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(nextExpiryDateText);

      final selectShelfInfo = ref.read(selectShelfInfoProvider);
      if (selectShelfInfo == null) {
        throw UnknownException('棚番が選択されていません。');
      }

      if (isScan) {
        await ref.read(checkExpiryMidServiceProvider).scanRegisterMidProduct(
              midProductInfo: productInfo,
              nextExpiryDate: nextExpiryDate,
              shelfNo: selectShelfInfo.shelfNumberText,
              zoneCode: selectShelfInfo.zoneCode,
              newProductCode: nextProductCode,
            );
      } else {
        await ref.read(checkExpiryMidServiceProvider).registerMidProduct(
              nextExpiryDate: nextExpiryDate,
              shelfNo: selectShelfInfo.shelfNumberText,
              zoneCode: selectShelfInfo.zoneCode,
              productCode: productInfo.productCode.value,
            );
      }
    });

    if (!state.hasError) {
      onSuccess?.call();
    }
  }

  void _checknextExpiryDate() {
    final productInfo = ref.watch(midProductInfoStateProvider);
    final nextExpiryDateText = ref.watch(nextExpiryDateTextProvider);
    final midShelfListInfo = ref.watch(midShelfListInfoProvider).valueOrNull;
    if (productInfo == null) {
      return;
    }

    final errorMeesage = getNextExpiryErrorMessage(
      nextExpiryDateText,
      jobDate: DateFormat('yyyy年MM月dd日').tryParse(productInfo.checkDay),
      limitDate: midShelfListInfo?.limitDate,
    );

    // エラーメッセージがある場合はエラーをスローする
    if (errorMeesage != null) {
      throw UnknownException(errorMeesage);
    }
  }

  /// 商品情報をクリア
  void clear() {
    ref.read(midProductInfoStateProvider.notifier).update(null);
  }
}

/// ミッド商品リスト（削除用）
@riverpod
class MidProductList extends _$MidProductList {
  @override
  FutureOr<List<MidMaintenanceListProduct>> build() async {
    final selectShelfInfo = ref.watch(selectShelfInfoProvider);

    return ref.read(checkExpiryMidServiceProvider).fetchMidMaintenanceProductList(
          selectShelfInfo?.shelfNumberText,
          selectShelfInfo?.zoneCode,
        );
  }
}

/// 選択された商品リスト
@riverpod
class SelectProductList extends _$SelectProductList {
  @override
  List<MidMaintenanceListProduct> build() => [];

  /// 追加
  void add(MidMaintenanceListProduct product) {
    state = [...state, product];
  }

  /// 削除
  void remove(MidMaintenanceListProduct product) {
    state = [...state]..remove(product);
  }

  /// クリア
  void clear() {
    state = [];
  }
}

/// ミッド商品削除
@riverpod
class DeleteMidProductListController extends _$DeleteMidProductListController {
  @override
  FutureOr<void> build() {}

  /// ミッド商品削除
  @riverpod
  Future<void> deleteList(VoidCallback? onSuccess) async {
    final selectShelfInfo = ref.watch(selectShelfInfoProvider);
    final deleteProductList = ref.watch(selectProductListProvider);
    try {
      final futureList = deleteProductList
          .map(
            (product) => ref.read(checkExpiryMidServiceProvider).deleteMidProductCheckInfo(
                  shelfNo: selectShelfInfo?.shelfNumberText,
                  zoneCode: selectShelfInfo?.zoneCode,
                  productCode: product.productCode.value,
                  nextExpiryDate: product.nextExpirationDate,
                ),
          )
          .toList();

      state = const AsyncLoading();

      state = await AsyncValue.guard(
        () => Future.wait(futureList),
      );
      if (!state.hasError) {
        onSuccess?.call();
      }
    } catch (err, stack) {
      state = AsyncValue.error(err, stack);
    }
    //選択したリストをクリアする
    ref.read(selectProductListProvider.notifier).clear();
    ref.invalidate(midProductListProvider);
  }
}
