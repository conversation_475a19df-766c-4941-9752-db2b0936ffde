// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/check_expiry_drug_service.dart';
import '../../../../application/check_expiry_service.dart';
import '../../../../domain/drug_product_info.dart';
import '../../../../domain/next_expiry_date.dart';
import '../../../../domain/product_code.dart';
import '../../../../domain/shelf_list_info.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';

part 'maintenance_drug_state.g.dart';

/// ドラッグ棚一覧情報
@riverpod
class DrugShelfListInfo extends _$DrugShelfListInfo {
  @override
  FutureOr<ShelfListInfo> build() async {
    return ref.read(checkExpiryServiceProvider).fetchDrugShelfList();
  }
}

/// ドラッグ商品情報
@riverpod
class DrugProductInfoState extends _$DrugProductInfoState {
  @override
  DrugProductInfo? build() => null;

  void update(DrugProductInfo? productInfo) {
    debugPrint('update productInfo');
    state = productInfo;

    if (productInfo != null) {
      ref.read(midFlgProvider.notifier).update(isMid: productInfo.isMid);
      ref.read(endFlgProvider.notifier).update(isEnd: productInfo.isEnd);
      final nextExpirationDate = productInfo.nextExpirationDate;
      if (nextExpirationDate != null) {
        ref.read(nextExpiryDateTextProvider.notifier).update(
              formatNextExpiryDate(
                DateFormat('yyyyMMdd').format(nextExpirationDate),
              ),
            );
      } else {
        ref.read(nextExpiryDateTextProvider.notifier).clear();
      }
    }
  }

  void clear() {
    state = null;
    ref.read(nextExpiryDateTextProvider.notifier).clear();
    ref.read(endFlgProvider.notifier).update(isEnd: false);
    ref.read(midFlgProvider.notifier).update(isMid: false);
  }
}

@riverpod
class DrugProductInfoController extends _$DrugProductInfoController {
  @override
  FutureOr<void> build() async {
    ref.showGlobalLoading();
  }

  Future<void> scan(
    String janCode, {
    VoidCallback? onRegisterSuccess,
  }) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final productCode = ProductCode.parseProductCode(janCode);
      if (productCode.isFreshProduct) {
        throw UnknownException('生鮮商品は登録できません。');
      }

      final drugProductInfo = ref.watch(drugProductInfoStateProvider);
      switch (drugProductInfo) {
        case null:
          await _fetch(productCode.value);

        case _:
          await _registerAndFetchNewData(productCode.value, onRegisterSuccess);
      }
    });
  }

  Future<void> _fetch(String janCode) async {
    if (janCode.isEmpty) {
      return;
    }

    final item = await ref.read(checkExpiryDrugServiceProvider).fetchDrugProductInfo(janCode);
    ref.read(drugProductInfoStateProvider.notifier).update(item);
  }

  /// 商品情報を登録して別の商品データ取得
  Future<void> _registerAndFetchNewData(
    String janCode,
    VoidCallback? onSuccess,
  ) async {
    if (janCode.isEmpty) {
      return;
    }
    final drugProductInfo = ref.watch(drugProductInfoStateProvider);
    final isMid = ref.watch(midFlgProvider);
    final isEnd = ref.watch(endFlgProvider);
    if (drugProductInfo == null) {
      return;
    }
    try {
      _checknextExpiryDate();
      if (!isChangeData()) {
        throw UnknownException('変更がありません。');
      }
      final nextExpiryDateText = ref.watch(nextExpiryDateTextProvider);
      final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(nextExpiryDateText);
      state = const AsyncValue.loading();
      final newDrugProductInfo = await AsyncValue.guard(
        () => ref.read(checkExpiryDrugServiceProvider).getProcDrugProdInfo(
              productCode: janCode,
              drugProductInfo: drugProductInfo,
              nextExpiryDate: nextExpiryDate,
              isMid: isMid,
              isEnd: isEnd,
            ),
      );
      ref.read(drugProductInfoStateProvider.notifier).update(newDrugProductInfo.value);
      state = const AsyncValue.data(null);
    } catch (err, stack) {
      state = AsyncValue.error(err, stack);
    }
    if (!state.hasError) {
      onSuccess?.call();
    }
  }

  /// ドラッグ商品情報を登録
  Future<void> register(
    VoidCallback? onSuccess,
  ) async {
    final drugProductInfo = ref.watch(drugProductInfoStateProvider);
    final isMid = ref.watch(midFlgProvider);
    final isEnd = ref.watch(endFlgProvider);
    if (drugProductInfo == null) {
      return;
    }
    _checknextExpiryDate();

    final nextExpiryDateText = ref.watch(nextExpiryDateTextProvider);
    final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(nextExpiryDateText);

    await ref.read(checkExpiryDrugServiceProvider).setDrugProdInfo(
          drugProductInfo: drugProductInfo,
          nextExpiryDate: nextExpiryDate,
          isMid: isMid,
          isEnd: isEnd,
        );

    if (!state.hasError) {
      onSuccess?.call();
    }
  }

  /// ドラッグ商品情報の削除
  Future<void> delete(
    VoidCallback? onSuccess,
  ) async {
    final drugProductInfo = ref.watch(drugProductInfoStateProvider);
    if (drugProductInfo == null) {
      return;
    }
    try {
      if (drugProductInfo.nextExpirationDate == null) {
        throw UnknownException('削除対象データがありません。');
      }
      state = const AsyncValue.loading();
      //取得する
      state = await AsyncValue.guard(
        () => ref.read(checkExpiryDrugServiceProvider).deleteDrugProdInfo(
              drugProductInfo,
            ),
      );
    } catch (err, stack) {
      state = AsyncValue.error(err, stack);
    }

    if (!state.hasError) {
      onSuccess?.call();
    }
  }

  void _checknextExpiryDate() {
    final drugProductInfo = ref.watch(drugProductInfoStateProvider);
    final nextExpiryDateText = ref.watch(nextExpiryDateTextProvider);
    final shelfListInfo = ref.watch(drugShelfListInfoProvider).valueOrNull;
    if (drugProductInfo == null) {
      return;
    }

    final errorMeesage = getNextExpiryErrorMessage(
      nextExpiryDateText,
      jobDate: drugProductInfo.checkDate,
      limitDate: shelfListInfo?.limitDate,
    );

    // エラーメッセージがある場合はエラーをスローする
    if (errorMeesage != null) {
      throw UnknownException(errorMeesage);
    }
  }

  /// 入力に変更があったかどうか
  bool isChangeData() {
    final drugProductInfo = ref.read(drugProductInfoStateProvider);
    final isMid = ref.read(midFlgProvider);
    final isEnd = ref.read(endFlgProvider);
    final nextExpiryDateText = ref.read(nextExpiryDateTextProvider);
    final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(nextExpiryDateText);
    return !(drugProductInfo?.isEnd == isEnd &&
        drugProductInfo?.isMid == isMid &&
        drugProductInfo?.nextExpirationDate == nextExpiryDate);
  }
}

/// エンドボタンのチェックマークの状態
@riverpod
class EndFlg extends _$EndFlg {
  @override
  bool build() => false;

  void update({required bool isEnd}) {
    debugPrint('update isEnd');
    state = isEnd;
  }
}

/// ミッドボタンのチェックマークの状態
@riverpod
class MidFlg extends _$MidFlg {
  @override
  bool build() => false;

  void update({required bool isMid}) {
    debugPrint('update isMid');
    state = isMid;
  }
}

/// 次回の賞味期限の文字
@riverpod
class NextExpiryDateText extends _$NextExpiryDateText {
  @override
  String build() => '';

  void update(String text) {
    debugPrint('update nextExpiryDateText');
    state = text;
  }

  void clear() {
    state = '';
  }
}

// 次回の賞味期限が有効であるかのフラグ
@riverpod
class NextExpiryDateValid extends _$NextExpiryDateValid {
  @override
  bool build() {
    final dateText = ref.watch(nextExpiryDateTextProvider);

    return isValidNextExpiryDate(dateText);
  }
}
