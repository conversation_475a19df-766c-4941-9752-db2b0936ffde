import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/snack_bars.dart';
import '../../../../common_widgets/default_bottom_app_bar.dart';
import '../../../../common_widgets/outline_round_text_button.dart';
import '../maintenance_mid_state.dart';

/// フッターのボトムバー
class MaintenanceMidListBottomAppBar extends ConsumerWidget {
  /// constructor
  const MaintenanceMidListBottomAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isSelected = ref.watch(selectProductListProvider).isNotEmpty;

    return DefaultBottomAppBar(
      actions: [
        OutlineRoundTextButton(
          '削除',
          onPressed: isSelected ? () => _onDeletePressed(context, ref) : null,
        ),
      ],
    );
  }

  Future<void> _onDeletePressed(BuildContext context, WidgetRef ref) async {
    final ok = await showAlertDialog(
      context: context,
      title: 'この商品を削除してもよろしいですか？',
      cancelActionText: 'キャンセル',
      defaultActionText: '削除する',
    );
    if (ok ?? false) {
      await ref
          .watch(
            deleteMidProductListControllerProvider.notifier,
          )
          .deleteList(
            () => showSnackBar(context, '削除しました'),
          );
    }
  }
}
