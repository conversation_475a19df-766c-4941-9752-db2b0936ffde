import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../device/application/pacer_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../../domain/drug_history.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/default_bottom_app_bar.dart';
import '../../../common_widgets/drug_history_table.dart';
import '../../../common_widgets/product_info_table.dart';
import 'history_drug_state.dart';

/// ドラッグのメンテナンス履歴ページ
/// 参照系だけ
class HistoryDrugPage extends HookConsumerWidget {
  /// constructor
  const HistoryDrugPage({super.key});

  Future<void> _onScan(WidgetRef ref, String code) => ref.read(drugHistoryListControllerProvider.notifier).scan(code);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const title = 'ドラッグ 履歴確認';
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final drugHistoryList = ref.watch(drugHistoryListControllerProvider).valueOrNull;

    ref
      ..listen(
        drugHistoryListControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(scanCodeProvider, (_, code) {
        if (code case AsyncData(:final value)) {
          _onScan(ref, value);
        }
      });

    return Scaffold(
      appBar: const DefaultAppBar(title: title),
      bottomNavigationBar: const DefaultBottomAppBar(isNotched: true),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: ScanFloatingIconButton(onScan: (code) => _onScan(ref, code)),
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8).copyWith(top: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ProductInfoTable.fromDrugHistory(
                drugHistory: drugHistoryList != null && drugHistoryList.isNotEmpty ? drugHistoryList.first : null,
                onProductCodeChanged: (code) => _onScan(ref, code),
              ),
              const Gap(12),
              if (drugHistoryList != null)
                Text(
                  '履歴',
                  style: texts.labelLarge?.copyWith(color: colors.subText),
                ),
              const Gap(4),
              Expanded(child: _HistoryTableList(drugHistoryList)),
            ],
          ),
        ),
      ),
    );
  }
}

class _HistoryTableList extends StatelessWidget {
  const _HistoryTableList(this.drugHistoryList);

  final List<DrugHistory>? drugHistoryList;

  @override
  Widget build(BuildContext context) {
    final drugHistoryList = this.drugHistoryList;

    return switch (drugHistoryList) {
      null => const SizedBox.shrink(),
      [] => const _NoHistoryText(),
      _ => ListView.separated(
          itemBuilder: (BuildContext context, int index) {
            final history = drugHistoryList.elementAtOrNull(index);
            if (history == null) return const SizedBox.shrink();
            return DrugHistoryTable.fromDrugHistory(history);
          },
          separatorBuilder: (BuildContext context, int index) => const Gap(8),
          itemCount: drugHistoryList.length,
        ),
    };
  }
}

/// 「履歴はまだありません」
class _NoHistoryText extends StatelessWidget {
  const _NoHistoryText();

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.all(32),
      child: Center(
        child: Text(
          '履歴はまだありません',
          style: texts.titleMedium?.copyWith(color: colors.subText, fontSize: 18),
        ),
      ),
    );
  }
}
