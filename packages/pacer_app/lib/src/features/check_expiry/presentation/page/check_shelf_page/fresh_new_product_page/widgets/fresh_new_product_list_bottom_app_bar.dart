import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../../../../utils/async_value_ui.dart';
import '../../../../../domain/fresh_new_product.dart';
import '../../../../common_widgets/default_bottom_app_bar.dart';
import '../../../../common_widgets/outline_round_text_button.dart';
import '../../../../common_widgets/round_text_button.dart';
import '../../../../routing/expiry_route.dart';
import '../../../shelf_list_page/shelf_list_page_state.dart';
import '../fresh_new_product_state.dart';

/// フッターのボトムバー
class FreshNewProductListBottomAppBar extends ConsumerWidget {
  /// constructor
  const FreshNewProductListBottomAppBar(this.selectProductList, {super.key});

  /// 選択した商品リスト
  final List<FreshNewProduct> selectProductList;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isActionButtonActive = selectProductList.isNotEmpty;
    ref.listen(
      deleteFreshNewProductListControllerProvider,
      (_, state) => state.showAlertDialogOnError(context),
    );

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        await _onBackPressed(context: context, ref: ref);
      },
      child: DefaultBottomAppBar(
        leading: BackButton(
          onPressed: () => _onBackPressed(
            context: context,
            ref: ref,
          ),
        ),
        actions: [
          OutlineRoundTextButton(
            '削除',
            onPressed: switch (isActionButtonActive) {
              true => () async {
                  final ok = await showAlertDialog(
                    context: context,
                    title: 'この商品を削除してもよろしいですか？',
                    cancelActionText: 'キャンセル',
                    defaultActionText: '削除する',
                  );
                  if (ok ?? false) {
                    await ref
                        .watch(
                          deleteFreshNewProductListControllerProvider.notifier,
                        )
                        .deleteList();
                    if (!context.mounted) return;
                    //データ次のデータへ
                    showSnackBar(context, '削除しました');
                  }
                },
              false => null,
            },
          ),
          const SizedBox(width: 8),
          RoundTextButton(
            '設定',
            onPressed: switch (isActionButtonActive) {
              true => () => const ExpiryFreshNewProductRegisterRoute().go(context),
              false => null,
            },
          ),
        ],
      ),
    );
  }

  /// 戻るボタンの処理
  Future<bool> _onBackPressed({
    required BuildContext context,
    required WidgetRef ref,
  }) async {
    // 棚一覧を更新してあげる
    ref.invalidate(shelfWorkInfoListProvider);
    context.pop();

    return false;
  }
}
