// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../domain/shelf_work_info.dart';

part 'check_shelf_page_props_controller.g.dart';

@immutable
class CheckShelfPageProps {
  const CheckShelfPageProps({
    required this.zoneCode,
    required this.shelfInfo,
  });
  final int zoneCode;
  final ShelfWorkInfo shelfInfo;

  @override
  String toString() {
    return '''
CheckShelfPageProps
    zoneCode: $zoneCode,
    shelfInfo: $shelfInfo,
''';
  }
}

/// ページ間の値受け渡し
@riverpod
class CheckShelfPagePropsController extends _$CheckShelfPagePropsController {
  @override
  CheckShelfPageProps? build() {
    ref.onDispose(() {
      debugPrint('dispose checkDrugShelfPageProps');
    });
    return null;
  }

  void update({
    required int zoneCode,
    required ShelfWorkInfo shelfInfo,
  }) {
    debugPrint('update checkDrugShelfPageProps');
    state = CheckShelfPageProps(zoneCode: zoneCode, shelfInfo: shelfInfo);
  }
}
