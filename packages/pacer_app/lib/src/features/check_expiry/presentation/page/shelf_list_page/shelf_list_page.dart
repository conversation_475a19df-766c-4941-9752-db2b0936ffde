// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../themes/app_color_scheme.dart';
import '../../../../../utils/async_value_ui.dart';
import '../../../../authentication/data/auth_repository.dart';
import '../../../application/check_expiry_service.dart';
import '../../../domain/shelf_work_info.dart';
import '../../common_widgets/default_app_bar.dart';
import '../../common_widgets/default_bottom_app_bar.dart';
import '../../common_widgets/empty_data_text.dart';
import '../../common_widgets/shelf_list_tile.dart';
import '../../routing/expiry_route.dart';
import '../check_shelf_page/check_shelf_page_props_controller.dart';
import 'shelf_list_page_state.dart';

class ShelfListPage extends ConsumerWidget {
  const ShelfListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final zoneInfo = ref.watch(selectingZoneProvider);

    ref
      ..watch(checkShelfPagePropsControllerProvider)
      ..listen(
        updateFreshDCTShelfWorkControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        updateShelfWorkControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      );

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        await _onBackPressed(context: context, ref: ref);
      },
      child: Scaffold(
        appBar: const DefaultAppBar(),
        bottomNavigationBar: DefaultBottomAppBar(
          leading: BackButton(
            onPressed: () => _onBackPressed(context: context, ref: ref),
          ),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (zoneInfo != null) _Title(zoneInfo.zoneName),
                const Gap(24),
                const _ListItemName(),
                if (zoneInfo != null) _ShelfNoListView(zoneInfo.zoneCode),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 戻るボタンの処理
  Future<bool> _onBackPressed({
    required BuildContext context,
    required WidgetRef ref,
  }) async {
    // ゾーンを更新してあげる
    ref.invalidate(zoneListProvider);
    context.pop();

    return false;
  }
}

class _Title extends StatelessWidget {
  const _Title(this.zoneName);

  final String? zoneName;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (zoneName != null)
          Text(
            '$zoneNameの棚一覧',
            style: texts.titleMedium?.copyWith(
              color: colors.primary,
              fontSize: 18,
            ),
          ),
        Text(
          'チェックをする棚を選択してください',
          style: texts.titleMedium,
        ),
      ],
    );
  }
}

class _ListItemName extends StatelessWidget {
  const _ListItemName();

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Text(
            '棚番',
            style: texts.titleSmall?.copyWith(color: colors.secondary),
          ),
          const Gap(48),
          Text(
            '棚名称',
            style: texts.titleSmall?.copyWith(color: colors.secondary),
          ),
        ],
      ),
    );
  }
}

class _ShelfNoListView extends ConsumerWidget {
  const _ShelfNoListView(this.zoneCode);

  final int zoneCode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    final shelfNoList = ref.watch(shelfWorkInfoListProvider);

    return Expanded(
      child: switch (shelfNoList) {
        AsyncLoading() => const Center(child: CircularProgressIndicator()),
        AsyncError() => const SizedBox.shrink(),
        AsyncData(:final value) when value.isEmpty => const EmptyDataText(),
        AsyncData(:final value) => ListView.separated(
            itemCount: value.length,
            separatorBuilder: (BuildContext context, int index) {
              return Divider(height: 1, color: colors.sub);
            },
            itemBuilder: (BuildContext context, int index) {
              final shelfInfo = value[index];

              return Column(
                children: [
                  ShelfListTile.fromShelfInfo(
                    shelfInfo,
                    onPressed: () => _onShelfListTilePressed(
                      ref: ref,
                      context: context,
                      shelfInfo: shelfInfo,
                    ),
                  ),
                  if (index == value.length - 1) Divider(height: 1, color: colors.sub),
                ],
              );
            },
          ),
      },
    );
  }

  Future<void> _onShelfListTilePressed({
    required WidgetRef ref,
    required BuildContext context,
    required ShelfWorkInfo? shelfInfo,
  }) async {
    if (shelfInfo == null || shelfInfo.isDone) {
      return;
    }

    // 作業中確認ダイアログの表示
    final ok = await showWorkChangeDialog(ref, context, shelfInfo);
    if (!ok) {
      return;
    }

    // 値下げデータのとそれ以外の場合で作業者セットのAPIを切り替える
    await switch (shelfInfo.isDiscount) {
      true => ref.read(updateFreshDCTShelfWorkControllerProvider.notifier).updateInfo(zoneCode, shelfInfo),
      false => ref.read(updateShelfWorkControllerProvider.notifier).updateWork(zoneCode, shelfInfo),
    };

    if (!context.mounted) return;

    ref.read(checkShelfPagePropsControllerProvider.notifier).update(zoneCode: zoneCode, shelfInfo: shelfInfo);

    goExpiryCheckShelf(
      context: context,
      shelfType: shelfInfo.shelfType,
      shelfProductType: shelfInfo.shelfProductType,
    );
  }

  /// 作業中確認ダイアログの表示
  Future<bool> showWorkChangeDialog(
    WidgetRef ref,
    BuildContext context,
    ShelfWorkInfo shelfInfo,
  ) async {
    if (!shelfInfo.isWorking) {
      return true;
    }
    final workInfo = await ref.read(checkExpiryServiceProvider).fetchWorkmanager(zoneCode, shelfInfo);
    if (!context.mounted) return false;

    final user = ref.read(authRepositoryProvider).currentUser;
    if (workInfo.userCode != user?.userCode) {
      final ok = await showAlertDialog(
        context: ref.context,
        title: '${workInfo.userName}さんが作業中ですが、中止してあなたが作業をしますか？',
        cancelActionText: 'いいえ',
        defaultActionText: 'はい',
      );
      if (ok != true) {
        return false;
      }
    }

    return true;
  }
}
