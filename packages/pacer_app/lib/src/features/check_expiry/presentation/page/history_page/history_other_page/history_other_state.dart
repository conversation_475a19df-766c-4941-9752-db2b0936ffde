import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/check_expiry_service.dart';
import '../../../../domain/shelf_history.dart';
import '../../../../domain/shelf_list_info.dart';

part 'history_other_state.g.dart';

/// 棚一覧情報
@riverpod
class ShelfInfoList extends _$ShelfInfoList {
  @override
  FutureOr<List<ShelfInfo>> build() async {
    return ref.read(checkExpiryServiceProvider).fetchOtherMaintenanceShelfList();
  }

  /// 棚番号から棚情報を取得
  ShelfInfo? get(String shelfNumberText) {
    final shelfInfoList = state.valueOrNull;
    if (shelfInfoList == null) {
      return null;
    }

    return shelfInfoList.where((e) => e.shelfNumberText == shelfNumberText).firstOrNull;
  }
}

/// 棚履歴情報
@riverpod
class ShelfHistoryList extends _$ShelfHistoryList {
  @override
  FutureOr<ShelfHistory> build(String shelfNumberText) async {
    return ref.read(checkExpiryServiceProvider).fetchShelfHistory(shelfNumberText);
  }
}

/// 選択された棚番情報
@riverpod
class SelectShelfInfo extends _$SelectShelfInfo {
  @override
  ShelfInfo? build() => null;

  /// 更新
  void update(ShelfInfo? shelfInfo) {
    debugPrint('update selectShelfInfo');
    state = shelfInfo;
  }

  /// クリア
  void clear() {
    state = null;
  }
}
