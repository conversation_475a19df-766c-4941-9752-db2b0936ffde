// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'end_return_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$endReturnProductControllerHash() => r'66d53dc8dac96ef0936ac019cb80c7a7c775c3e2';

/// エンド戻し商品の情報取得の状態
///
/// Copied from [EndReturnProductController].
@ProviderFor(EndReturnProductController)
final endReturnProductControllerProvider =
    AutoDisposeAsyncNotifierProvider<EndReturnProductController, EndReturnProduct?>.internal(
  EndReturnProductController.new,
  name: r'endReturnProductControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$endReturnProductControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EndReturnProductController = AutoDisposeAsyncNotifier<EndReturnProduct?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
