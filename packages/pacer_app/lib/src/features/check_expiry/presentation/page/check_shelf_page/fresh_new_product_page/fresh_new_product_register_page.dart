// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/fresh_new_product.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/next_expiry_date_text_field.dart';
import '../../../common_widgets/regist_product_table.dart';
import '../check_shelf_page_props_controller.dart';
import 'fresh_new_product_state.dart';
import 'widgets/fresh_new_product_regist_bottom_app_bar.dart';

/// 生鮮新規商品登録ページ
class FreshNewProductRegisterPage extends HookConsumerWidget {
  const FreshNewProductRegisterPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectProductList = ref.watch(selectProductListProvider);
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final currentRegistProduct = ref.watch(currentRegistProductProvider);
    final focusNode = useFocusNode();
    final currentIndex = ref.watch(currentRegistProductProvider.notifier).getIndex();
    final shelfList = ref.watch(freshShelfListProvider).valueOrNull;
    final selectZoonShelfInfo = ref.watch(selectZoonShelfInfoProvider);
    final freshResistInfo = ref.watch(freshResistInfoProvider);

    return Focus(
      focusNode: focusNode,
      child: GestureDetector(
        onTap: focusNode.requestFocus,
        child: Scaffold(
          appBar: const DefaultAppBar(),
          bottomNavigationBar: const FreshNewProductRegistBottomAppBar(),
          body: switch (freshResistInfo) {
            AsyncData() => SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _Title(props?.shelfInfo.shelfName ?? ''),
                    const SizedBox(height: 12),
                    _SelectedProductText(
                      currentCount: currentIndex + 1,
                      maxCount: selectProductList.length,
                    ),
                    const SizedBox(height: 12),
                    if (currentRegistProduct != null)
                      RegistProductTable.freshNewProduct(
                        product: currentRegistProduct,
                        shelfInfoList: shelfList ?? [],
                        selectShelfInfo: selectZoonShelfInfo,
                        onShelfInfoChanged: ref.read(selectZoonShelfInfoProvider.notifier).update,
                      ),
                    const SizedBox(height: 12),
                    const _NextExpiryDateTextField(),
                    const SizedBox(height: 24),
                    _WorkProgress(
                      currentCount: currentIndex + 1,
                      maxCount: selectProductList.length,
                    ),
                    const SizedBox(height: 6),
                  ],
                ),
              ),
            AsyncError() => const SizedBox.shrink(),
            _ => const Center(child: CircularProgressIndicator())
          },
        ),
      ),
    );
  }

  void onCheckeChanged({
    required WidgetRef ref,
    required bool? isCheckd,
    required FreshNewProduct product,
  }) {
    if (isCheckd == null) {
      return;
    }
    isCheckd
        ? ref.read(selectProductListProvider.notifier).add(product)
        : ref.read(selectProductListProvider.notifier).remove(product);
  }
}

class _NextExpiryDateTextField extends HookConsumerWidget {
  const _NextExpiryDateTextField();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final text = ref.watch(nextExpiryDateTextProvider);
    final isValid = ref.watch(nextExpiryDateValidProvider);
    final currentRegistProduct = ref.watch(currentRegistProductProvider);
    final registInfo = ref.watch(freshResistInfoProvider);
    final oldNextExpirationDate = currentRegistProduct?.nextBestBeforeDate;

    return NextExpiryDateTextField(
      inputText: text,
      onChanged: ref.read(nextExpiryDateTextProvider.notifier).update,
      onFieldSubmitted: (text) async {
        final isShowDialog = await showNextExpiryErrorAlertDialog(
          context,
          text,
          jobDate: oldNextExpirationDate,
          limitDate: registInfo.valueOrNull?.limitDate,
        );
        if (isShowDialog) {
          ref.read(nextExpiryDateTextProvider.notifier).clear();
        }
      },
      isValid: isValid,
    );
  }
}

/// タイトル部分（ヘッダー）
class _Title extends StatelessWidget {
  const _Title(this.shelfName);

  final String shelfName;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          shelfName,
          style: texts.titleMedium?.copyWith(
            color: colors.primary,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          '棚番と次回賞味期限を入力してください',
          style: texts.titleMedium,
        ),
      ],
    );
  }
}

/// 対象商品（0/10）の表示
class _SelectedProductText extends StatelessWidget {
  const _SelectedProductText({
    required this.currentCount,
    required this.maxCount,
  });

  final int currentCount;
  final int maxCount;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Text(
      '対象商品 ($currentCount/$maxCount)',
      style: texts.labelLarge?.copyWith(color: colors.subText),
    );
  }
}

///下部の作業進捗
class _WorkProgress extends StatelessWidget {
  const _WorkProgress({
    required this.currentCount,
    required this.maxCount,
  });

  final int currentCount;
  final int maxCount;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Align(
      alignment: Alignment.centerRight,
      child: Text(
        '作業進捗 $currentCount/$maxCount',
        style: texts.titleMedium,
      ),
    );
  }
}
