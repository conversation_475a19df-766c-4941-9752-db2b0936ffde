import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../domain/product_check_info.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/deadline.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/next_expiry_date_text_field.dart';
import '../../../common_widgets/product_info_table.dart';
import '../check_shelf_page_props_controller.dart';
import 'check_mid_shelf_state.dart';
import 'widgets/bottom_app_bar.dart';

/// ミッドの棚チェックページ
class CheckMidShelfPage extends HookConsumerWidget {
  /// constructor
  const CheckMidShelfPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final focusNode = useFocusNode();

    final props = ref.watch(checkShelfPagePropsControllerProvider);
    final drugProductCheckInfo = ref.watch(checkMidShelfInfoProvider);

    ref.listen(
      checkMidShelfInfoProvider,
      (_, state) => state.showAlertDialogOnError(context),
    );

    return Focus(
      focusNode: focusNode,
      child: GestureDetector(
        onTap: focusNode.requestFocus,
        child: Scaffold(
          appBar: const DefaultAppBar(),
          bottomNavigationBar: const CheckMidShelfBottomAppBar(),
          body: SafeArea(
            child: switch (drugProductCheckInfo) {
              AsyncLoading() => const Center(child: CircularProgressIndicator()),
              AsyncError() => const SizedBox.shrink(),
              AsyncData(value: final value && ProductCheckInfo(:final productInfo)) => SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _Title(
                        numberText: props?.shelfInfo.shelfNumberText,
                        name: props?.shelfInfo.shelfName,
                      ),
                      const Gap(10),
                      Deadline(
                        limitDate: productInfo.expirationDate,
                        isNewproduct: productInfo.isNewproduct,
                      ),
                      const Gap(12),
                      _SelectedProductText(progress: productInfo.taskCount),
                      const Gap(4),
                      ProductInfoTable.fromProductCheckInfo(value),
                      const Gap(12),
                      const _NextExpiryDateTextField(),
                      const Gap(24),
                      _WorkProgress(progress: productInfo.taskCount),
                    ],
                  ),
                ),
            },
          ),
        ),
      ),
    );
  }
}

/// タイトル（番号 商品名）
class _Title extends StatelessWidget {
  const _Title({
    required this.numberText,
    required this.name,
  });

  final String? numberText;
  final String? name;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (numberText != null)
          Text(
            numberText ?? '',
            style: texts.titleMedium?.copyWith(color: colors.primary, fontSize: 18),
          ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            name ?? '',
            style: texts.titleMedium?.copyWith(color: colors.primary, fontSize: 18),
          ),
        ),
      ],
    );
  }
}

/// 対象商品（0/10）の表示
class _SelectedProductText extends StatelessWidget {
  const _SelectedProductText({
    required this.progress,
  });

  final String progress;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Text(
      '対象商品 $progress',
      style: texts.labelLarge?.copyWith(color: colors.subText),
    );
  }
}

class _NextExpiryDateTextField extends HookConsumerWidget {
  const _NextExpiryDateTextField();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final text = ref.watch(nextExpiryDateTextProvider);
    final isValid = ref.watch(nextExpiryDateValidProvider);
    final midProductCheckInfo = ref.watch(checkMidShelfInfoProvider).value;

    return NextExpiryDateTextField(
      inputText: text,
      onChanged: ref.read(nextExpiryDateTextProvider.notifier).update,
      onFieldSubmitted: (text) async {
        final isShowDialog = await showNextExpiryErrorAlertDialog(
          context,
          text,
          jobDate: midProductCheckInfo?.productInfo.jobDate,
          limitDate: midProductCheckInfo?.limitDate,
        );
        if (isShowDialog) {
          ref.read(nextExpiryDateTextProvider.notifier).clear();
        }
      },
      isValid: isValid,
    );
  }
}

///下部の作業進捗
class _WorkProgress extends StatelessWidget {
  const _WorkProgress({
    required this.progress,
  });

  final String progress;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Align(
      alignment: Alignment.centerRight,
      child: Text(
        '作業進捗 ${removeParentheses(progress)}',
        style: texts.titleMedium,
      ),
    );
  }

  String removeParentheses(String input) {
    // 括弧()を取り除く
    return input.replaceAll(RegExp('[()]'), '');
  }
}
