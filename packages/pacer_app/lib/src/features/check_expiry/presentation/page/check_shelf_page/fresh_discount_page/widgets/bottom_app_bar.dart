import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../../../../utils/async_value_ui.dart';
import '../../../../../../scrap/routing/scrap_route.dart';
import '../../../../common_widgets/default_bottom_app_bar.dart';
import '../../../../common_widgets/outline_round_text_button.dart';
import '../../../../common_widgets/round_text_button.dart';
import '../../../../common_widgets/work_stop_alert_dialog.dart';
import '../../../shelf_list_page/shelf_list_page_state.dart';
import '../fresh_discount_state.dart';

/// フッターのボトムバー
class FreshDiscountBottomAppBar extends ConsumerWidget {
  /// constructor
  const FreshDiscountBottomAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectProductList = ref.watch(selectProductListProvider);
    final freshDiscountProductList = ref.watch(freshDiscountProductListProvider).valueOrNull ?? [];
    final isActionButtonActive = selectProductList.isNotEmpty;
    final isAllSelect = selectProductList.length == freshDiscountProductList.length;

    ref
      ..listen(
        workStopShelfCheckControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        completeDiscountControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      );

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        await _onBackPressed(context: context, ref: ref);
      },
      child: DefaultBottomAppBar(
        leading: BackButton(
          onPressed: () => _onBackPressed(context: context, ref: ref),
        ),
        actions: [
          OutlineRoundTextButton(
            '廃棄',
            onPressed: () async {
              final ok = await showAlertDialog(
                context: ref.context,
                title: '廃棄しますか？',
                cancelActionText: 'いいえ',
                defaultActionText: 'はい',
              );
              if (ok ?? false) {
                if (!context.mounted) return;
                await const ScrapHomeRoute().push<void>(context);
              }
            },
          ),
          const SizedBox(width: 8),
          RoundTextButton(
            '完了',
            onPressed: switch (isActionButtonActive) {
              true => () => _onCompletePressed(
                    ref: ref,
                    context: context,
                    isAllSelect: isAllSelect,
                  ),
              false => null,
            },
          ),
        ],
      ),
    );
  }

  /// 戻るボタンの処理
  Future<bool> _onBackPressed({
    required BuildContext context,
    required WidgetRef ref,
  }) async {
    final workStopType = await showWorkStopAlertAlertDialog(
      context: ref.context,
    );

    //キャンセルなら何もしない
    if (workStopType == null) {
      return false;
    }

    await ref
        .read(
          workStopShelfCheckControllerProvider.notifier,
        )
        .workStop(
          workStopType: workStopType,
        );

    if (!context.mounted) return false;

    // 棚一覧を更新してあげる
    ref.invalidate(shelfWorkInfoListProvider);
    context.pop();

    return false;
  }

  /// 完了ボタンの処理
  Future<void> _onCompletePressed({
    required BuildContext context,
    required WidgetRef ref,
    required bool isAllSelect,
  }) async {
    final ok = await showAlertDialog(
      context: ref.context,
      title: '作業を完了し、商品のデータを削除してもよろしいですか？',
      cancelActionText: 'いいえ',
      defaultActionText: 'はい',
    );
    if (ok ?? false) {
      //完了処理
      await ref.read(completeDiscountControllerProvider.notifier).complete(
        () {
          showSnackBar(context, '完了しました。');
          if (isAllSelect) {
            context.pop();
            // 棚一覧を更新してあげる
            ref.invalidate(shelfWorkInfoListProvider);
          } else {
            ref.read(selectProductListProvider.notifier).clear();
            ref.read(freshDiscountProductListProvider.notifier).refresh();
          }
        },
      );
    }
  }
}
