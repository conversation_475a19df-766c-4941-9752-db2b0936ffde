// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fresh_discount_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freshDiscountProductListHash() => r'a34d4755475b7066fe05a3622beadf0d61def824';

/// 取得した商品リスト
///
/// Copied from [FreshDiscountProductList].
@ProviderFor(FreshDiscountProductList)
final freshDiscountProductListProvider =
    AutoDisposeAsyncNotifierProvider<FreshDiscountProductList, List<FreshDiscountProduct>>.internal(
  FreshDiscountProductList.new,
  name: r'freshDiscountProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshDiscountProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshDiscountProductList = AutoDisposeAsyncNotifier<List<FreshDiscountProduct>>;
String _$selectProductListHash() => r'e3b1c4eaf64eccaf1e844dc53ef0bbfa5a5faee0';

/// 選択された商品リスト
///
/// Copied from [SelectProductList].
@ProviderFor(SelectProductList)
final selectProductListProvider = AutoDisposeNotifierProvider<SelectProductList, List<FreshDiscountProduct>>.internal(
  SelectProductList.new,
  name: r'selectProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectProductList = AutoDisposeNotifier<List<FreshDiscountProduct>>;
String _$workStopShelfCheckControllerHash() => r'd15011e99ebbe5b4ebb0a27cf03f4bfbd11042f6';

///作業中断、一時停止の場合の処理
///
/// Copied from [WorkStopShelfCheckController].
@ProviderFor(WorkStopShelfCheckController)
final workStopShelfCheckControllerProvider =
    AutoDisposeAsyncNotifierProvider<WorkStopShelfCheckController, void>.internal(
  WorkStopShelfCheckController.new,
  name: r'workStopShelfCheckControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$workStopShelfCheckControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WorkStopShelfCheckController = AutoDisposeAsyncNotifier<void>;
String _$completeDiscountControllerHash() => r'1c569b71c5a3b0572a86db54883ae76b858d1261';

/// 値下げ完了処理
///
/// Copied from [CompleteDiscountController].
@ProviderFor(CompleteDiscountController)
final completeDiscountControllerProvider = AutoDisposeAsyncNotifierProvider<CompleteDiscountController, void>.internal(
  CompleteDiscountController.new,
  name: r'completeDiscountControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$completeDiscountControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CompleteDiscountController = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
