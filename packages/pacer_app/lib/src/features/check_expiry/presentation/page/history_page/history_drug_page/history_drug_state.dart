import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../application/check_expiry_drug_service.dart';
import '../../../../domain/drug_history.dart';
import '../../../../domain/product_code.dart';

part 'history_drug_state.g.dart';

/// ドラッグ履歴一覧取得の状態
@riverpod
class DrugHistoryListController extends _$DrugHistoryListController {
  @override
  FutureOr<List<DrugHistory>?> build() async {
    return null;
  }

  /// 一覧履歴の取得
  Future<void> scan(String janCode) async {
    if (janCode.isEmpty) {
      return;
    }
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final productCode = ProductCode.parseProductCode(janCode);
      if (productCode.isFreshProduct) {
        throw UnknownException('生鮮商品は登録できません。');
      }
      return ref.read(checkExpiryDrugServiceProvider).getDrugHistory(productCode: productCode.value);
    });
  }
}
