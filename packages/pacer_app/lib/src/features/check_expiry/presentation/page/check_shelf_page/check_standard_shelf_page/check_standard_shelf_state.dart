import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../../exceptions/app_exception.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/check_expiry_service.dart';
import '../../../../domain/check_shelf_info.dart';
import '../../../../domain/shelf_work_info.dart';
import '../../../../domain/special_product_summary.dart';
import '../../../common_widgets/work_stop_alert_dialog.dart';
import '../check_shelf_page_props_controller.dart';

part 'check_standard_shelf_state.g.dart';

/// 定番の棚情報
@riverpod
class CheckStandardShelfInfo extends _$CheckStandardShelfInfo {
  @override
  FutureOr<StandardCheckShelfInfo> build() async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);

    return ref.read(checkExpiryServiceProvider).fetchStandardShelfInfo(props?.zoneCode, props?.shelfInfo);
  }

  /// 棚チェック情報を更新する
  void updateCheckInfo(StandardCheckShelfInfo checkInfo) {
    state = AsyncData(checkInfo);
  }
}

/// 特殊商品一覧
@riverpod
class SpecialProductList extends _$SpecialProductList {
  @override
  FutureOr<List<SpecialProductSummary>> build() async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);

    return ref.read(checkExpiryServiceProvider).fetchSpecialProductList(
          props?.zoneCode,
          props?.shelfInfo.shelfNumberText,
        );
  }
}

///作業中断、一時停止の場合の処理
@riverpod
class WorkStopShelfCheckController extends _$WorkStopShelfCheckController {
  @override
  FutureOr<void> build() {}

  ///作業中断、一時停止の更新
  Future<void> workStop({
    required WorkStopType workStopType,
  }) async {
    final props = ref.watch(checkShelfPagePropsControllerProvider);
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () => ref.read(checkExpiryServiceProvider).workStopShelfCheck(
            zoneCode: props?.zoneCode,
            shelfNumberText: props?.shelfInfo.shelfNumberText,
            checkPlanWeek: props?.shelfInfo.checkPlanWeek,
            workStopType: workStopType,
          ),
    );
  }
}

///作業完了の場合の処理
@riverpod
class CompleteShelfCheckController extends _$CompleteShelfCheckController {
  @override
  FutureOr<void> build() {
    ref.showGlobalLoading();
  }

  ///完了
  Future<void> complete(
    String jan, {
    required VoidCallback onSuccess,
  }) async {
    final props = ref.read(checkShelfPagePropsControllerProvider);

    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      if (props case CheckShelfPageProps(shelfInfo: ShelfWorkInfo(shelfNumberText: final code)) when code != jan) {
        throw UnknownException('正しい棚番をスキャンしてください。');
      }
      return ref.read(checkExpiryServiceProvider).finishShelfWork(props?.zoneCode, props?.shelfInfo);
    });
    if (!state.hasError) {
      onSuccess();
    }
  }
}

/// スキャンが有効かどうか
@riverpod
class IsScanEnable extends _$IsScanEnable {
  @override
  bool build() => false;

  /// スキャンを有効にする
  void enableScan() {
    state = true;
  }

  /// スキャンを無効にする
  void disableScan() {
    state = false;
  }
}
