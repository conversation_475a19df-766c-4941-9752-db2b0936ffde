// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_mid_shelf_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkMidShelfInfoHash() => r'fb439f1a2394a1c2ce0810f591d1fac414955e31';

///ミッドの棚情報
///
/// Copied from [CheckMidShelfInfo].
@ProviderFor(CheckMidShelfInfo)
final checkMidShelfInfoProvider = AutoDisposeAsyncNotifierProvider<CheckMidShelfInfo, ProductCheckInfo>.internal(
  CheckMidShelfInfo.new,
  name: r'checkMidShelfInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkMidShelfInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckMidShelfInfo = AutoDisposeAsyncNotifier<ProductCheckInfo>;
String _$updateCheckMidShelfInfoControllerHash() => r'b6cdf66ffcf6e0e35f901eca11a49c2c927cb9b5';

///賞味期限チェックの完了処理
///
/// Copied from [UpdateCheckMidShelfInfoController].
@ProviderFor(UpdateCheckMidShelfInfoController)
final updateCheckMidShelfInfoControllerProvider =
    AutoDisposeAsyncNotifierProvider<UpdateCheckMidShelfInfoController, void>.internal(
  UpdateCheckMidShelfInfoController.new,
  name: r'updateCheckMidShelfInfoControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$updateCheckMidShelfInfoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateCheckMidShelfInfoController = AutoDisposeAsyncNotifier<void>;
String _$workStopShelfCheckControllerHash() => r'0de2c8723024bcbf6ccba1ecd9bc87bb09d308a8';

///作業中断、一時停止の場合の処理
///
/// Copied from [WorkStopShelfCheckController].
@ProviderFor(WorkStopShelfCheckController)
final workStopShelfCheckControllerProvider =
    AutoDisposeAsyncNotifierProvider<WorkStopShelfCheckController, void>.internal(
  WorkStopShelfCheckController.new,
  name: r'workStopShelfCheckControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$workStopShelfCheckControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WorkStopShelfCheckController = AutoDisposeAsyncNotifier<void>;
String _$deleteMidProductCheckInfoControllerHash() => r'd5ca7b2b891ddfecd6e4644de8932a89c52a6a82';

///賞味期限チェック削除用の処理
///
/// Copied from [DeleteMidProductCheckInfoController].
@ProviderFor(DeleteMidProductCheckInfoController)
final deleteMidProductCheckInfoControllerProvider =
    AutoDisposeAsyncNotifierProvider<DeleteMidProductCheckInfoController, void>.internal(
  DeleteMidProductCheckInfoController.new,
  name: r'deleteMidProductCheckInfoControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$deleteMidProductCheckInfoControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteMidProductCheckInfoController = AutoDisposeAsyncNotifier<void>;
String _$nextExpiryDateTextHash() => r'014707841960da0115e345febff23b89354860b0';

/// 次回の賞味期限の文字
///
/// Copied from [NextExpiryDateText].
@ProviderFor(NextExpiryDateText)
final nextExpiryDateTextProvider = AutoDisposeNotifierProvider<NextExpiryDateText, String>.internal(
  NextExpiryDateText.new,
  name: r'nextExpiryDateTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateText = AutoDisposeNotifier<String>;
String _$nextExpiryDateValidHash() => r'7d10dd7d4ab3c6ba95699f6ee6e652c4cb1b7b37';

/// 次回の賞味期限が有効であるかのフラグ
///
/// Copied from [NextExpiryDateValid].
@ProviderFor(NextExpiryDateValid)
final nextExpiryDateValidProvider = AutoDisposeNotifierProvider<NextExpiryDateValid, bool>.internal(
  NextExpiryDateValid.new,
  name: r'nextExpiryDateValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$nextExpiryDateValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NextExpiryDateValid = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
