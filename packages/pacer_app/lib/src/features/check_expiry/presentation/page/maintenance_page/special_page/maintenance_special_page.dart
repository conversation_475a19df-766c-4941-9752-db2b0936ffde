import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../device/application/pacer_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../../domain/check_cycle.dart';
import '../../../common_widgets/check_expiry_date_text_field.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/product_info_table.dart';
import '../../../common_widgets/special_maintenance_shelf_info_table.dart';
import 'widgets/bottom_app_bar.dart';
import 'widgets/maintenance_special_state.dart';

/// 特殊のメンテナンスページ
class MaintenanceSpecialPage extends HookConsumerWidget {
  /// constructor
  const MaintenanceSpecialPage({super.key});

  Future<void> _onScan(WidgetRef ref, String code) async {
    await ref.read(specialProductControllerProvider.notifier).scan(
      code,
      onRegisterSuccess: () {
        showSnackBar(ref.context, '登録しました。');
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final focusNode = useFocusNode();

    final specialProduct = ref.watch(specialProductStateProvider);

    ref
      ..listen(
        specialProductControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        checkCycleListProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(
        standardShelfListProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(scanCodeProvider, (_, code) {
        if (code case AsyncData(:final value)) {
          _onScan(ref, value);
        }
      })
      ..watch(standardShelfListProvider);

    return Focus(
      focusNode: focusNode,
      child: GestureDetector(
        onTap: focusNode.requestFocus,
        child: Scaffold(
          appBar: const DefaultAppBar(title: 'メンテナンス（特殊） 登録'),
          bottomNavigationBar: const MaintenanceSpecialBottomAppBar(),
          floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
          floatingActionButton: ScanFloatingIconButton(onScan: (code) => _onScan(ref, code)),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 24,
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ProductInfoTable.fromSpecialProduct(
                      specialProduct: specialProduct,
                      onProductCodeChanged: (code) => _onScan(ref, code),
                    ),
                    const Gap(8),
                    const SpecialMaintenanceShelfInfoTable(),
                    const Gap(12),
                    const _CheckFrequencyDropdown(),
                    const Gap(12),
                    const _CheckWeekTextField(),
                    const Gap(16),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// タイトル部分（ヘッダー）
class _CheckFrequencyDropdown extends HookConsumerWidget {
  const _CheckFrequencyDropdown();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final checkCycleList = ref.watch(checkCycleListProvider).valueOrNull;
    final selectCheckCycle = ref.watch(selectCheckCycleProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'チェック頻度',
          style: texts.labelLarge?.copyWith(color: colors.subText),
        ),
        ColoredBox(
          color: colors.button,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: DropdownButton<CheckCycle?>(
              underline: const SizedBox.shrink(),
              isExpanded: true,
              items: checkCycleList
                  ?.map(
                    (e) => DropdownMenuItem(
                      value: e,
                      child: Text(e.checkCycleName),
                    ),
                  )
                  .toList(),
              onChanged: ref.read(selectCheckCycleProvider.notifier).update,
              value: selectCheckCycle,
              hint: const Text('チェック頻度を選択してください'),
              menuMaxHeight: 200,
            ),
          ),
        ),
      ],
    );
  }
}

/// 初回チェック週のテキストフィールド
class _CheckWeekTextField extends HookConsumerWidget {
  const _CheckWeekTextField();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final text = ref.watch(checkWeekTextProvider);
    final isValid = ref.watch(checkWeekTextValidProvider);

    return CheckWeekTextField(
      inputText: text,
      onChanged: ref.read(checkWeekTextProvider.notifier).update,
      isValid: isValid,
    );
  }
}
