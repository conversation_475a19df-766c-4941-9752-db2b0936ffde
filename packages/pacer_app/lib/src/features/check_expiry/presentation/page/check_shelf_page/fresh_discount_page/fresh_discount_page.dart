import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/fresh_discount_product.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/discount_product_table.dart';
import '../../../common_widgets/empty_data_text.dart';
import '../check_shelf_page_props_controller.dart';
import 'fresh_discount_state.dart';
import 'widgets/bottom_app_bar.dart';

/// 生鮮新規商品一覧ページ
class FreshDiscountPage extends HookConsumerWidget {
  /// constructor
  const FreshDiscountPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final discountProductList = ref.watch(freshDiscountProductListProvider);
    final selectProductList = ref.watch(selectProductListProvider);
    final props = ref.watch(checkShelfPagePropsControllerProvider);

    return Scaffold(
      appBar: const DefaultAppBar(),
      bottomNavigationBar: const FreshDiscountBottomAppBar(),
      body: switch (discountProductList) {
        AsyncLoading() => const Center(child: CircularProgressIndicator()),
        AsyncError() => const SizedBox.shrink(),
        AsyncData(:final value) when value.isEmpty => const EmptyDataText(),
        AsyncData(:final value) => ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            itemCount: value.length,
            separatorBuilder: (BuildContext context, int index) {
              return const Gap(6);
            },
            itemBuilder: (BuildContext context, int index) {
              final product = value[index];
              final isCheckd = selectProductList.contains(product);

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (index == 0) ...[
                    const Gap(24),
                    _Title(props?.shelfInfo.shelfName ?? ''),
                    _ProductCountText(value.length),
                  ],
                  DiscountProductTable.discountNewProduct(
                    product: product,
                    isCheckd: isCheckd,
                    onChanged: ({bool? isCheckd}) => onCheckeChanged(
                      ref: ref,
                      isCheckd: isCheckd,
                      product: product,
                    ),
                  ),
                  if (index == value.length - 1) ...[
                    const Gap(24),
                  ],
                ],
              );
            },
          ),
      },
    );
  }

  /// チェックボックスの状態変更時
  void onCheckeChanged({
    required WidgetRef ref,
    required bool? isCheckd,
    required FreshDiscountProduct product,
  }) {
    if (isCheckd == null) {
      return;
    }
    isCheckd
        ? ref.read(selectProductListProvider.notifier).add(product)
        : ref.read(selectProductListProvider.notifier).remove(product);
  }
}

/// タイトル部分（ヘッダー）
class _Title extends StatelessWidget {
  const _Title(this.shelfName);

  final String shelfName;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          shelfName,
          style: texts.titleMedium?.copyWith(color: colors.primary, fontSize: 18),
        ),
        const Gap(6),
        Text('値下げ商品をチェックしてください', style: texts.titleMedium),
      ],
    );
  }
}

/// 対象商品（0）の表示
class _ProductCountText extends StatelessWidget {
  const _ProductCountText(this.count);

  final int count;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Text(
        '対象商品 ($count)',
        style: texts.labelLarge?.copyWith(color: colors.subText),
      ),
    );
  }
}
