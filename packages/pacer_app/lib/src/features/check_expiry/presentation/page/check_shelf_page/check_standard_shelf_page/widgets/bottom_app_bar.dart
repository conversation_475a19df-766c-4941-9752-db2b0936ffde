part of '../check_standard_shelf_page.dart';

/// フッターのボトムバー
class CheckStandardShelfBottomAppBar extends ConsumerWidget {
  /// contractor
  const CheckStandardShelfBottomAppBar({super.key, required this.onScan});

  /// スキャン時の処理
  // ignore: avoid_positional_boolean_parameters
  final void Function(WidgetRef, String, bool) onScan;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        await _onBackPressed(ref);
      },
      child: DefaultBottomAppBar(
        leading: BackButton(onPressed: () => _onBackPressed(ref)),
        actions: [
          RoundTextButton('完了', onPressed: () => _onCompletePressed(ref)),
        ],
      ),
    );
  }

  /// 戻るボタンの処理
  Future<bool> _onBackPressed(WidgetRef ref) async {
    final context = ref.context;
    final workStopType = await showWorkStopAlertAlertDialog(context: context);
    //キャンセルなら何もしない
    if (workStopType == null) return false;

    await ref.watch(workStopShelfCheckControllerProvider.notifier).workStop(workStopType: workStopType);

    if (!context.mounted) return false;

    // 棚一覧を更新してあげる
    ref.invalidate(shelfWorkInfoListProvider);
    context.pop();

    return false;
  }

  /// 次へボタンの処理
  Future<void> _onCompletePressed(WidgetRef ref) async {
    ref.read(isScanEnableProvider.notifier).enableScan();
    final ok = await showScanAlertDialog(
      ref: ref,
      title: '棚番のバーコードをスキャンしてください。',
      defaultActionText: '戻る',
    );
    if (ok ?? false) {
      ref.read(isScanEnableProvider.notifier).disableScan();
    }
  }

  /// 専用ダイアログ
  Future<bool?> showScanAlertDialog({
    required WidgetRef ref,
    required String title,
    String? content,
    String? cancelActionText,
    String defaultActionText = 'OK',
  }) async {
    return showDialog(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: content != null ? Text(content) : null,
        actions: [
          OutlinedButton(
            key: kDialogDefaultKey,
            child: Text(defaultActionText),
            onPressed: () => Navigator.of(context).pop(true),
          ),
          ScanFloatingIconButton(
            onScan: (code) => onScan(ref, code, ref.read(isScanEnableProvider)),
          ),
        ],
      ),
    );
  }
}
