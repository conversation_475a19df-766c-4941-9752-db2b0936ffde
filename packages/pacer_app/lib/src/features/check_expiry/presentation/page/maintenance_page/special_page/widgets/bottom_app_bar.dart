import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/snack_bars.dart';
import '../../../../common_widgets/default_bottom_app_bar.dart';
import '../../../../common_widgets/outline_round_text_button.dart';
import '../../../../common_widgets/round_text_button.dart';
import 'maintenance_special_state.dart';

/// フッターのボトムバー
class MaintenanceSpecialBottomAppBar extends ConsumerWidget {
  /// constructor
  const MaintenanceSpecialBottomAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDataExists = ref.watch(specialProductStateProvider) != null;

    return DefaultBottomAppBar(
      isNotched: true,
      height: 100,
      actions: [
        OutlineRoundTextButton(
          '取消',
          onPressed: () => _onResetPressed(context, ref),
        ),
        const SizedBox(width: 8),
        OutlineRoundTextButton(
          '削除',
          onPressed: isDataExists ? () => _onDeletePressed(context, ref) : null,
        ),
        const SizedBox(width: 8),
        RoundTextButton(
          '登録',
          onPressed: isDataExists ? () => _onRegisterPressed(context, ref) : null,
        ),
      ],
    );
  }

  void _reset(BuildContext context, WidgetRef ref) {
    //画面の初期化
    ref.read(specialProductStateProvider.notifier).clear();
  }

  Future<void> _onResetPressed(BuildContext context, WidgetRef ref) async {
    _reset(context, ref);
    showSnackBar(context, '入力内容を取り消しました');
  }

  Future<void> _onDeletePressed(BuildContext context, WidgetRef ref) async {
    final ok = await showAlertDialog(
      context: ref.context,
      title: '削除します。よろしいですか？',
      cancelActionText: 'キャンセル',
      defaultActionText: '削除する',
    );
    if (ok ?? false) {
      await ref.watch(specialProductControllerProvider.notifier).delete(
        () {
          _reset(context, ref);
          showSnackBar(context, '削除しました。');
        },
      );
    }
  }

  Future<void> _onRegisterPressed(BuildContext context, WidgetRef ref) async {
    final isShelfEmpty = ref.watch(specialShelfInfoListProvider).every((e) => e.zoneCode == null);
    if (isShelfEmpty) {
      await showAlertDialog(
        context: ref.context,
        title: '棚番を入力してください。',
      );
      return;
    }

    final isChangeData = ref.read(specialProductControllerProvider.notifier).isChangeData();
    if (!isChangeData) {
      await showAlertDialog(context: context, title: '変更内容がありません。');
      return;
    }

    // 選択中のチェック頻度
    final selectCheckCycle = ref.read(selectCheckCycleProvider)?.checkCycleNumber;

    if (selectCheckCycle == null) {
      await showAlertDialog(context: context, title: 'チェック頻度が選択されていません。');
      return;
    }

    // 商品階層別チェック頻度
    // 取得失敗時（API失敗時）は'0'で設定する
    final minCheckCycleList = await ref.read(fetchMinCheckCycleStateProvider.future);
    final minCheckCycle = int.tryParse(minCheckCycleList?.firstOrNull?.minCycle ?? '0') ?? 0;

    if (selectCheckCycle > minCheckCycle) {
      await showAlertDialog(
        context: context,
        title: '商品階層別チェック頻度より大きいサイクルは設定できません。',
      );
      return;
    }

    final ok = await showAlertDialog(
      context: ref.context,
      title: 'データを登録します。よろしいですか？',
      cancelActionText: 'キャンセル',
      defaultActionText: '登録する',
    );
    if (ok ?? false) {
      await ref.watch(specialProductControllerProvider.notifier).register(
        () {
          _reset(context, ref);
          showSnackBar(context, '登録しました。');
        },
      );
    }
  }
}
