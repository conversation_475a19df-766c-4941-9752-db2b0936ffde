// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_standard_shelf_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkStandardShelfInfoHash() => r'8affd0c3d161139720bcb3118c7df554c30bd4b8';

/// 定番の棚情報
///
/// Copied from [CheckStandardShelfInfo].
@ProviderFor(CheckStandardShelfInfo)
final checkStandardShelfInfoProvider =
    AutoDisposeAsyncNotifierProvider<CheckStandardShelfInfo, StandardCheckShelfInfo>.internal(
  CheckStandardShelfInfo.new,
  name: r'checkStandardShelfInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkStandardShelfInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckStandardShelfInfo = AutoDisposeAsyncNotifier<StandardCheckShelfInfo>;
String _$specialProductListHash() => r'd048a3199f4d9ba292da5ae01207f00e96661bb2';

/// 特殊商品一覧
///
/// Copied from [SpecialProductList].
@ProviderFor(SpecialProductList)
final specialProductListProvider =
    AutoDisposeAsyncNotifierProvider<SpecialProductList, List<SpecialProductSummary>>.internal(
  SpecialProductList.new,
  name: r'specialProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$specialProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SpecialProductList = AutoDisposeAsyncNotifier<List<SpecialProductSummary>>;
String _$workStopShelfCheckControllerHash() => r'0de2c8723024bcbf6ccba1ecd9bc87bb09d308a8';

///作業中断、一時停止の場合の処理
///
/// Copied from [WorkStopShelfCheckController].
@ProviderFor(WorkStopShelfCheckController)
final workStopShelfCheckControllerProvider =
    AutoDisposeAsyncNotifierProvider<WorkStopShelfCheckController, void>.internal(
  WorkStopShelfCheckController.new,
  name: r'workStopShelfCheckControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$workStopShelfCheckControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WorkStopShelfCheckController = AutoDisposeAsyncNotifier<void>;
String _$completeShelfCheckControllerHash() => r'83ff296fa8ef6060929233dd31785808c7022d39';

///作業完了の場合の処理
///
/// Copied from [CompleteShelfCheckController].
@ProviderFor(CompleteShelfCheckController)
final completeShelfCheckControllerProvider =
    AutoDisposeAsyncNotifierProvider<CompleteShelfCheckController, void>.internal(
  CompleteShelfCheckController.new,
  name: r'completeShelfCheckControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$completeShelfCheckControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CompleteShelfCheckController = AutoDisposeAsyncNotifier<void>;
String _$isScanEnableHash() => r'b0de3a836cfcaf3a216297bc6503b0703b64838b';

/// スキャンが有効かどうか
///
/// Copied from [IsScanEnable].
@ProviderFor(IsScanEnable)
final isScanEnableProvider = AutoDisposeNotifierProvider<IsScanEnable, bool>.internal(
  IsScanEnable.new,
  name: r'isScanEnableProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$isScanEnableHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$IsScanEnable = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
