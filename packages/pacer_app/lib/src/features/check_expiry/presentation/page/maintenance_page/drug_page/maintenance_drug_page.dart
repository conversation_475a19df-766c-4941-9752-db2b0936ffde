import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../device/application/pacer_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../common_widgets/check_box_button.dart';
import '../../../common_widgets/check_next_expiry_dialog.dart';
import '../../../common_widgets/default_app_bar.dart';
import '../../../common_widgets/drug_maintenance_shelf_info_table.dart';
import '../../../common_widgets/next_expiry_date_text_field.dart';
import '../../../common_widgets/product_info_table.dart';
import 'maintenance_drug_state.dart';
import 'widgets/bottom_app_bar.dart';

/// ドラッグのメンテナンスページ
class MaintenanceDrugPage extends HookConsumerWidget {
  /// init
  const MaintenanceDrugPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const title = 'ドラッグ 使用期限商品登録';

    final focusNode = useFocusNode();
    final drugProductInfo = ref.watch(drugProductInfoStateProvider);

    ref
      ..listen(
        drugProductInfoControllerProvider,
        (_, state) => state.showAlertDialogOnError(context),
      )
      ..listen(scanCodeProvider, (_, code) {
        if (code case AsyncData(:final value)) {
          _scan(ref, value);
        }
      })
      ..watch(drugShelfListInfoProvider);

    return Focus(
      focusNode: focusNode,
      child: GestureDetector(
        onTap: focusNode.requestFocus,
        child: Scaffold(
          appBar: const DefaultAppBar(title: title),
          bottomNavigationBar: const MaintenanceDrugBottomAppBar(),
          floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
          floatingActionButton: ScanFloatingIconButton(
            onScan: (jan) => _scan(ref, jan),
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ProductInfoTable.fromDrugProductInfo(
                    drugProductInfo: drugProductInfo,
                    onProductCodeChanged: (jan) => _scan(ref, jan),
                  ),
                  const Gap(8),
                  DrugMaintenanceShelfInfoTable(
                    shelfName1: drugProductInfo?.shelfName1,
                    shelfNumberText1: drugProductInfo?.shelfNumberText1,
                    shelfName2: drugProductInfo?.shelfName2,
                    shelfNumberText2: drugProductInfo?.shelfNumberText2,
                  ),
                  const Gap(12),
                  const _AlignmentCheck(),
                  const Gap(12),
                  const _NextExpiryDateTextField(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _scan(WidgetRef ref, String jan) {
    ref.watch(drugProductInfoControllerProvider.notifier).scan(
      jan,
      onRegisterSuccess: () {
        showSnackBar(ref.context, '登録しました。');
      },
    );
  }
}

// 定番棚以外の配置
class _AlignmentCheck extends HookConsumerWidget {
  const _AlignmentCheck();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final endFlgState = ref.watch(endFlgProvider);
    final midFlgState = ref.watch(midFlgProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '定番棚以外の配置',
          style: texts.titleSmall?.copyWith(color: colors.subText),
        ),
        const Gap(4),
        Row(
          children: [
            CheckBoxButton(
              'エンド',
              isCheckd: endFlgState,
              onChanged: ({bool? isCheckd}) => ref.read(endFlgProvider.notifier).update(isEnd: isCheckd ?? false),
            ),
            const Gap(8),
            CheckBoxButton(
              'ミッド',
              isCheckd: midFlgState,
              onChanged: ({bool? isCheckd}) => ref.read(midFlgProvider.notifier).update(isMid: isCheckd ?? false),
            ),
          ],
        ),
      ],
    );
  }
}

class _NextExpiryDateTextField extends HookConsumerWidget {
  const _NextExpiryDateTextField();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final text = ref.watch(nextExpiryDateTextProvider);
    final isValid = ref.watch(nextExpiryDateValidProvider);
    final drugProductInfo = ref.watch(drugProductInfoStateProvider);
    final shelfListInfo = ref.watch(drugShelfListInfoProvider).valueOrNull;

    return NextExpiryDateTextField(
      inputText: text,
      onChanged: ref.read(nextExpiryDateTextProvider.notifier).update,
      onFieldSubmitted: (text) async {
        final isShowDialog = await showNextExpiryErrorAlertDialog(
          context,
          text,
          jobDate: drugProductInfo?.checkDate,
          limitDate: shelfListInfo?.limitDate,
        );
        if (isShowDialog) {
          ref.read(nextExpiryDateTextProvider.notifier).clear();
        }
      },
      isValid: isValid,
    );
  }
}
