// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../application/check_expiry_fresh_service.dart';
import '../../../application/check_expiry_service.dart';
import '../../../domain/shelf_work_info.dart';
import '../../../domain/zone_info.dart';

part 'shelf_list_page_state.g.dart';

/// ページ間の値受け渡し用
@riverpod
class SelectingZone extends _$SelectingZone {
  @override
  Zone? build() => null;

  void update(Zone zoneInfo) {
    debugPrint('update shelfListPagePropsController');
    state = zoneInfo;
  }
}

///棚の作業情報
@riverpod
class ShelfWorkInfoList extends _$ShelfWorkInfoList {
  @override
  FutureOr<List<ShelfWorkInfo>> build() async {
    final props = ref.watch(selectingZoneProvider);

    return ref.read(checkExpiryServiceProvider).fetchShelfNoList(props?.zoneCode);
  }
}

///ゾーンリスト
@riverpod
class ZoneList extends _$ZoneList {
  @override
  FutureOr<List<Zone>> build() async {
    return ref.read(checkExpiryServiceProvider).fetchZoneList();
  }
}

/// 棚番設定画面、値下データをクリック時、作業中設定
@riverpod
class UpdateShelfWorkController extends _$UpdateShelfWorkController {
  @override
  FutureOr<void> build() {}

  Future<void> updateWork(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(
      () => ref.read(checkExpiryServiceProvider).setShelfWork(
            zoneCode,
            shelfInfo,
          ),
    );
  }
}

/// 棚番設定画面、値下データをクリック時、作業中設定
@riverpod
class UpdateFreshDCTShelfWorkController extends _$UpdateFreshDCTShelfWorkController {
  @override
  FutureOr<void> build() {}

  Future<void> updateInfo(
    int? zoneCode,
    ShelfWorkInfo? shelfInfo,
  ) async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(
      () => ref.read(checkExpiryFreshServiceProvider).setFreshDCTShelfWork(zoneCode, shelfInfo),
    );
  }
}
