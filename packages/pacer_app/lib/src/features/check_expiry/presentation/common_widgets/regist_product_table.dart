// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/fresh_new_product.dart';
import '../../domain/zoon_shelf_info.dart';

class RegistProductTable extends StatelessWidget {
  const RegistProductTable({
    super.key,
    required this.productCode,
    required this.productName,
    required this.shelfName,
    required this.brandName,
    required this.shelfNumberText,
    required this.specName,
    required this.shelfInfoList,
    required this.selectShelfInfo,
    required this.onShelfInfoChanged,
  });

  RegistProductTable.freshNewProduct({
    required FreshNewProduct product,
    required List<ZoonShelfInfo> shelfInfoList,
    required ZoonShelfInfo? selectShelfInfo,
    required void Function(ZoonShelfInfo?)? onShelfInfoChanged,
  }) : this(
          productCode: product.productCode.value,
          productName: product.productName,
          shelfName: product.shelfName,
          shelfNumberText: product.shelfNumberText,
          brandName: product.brandName,
          specName: product.specName,
          shelfInfoList: shelfInfoList,
          selectShelfInfo: selectShelfInfo,
          onShelfInfoChanged: onShelfInfoChanged,
          key: null,
        );

  final String productCode;
  final String productName;
  final String shelfName;
  final String shelfNumberText;
  final String brandName;
  final String specName;
  final List<ZoonShelfInfo> shelfInfoList;
  final ZoonShelfInfo? selectShelfInfo;
  final void Function(ZoonShelfInfo?)? onShelfInfoChanged;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3), // 1列目は全体の50%
        1: FractionColumnWidth(0.7), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(10),
        ),
      ),
      children: [
        TableRow(
          children: [
            const _ItemNameText('棚番'),
            ColoredBox(
              color: colors.button,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: DropdownButton<ZoonShelfInfo?>(
                  underline: const SizedBox.shrink(),
                  isExpanded: true,
                  items: shelfInfoList
                      .map(
                        (e) => DropdownMenuItem(
                          value: e,
                          child: Text(
                            '${e.shelfNumberText}  ${e.shelfName}',
                          ),
                        ),
                      )
                      .toList(),
                  onChanged: onShelfInfoChanged,
                  value: selectShelfInfo,
                  hint: const Text('棚番を選択してください'),
                ),
              ),
            ),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('棚名称'),
            _ValueText(shelfName),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('JAN'),
            _ValueText(productCode),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('商品名'),
            _ValueText(productName),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('規格'),
            _ValueText(specName),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('ブランド'),
            _ValueText(brandName),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
        ),
      ),
    );
  }
}
