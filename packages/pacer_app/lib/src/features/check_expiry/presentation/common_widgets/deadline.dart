// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

import 'package:intl/intl.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/enum/shelf_product_type.dart';

/// 見切り表示
class Deadline extends StatelessWidget {
  const Deadline({
    super.key,
    required this.limitDate,
    required this.isNewproduct,
    this.shelfProductType = ShelfProductType.other,
  });

  final DateTime? limitDate;
  final bool isNewproduct;
  final ShelfProductType shelfProductType;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final displayDate = limitDate;

    if (displayDate == null) {
      return const SizedBox.shrink();
    }

    if (isNewproduct) {
      return Text(
        '新規商品です',
        style: texts.titleLarge?.copyWith(fontSize: 18),
      );
    }

    return RichText(
      text: TextSpan(
        style: Theme.of(context).textTheme.bodyMedium,
        children: [
          TextSpan(
            text: DateFormat('yyyy年').format(displayDate),
            style: texts.titleSmall,
          ),
          WidgetSpan(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: colors.accent,
                borderRadius: const BorderRadius.all(Radius.circular(4)),
              ),
              child: Text(
                switch (shelfProductType) {
                  ShelfProductType.unspecified => '',
                  ShelfProductType.alcohol => DateFormat('MM月').format(displayDate),
                  ShelfProductType.rice => DateFormat('MM月dd日').format(displayDate),
                  ShelfProductType.other => DateFormat('MM月dd日').format(displayDate),
                },
              ),
            ),
          ),
          TextSpan(
            text: 'までの商品を見切ってください',
            style: texts.titleSmall,
          ),
        ],
      ),
    );
  }
}
