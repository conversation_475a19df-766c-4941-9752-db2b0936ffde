import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/check_week.dart';

/// 数値入力時の最大文字数
const int maxCheckWeekNumberLength = 6;

/// yyyy年ww週で変換された際の最大文字数
const int maxCheckWeekValidLength = 8;

/// 次回の賞味期限入力欄
class CheckWeekTextField extends HookConsumerWidget {
  /// constructor
  const CheckWeekTextField({
    super.key,
    required this.inputText,
    required this.onChanged,
    required this.isValid,
    this.title = '初回チェック週',
    this.hintText = '初回にチェックする週を入力してください',
    this.autofocus = false,
    this.onPressed,
    this.focusNode,
  });

  /// タイトル
  final String title;

  /// ヒントテキスト
  final String hintText;

  /// 入力テキスト
  final String inputText;

  /// 入力時のコールバック
  final void Function(String)? onChanged;

  /// ボタン押下時のコールバック
  final VoidCallback? onPressed;

  /// 入力値の正当性
  final bool isValid;

  /// オートフォーカス
  final bool autofocus;

  /// フォーカスノード
  final FocusNode? focusNode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textEditingController = useTextEditingController();
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    useEffect(
      () {
        textEditingController.text = inputText;

        return;
      },
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: texts.titleSmall?.copyWith(color: colors.subText),
        ),
        const SizedBox(height: 4),
        Focus(
          child: TextFormField(
            focusNode: focusNode,
            controller: textEditingController,
            keyboardType: TextInputType.number,
            maxLength: isValid ? maxCheckWeekNumberLength : maxCheckWeekValidLength,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            validator: (text) => validateInput(text: text, isValid: isValid),
            decoration: InputDecoration(
              filled: true,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: colors.primary.withOpacity(0.2),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: colors.primary.withOpacity(0.2),
                ),
              ),
              fillColor: colors.button,
              focusColor: colors.button,
              hintText: hintText,
              hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
              counterText: '',
            ),
            onChanged: onChanged,
            onTap: () => changeNumber(textEditingController),
            onFieldSubmitted: (text) => changeFormatText(textEditingController),
            autofocus: autofocus,
          ),
          onFocusChange: (hasFocus) {
            if (!hasFocus) {
              changeFormatText(textEditingController);
            }
          },
        ),
      ],
    );
  }

  /// 数値に変換する
  void changeNumber(TextEditingController controller) {
    onPressed?.call();
    controller.text = controller.text.replaceAll(RegExp(r'\D'), '');
  }

  /// 入力値を変換する
  void changeFormatText(TextEditingController controller) {
    controller.text = formatWeekText(controller.text);
    onChanged?.call(controller.text);
  }

  /// 入力値の正当性をチェックする
  String? validateInput({
    required String? text,
    required bool isValid,
  }) {
    if (text == '') {
      return null;
    }
    if (!isValid && (text == null || text.length != maxCheckWeekNumberLength || !isNumeric(text))) {
      return '6桁の数字で入力してください（例：202319）';
    }

    return null;
  }

  /// 数値かどうかを判定する
  bool isNumeric(String? value) {
    if (value == null || value.isEmpty) {
      return false;
    }

    return int.tryParse(value) != null;
  }
}
