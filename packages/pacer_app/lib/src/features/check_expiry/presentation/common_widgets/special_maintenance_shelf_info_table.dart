import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/alert_dialogs.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/adaptive_number_input_type.dart';
import '../page/maintenance_page/special_page/widgets/maintenance_special_state.dart';

/// 特殊メンテナンスの棚情報のテーブル
class SpecialMaintenanceShelfInfoTable extends HookConsumerWidget {
  /// constructor
  const SpecialMaintenanceShelfInfoTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final shelfNumberController1 = useTextEditingController();
    final shelfNumberController2 = useTextEditingController();
    final shelfNumberController3 = useTextEditingController();

    final specialShelfInfoList = ref.watch(specialShelfInfoListProvider);

    useEffect(
      () {
        shelfNumberController1.text = specialShelfInfoList.elementAtOrNull(0)?.shelfNumberText ?? '';
        shelfNumberController2.text = specialShelfInfoList.elementAtOrNull(1)?.shelfNumberText ?? '';
        shelfNumberController3.text = specialShelfInfoList.elementAtOrNull(2)?.shelfNumberText ?? '';

        return null;
      },
      [specialShelfInfoList],
    );

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.2), // 1列目は全体の20%
        1: FractionColumnWidth(0.265), // 2列目は全体の40%
        2: FractionColumnWidth(0.265), // 2列目は全体の40%
        3: FractionColumnWidth(0.265), // 2列目は全体の40%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        const TableRow(
          children: [
            _ItemNameText(''),
            _ValueText('棚番1'),
            _ValueText('棚番2'),
            _ValueText('棚番3'),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('棚番'),
            _ValueTextField(
              textEditingController: shelfNumberController1,
              onChanged: (text) => ref
                  .read(specialShelfInfoListProvider.notifier)
                  .updateShelfNumberText(index: 0, shelfNumberText: text),
              initialValue: specialShelfInfoList.elementAtOrNull(0)?.shelfNumberText ?? '',
            ),
            _ValueTextField(
              textEditingController: shelfNumberController2,
              onChanged: (text) => ref
                  .read(specialShelfInfoListProvider.notifier)
                  .updateShelfNumberText(index: 1, shelfNumberText: text),
              initialValue: specialShelfInfoList.elementAtOrNull(1)?.shelfNumberText ?? '',
            ),
            _ValueTextField(
              textEditingController: shelfNumberController3,
              onChanged: (text) => ref
                  .read(specialShelfInfoListProvider.notifier)
                  .updateShelfNumberText(index: 2, shelfNumberText: text),
              initialValue: specialShelfInfoList.elementAtOrNull(2)?.shelfNumberText ?? '',
            ),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('棚名称'),
            _ValueText(
              specialShelfInfoList.elementAtOrNull(0)?.shelfName ?? '',
            ),
            _ValueText(
              specialShelfInfoList.elementAtOrNull(1)?.shelfName ?? '',
            ),
            _ValueText(
              specialShelfInfoList.elementAtOrNull(2)?.shelfName ?? '',
            ),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('ゾーン'),
            _ValueText(specialShelfInfoList.elementAtOrNull(0)?.zoneName ?? ''),
            _ValueText(specialShelfInfoList.elementAtOrNull(1)?.zoneName ?? ''),
            _ValueText(specialShelfInfoList.elementAtOrNull(2)?.zoneName ?? ''),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(
    this.text,
  );

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.titleMedium,
      ),
    );
  }
}

class _ValueTextField extends HookConsumerWidget {
  const _ValueTextField({
    required this.textEditingController,
    required this.onChanged,
    required this.initialValue,
  });

  final TextEditingController textEditingController;
  final void Function(String)? onChanged;
  final String initialValue;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useEffect(
      () {
        textEditingController.text = initialValue;

        return;
      },
      [],
    );

    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    return Focus(
      child: TextFormField(
        controller: textEditingController,
        keyboardType: TextInputType.number.withEnter(),
        autovalidateMode: AutovalidateMode.onUserInteraction,
        decoration: InputDecoration(
          filled: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 4),
          fillColor: colors.button,
          focusColor: colors.button,
          hintText: '棚番を入力',
          hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
          counterText: '',
          border: InputBorder.none,
        ),
      ),
      onFocusChange: (hasFocus) {
        if (!hasFocus) {
          _onChanged(
            controller: textEditingController,
            ref: ref,
            context: context,
            initialValue: initialValue,
          );
        }
      },
    );
  }

  Future<void> _onChanged({
    required TextEditingController controller,
    required WidgetRef ref,
    required BuildContext context,
    required String initialValue,
  }) async {
    final shelfNumberText = controller.text;
    final isExist = ref.read(standardShelfListProvider.notifier).isExist(shelfNumberText);
    if (shelfNumberText.isEmpty) {
      textEditingController.text = initialValue;

      return;
    }

    if (!isExist) {
      textEditingController.text = initialValue;
      await showAlertDialog(
        context: context,
        title: '存在しない棚番です。正しい番号を入力してください。',
      );

      return;
    }
    onChanged?.call(shelfNumberText);
  }
}
