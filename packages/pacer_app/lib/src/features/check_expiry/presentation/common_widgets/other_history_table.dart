import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/enum/shelf_type.dart';
import '../../domain/shelf_history.dart';

/// 生鮮とミッドの履歴テーブル
class OtherHistoryTable extends StatelessWidget {
  /// constructor
  const OtherHistoryTable({
    super.key,
    this.productCode = '',
    this.productName = '',
    this.checkPlanWeek = '',
    this.specName = '',
    this.brandName = '',
    this.checkWeek = '',
    this.author = '',
    this.startDate,
    this.shelfType = ShelfType.standard,
  });

  /// constructor
  OtherHistoryTable.fromHistoryProduct(
    HistoryProduct historyProduct,
  ) : this(
          productCode: historyProduct.productCode.value,
          productName: historyProduct.productName,
          specName: historyProduct.specName,
          brandName: historyProduct.brandName,
          checkPlanWeek: historyProduct.checkPlanWeek.toString(),
          checkWeek: historyProduct.checkWeek,
          startDate: historyProduct.startDate,
          author: '${historyProduct.author} ${historyProduct.employeeName}',
          shelfType: historyProduct.shelfType,
          key: null,
        );

  /// 品番
  final String productCode;

  /// 品番
  final String productName;

  /// 規格名
  final String specName;

  /// ブランド名
  final String brandName;

  /// 指示週
  final String checkPlanWeek;

  /// 実施週
  final String checkWeek;

  /// 実施日時
  final DateTime? startDate;

  /// 実施社員
  final String author;

  /// 棚の種別
  final ShelfType shelfType;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final implementationDate = startDate;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.25), // 1列目は全体の50%
        1: FractionColumnWidth(0.75), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        if (shelfType != ShelfType.standard && productCode.isNotEmpty) ...[
          TableRow(
            children: [
              const _ItemNameText('JAN'),
              _ValueText(productCode),
            ],
          ),
          TableRow(
            children: [
              const _ItemNameText('商品名'),
              _ValueText(productName),
            ],
          ),
          TableRow(
            children: [
              const _ItemNameText('規格'),
              _ValueText(specName),
            ],
          ),
          TableRow(
            children: [
              const _ItemNameText('ブランド'),
              _ValueText(brandName),
            ],
          ),
        ],
        TableRow(
          children: [
            const _ItemNameText('指示週'),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _ValueText(checkPlanWeek),
                const _VerticalDivider(),
                const _ItemNameText('実施週'),
                const _VerticalDivider(),
                _ValueText(checkWeek),
              ],
            ),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('実施日時'),
            _ValueText(
              implementationDate != null ? DateFormat('yyyy-MM-dd HH:mm').format(implementationDate) : '',
            ),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('実施社員'),
            _ValueText(author),
          ],
        ),
        if (shelfType == ShelfType.drug)
          const TableRow(
            children: [
              _ItemNameText('備考'),
              _ValueText('特殊あり'),
            ],
          ),
      ],
    );
  }
}

class _VerticalDivider extends StatelessWidget {
  const _VerticalDivider();
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return SizedBox(
      height: 35,
      width: 1,
      child: VerticalDivider(
        color: colors.line,
      ),
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.titleMedium,
        textAlign: TextAlign.right,
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(
    this.text,
  );

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.titleMedium,
      ),
    );
  }
}
