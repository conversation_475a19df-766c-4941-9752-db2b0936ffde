// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

class CheckBoxButton extends StatelessWidget {
  const CheckBoxButton(
    this.text, {
    this.buttonColor,
    this.textColor,
    this.boarderColor,
    this.width,
    this.height,
    super.key,
    this.fontSize = 16,
    this.onChanged,
    this.isCheckd = false,
  });

  final String text;
  final Color? buttonColor;
  final Color? textColor;
  final Color? boarderColor;
  final double? width;
  final double? height;
  final double? fontSize;
  final void Function({bool? isCheckd})? onChanged;
  final bool isCheckd;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return SizedBox(
      width: width,
      height: height,
      child: OutlinedButton(
        onPressed: () => {
          onChanged?.call(isCheckd: !isCheckd),
        },
        style: OutlinedButton.styleFrom(
          backgroundColor: buttonColor ?? colors.primary.withOpacity(0.05),
          side: BorderSide(
            color: boarderColor ?? colors.primary.withOpacity(0.2),
          ),
          shape: const StadiumBorder(),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: Checkbox(
                value: isCheckd,
                onChanged: (value) => onChanged?.call(isCheckd: value),
              ),
            ),
            const SizedBox(width: 4),
            Text(
              text,
              style: texts.titleMedium?.copyWith(
                fontSize: fontSize,
                color: textColor ?? colors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
