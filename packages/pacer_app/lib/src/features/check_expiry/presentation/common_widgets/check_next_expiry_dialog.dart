import 'package:clock/clock.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';

import '../../../../common_widgets/alert_dialogs.dart';
import '../../domain/next_expiry_date.dart';

/// 次回賞味期限のエラーダイアログを表示する
Future<bool> showNextExpiryErrorAlertDialog(
  BuildContext context,
  String text, {
  required DateTime? jobDate,
  required DateTime? limitDate,
}) async {
  // 空文字の場合はエラーとしない
  if (text.isEmpty) {
    return false;
  }
  final errorMessage = getNextExpiryErrorMessage(
    text,
    jobDate: jobDate,
    limitDate: limitDate,
  );
  if (errorMessage != null) {
    await showAlertDialog(context: context, title: errorMessage);
    return true;
  }
  return false;
}

/// 次回賞味期限のエラーメッセージを取得する
String? getNextExpiryErrorMessage(
  String nextExpiryDateText, {
  DateTime? jobDate,
  DateTime? limitDate,
  bool isCheckDiscard = true, //見切り対象エラー表示するかどうか
  bool isTodayPast = true, //今日を過去の日付にするかどうか
}) {
  final formatText = formatNextExpiryDate(nextExpiryDateText);
  final isValid = isValidNextExpiryDate(formatText);
  // 入力不正エラー
  if (!isValid) {
    return '賞味期限入力が不正です。8桁の数字を入力してください。';
  }
  final nextExpiryDate = DateFormat('yyyy年MM月dd日').parse(formatText);
  // isTodayPastで比較する日付を変更する
  final now = clock.now().add(Duration(days: isTodayPast ? 1 : 0));
  final isExpired = nextExpiryDate.isBefore(DateTime(now.year, now.month, now.day));

  // 過去の日付は入力不可エラー
  if (isExpired) {
    return '過去の日付は入力できません。';
  }

  // 今回の見切り対象の場合は入力不可エラー
  final isDiscard = jobDate != null && (nextExpiryDate.isBefore(jobDate) || nextExpiryDate.isAtSameMomentAs(jobDate));
  if (isCheckDiscard && isDiscard) {
    return '入力された賞味期限は今回の見切り対象です。';
  }

  final isExceedingMax = limitDate != null && nextExpiryDate.isAfter(limitDate);
  if (isExceedingMax) {
    return '入力された賞味期限日付は大きすぎます。';
  }

  return null;
}
