// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

enum WorkStopType {
  abort('作業中止'),
  pause('作業一時停止');

  const WorkStopType(this.title);

  final String title;
}

class WorkStopAlertDialog extends HookConsumerWidget {
  const WorkStopAlertDialog(this.selectedTypeNotifier, {super.key});

  final ValueNotifier<WorkStopType?> selectedTypeNotifier;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;

    return SizedBox(
      height: 100,
      child: Column(
        children: WorkStopType.values
            .map(
              (backType) => InkWell(
                onTap: () => selectedTypeNotifier.value = backType,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      backType.title,
                      style: texts.bodyLarge,
                    ),
                    ValueListenableBuilder<WorkStopType?>(
                      valueListenable: selectedTypeNotifier,
                      builder: (context, value, child) {
                        return Radio<WorkStopType>(
                          activeColor: Colors.blue,
                          value: backType,
                          groupValue: selectedTypeNotifier.value,
                          onChanged: (newValue) => selectedTypeNotifier.value = newValue,
                        );
                      },
                    ),
                  ],
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}

/// ダイアログを表示する
Future<WorkStopType?> showWorkStopAlertAlertDialog({
  required BuildContext context,
}) async {
  final texts = Theme.of(context).textTheme;
  final selectedTypeNotifier = ValueNotifier<WorkStopType?>(WorkStopType.abort);

  return showDialog(
    barrierDismissible: false,
    context: context,
    builder: (context) => AlertDialog(
      title: Text(
        '棚選択に戻る場合は、作業中止または作業一時停止を選択してください。',
        style: texts.titleLarge?.copyWith(fontSize: 18),
      ),
      content: WorkStopAlertDialog(selectedTypeNotifier),
      actions: <Widget>[
        OutlinedButton(
          child: const Text('キャンセル'),
          onPressed: () => Navigator.of(context).pop(),
        ),
        OutlinedButton(
          child: const Text('決定する'),
          onPressed: () => Navigator.of(context).pop(selectedTypeNotifier.value),
        ),
      ],
    ),
  );
}
