import 'package:flutter/material.dart';

import '../../../../../gen/assets.gen.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../domain/shelf_work_info.dart';

/// 棚一覧のリストタイル
class ShelfListTile extends StatelessWidget {
  /// constructor
  const ShelfListTile({
    super.key,
    required this.shelfName,
    required this.shelfNumberText,
    required this.isDone,
    required this.isWorking,
    required this.isExcess,
    required this.isSpecial,
    required this.isDiscount,
    this.onPressed,
  });

  /// constructor
  ShelfListTile.fromShelfInfo(
    ShelfWorkInfo shelfInfo, {
    VoidCallback? onPressed,
  }) : this(
          shelfName: shelfInfo.shelfName,
          shelfNumberText: shelfInfo.shelfNumberText,
          isDone: shelfInfo.isDone,
          isWorking: shelfInfo.isWorking,
          isExcess: shelfInfo.isExcess,
          isSpecial: shelfInfo.isSpecial,
          isDiscount: shelfInfo.isDiscount,
          onPressed: onPressed,
          key: null,
        );

  /// 棚名称
  final String shelfName;

  /// 棚番号
  final String shelfNumberText;

  /// 完了しているかどうか
  final bool isDone;

  /// 作業中かどうか
  final bool isWorking;

  /// 超過しているかどうか
  final bool isExcess;

  /// 特殊商品かどうか
  final bool isSpecial;

  /// 値下げかどうか
  final bool isDiscount;

  /// タップ時のコールバック
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final tileColor = isDone ? colors.successContainer : Colors.transparent;

    return InkWell(
      onTap: onPressed,
      child: ColoredBox(
        color: tileColor,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16).copyWith(left: 18, right: 8),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    switch ((isWorking: isWorking, isSpecial: isSpecial)) {
                      (isWorking: true, isSpecial: false) => Text(
                          '作業中',
                          style: texts.bodySmall?.copyWith(color: colors.error),
                        ),
                      (isWorking: false, isSpecial: true) => Text(
                          '特殊あり',
                          style: texts.bodySmall?.copyWith(color: colors.primary),
                        ),
                      (isWorking: true, isSpecial: true) => RichText(
                          text: TextSpan(
                            style: texts.bodySmall?.copyWith(color: colors.onSurface),
                            children: [
                              TextSpan(
                                text: '作業中',
                                style: texts.bodySmall?.copyWith(color: colors.error),
                              ),
                              const TextSpan(text: ' ・ '),
                              TextSpan(
                                text: '特殊あり',
                                style: texts.bodySmall?.copyWith(color: colors.primary),
                              ),
                            ],
                          ),
                        ),
                      _ => const SizedBox.shrink()
                    },
                    Row(
                      children: [
                        Text(
                          shelfNumberText,
                          style: texts.titleMedium,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            shelfName,
                            style: texts.titleMedium,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (isDone)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Assets.commonIcon.iconCheckCircle.image(width: 24),
                ),
              if (isExcess)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Assets.commonIcon.iconStatusWarning.image(width: 24),
                ),
              if (isDiscount)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Assets.commonIcon.iconStatusPrice.image(width: 24),
                ),
              const SizedBox(width: 8),
              const Icon(Icons.arrow_right, size: 24),
            ],
          ),
        ),
      ),
    );
  }
}
