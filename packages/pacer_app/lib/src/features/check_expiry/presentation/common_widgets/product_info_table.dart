// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/drug_history.dart';
import '../../domain/drug_product_info.dart';
import '../../domain/end_return_product.dart';
import '../../domain/product_check_info.dart';
import '../../domain/special_product.dart';
import 'scan_table_field.dart';

class ProductInfoTable extends StatelessWidget {
  const ProductInfoTable({
    super.key,
    this.productCode = '',
    this.productName = '',
    this.specName = '',
    this.brandName = '',
    this.stock,
    this.onProductCodeChanged,
  });

  ProductInfoTable.fromProductCheckInfo(
    ProductCheckInfo productCheckInfo,
  ) : this(
          productCode: productCheckInfo.productInfo.productCode.value,
          productName: productCheckInfo.productInfo.productName,
          specName: productCheckInfo.productInfo.specName,
          brandName: productCheckInfo.productInfo.brandName,
          stock: productCheckInfo.inventoryInfo?.stock,
          onProductCodeChanged: null,
          key: null,
        );

  ProductInfoTable.fromDrugProductInfo({
    required DrugProductInfo? drugProductInfo,
    required void Function(String) onProductCodeChanged,
  }) : this(
          productCode: drugProductInfo?.productCode.value ?? '',
          productName: drugProductInfo?.productName ?? '',
          specName: drugProductInfo?.specName ?? '',
          brandName: drugProductInfo?.brandName ?? '',
          stock: drugProductInfo?.inventoryInfo.stock,
          onProductCodeChanged: onProductCodeChanged,
          key: null,
        );

  ProductInfoTable.fromDrugHistory({
    required DrugHistory? drugHistory,
    required void Function(String) onProductCodeChanged,
  }) : this(
          productCode: drugHistory?.productCode.value ?? '',
          productName: drugHistory?.productName ?? '',
          specName: drugHistory?.specName ?? '',
          brandName: drugHistory?.brandName ?? '',
          stock: drugHistory?.inventoryInfo.stock,
          onProductCodeChanged: onProductCodeChanged,
          key: null,
        );

  ProductInfoTable.fromEndReturnProduct({
    required EndReturnProduct? endReturnProduct,
    required void Function(String) onProductCodeChanged,
  }) : this(
          productCode: endReturnProduct?.productCode.value ?? '',
          productName: endReturnProduct?.productName ?? '',
          specName: endReturnProduct?.specName ?? '',
          brandName: endReturnProduct?.brandName ?? '',
          stock: endReturnProduct?.stock,
          onProductCodeChanged: onProductCodeChanged,
          key: null,
        );

  ProductInfoTable.fromSpecialProduct({
    required SpecialProduct? specialProduct,
    required void Function(String) onProductCodeChanged,
  }) : this(
          productCode: specialProduct?.productCode.value ?? '',
          productName: specialProduct?.productName ?? '',
          specName: specialProduct?.specName ?? '',
          brandName: specialProduct?.brandName ?? '',
          stock: specialProduct?.stock,
          onProductCodeChanged: onProductCodeChanged,
          key: null,
        );

  final String productCode;
  final String productName;
  final String specName;
  final String brandName;
  final int? stock;
  final void Function(String)? onProductCodeChanged;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.33),
        1: FractionColumnWidth(0.67),
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        if (onProductCodeChanged != null)
          TableRow(
            children: [
              const _ItemNameText('JAN'),
              ScanTableField(
                productCode: productCode,
                onProductCodeChanged: onProductCodeChanged,
              ),
            ],
          )
        else
          TableRow(
            children: [
              const _ItemNameText('JAN'),
              _ValueText(productCode),
            ],
          ),
        TableRow(
          children: [
            const _ItemNameText('商品名'),
            _ValueText(productName),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('規格'),
            _ValueText(specName),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('ブランド'),
            _ValueText(brandName),
          ],
        ),
        TableRow(
          children: [
            _ItemNameText(
              '論理在庫(単品)',
              style: texts.titleSmall,
            ),
            _ValueText(stock != null ? '$stock個' : ''),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text, {this.style});

  final String text;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: style ?? texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(
    this.text,
  );

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.titleMedium,
      ),
    );
  }
}
