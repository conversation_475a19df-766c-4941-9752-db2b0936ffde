import 'package:flutter/material.dart';

import '../../../../themes/app_color_scheme.dart';

/// ドラッグメンテナンスの棚情報のテーブル
class DrugMaintenanceShelfInfoTable extends StatelessWidget {
  /// constructor
  const DrugMaintenanceShelfInfoTable({
    super.key,
    this.shelfNumberText1,
    this.shelfNumberText2,
    this.shelfName1,
    this.shelfName2,
  });

  /// 棚番号1
  final String? shelfNumberText1;

  /// 棚番号2
  final String? shelfNumberText2;

  /// 棚名称1
  final String? shelfName1;

  /// 棚名称2
  final String? shelfName2;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.2), // 1列目は全体の20%
        1: FractionColumnWidth(0.4), // 2列目は全体の40%
        2: FractionColumnWidth(0.4), // 2列目は全体の40%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        const TableRow(
          children: [
            _ItemNameText(''),
            _ValueText('棚番1'),
            _ValueText('棚番2'),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('棚番'),
            _ValueText(shelfNumberText1 ?? ''),
            _ValueText(shelfNumberText2 ?? ''),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('棚名称'),
            _ValueText(shelfName1 ?? ''),
            _ValueText(shelfName2 ?? ''),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.titleMedium,
        textAlign: TextAlign.right,
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(
    this.text,
  );

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.titleMedium,
      ),
    );
  }
}
