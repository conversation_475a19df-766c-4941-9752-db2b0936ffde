import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/adaptive_number_input_type.dart';

///テーブル用のスキャンフィールド
class ScanTableField extends HookWidget {
  /// constructor
  const ScanTableField({
    super.key,
    required this.productCode,
    required this.onProductCodeChanged,
  });

  /// 商品コード
  final String productCode;

  /// 商品コードが変更されたときのコールバック
  final void Function(String)? onProductCodeChanged;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;
    final textEditingController = TextEditingController();

    useEffect(
      () {
        textEditingController.text = productCode;

        return;
      },
    );

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: ColoredBox(
        color: colors.button,
        child: TextFormField(
          onTapOutside: (_) => FocusManager.instance.primaryFocus?.unfocus(),
          controller: textEditingController,
          keyboardType: TextInputType.number.withEnter(),
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            filled: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 4),
            fillColor: colors.button,
            focusColor: colors.button,
            hintText: 'JANをスキャンしてください',
            hintStyle: texts.bodyMedium?.copyWith(color: colors.subText).copyWith(fontSize: 13),
            counterText: '',
            border: InputBorder.none,
          ),
          onFieldSubmitted: onProductCodeChanged,
        ),
      ),
    );
  }
}
