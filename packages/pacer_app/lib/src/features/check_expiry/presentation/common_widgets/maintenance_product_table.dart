import 'package:flutter/material.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/fresh_maintenance_product.dart';
import '../../domain/mid_maintenance_product.dart';
import '../../domain/shelf_list_info.dart';
import 'scan_table_field.dart';

/// メンテナンスで表示するテーブル
class MaintenanceProductTable extends StatelessWidget {
  /// constructor
  const MaintenanceProductTable({
    super.key,
    required this.onProductCodeChanged,
    required this.productCode,
    required this.productName,
    required this.shelfName,
    required this.brandName,
    required this.shelfNumberText,
    required this.specName,
    required this.shelfInfoList,
    required this.selectShelfInfo,
    required this.onShelfInfoChanged,
    required this.stock,
  });

  /// constructor
  MaintenanceProductTable.freshMaintenanceProduct({
    required FreshMaintenanceProduct? product,
    required List<ShelfInfo> shelfInfoList,
    required ShelfInfo? selectShelfInfo,
    required void Function(ShelfInfo?)? onShelfInfoChanged,
    required void Function(String) onProductCodeChanged,
  }) : this(
          productCode: product?.productCode.value ?? '',
          productName: product?.productName ?? '',
          shelfName: selectShelfInfo?.shelfName ?? '',
          shelfNumberText: selectShelfInfo?.shelfNumberText ?? '',
          brandName: product?.brandName ?? '',
          specName: product?.specName ?? '',
          shelfInfoList: shelfInfoList,
          selectShelfInfo: selectShelfInfo,
          onShelfInfoChanged: onShelfInfoChanged,
          stock: null, //生鮮は在庫が取得できないためnull
          key: null,
          onProductCodeChanged: onProductCodeChanged,
        );

  /// constructor
  MaintenanceProductTable.midMaintenanceProduct({
    required MidMaintenanceProduct? product,
    required List<ShelfInfo> shelfInfoList,
    required ShelfInfo? selectShelfInfo,
    required void Function(ShelfInfo?)? onShelfInfoChanged,
    required void Function(String) onProductCodeChanged,
  }) : this(
          productCode: product?.productCode.value ?? '',
          productName: product?.productName ?? '',
          shelfName: selectShelfInfo?.shelfName ?? '',
          shelfNumberText: selectShelfInfo?.shelfNumberText ?? '',
          brandName: product?.brandName ?? '',
          specName: product?.specName ?? '',
          shelfInfoList: shelfInfoList,
          selectShelfInfo: selectShelfInfo,
          onShelfInfoChanged: onShelfInfoChanged,
          stock: product?.stock,
          onProductCodeChanged: onProductCodeChanged,
          key: null,
        );

  /// 商品コード
  final String productCode;

  /// 商品名
  final String productName;

  /// 棚名称
  final String shelfName;

  /// 棚番号
  final String shelfNumberText;

  /// ブランド名
  final String brandName;

  /// 規格
  final String specName;

  /// 棚番一覧
  final List<ShelfInfo> shelfInfoList;

  /// 選択された棚番
  final ShelfInfo? selectShelfInfo;

  /// 棚番変更イベント
  final void Function(ShelfInfo?)? onShelfInfoChanged;

  /// 在庫数
  final int? stock;

  /// 商品コードが変更されたときのコールバック
  final void Function(String) onProductCodeChanged;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.33),
        1: FractionColumnWidth(0.67),
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(10),
        ),
      ),
      children: [
        TableRow(
          children: [
            const _ItemNameText('棚番'),
            ColoredBox(
              color: colors.button,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: DropdownButton<ShelfInfo?>(
                  underline: const SizedBox.shrink(),
                  isExpanded: true,
                  items: shelfInfoList
                      .map(
                        (e) => DropdownMenuItem(
                          value: e,
                          child: Text(
                            '${e.shelfNumberText}  ${e.shelfName}',
                          ),
                        ),
                      )
                      .toList(),
                  onChanged: onShelfInfoChanged,
                  value: selectShelfInfo,
                  hint: Text(
                    '棚番を選択してください',
                    style: texts.bodyMedium,
                  ),
                ),
              ),
            ),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('棚名称'),
            _ValueText(shelfName),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('JAN'),
            ScanTableField(
              productCode: productCode,
              onProductCodeChanged: onProductCodeChanged,
            ),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('商品名'),
            _ValueText(productName),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('規格'),
            _ValueText(specName),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('ブランド'),
            _ValueText(brandName),
          ],
        ),
        TableRow(
          children: [
            _ItemNameText(
              '論理在庫(単品)',
              style: texts.titleSmall,
            ),
            _ValueText(stock != null ? '$stock個' : ''),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text, {this.style});

  final String text;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: style ?? texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
        ),
      ),
    );
  }
}
