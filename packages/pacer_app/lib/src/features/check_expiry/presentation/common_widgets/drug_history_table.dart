import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/drug_history.dart';

/// ドラッグの履歴テーブル
class DrugHistoryTable extends StatelessWidget {
  /// constructor
  const DrugHistoryTable({
    super.key,
    this.shelfNumberText = '',
    this.checkPlanWeek = '',
    this.checkWeek = '',
    this.author = '',
    this.startDate,
  });

  /// constructor
  DrugHistoryTable.fromDrugHistory(
    DrugHistory drugHistory,
  ) : this(
          shelfNumberText: drugHistory.shelfNumberText,
          checkPlanWeek: drugHistory.checkPlanWeek,
          checkWeek: drugHistory.checkWeek,
          startDate: drugHistory.startDate,
          author: '${drugHistory.author} ${drugHistory.employeeName}',
          key: null,
        );

  /// 棚番
  final String shelfNumberText;

  /// 指示週
  final String checkPlanWeek;

  /// 実施週
  final String checkWeek;

  /// 実施日時
  final DateTime? startDate;

  /// 実施社員
  final String author;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final implementationDate = startDate;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.25), // 1列目は全体の50%
        1: FractionColumnWidth(0.75), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        TableRow(
          children: [
            const _ItemNameText('棚番'),
            _ValueText(shelfNumberText),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('指示週'),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _ValueText(checkPlanWeek),
                const _VerticalDivider(),
                const _ItemNameText('実施週'),
                const _VerticalDivider(),
                _ValueText(checkWeek),
              ],
            ),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('実施日時'),
            _ValueText(
              implementationDate != null ? DateFormat('yyyy-MM-dd HH:mm').format(implementationDate) : '',
            ),
          ],
        ),
        TableRow(
          children: [
            const _ItemNameText('実施社員'),
            _ValueText(author),
          ],
        ),
      ],
    );
  }
}

class _VerticalDivider extends StatelessWidget {
  const _VerticalDivider();
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return SizedBox(
      height: 35,
      width: 1,
      child: VerticalDivider(
        color: colors.line,
      ),
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.titleMedium,
        textAlign: TextAlign.right,
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(
    this.text,
  );

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        text,
        style: texts.titleMedium,
      ),
    );
  }
}
