// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

/// 賞味期限チェックのAppBar
class DefaultAppBar extends StatelessWidget implements PreferredSizeWidget {
  const DefaultAppBar({
    super.key,
    this.title,
  });

  final String? title;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return AppBar(
      backgroundColor: colors.primary,
      title: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          title ?? '賞味期限チェック',
          style: texts.titleMedium?.copyWith(color: colors.onPrimary),
        ),
      ),
      centerTitle: true,
      automaticallyImplyLeading: false,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(35);
}
