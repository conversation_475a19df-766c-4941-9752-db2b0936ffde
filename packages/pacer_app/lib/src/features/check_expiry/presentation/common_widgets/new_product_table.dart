import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/fresh_new_product.dart';

/// 新規登録一覧で表示するテーブル
class NewProductTable extends StatelessWidget {
  /// constructor
  const NewProductTable({
    super.key,
    required this.productCode,
    required this.productName,
    required this.shelfName,
    required this.brandName,
    required this.shelfNumberText,
    required this.nextBestBeforeDate,
    required this.specName,
    required this.onChanged,
    required this.isCheckd,
    required this.stock,
  });

  /// constructor
  NewProductTable.freshNewProduct({
    required FreshNewProduct product,
    required void Function({bool? isCheckd})? onChanged,
    required bool isCheckd,
  }) : this(
          productCode: product.productCode.value,
          productName: product.productName,
          shelfName: product.shelfName,
          shelfNumberText: product.shelfNumberText,
          brandName: product.brandName,
          nextBestBeforeDate: product.nextBestBeforeDate,
          specName: product.specName,
          onChanged: onChanged,
          isCheckd: isCheckd,
          stock: null,
          key: null,
        );

  /// 商品コード
  final String productCode;

  /// 商品名
  final String productName;

  /// 棚名称
  final String shelfName;

  /// 棚番号
  final String shelfNumberText;

  /// ブランド名
  final String brandName;

  /// 規格
  final String specName;

  /// 次回賞味期限
  final DateTime? nextBestBeforeDate;

  /// 在庫数
  final int? stock;

  /// チェックボックスの変更イベント
  final void Function({bool? isCheckd})? onChanged;

  /// チェック状態
  final bool isCheckd;
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final date = nextBestBeforeDate;
    final nextBestBeforeDateText = date != null ? DateFormat('yyyy年MM月dd日').format(date) : '';
    final texts = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: isCheckd ? colors.button : null,
        border: Border.fromBorderSide(BorderSide(color: colors.line)),
        borderRadius: const BorderRadius.all(
          Radius.circular(10),
        ),
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            InkWell(
              onTap: () => onChanged?.call(isCheckd: !isCheckd),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Checkbox(
                    value: isCheckd,
                    onChanged: (value) => onChanged?.call(isCheckd: value),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Table(
                columnWidths: const {
                  0: FractionColumnWidth(0.33),
                  1: FractionColumnWidth(0.67),
                },
                border: TableBorder.all(
                  color: colors.line,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(10),
                    bottomRight: Radius.circular(10),
                  ),
                ),
                children: [
                  TableRow(
                    children: [
                      const _ItemNameText('棚番'),
                      _ValueText(
                        shelfNumberText == '0' ? '' : shelfNumberText,
                      ),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('棚名称'),
                      _ValueText(shelfName),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('JAN'),
                      _ValueText(productCode),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('商品名'),
                      _ValueText(productName),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('規格'),
                      _ValueText(specName),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('ブランド'),
                      _ValueText(brandName),
                    ],
                  ),
                  TableRow(
                    children: [
                      _ItemNameText(
                        '論理在庫(単品)',
                        style: texts.titleSmall,
                      ),
                      _ValueText(stock != null ? '$stock個' : ''),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('次回賞味期限'),
                      _ValueText(nextBestBeforeDateText),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text, {this.style});

  final String text;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: style ?? texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
        ),
      ),
    );
  }
}
