import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/fresh_maintenance_list_product.dart';
import '../../domain/mid_maintenance_list_product.dart';

/// メンテナンスの一覧で表示するテーブル
class MaintenanceProductListTable extends StatelessWidget {
  /// constructor
  const MaintenanceProductListTable({
    super.key,
    required this.productCode,
    required this.productName,
    required this.brandName,
    required this.nextExpirationDate,
    required this.specName,
    required this.onChanged,
    required this.isCheckd,
    required this.stock,
  });

  /// constructor
  MaintenanceProductListTable.freshMaintenanceListProduct({
    required FreshMaintenanceListProduct product,
    required void Function({bool? isCheckd})? onChanged,
    required bool isCheckd,
  }) : this(
          productCode: product.productCode.value,
          productName: product.productName,
          brandName: product.brandName,
          nextExpirationDate: product.nextExpirationDate,
          specName: product.specName,
          onChanged: onChanged,
          isCheckd: isCheckd,
          stock: null,
          key: null,
        );

  /// constructor
  MaintenanceProductListTable.midMaintenanceListProduct({
    required MidMaintenanceListProduct product,
    required void Function({bool? isCheckd})? onChanged,
    required bool isCheckd,
  }) : this(
          productCode: product.productCode.value,
          productName: product.productName,
          brandName: product.brandName,
          nextExpirationDate: product.nextExpirationDate,
          specName: product.specName,
          onChanged: onChanged,
          isCheckd: isCheckd,
          stock: product.stockQuantity,
          key: null,
        );

  /// 商品コード
  final String productCode;

  /// 商品名
  final String productName;

  /// ブランド名
  final String brandName;

  /// 規格
  final String specName;

  /// 次回賞味期限日
  final DateTime? nextExpirationDate;

  /// チェックボックスの変更イベント
  final void Function({bool? isCheckd})? onChanged;

  /// チェック状態
  final bool isCheckd;

  /// 在庫数
  final int? stock;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final date = nextExpirationDate;
    final nextExpirationDateText = date != null ? DateFormat('yyyy年MM月dd日').format(date) : '';
    final texts = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: isCheckd ? colors.button : null,
        border: Border.fromBorderSide(BorderSide(color: colors.line)),
        borderRadius: const BorderRadius.all(
          Radius.circular(10),
        ),
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            InkWell(
              onTap: () => onChanged?.call(isCheckd: !isCheckd),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Checkbox(
                    value: isCheckd,
                    onChanged: (value) => onChanged?.call(isCheckd: value),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Table(
                columnWidths: const {
                  0: FractionColumnWidth(0.33),
                  1: FractionColumnWidth(0.67),
                },
                border: TableBorder.all(
                  color: colors.line,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(10),
                    bottomRight: Radius.circular(10),
                  ),
                ),
                children: [
                  TableRow(
                    children: [
                      const _ItemNameText('JAN'),
                      _ValueText(productCode),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('商品名'),
                      _ValueText(productName),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('規格'),
                      _ValueText(specName),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('ブランド'),
                      _ValueText(brandName),
                    ],
                  ),
                  TableRow(
                    children: [
                      _ItemNameText('論理在庫(単品)', style: texts.titleSmall),
                      _ValueText(stock != null ? '$stock個' : ''),
                    ],
                  ),
                  TableRow(
                    children: [
                      const _ItemNameText('次回\n賞味期限'),
                      _ValueText(nextExpirationDateText),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text, {this.style});

  final String text;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: style ?? texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
        ),
      ),
    );
  }
}
