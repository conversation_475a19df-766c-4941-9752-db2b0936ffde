import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/next_expiry_date.dart';

/// 数値入力時の最大文字数
const int maxNextExpiryDateNumberLength = 8;

/// yyyy年mm月dd日変換された際の最大文字数
const int maxNextExpiryDateValidLength = 11;

/// 次回の賞味期限入力欄
class NextExpiryDateTextField extends HookConsumerWidget {
  /// constructor
  const NextExpiryDateTextField({
    super.key,
    required this.inputText,
    required this.onChanged,
    this.onFieldSubmitted,
    required this.isValid,
    this.title = '次回の賞味期限',
    this.hintText = '次回賞味期限の日付を入力してください',
    this.autofocus = false,
    this.onPressed,
    this.focusNode,
  });

  /// タイトル
  final String title;

  /// ヒントテキスト
  final String hintText;

  /// 入力テキスト
  final String inputText;

  /// 入力時のコールバック
  final void Function(String)? onChanged;

  /// 入力完了時のコールバック
  final void Function(String)? onFieldSubmitted;

  /// ボタン押下時のコールバック
  final VoidCallback? onPressed;

  /// 入力値の正当性
  final bool isValid;

  /// オートフォーカス
  final bool autofocus;

  /// フォーカスノード
  final FocusNode? focusNode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textEditingController = useTextEditingController();
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    useEffect(
      () {
        textEditingController.text = inputText;

        return;
      },
      [inputText],
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: texts.titleSmall?.copyWith(color: colors.subText),
        ),
        const SizedBox(height: 4),
        Focus(
          child: TextFormField(
            focusNode: focusNode,
            controller: textEditingController,
            keyboardType: TextInputType.number,
            maxLength: isValid ? maxNextExpiryDateValidLength : maxNextExpiryDateNumberLength,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            validator: (text) => validateInput(text: text, isValid: isValid),
            decoration: InputDecoration(
              filled: true,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: colors.primary.withOpacity(0.2),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: colors.primary.withOpacity(0.2),
                ),
              ),
              fillColor: colors.button,
              focusColor: colors.button,
              hintText: hintText,
              hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
              counterText: '',
            ),
            onChanged: onChanged,
            onTap: () => changeNumber(textEditingController),
            onFieldSubmitted: (text) => changeFormatText(textEditingController),
            autofocus: autofocus,
          ),
          onFocusChange: (hasFocus) {
            if (!hasFocus) {
              onFieldSubmitted?.call(textEditingController.text);
              changeFormatText(textEditingController);
            }
          },
        ),
      ],
    );
  }

  /// 数値に変換する
  void changeNumber(TextEditingController controller) {
    onPressed?.call();
    controller.text = controller.text.replaceAll(RegExp(r'\D'), '');
  }

  /// 入力値を変換する
  void changeFormatText(TextEditingController controller) {
    controller.text = formatNextExpiryDate(controller.text);
    onChanged?.call(controller.text);
  }

  /// 入力値のバリデーション
  String? validateInput({
    required String? text,
    required bool isValid,
  }) {
    if (text == '') {
      return null;
    }
    if (!isValid && (text == null || text.length != maxNextExpiryDateNumberLength || !isNumeric(text))) {
      return '8桁の数字で入力してください（例：20231231）';
    }

    return null;
  }

  /// 数値判定
  bool isNumeric(String? value) {
    if (value == null || value.isEmpty) {
      return false;
    }

    return int.tryParse(value) != null;
  }
}
