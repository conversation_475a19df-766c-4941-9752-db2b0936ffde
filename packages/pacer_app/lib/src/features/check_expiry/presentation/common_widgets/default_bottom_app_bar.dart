import 'package:flutter/material.dart';

/// 賞味期限チェックのBottomAppBar
class DefaultBottomAppBar extends StatelessWidget {
  /// constructor
  const DefaultBottomAppBar({
    super.key,
    this.leading,
    this.height,
    this.actions = const [],
    this.isNotched = false,
  });

  /// 左のWidget（終了、戻るなど）
  final Widget? leading;

  /// 右アクションのWidget
  final List<Widget> actions;

  /// 高さ
  final double? height;

  /// FIB埋め込みのノッチの有無
  final bool isNotched;

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      shape: isNotched ? const CircularNotchedRectangle() : null,
      notchMargin: 8,
      height: height,
      padding: const EdgeInsets.only(left: 12, right: 9, bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          leading ?? const BackButton(),
          const Spacer(),
          ...actions,
        ],
      ),
    );
  }
}
