// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

class RoundTextButton extends StatelessWidget {
  const RoundTextButton(
    this.text, {
    this.buttonColor,
    this.textColor,
    this.width,
    this.height,
    super.key,
    this.fontSize = 16,
    this.onPressed,
  });

  final String text;
  final Color? buttonColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final double? fontSize;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          disabledBackgroundColor: colors.outline.withOpacity(0.3),
          foregroundColor: textColor ?? colors.onPrimary,
          backgroundColor: buttonColor ?? colors.primary,
          textStyle: texts.titleMedium?.copyWith(
            fontSize: fontSize,
            color: onPressed != null ? textColor ?? colors.onPrimary : colors.outline,
          ),
        ),
        child: Text(text),
      ),
    );
  }
}
