// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

import '../../../../themes/app_color_scheme.dart';

class EmptyDataText extends StatelessWidget {
  const EmptyDataText({
    super.key,
    this.title = 'データがありません',
  });

  final String title;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    return Center(
      child: Text(
        title,
        style: texts.titleMedium?.copyWith(
          color: colors.subText,
        ),
      ),
    );
  }
}
