// ignore_for_file: public_member_api_docs

/// 在庫修正
const String kJAN = 'JAN';
const String kStockAdjustTitle = '在庫修正';
const String kEditReason = '修正理由';
const String kBox = 'ケース';
const String kPackage = 'パック';
const String kSingleGoods = '単品';
const String kMultiplication = 'x';
const String kEqual = '=';
const String kTodayReserveNumber = '本日納品予定数';
const String kTheoryNumber = '論理在庫数';
const String kReallyNumber = '実際在庫数';
const String kFinish = '終了';
const String kOverview = '一覧';
const String kPleaseSelect = '選択してください';

/// 在庫修正・一覧
const String kListTitle = '商品情報一覧';
const String kGoodsName = '商品名';
const String kType = '種類';
const String kDelete = '削除';
const String kModify = '修正';
const String kConfirm = '一括確定';
const String kNo = 'いいえ';
const String kYes = 'はい';
const String kClose = '閉じる';
const String kOk = 'ok';

const String kPleaseInputCode = 'バーコードをスキャンまたは入力してください。';
const String kGetGoodsError = '商品情報を取得する際にエラーが発生しました。';
const String kInvalidStockNumber = '無効な実際在庫数量。';
const String kAdjustStockWarning = '在庫数を大幅に変更しようとしています。続けますか？';
const String kAreYouSureExitApp = 'アプリケーションを終了します。よろしいでしょうか?';
const String kPleaseSelectReason = '在庫修正理由を選択してください。';
const String kInputCorrectGoodsCode = '正しい商品を入力してください。';
const String kRequestError = '取得に失敗しました';
const String kDeleteSelectItem = '指定されている行を削除します。よろしいですか？';
const String kItHasBeenDelete = '削除しました。';
const String kNoDeleteItem = '削除対象データがありません。';
const String kDeleting = '削除中...';
const String kUnableDeleteError = '削除できません、何らかのエラーが発生しました。';
const String kPleaseSelectItem = '商品情報を選択してください。';
const String kConfirmAllGoodsAtOnce = '全部の商品を一括確定します。よろしいでしょうか？';
const String kConfirmAllGoodsSuccess = '一括確定処理成功しました。';
const String kSubmitting = '一括確定中...';
const String kUnknownReason = '未知の理由';
const String kExitAppWithoutConfirmData = 'まだ登録しないでアプリケーションを終了します。よろしいでしょうか?';
const String kNoGoods = '対象商品がありません。';
const String kInsertingGoods = 'データ登録中、少々お待ちください。';
const String kBundleGoodsTip = 'バンドル対象商品です。在庫修正は単品JANで行ってください。';
const String kSumTooBig = '9999までしか登録できません。';
