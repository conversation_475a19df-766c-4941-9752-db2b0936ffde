import 'package:equatable/equatable.dart';

import 'goods.dart';

/// pop result
class PopResult extends Equatable {
  /// init
  const PopResult({
    required this.isFromEdit,
    this.goods,
  });

  /// from edit button action
  final bool isFromEdit;

  /// goods info (isFromEdit == true)
  final Goods? goods;

  @override
  List<Object?> get props => [
        isFromEdit,
        goods,
      ];
}
