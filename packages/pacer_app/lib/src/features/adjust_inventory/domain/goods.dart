import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/stock_adjust/v1/stock_adjust.pb.dart';

/// 商品
class Goods extends Equatable {
  /// 標準コンストラクタ
  const Goods({
    required this.id,
    required this.productId,
    required this.productCode,
    required this.retailStock,
    required this.productName,
    required this.stocksQuantity,
    required this.reasonNumber,
    required this.reasonDetailContent,
    required this.itemType,
  });

  /// Grpcの結果を構築します
  factory Goods.fromGrpc(GetStockItemResponse_StockItemInfo goodsInfo) => Goods(
        id: goodsInfo.id,
        productId: goodsInfo.productId,
        productCode: goodsInfo.productCode,
        retailStock: goodsInfo.retailStock,
        productName: goodsInfo.productName,
        stocksQuantity: goodsInfo.stocksQuantity,
        reasonNumber: goodsInfo.reasonNumber,
        reasonDetailContent: goodsInfo.reasonDetailContent,
        itemType: goodsInfo.itemType,
      );

  /// 在庫補正ID
  final String id;

  /// 商品ID
  final String productId;

  /// 商品コード
  final String productCode;

  /// 論理在庫数
  final int retailStock;

  /// 商品名称
  final String productName;

  /// 実際在庫数
  final int stocksQuantity;

  /// 在庫補正理由区分ID
  final String reasonNumber;

  /// 在庫補正理由
  final String reasonDetailContent;

  /// 種類 固定値
  final String itemType;

  @override
  List<Object?> get props => [
        id,
        productId,
        productCode,
        retailStock,
        productName,
        stocksQuantity,
        reasonNumber,
        reasonDetailContent,
        itemType,
      ];
}
