import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/stock_adjust/v1/stock_adjust.pb.dart';

import 'enum/goods_type.dart';
import 'update_goods.dart';

/// 在庫修正商品情報
class InventoryGoods extends Equatable {
  /// init
  const InventoryGoods({
    required this.id,
    required this.productId,
    required this.name,
    required this.state,
    required this.recordNumber,
    required this.code,
    required this.caseIrisu,
    required this.packIrisu,
    required this.deliveryExpectedNumber,
    required this.retailStock,
    required this.productOrBundleFlag,
  });

  /// Grpcの結果を構築します
  factory InventoryGoods.fromGrpc(GetGoodsResponse reps) => InventoryGoods(
        id: reps.id,
        productId: reps.productId,
        name: reps.name,
        state: reps.state,
        recordNumber: reps.recordNumber,
        code: reps.singleProductCode,
        caseIrisu: reps.caseIrisu,
        packIrisu: reps.packIrisu,
        deliveryExpectedNumber: reps.deliveryExpectedNumber,
        retailStock: reps.retailStock,
        productOrBundleFlag: GoodsType.fromValue(reps.productOrBundleFlag.value),
      );

  /// Grpcの結果を構築します
  factory InventoryGoods.fromUpdateGoods(
    String productCode,
    UpdateGoods goodsInfo,
  ) =>
      InventoryGoods(
        id: goodsInfo.id,
        productId: goodsInfo.productId,
        name: goodsInfo.productName,
        state: goodsInfo.state,
        recordNumber: goodsInfo.recNumber,
        code: productCode,
        caseIrisu: goodsInfo.caseIrisu,
        packIrisu: goodsInfo.packIrisu,
        deliveryExpectedNumber: '',
        retailStock: int.tryParse(goodsInfo.retailStock) ?? 0,
        productOrBundleFlag: GoodsType.fromValue(1),
      );

  /// 在庫補正ID
  final String id;

  /// 商品ID
  final String productId;

  /// 商品名称
  final String name;

  /// レコード状態
  final String state;

  /// レコード番号
  final String recordNumber;

  /// 単品商品コード
  final String code;

  /// ケース入り数
  final String caseIrisu;

  /// パック入り数
  final String packIrisu;

  /// 本日納品予定数
  final String deliveryExpectedNumber;

  /// 論理在庫数
  final int retailStock;

  /// 商品区分 1：単品 2：バンドル
  final GoodsType productOrBundleFlag;

  @override
  List<Object?> get props => [
        id,
        productId,
        name,
        state,
        recordNumber,
        code,
        caseIrisu,
        packIrisu,
        deliveryExpectedNumber,
        retailStock,
        productOrBundleFlag,
      ];
}
