import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/stock_adjust/v1/stock_adjust.pbgrpc.dart';

/// 更新の商品情報
class UpdateGoods extends Equatable {
  /// init
  const UpdateGoods({
    required this.productName,
    required this.reasonNumber,
    required this.retailStock,
    required this.totalStockQuantity,
    required this.caseIrisu,
    required this.packIrisu,
    required this.productId,
    required this.id,
    required this.recNumber,
    required this.state,
  });

  /// Grpcの結果を構築します
  factory UpdateGoods.fromGrpc(
    GetUpdateDataResponse_UpdateDataInfo goodsInfo,
  ) =>
      UpdateGoods(
        productName: goodsInfo.productName,
        reasonNumber: goodsInfo.reasonNumber,
        retailStock: goodsInfo.retailStock,
        totalStockQuantity: goodsInfo.totalStockQuantity,
        caseIrisu: goodsInfo.caseIrisu,
        productId: goodsInfo.productId,
        id: goodsInfo.id,
        recNumber: goodsInfo.recNumber,
        state: goodsInfo.state,
        packIrisu: goodsInfo.packIrisu,
      );

  /// 商品名称
  final String productName;

  /// 在庫補正理由区分ID
  final String reasonNumber;

  /// 論理在庫数
  final String retailStock;

  /// 実在庫数
  final int totalStockQuantity;

  /// ケース入り数
  final String caseIrisu;

  /// パック入り数
  final String packIrisu;

  /// 商品id
  final String productId;

  /// 在庫補正ID
  final String id;

  /// レコード番号 数理さんから提供されたレコード番号です。
  /// フォーマットを言っても、こちらもわからないです。
  /// 書いた例[12217.0:3]を返したので、戻り値を参照してください。
  final String recNumber;

  /// Status name, used for page screen display
  final String state;

  @override
  List<Object?> get props => [
        productName,
        reasonNumber,
        retailStock,
        caseIrisu,
        packIrisu,
        totalStockQuantity,
        retailStock,
        id,
        recNumber,
        state,
        productId,
      ];
}
