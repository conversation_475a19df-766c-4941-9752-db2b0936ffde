import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/stock_adjust/v1/stock_adjust.pb.dart';

/// 在庫修正理由取得
class ReasonOption extends Equatable {
  /// init
  const ReasonOption({
    required this.reasonCode,
    required this.description,
    required this.detailContent,
  });

  /// Grpcの結果を構築します
  factory ReasonOption.fromGrpc(GetAdjustReasonResponse_ReasonInfo reason) => ReasonOption(
        reasonCode: reason.id,
        description: reason.content,
        detailContent: reason.detailContent,
      );

  /// 区分コード
  final String reasonCode;

  /// 区分名
  final String description;

  /// 区分名
  final String detailContent;

  @override
  List<Object?> get props => [
        reasonCode,
        description,
        detailContent,
      ];
}
