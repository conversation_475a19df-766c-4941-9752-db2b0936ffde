// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'adjust_inventory_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adjustInventoryRepositoryHash() => r'7cac4201d672275c61a2ab68ae9c6ffed8f19189';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// 在庫修正repository
///
/// Copied from [adjustInventoryRepository].
@ProviderFor(adjustInventoryRepository)
const adjustInventoryRepositoryProvider = AdjustInventoryRepositoryFamily();

/// 在庫修正repository
///
/// Copied from [adjustInventoryRepository].
class AdjustInventoryRepositoryFamily extends Family {
  /// 在庫修正repository
  ///
  /// Copied from [adjustInventoryRepository].
  const AdjustInventoryRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'adjustInventoryRepositoryProvider';

  /// 在庫修正repository
  ///
  /// Copied from [adjustInventoryRepository].
  AdjustInventoryRepositoryProvider call({
    required AppUser? caller,
  }) {
    return AdjustInventoryRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  AdjustInventoryRepositoryProvider getProviderOverride(
    covariant AdjustInventoryRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(AdjustInventoryRepository Function(AdjustInventoryRepositoryRef ref) create) {
    return _$AdjustInventoryRepositoryFamilyOverride(this, create);
  }
}

class _$AdjustInventoryRepositoryFamilyOverride implements FamilyOverride {
  _$AdjustInventoryRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final AdjustInventoryRepository Function(AdjustInventoryRepositoryRef ref) create;

  @override
  final AdjustInventoryRepositoryFamily overriddenFamily;

  @override
  AdjustInventoryRepositoryProvider getProviderOverride(
    covariant AdjustInventoryRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// 在庫修正repository
///
/// Copied from [adjustInventoryRepository].
class AdjustInventoryRepositoryProvider extends Provider<AdjustInventoryRepository> {
  /// 在庫修正repository
  ///
  /// Copied from [adjustInventoryRepository].
  AdjustInventoryRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => adjustInventoryRepository(
            ref as AdjustInventoryRepositoryRef,
            caller: caller,
          ),
          from: adjustInventoryRepositoryProvider,
          name: r'adjustInventoryRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$adjustInventoryRepositoryHash,
          dependencies: AdjustInventoryRepositoryFamily._dependencies,
          allTransitiveDependencies: AdjustInventoryRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  AdjustInventoryRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    AdjustInventoryRepository Function(AdjustInventoryRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AdjustInventoryRepositoryProvider._internal(
        (ref) => create(ref as AdjustInventoryRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  ProviderElement<AdjustInventoryRepository> createElement() {
    return _AdjustInventoryRepositoryProviderElement(this);
  }

  AdjustInventoryRepositoryProvider _copyWith(
    AdjustInventoryRepository Function(AdjustInventoryRepositoryRef ref) create,
  ) {
    return AdjustInventoryRepositoryProvider._internal(
      (ref) => create(ref as AdjustInventoryRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is AdjustInventoryRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin AdjustInventoryRepositoryRef on ProviderRef<AdjustInventoryRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _AdjustInventoryRepositoryProviderElement extends ProviderElement<AdjustInventoryRepository>
    with AdjustInventoryRepositoryRef {
  _AdjustInventoryRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as AdjustInventoryRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
