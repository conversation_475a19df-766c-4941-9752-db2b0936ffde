import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/stock_adjust/v1/stock_adjust.pbgrpc.dart';
import 'package:shinise_core_client/stock_scan/v1/stock_scan.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../../utils/device_ip_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/goods.dart';
import '../domain/inventory_goods.dart';
import '../domain/reason_option.dart';
import '../domain/update_goods.dart';

part 'adjust_inventory_repository.g.dart';

/// 在庫修正repository
@Riverpod(keepAlive: true)
AdjustInventoryRepository adjustInventoryRepository(
  AdjustInventoryRepositoryRef ref, {
  required AppUser? caller,
}) =>
    AdjustInventoryRepository(caller: caller);

/// 在庫修正
class AdjustInventoryRepository {
  /// init
  AdjustInventoryRepository({
    required this.caller,
  });

  /// 操作を行うユーザー
  final AppUser? caller;

  final _uri = Env.getApiBaseUrl();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));

  /// 在庫修正理由取得
  Future<List<ReasonOption>> getReasonList({
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = StockAdjustServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final reasonNameRes = await stub.getAdjustReason(
        GetAdjustReasonRequest(
          storeCode: storeCode,
          storeId: storeId,
        ),
      );

      return switch (reasonNameRes) {
        GetAdjustReasonResponse(code: == '000') => reasonNameRes.reasonInfo.map(ReasonOption.fromGrpc).toList(),
        _ => throw UnknownException(reasonNameRes.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 在庫修正商品情報取得
  Future<InventoryGoods> getGoodsInfo({
    required String productCode,
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = StockAdjustServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getGoods(
        GetGoodsRequest(
          storeCode: storeCode,
          storeId: storeId,
          productCode: productCode,
        ),
      );

      return switch (resp) {
        GetGoodsResponse(code: == '000') => InventoryGoods.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 在庫修正追加
  Future<bool> insertGoods({
    required String state,
    required String productId,
    required String productCode,
    required String recordNumber,
    required String reasonNumber,
    required String realStock,
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = StockAdjustServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final request = InsertDateRequest(
        state: state,
        productId: productId,
        productCode: productCode,
        recordNumber: recordNumber,
        reasonNumber: reasonNumber,
        realStock: realStock,
        storeCode: storeCode,
        storeId: storeId,
      );
      final resp = await stub.insertDate(
        request,
      );

      return switch (resp) {
        InsertDateResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 在庫修正一覧
  Future<List<Goods>> getGoodsList({
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = StockAdjustServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final goodsListRes = await stub.getStockItem(
        GetStockItemRequest(
          storeCode: storeCode,
          storeId: storeId,
        ),
      );

      return switch (goodsListRes) {
        GetStockItemResponse(code: == '000') => goodsListRes.stockItemInfo.map(Goods.fromGrpc).toList(),
        GetStockItemResponse() => throw UnknownException(goodsListRes.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 一覧中の商品を削除する
  Future<bool> deleteGoodsById({
    required String storeCode,
    required Goods goods,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = StockAdjustServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.deleteSelectedItem(
        DeleteSelectedItemRequest(
          productCode: goods.productCode,
          productId: goods.productId,
          reallyNumber: goods.stocksQuantity.toString(),
          reasonNumber: goods.reasonNumber,
          adjustId: goods.id,
          storeId: storeId,
          storeCode: storeCode,
        ),
      );

      return switch (resp) {
        DeleteSelectedItemResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 一括確定
  Future<bool> confirmGoodsInfo({
    required List<Goods> goodsList,
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = StockAdjustServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.insertData(
        InsertDataRequest(
          storeCode: storeCode,
          storeId: storeId,
        ),
      );

      /// 在庫修正側のDBの商品情報をgoodsListの数だけ更新するために、
      /// ここで欠品スキャンのupdateStockをgoodsListの数だけ呼び出す
      /// 目的は、こうしないとisStockAdjustedが修正した商品分だけ更新されないため
      if (resp.code != '000') {
        throw UnknownException(resp.message);
      } else {
        for (final goods in goodsList) {
          final stub = StockScanServiceClient(
            channel,
            options: _callOptions,
            interceptors: [
              ShiniseInterceptor(caller: caller),
            ],
          );

          final resp = await stub.updateStock(
            UpdateStockRequest(
              storeCode: storeCode,
              productCode: goods.productCode,
            ),
          );

          if (resp case UpdateStockResponse(code: != '200')) {
            throw UnknownException(resp.message);
          }
        }

        return true;
      }
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 更新の商品情報取得
  Future<UpdateGoods> getUpdateGoods({
    required String productCode,
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = StockAdjustServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getUpdateData(
        GetUpdateDataRequest(
          productCode: productCode,
          storeCode: storeCode,
          storeId: storeId,
        ),
      );

      return switch (resp) {
        GetUpdateDataResponse(code: == '000') => UpdateGoods.fromGrpc(resp.updateDataInfo.first),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}

extension on GrpcError {
  AppException handle() {
    return switch (this) {
      GrpcError(code: StatusCode.notFound) => ProductNotFoundException(),
      GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
      GrpcError(code: StatusCode.invalidArgument) => WrongProductCodeException(),
      GrpcError(code: _) => UnknownException('エラーが発生しました'),
    };
  }
}
