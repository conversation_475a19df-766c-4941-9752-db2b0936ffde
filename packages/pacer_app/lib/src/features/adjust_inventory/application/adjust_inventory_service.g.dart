// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'adjust_inventory_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adjustInventoryServiceHash() => r'df0122aa1f610b044e4cc8e85476242bbde89978';

/// 在庫修正 service
///
/// Copied from [adjustInventoryService].
@ProviderFor(adjustInventoryService)
final adjustInventoryServiceProvider = Provider<AdjustInventoryService>.internal(
  adjustInventoryService,
  name: r'adjustInventoryServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$adjustInventoryServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AdjustInventoryServiceRef = ProviderRef<AdjustInventoryService>;
String _$getReasonListHash() => r'fb6db82ba4d11427692718596900d6edc34f53eb';

/// 在庫修正理由取得
///
/// Copied from [getReasonList].
@ProviderFor(getReasonList)
final getReasonListProvider = AutoDisposeFutureProvider<List<ReasonOption>>.internal(
  getReasonList,
  name: r'getReasonListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getReasonListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef GetReasonListRef = AutoDisposeFutureProviderRef<List<ReasonOption>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
