import 'dart:developer';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/adjust_inventory_repository.dart';
import '../domain/goods.dart';
import '../domain/inventory_goods.dart';
import '../domain/reason_option.dart';
import '../domain/update_goods.dart';

part 'adjust_inventory_service.g.dart';

/// 在庫修正 service
@Riverpod(keepAlive: true)
AdjustInventoryService adjustInventoryService(AdjustInventoryServiceRef ref) => AdjustInventoryService(ref);

/// 在庫修正 service
class AdjustInventoryService {
  /// init
  AdjustInventoryService(this.ref);

  /// ref
  final Ref ref;

  /// get goods list
  FutureOr<List<Goods>> getGoodsList() async {
    ref
      ..onCancel(() => log('cancel: getGoodsList'))
      ..onResume(() => log('resume: getGoodsList'))
      ..onDispose(() => log('dispose: getGoodsList'));

    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref
        .read(adjustInventoryRepositoryProvider(caller: caller))
        .getGoodsList(storeCode: store.code, storeId: store.id);
  }

  /// delete goods
  FutureOr<bool> deleteGoods({
    required Goods goods,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref.read(adjustInventoryRepositoryProvider(caller: caller)).deleteGoodsById(
          storeCode: store.code,
          storeId: store.id,
          goods: goods,
        );
  }

  /// confirm all goods
  FutureOr<bool> confirmAllGoods({required List<Goods> goodsList}) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref
        .read(adjustInventoryRepositoryProvider(caller: caller))
        .confirmGoodsInfo(storeCode: store.code, storeId: store.id, goodsList: goodsList);
  }

  /// get goods info by productCode
  FutureOr<InventoryGoods> getGoodsInfo({
    required String productCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref.read(adjustInventoryRepositoryProvider(caller: caller)).getGoodsInfo(
          productCode: productCode,
          storeCode: store.code,
          storeId: store.id,
        );
  }

  /// insert goods data
  FutureOr<bool> insertGoods({
    required String state,
    required String productId,
    required String productCode,
    required String recordNumber,
    required String reasonNumber,
    required String realStock,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref.read(adjustInventoryRepositoryProvider(caller: caller)).insertGoods(
          state: state,
          productId: productId,
          productCode: productCode,
          recordNumber: recordNumber,
          reasonNumber: reasonNumber,
          realStock: realStock,
          storeCode: store.code,
          storeId: store.id,
        );
  }

  /// get update goods info by productCode
  FutureOr<UpdateGoods> getUpdateGoodsInfo({
    required String productCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref.read(adjustInventoryRepositoryProvider(caller: caller)).getUpdateGoods(
          productCode: productCode,
          storeCode: store.code,
          storeId: store.id,
        );
  }
}

/// 在庫修正理由取得
@riverpod
Future<List<ReasonOption>> getReasonList(GetReasonListRef ref) async {
  ref
    ..onCancel(() => log('cancel: getReasonList'))
    ..onResume(() => log('resume: getReasonList'))
    ..onDispose(() => log('dispose: getReasonList'));

  final caller = ref.read(authRepositoryProvider).currentUser;
  if (caller == null) {
    throw ParseAuthFailure(caller.toString());
  }
  final store = caller.clockInStore;

  return ref
      .read(adjustInventoryRepositoryProvider(caller: caller))
      .getReasonList(storeCode: store.code, storeId: store.id);
}
