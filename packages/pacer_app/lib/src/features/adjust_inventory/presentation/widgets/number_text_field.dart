import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/adaptive_number_input_type.dart';

/// 数値入力時の最大文字数
const int maxCheckWeekNumberLength = 4;

/// number text field
class NumberTextField extends HookConsumerWidget {
  /// constructor
  const NumberTextField({
    super.key,
    required this.inputText,
    required this.onFieldSubmitted,
    this.onChanged,
    this.hintText = '',
    this.autofocus = false,
    this.onPressed,
    this.focusNode,
  });

  /// ヒントテキスト
  final String hintText;

  /// 入力テキスト
  final String inputText;

  /// 入力時のコールバック
  final void Function(String)? onChanged;

  /// submit call back
  final void Function(String)? onFieldSubmitted;

  /// ボタン押下時のコールバック
  final VoidCallback? onPressed;

  /// オートフォーカス
  final bool autofocus;

  /// フォーカスノード
  final FocusNode? focusNode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textEditingController = useTextEditingController();
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    useEffect(
      () {
        textEditingController.text = inputText;

        return;
      },
    );

    return Focus(
      child: TextFormField(
        focusNode: focusNode,
        controller: textEditingController,
        keyboardType: TextInputType.number.withEnter(),
        maxLength: maxCheckWeekNumberLength,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        decoration: InputDecoration(
          filled: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          fillColor: colors.button,
          focusColor: colors.button,
          hintText: hintText,
          hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
          counterText: '',
        ),
        onChanged: onChanged,
        onTap: () {
          changeNumber(textEditingController);
          textEditingController.selection = TextSelection(
            baseOffset: 0,
            extentOffset: textEditingController.text.length,
          );
        },
        autofocus: autofocus,
      ),
      onFocusChange: (hasFocus) {
        if (!hasFocus && inputText != textEditingController.text) {
          changeFormatText(textEditingController);
          submit(textEditingController.text);
        }
      },
    );
  }

  /// 数値に変換する
  void changeNumber(TextEditingController controller) {
    onPressed?.call();
    controller.text = controller.text.replaceAll(RegExp(r'\D'), '');
  }

  /// 入力値を変換する
  void changeFormatText(TextEditingController controller) {
    onChanged?.call(controller.text);
  }

  /// done
  void submit(String inputValue) {
    final value = inputValue.replaceAll(RegExp(r'\D'), '');
    onFieldSubmitted?.call(value);
  }
}
