import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart'; // Import the hooks package
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/adaptive_number_input_type.dart';
import '../../../../utils/remove_zero_formatter.dart';
import '../../../device/application/pacer_service.dart';

/// scanCodeProviderを監視するか、手動でJANを入力するテキストボックス
class JanTextField extends HookConsumerWidget {
  /// 標準コンストラクタ
  const JanTextField(
    this.hintText, {
    super.key,
    required this.controller,
    required this.onFieldSubmitted,
    required this.notifyPagePath,
    this.autofocus = false,
    this.readOnly = false,
  });

  /// テキストボックスの左側に表示するテキスト
  final bool autofocus;

  /// placeholder
  final String hintText;

  /// テキストエディターコントローラー
  final TextEditingController controller;

  /// スキャンまたは入力が完了したコールバック
  final void Function(String) onFieldSubmitted;

  /// receive notify page route uri
  final String notifyPagePath;

  /// readOnly
  final bool readOnly;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchFocusNode = useFocusNode();
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    ref.listen(scanCodeProvider, (_, next) {
      final location = GoRouterState.of(context).uri.toString();
      if (location == notifyPagePath) {
        final code = next.asData?.valueOrNull;
        if (code == null) return;
        onFieldSubmitted(code);
      }
    });

    return TextFormField(
      autofocus: autofocus,
      controller: controller,
      onFieldSubmitted: onFieldSubmitted,
      focusNode: searchFocusNode,
      keyboardType: TextInputType.number.withEnter(),
      readOnly: readOnly,
      decoration: InputDecoration(
        filled: true,
        fillColor: colors.button,
        focusColor: colors.button,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: colors.primary.withOpacity(0.2),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: colors.primary.withOpacity(0.2),
          ),
        ),
        prefixIconConstraints: const BoxConstraints(),
        border: const OutlineInputBorder(),
        errorStyle: const TextStyle(fontSize: 0, height: 0),
        hintText: hintText,
        hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
      ),
      inputFormatters: [
        LengthLimitingTextInputFormatter(32),
        RemoveZeroTextInputFormatter(),
      ],
    );
  }
}
