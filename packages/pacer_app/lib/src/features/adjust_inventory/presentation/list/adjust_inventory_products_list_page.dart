import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../exceptions/app_exception.dart';
import '../../config/strings.dart';
import '../../domain/goods.dart';
import '../../domain/pop_result.dart';
import '../form/adjust_inventory_controller.dart';
import 'adjust_inventory_list_cell.dart';
import 'adjust_inventory_list_controller.dart';

/// 商品情報一覧表示
class AdjustInventoryProductsListPage extends ConsumerStatefulWidget {
  /// 標準コンストラクタ
  const AdjustInventoryProductsListPage({super.key, this.isFromMissingModule});

  /// is from missing module
  final bool? isFromMissingModule;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AdjustInventoryProductsListPageState();
}

class _AdjustInventoryProductsListPageState extends ConsumerState<AdjustInventoryProductsListPage> {
  @override
  Widget build(BuildContext context) {
    final goodsArray = ref.watch(adjustIntervalListProvider);
    ref
      ..listen(confirmAllGoodsStateProvider, (previous, next) {
        final result = next.asData?.value;
        if (result != null) {
          context.pop();

          /// 一括確定処理成功しました。
          showAlert(
            kConfirmAllGoodsSuccess,
            buttons: [
              OutlinedButton(
                onPressed: (widget.isFromMissingModule ?? false) ? () => _popToMissingModule(context) : _refreshPage,
                child: const Text(kOk),
              ),
            ],
          );
        }
      })
      ..listen(deleteGoodsStateProvider, (previous, next) {
        final result = next.asData?.value;
        if (result != null) {
          context.pop();

          /// 削除しました。
          showAlert(
            kItHasBeenDelete,
            barrierDismissible: false,
            buttons: [
              OutlinedButton(
                onPressed: _refreshPage,
                child: const Text(kOk),
              ),
            ],
          );
        }
      })
      ..listen(adjustIntervalListProvider, (previous, next) {
        final result = next.asData?.value;
        if (result != null && result.isEmpty) {
          showAlert(
            kNoGoods,
            buttons: [
              OutlinedButton(
                onPressed: _onClose,
                child: const Text(kOk),
              ),
            ],
          );
        }
      })
      ..watch(selectedFixReasonCodeStateProvider);

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kListTitle),
      ),
      body: switch (goodsArray) {
        AsyncData(:final value) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            child: Column(
              children: [
                _Header(),
                Expanded(
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      final cellInfo = value[index];

                      return Consumer(
                        builder: (ctx, ref, _) {
                          final selectedGoods = ref.watch(selectGoodsStateProvider);

                          return AdjustInventoryListCell(
                            goods: cellInfo,
                            isSelected: selectedGoods == cellInfo,
                            onPressed: () => ref.read(selectGoodsStateProvider.notifier).update(cellInfo),
                          );
                        },
                      );
                    },
                    separatorBuilder: (context, _) => const SizedBox(
                      height: 12,
                    ),
                    itemCount: value.length,
                  ),
                ),
              ],
            ),
          ),
        AsyncError(:final error) => Text(
            switch (error) {
              UnknownException() => error.message,

              /// 取得に失敗しました
              _ => kRequestError,
            },
          ),
        _ => const Center(
            child: CircularProgressIndicator(),
          ),
      },
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const BackButton(),

            /// 削除
            _CustomOutLineButton(
              title: kDelete,
              click: _deleteClick,
            ),

            /// 修正
            _CustomOutLineButton(
              title: kModify,
              click: _editClick,
            ),

            /// 一括確定
            _CustomOutLineButton(
              title: kConfirm,
              click: _confirmClick,
            ),
          ],
        ),
      ),
    );
  }

  void _popToMissingModule(
    BuildContext context,
  ) {
    /// close alert
    context
      ..pop()
      ..pop(const PopResult(isFromEdit: false));
  }

  void _deleteClick() {
    final deletingGoods = ref.read(selectGoodsStateProvider);

    /// 削除対象データがありません
    if (deletingGoods == null) {
      showAlert(kNoDeleteItem);

      return;
    }

    /// 指定されている行を削除します。よろしいですか？
    showAlert(
      kDeleteSelectItem,
      buttons: [
        OutlinedButton(
          onPressed: context.pop,
          child: const Text(kNo),
        ),
        OutlinedButton(
          onPressed: () => deleteSelectedGoods(deletingGoods),
          child: const Text(kYes),
        ),
      ],
    );
  }

  void deleteSelectedGoods(Goods deletingGoods) {
    ref.read(deleteGoodsStateProvider.notifier).deleteGoods(deletingGoods);
    context.pop();

    /// 削除進度のダイアログを表示
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final deleteGoodsState = ref.watch(deleteGoodsStateProvider);

            return AlertDialog(
              /// 削除中...
              title: const Text(kDeleting),
              content: switch (deleteGoodsState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 削除できません、何らかのエラーが発生しました。
                      _ => kUnableDeleteError,
                    },
                    style: errorTextStyle,
                  ),
                _ => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (deleteGoodsState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _refreshPage() {
    /// close alert
    context.pop();
    ref.read(selectGoodsStateProvider.notifier).clear();
    ref.read(adjustIntervalListProvider.notifier).reload();
  }

  void _onClose() {
    /// close alert
    context.pop();

    /// close page
    Navigator.of(context).pop();
  }

  void _editClick() {
    final selectedGoods = ref.read(selectGoodsStateProvider);
    if (selectedGoods == null) {
      /// 商品情報を選択してください。
      showAlert(kPleaseSelectItem);

      return;
    }
    context.pop(
      PopResult(
        isFromEdit: true,
        goods: selectedGoods,
      ),
    );
  }

  void _confirmClick() {
    final goodsList = ref.read(adjustIntervalListProvider).value ?? [];
    if (goodsList.isEmpty) return;

    /// 全部の商品を一括確定します。よろしいでしょうか？
    showAlert(
      kConfirmAllGoodsAtOnce,
      buttons: [
        OutlinedButton(
          onPressed: context.pop,
          child: const Text(kNo),
        ),
        OutlinedButton(
          onPressed: () => confirmAllGoods(goodsList: goodsList),
          child: const Text(kYes),
        ),
      ],
    );
  }

  void confirmAllGoods({required List<Goods> goodsList}) {
    ref.read(confirmAllGoodsStateProvider.notifier).confirmGoods(goodsList: goodsList);
    context.pop();

    /// 一括確定進度のダイアログを表示
    showDialog<bool>(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final confirmGoodsState = ref.watch(confirmAllGoodsStateProvider);

            return AlertDialog(
              /// 一括確定中...
              title: const Text(kSubmitting),
              content: switch (confirmGoodsState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                _ => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (confirmGoodsState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void showAlert(
    String title, {
    List<Widget>? buttons,
    bool barrierDismissible = true,
  }) {
    buttons ??= [
      OutlinedButton(
        onPressed: context.pop,
        child: const Text(kOk),
      ),
    ];
    showDialog<AlertDialog>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AlertDialog(
        title: Text(title),
        actions: buttons,
      ),
    );
  }
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyMedium?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Container(
      decoration: BoxDecoration(
        color: colors.surfaceContainerHighest,
        border: Border.fromBorderSide(BorderSide(color: colors.outlineVariant)),
      ),
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          IntrinsicHeight(
            child: Row(
              children: [
                /// JAN
                Expanded(
                  flex: 3,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      kJAN,
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),

                /// 論理在庫数
                Expanded(
                  child: Text(
                    kTheoryNumber,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
          Divider(
            height: 1,
            thickness: 1,
            color: colors.outlineVariant,
          ),
          IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),

                    /// 商品名
                    child: Text(
                      kGoodsName,
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  /// 実際在庫数
                  child: Text(
                    kReallyNumber,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
          Divider(
            height: 1,
            thickness: 1,
            color: colors.outlineVariant,
          ),
          IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),

                    /// 修正理由
                    child: Text(
                      kEditReason,
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  /// 種類
                  child: Text(
                    kType,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _CustomOutLineButton extends StatelessWidget {
  const _CustomOutLineButton({
    required this.title,
    required this.click,
  });

  final String title;
  final VoidCallback click;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return OutlinedButton(
      style: OutlinedButton.styleFrom(
        backgroundColor: colors.secondaryContainer,
      ),
      onPressed: click,
      child: Text(
        title,
        style: TextStyle(color: colors.onSecondaryContainer),
      ),
    );
  }
}
