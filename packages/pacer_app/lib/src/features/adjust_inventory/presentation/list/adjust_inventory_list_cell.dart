import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../config/strings.dart';
import '../../domain/goods.dart';
import '../form/adjust_inventory_controller.dart';

/// 商品情報一覧表示
class AdjustInventoryListCell extends ConsumerStatefulWidget {
  /// 標準コンストラクタ
  const AdjustInventoryListCell({
    super.key,
    required this.goods,
    required this.isSelected,
    required this.onPressed,
  });

  /// 商品
  final Goods goods;

  /// 選択されているかどうか
  final bool isSelected;

  /// クリックイベントのコールバック
  final VoidCallback onPressed;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AdjustInventoryListCellState();
}

class _AdjustInventoryListCellState extends ConsumerState<AdjustInventoryListCell> {
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final reasonList = ref.watch(getReasonNameListProvider);

    final containerColor = widget.isSelected ? colors.primaryContainer : colors.surface;

    final textColor = widget.isSelected ? colors.onPrimaryContainer : colors.onSurface;

    final textTheme = texts.bodyMedium?.copyWith(
      color: textColor,
      height: 2,
    );

    final reasonDescription = reasonList
            .firstWhereOrNull(
              (element) => element.reasonCode == widget.goods.reasonNumber,
            )
            ?.description ??
        kUnknownReason;

    return GestureDetector(
      onTap: () => widget.onPressed(),
      child: Container(
        decoration: BoxDecoration(
          color: containerColor,
          border: Border.fromBorderSide(
            BorderSide(
              color: colors.outlineVariant,
            ),
          ),
          borderRadius: const BorderRadius.all(Radius.circular(10)),
        ),
        child: Column(
          children: [
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        widget.goods.productCode,
                        textAlign: TextAlign.center,
                        style: textTheme,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    child: Text(
                      widget.goods.retailStock.toString(),
                      textAlign: TextAlign.center,
                      style: textTheme,
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: colors.outlineVariant,
            ),
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        widget.goods.productName,
                        textAlign: TextAlign.center,
                        style: textTheme,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    child: Text(
                      '${widget.goods.stocksQuantity}',
                      textAlign: TextAlign.center,
                      style: textTheme,
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: colors.outlineVariant,
            ),
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        reasonDescription,
                        textAlign: TextAlign.center,
                        style: textTheme,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    child: Text(
                      widget.goods.itemType,
                      textAlign: TextAlign.center,
                      style: textTheme,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
