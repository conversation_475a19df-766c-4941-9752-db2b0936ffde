// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'adjust_inventory_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adjustIntervalListHash() => r'67741a89c807590bc96ec5784c97e9d447500df9';

/// goods list
///
/// Copied from [AdjustIntervalList].
@ProviderFor(AdjustIntervalList)
final adjustIntervalListProvider = AutoDisposeAsyncNotifierProvider<AdjustIntervalList, List<Goods>>.internal(
  AdjustIntervalList.new,
  name: r'adjustIntervalListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$adjustIntervalListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AdjustIntervalList = AutoDisposeAsyncNotifier<List<Goods>>;
String _$selectGoodsStateHash() => r'ee785de11236f294394a7f3fdc1c0a1c28f0bd68';

/// 选中商品
///
/// Copied from [SelectGoodsState].
@ProviderFor(SelectGoodsState)
final selectGoodsStateProvider = AutoDisposeNotifierProvider<SelectGoodsState, Goods?>.internal(
  SelectGoodsState.new,
  name: r'selectGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectGoodsState = AutoDisposeNotifier<Goods?>;
String _$deleteGoodsStateHash() => r'258fc09debd165986571da4577844fc022bc1fc9';

/// 商品の削除
///
/// Copied from [DeleteGoodsState].
@ProviderFor(DeleteGoodsState)
final deleteGoodsStateProvider = AutoDisposeAsyncNotifierProvider<DeleteGoodsState, bool>.internal(
  DeleteGoodsState.new,
  name: r'deleteGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deleteGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteGoodsState = AutoDisposeAsyncNotifier<bool>;
String _$confirmAllGoodsStateHash() => r'0e891ae9c2b7fa8a1d1c269c333b79962bb35a90';

/// 一括確定
///
/// Copied from [ConfirmAllGoodsState].
@ProviderFor(ConfirmAllGoodsState)
final confirmAllGoodsStateProvider = AutoDisposeAsyncNotifierProvider<ConfirmAllGoodsState, bool>.internal(
  ConfirmAllGoodsState.new,
  name: r'confirmAllGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmAllGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmAllGoodsState = AutoDisposeAsyncNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
