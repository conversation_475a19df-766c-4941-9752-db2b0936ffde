import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../application/adjust_inventory_service.dart';
import '../../domain/goods.dart';

part 'adjust_inventory_list_controller.g.dart';

/// goods list
@riverpod
class AdjustIntervalList extends _$AdjustIntervalList {
  @override
  FutureOr<List<Goods>> build() async {
    return ref.watch(adjustInventoryServiceProvider).getGoodsList();
  }

  /// upload
  void reload() {
    state = const AsyncValue.loading();

    ref.invalidateSelf();
  }
}

/// 选中商品
@riverpod
class SelectGoodsState extends _$SelectGoodsState {
  /// init
  @override
  Goods? build() {
    final goodsArray = ref.watch(adjustIntervalListProvider).asData?.value ?? [];

    return goodsArray.isEmpty ? null : goodsArray.first;
  }

  /// update selected goods
  Future<void> update(Goods? goods) async {
    log('scrap reason select: $goods');
    state = goods;
  }

  /// clean selected goods
  void clear() {
    state = null;
  }
}

/// 商品の削除
@riverpod
class DeleteGoodsState extends _$DeleteGoodsState {
  /// init
  @override
  FutureOr<bool> build() {
    return false;
  }

  /// delete goods
  FutureOr<void> deleteGoods(Goods goods) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(adjustInventoryServiceProvider).deleteGoods(goods: goods);
      },
    );
  }
}

/// 一括確定
@riverpod
class ConfirmAllGoodsState extends _$ConfirmAllGoodsState {
  /// init
  @override
  FutureOr<bool> build() {
    return true;
  }

  /// confirm all goods
  FutureOr<void> confirmGoods({required List<Goods> goodsList}) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(adjustInventoryServiceProvider).confirmAllGoods(goodsList: goodsList);
      },
    );
  }
}
