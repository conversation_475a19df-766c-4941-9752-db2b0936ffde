// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'adjust_inventory_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getReasonNameListHash() => r'e50c43062c47f190a53e9119e44a3f1cf27e6646';

/// get reason array
///
/// Copied from [getReasonNameList].
@ProviderFor(getReasonNameList)
final getReasonNameListProvider = AutoDisposeProvider<List<ReasonOption>>.internal(
  getReasonNameList,
  name: r'getReasonNameListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getReasonNameListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef GetReasonNameListRef = AutoDisposeProviderRef<List<ReasonOption>>;
String _$selectedFixReasonCodeStateHash() => r'85bab77d90b6346239b509a520089de4203c6d3e';

/// select reason
///
/// Copied from [SelectedFixReasonCodeState].
@ProviderFor(SelectedFixReasonCodeState)
final selectedFixReasonCodeStateProvider = AutoDisposeNotifierProvider<SelectedFixReasonCodeState, String?>.internal(
  SelectedFixReasonCodeState.new,
  name: r'selectedFixReasonCodeStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectedFixReasonCodeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedFixReasonCodeState = AutoDisposeNotifier<String?>;
String _$productCodeHash() => r'494244d5581d6cd905f1dc08525f6232009b98a1';

/// jan
///
/// Copied from [ProductCode].
@ProviderFor(ProductCode)
final productCodeProvider = AutoDisposeNotifierProvider<ProductCode, String>.internal(
  ProductCode.new,
  name: r'productCodeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$productCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProductCode = AutoDisposeNotifier<String>;
String _$insertDataHash() => r'e35abac85093e03bf7212eed4a2488c8033ead40';

/// insert data
///
/// Copied from [InsertData].
@ProviderFor(InsertData)
final insertDataProvider = AutoDisposeAsyncNotifierProvider<InsertData, bool?>.internal(
  InsertData.new,
  name: r'insertDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$insertDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InsertData = AutoDisposeAsyncNotifier<bool?>;
String _$inputJanStateHash() => r'aa4808bcf1eb6ec05fca175c6b278921d867c930';

/// jan input
///
/// Copied from [InputJanState].
@ProviderFor(InputJanState)
final inputJanStateProvider = AutoDisposeAsyncNotifierProvider<InputJanState, InventoryGoods?>.internal(
  InputJanState.new,
  name: r'inputJanStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputJanStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputJanState = AutoDisposeAsyncNotifier<InventoryGoods?>;
String _$goodsInputNumberNotifierHash() => r'1af8d6c85602133b7d857cb67ce6f8cf7dcb414c';

/// all input number
///
/// Copied from [GoodsInputNumberNotifier].
@ProviderFor(GoodsInputNumberNotifier)
final goodsInputNumberNotifierProvider =
    AutoDisposeNotifierProvider<GoodsInputNumberNotifier, GoodsInputNumberState>.internal(
  GoodsInputNumberNotifier.new,
  name: r'goodsInputNumberNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$goodsInputNumberNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GoodsInputNumberNotifier = AutoDisposeNotifier<GoodsInputNumberState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
