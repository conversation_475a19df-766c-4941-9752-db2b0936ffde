import 'dart:async';
import 'dart:developer';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../app/application/global_loading_service.dart';
import '../../application/adjust_inventory_service.dart';
import '../../domain/goods.dart';
import '../../domain/inventory_goods.dart';
import '../../domain/reason_option.dart';

part 'adjust_inventory_controller.g.dart';

/// get reason array
@riverpod
List<ReasonOption> getReasonNameList(GetReasonNameListRef ref) {
  return ref.watch(getReasonListProvider).asData?.value ?? [];
}

/// select reason
@riverpod
class SelectedFixReasonCodeState extends _$SelectedFixReasonCodeState {
  @override
  String? build() {
    final reasonArray = ref.watch(getReasonNameListProvider);
    if (reasonArray.isEmpty) return null;

    return reasonArray.first.reasonCode;
  }

  /// update selected reason
  void update(String? reasonCode) {
    log('scrap reason select: $reasonCode');
    state = reasonCode;
  }

  /// clean selected reason
  void clear() {
    final reasonArray = ref.read(getReasonNameListProvider);
    if (reasonArray.isEmpty) return state = null;
    state = reasonArray.first.reasonCode;
  }
}

/// jan
@riverpod
class ProductCode extends _$ProductCode {
  /// jan valid length
  static final List<int> toProcessJanCodeLengths = [20, 26];

  /// build
  @override
  String build() {
    return '';
  }

  ///
  Future<void> update(String value) async {
    state = _janHandle(value).replaceFirst(RegExp('^0+'), '');
  }

  /// clear value
  void clear() {
    state = '';
  }

  String _janHandle(String value) {
    if (toProcessJanCodeLengths.contains(value.length)) {
      final result = value.substring(0, 13);
      log('taking the first 13 characters ; $result');

      return result;
    }

    return value;
  }
}

/// insert data
@riverpod
class InsertData extends _$InsertData {
  /// init
  @override
  FutureOr<bool?> build() => false;

  /// insert goods
  FutureOr<void> insertData() async {
    final oldProduct = ref.read(inputJanStateProvider).asData?.value;
    if (oldProduct == null) return;
    final selectedReasonCode = ref.read(selectedFixReasonCodeStateProvider);
    if (selectedReasonCode == null) return;
    final inputNumberState = ref.read(goodsInputNumberNotifierProvider);
    final realStockNumberValue = int.tryParse(inputNumberState.sum);
    if (realStockNumberValue == null) return;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(adjustInventoryServiceProvider).insertGoods(
              state: oldProduct.state,
              productId: oldProduct.productId,
              productCode: oldProduct.code,
              recordNumber: oldProduct.recordNumber,
              reasonNumber: selectedReasonCode,
              realStock: inputNumberState.sum,
            );
      },
    );
  }
}

/// jan input
@riverpod
class InputJanState extends _$InputJanState {
  @override
  FutureOr<InventoryGoods?> build() {
    ref.showGlobalLoading();
    final scanProductCode = ref.watch(productCodeProvider);
    final service = ref.watch(adjustInventoryServiceProvider);

    if (scanProductCode.isEmpty) return null;

    /// oldProductが空の場合、製品情報の初回読み込みを表します。
    /// もしproductがnullの場合、getProductInfoインターフェースを直接使用します。
    final oldProduct = state.value;
    if (oldProduct == null) {
      return service.getGoodsInfo(productCode: scanProductCode);
    }

    return insertDataAndFetchNextProductInfo(oldProduct: oldProduct);
  }

  /// もしnextProductCodeに空が渡された場合、nullを返します。
  Future<InventoryGoods?> insertDataAndFetchNextProductInfo({
    required InventoryGoods oldProduct,
  }) async {
    final service = ref.watch(adjustInventoryServiceProvider);
    final scanProductCode = ref.watch(productCodeProvider);
    final selectedReasonCode = ref.read(selectedFixReasonCodeStateProvider);
    if (selectedReasonCode == null) return null;

    final inputNumberState = ref.read(goodsInputNumberNotifierProvider);
    final realStockNumberValue = int.tryParse(inputNumberState.sum);
    if (realStockNumberValue == null) return null;

    /// upload previous goods
    final isInsertSuccess = await service.insertGoods(
      state: oldProduct.state,
      productId: oldProduct.productId,
      productCode: oldProduct.code,
      recordNumber: oldProduct.recordNumber,
      reasonNumber: selectedReasonCode,
      realStock: inputNumberState.sum,
    );
    if (!isInsertSuccess) {
      return oldProduct;
    }

    /// clean history input data
    ref.read(goodsInputNumberNotifierProvider.notifier).clear();
    ref.read(selectedFixReasonCodeStateProvider.notifier).clear();

    if (scanProductCode.isEmpty) return null;

    return service.getGoodsInfo(productCode: scanProductCode);
  }

  /// insert data
  Future<bool> insertCurrentGoods() async {
    state = const AsyncLoading();

    final oldProduct = state.value;
    if (oldProduct == null) return true;

    final result = await AsyncValue.guard(
      () => insertDataAndFetchNextProductInfo(oldProduct: oldProduct),
    );
    state = result;
    if (result.hasError) {
      return false;
    }

    return true;
  }

  /// get update goods from overview
  Future<bool> getUpdateGoods({
    required Goods goods,
  }) async {
    final service = ref.watch(adjustInventoryServiceProvider);
    final inputNumberNotifier = ref.read(goodsInputNumberNotifierProvider.notifier);
    final selectedReasonCodeNotifier = ref.read(selectedFixReasonCodeStateProvider.notifier);
    state = const AsyncLoading();

    final result = await AsyncValue.guard(
      () async => service.getUpdateGoodsInfo(productCode: goods.productCode),
    );
    if (result.hasError) return false;

    final updateGoods = result.asData?.value;
    if (updateGoods == null) return false;

    final inventoryGoods = InventoryGoods.fromUpdateGoods(
      goods.productCode,
      updateGoods,
    );
    state = AsyncData(inventoryGoods);

    /// update really stock
    inputNumberNotifier.updateSum(updateGoods.totalStockQuantity.toString());

    selectedReasonCodeNotifier.update(goods.reasonNumber);

    return true;
  }

  /// clear
  void clear() {
    state = const AsyncData(null);
  }
}

/// all input number
@riverpod
class GoodsInputNumberNotifier extends _$GoodsInputNumberNotifier {
  /// init
  @override
  GoodsInputNumberState build() => GoodsInputNumberState();

  /// update box number
  void updateBoxNumber(String boxNumber) {
    state = state.copyWith(
      boxNumber: boxNumber,
      boxSum: _getBoxSum(boxNumber),
      sum: _getSum(boxNumber, state.packageNumber, state.singleGoodsNumber),
    );
  }

  /// update package number
  void updatePackageNumber(String packageNumber) {
    state = state.copyWith(
      packageNumber: packageNumber,
      packageSum: _getPackageSum(packageNumber),
      sum: _getSum(state.boxNumber, packageNumber, state.singleGoodsNumber),
    );
  }

  /// update single goods number
  void updateSingleGoodsNumber(String singleGoodsNumber) {
    state = state.copyWith(
      singleGoodsNumber: singleGoodsNumber,
      sum: _getSum(state.boxNumber, state.packageNumber, singleGoodsNumber),
    );
  }

  /// update sum
  void updateSum(String sum) {
    state = state.copyWith(
      sum: sum,
    );
  }

  /// clear
  void clear() {
    state = GoodsInputNumberState();
  }

  String _getBoxSum(String boxNumber) {
    if (boxNumber.isEmpty) return '';
    final eachBoxNumber = ref.read(inputJanStateProvider).value?.caseIrisu ?? '';
    final sum = (int.tryParse(boxNumber) ?? 0) * (int.tryParse(eachBoxNumber) ?? 0);

    return sum.toString();
  }

  String _getPackageSum(String packageNumber) {
    if (packageNumber.isEmpty) return '';
    final eachPackageNumber = ref.read(inputJanStateProvider).value?.packIrisu ?? '';
    final sum = (int.tryParse(packageNumber) ?? 0) * (int.tryParse(eachPackageNumber) ?? 0);

    return sum.toString();
  }

  String _getSum(
    String boxNumber,
    String packageNumber,
    String singleGoodsNumber,
  ) {
    final sum = (int.tryParse(_getBoxSum(boxNumber)) ?? 0) +
        (int.tryParse(_getPackageSum(packageNumber)) ?? 0) +
        (int.tryParse(singleGoodsNumber) ?? 0);

    return sum.toString();
  }
}

/// all input number
class GoodsInputNumberState {
  /// init
  GoodsInputNumberState({
    this.boxNumber = '',
    this.boxSum = '',
    this.packageNumber = '',
    this.packageSum = '',
    this.singleGoodsNumber = '',
    this.sum = '',
  });

  /// input box number
  final String boxNumber;

  /// box sum
  final String boxSum;

  /// input package number
  final String packageNumber;

  /// package sum
  final String packageSum;

  /// 単品商品コード
  final String singleGoodsNumber;

  /// sum
  final String sum;

  /// copy
  GoodsInputNumberState copyWith({
    String? boxNumber,
    String? boxSum,
    String? packageNumber,
    String? packageSum,
    String? singleGoodsNumber,
    String? sum,
  }) {
    return GoodsInputNumberState(
      boxNumber: boxNumber ?? this.boxNumber,
      boxSum: boxSum ?? this.boxSum,
      packageNumber: packageNumber ?? this.packageNumber,
      packageSum: packageSum ?? this.packageSum,
      singleGoodsNumber: singleGoodsNumber ?? this.singleGoodsNumber,
      sum: sum ?? this.sum,
    );
  }
}
