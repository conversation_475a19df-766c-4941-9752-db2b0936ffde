import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/alert_dialogs.dart';
import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../exceptions/app_exception.dart';
import '../../../../routing/app_router.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/async_value_ui.dart';
import '../../../../utils/keep_alive.dart';
import '../../../../utils/validator.dart';
import '../../../app/application/global_loading_service.dart';
import '../../../device/presentation/scan_window.dart';
import '../../config/strings.dart';
import '../../domain/enum/goods_type.dart';
import '../../domain/inventory_goods.dart';
import '../../domain/pop_result.dart';
import '../../domain/reason_option.dart';
import '../routing/adjust_inventory_route.dart';
import '../widgets/jan_text_field.dart';
import '../widgets/number_text_field.dart';
import 'adjust_inventory_controller.dart';
import 'select_reason.dart';

/// 在庫修正
class AdjustInventoryPage extends ConsumerStatefulWidget {
  /// 標準コンストラクタ
  const AdjustInventoryPage({
    super.key,
    this.productCode = '',
  });

  /// product code
  /// [欠品スキャン]ページからジャンプするには、製品コードをご持参ください。
  final String? productCode;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AdjustInventoryPageState();
}

class _AdjustInventoryPageState extends ConsumerState<AdjustInventoryPage> {
  static const double _lineHeight = 10;
  static const double _edge = 8;

  /// JAN入力コントローラー
  final TextEditingController _janTextController = TextEditingController();

  /// alert dialog key
  final alertGlobalKey = GlobalKey<State>();

  /// is from missing module
  late final bool? isFromMissingModule;

  @override
  void initState() {
    isFromMissingModule = (widget.productCode ?? '').isNotEmpty;
    super.initState();
  }

  @override
  void dispose() {
    _janTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;
    final fixReasonList = ref.watch(getReasonNameListProvider);
    final inputJanState = ref.watch(inputJanStateProvider);

    ref
      ..listen(getReasonNameListProvider, (_, state) {
        if (state.isNotEmpty && (isFromMissingModule ?? false)) {
          _onEditingComplete(widget.productCode ?? '');
        }
      })
      ..listen(goodsInputNumberNotifierProvider, (previous, next) {
        final sum = next.sum;
        log('sum: $sum');
      })
      ..listen(inputJanStateProvider, (previous, next) {
        next.showSnackBarOnError(context);
        final goodsInfo = next.asData?.value;
        if (goodsInfo == null) return;
        _janTextController.text = goodsInfo.code;
        if (goodsInfo.productOrBundleFlag == GoodsType.bundleGoods) {
          showAlert(kBundleGoodsTip);
        }
      })
      ..listen(insertDataProvider, (previous, next) {
        final result = next.asData?.value;
        if (result != null) {
          /// close alert
          context.pop();
          _pushListPage();
        }
      })
      ..keepOnly(selectedFixReasonCodeStateProvider);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop || ref.watch(globalLoadingServiceProvider)) return;
        await _onBackClick();
      },
      child: Scaffold(
        appBar: PacerAppBar(
          context: context,
          title: const Text(kStockAdjustTitle),
        ),
        body: ListView(
          padding: const EdgeInsets.symmetric(horizontal: _edge),
          children: [
            const SizedBox(
              height: _lineHeight,
            ),
            JanTextField(
              kJAN,
              controller: _janTextController,
              notifyPagePath: '/adjust_inventory',
              onFieldSubmitted: _onEditingComplete,
              readOnly: isFromMissingModule ?? false,
            ),
            const SizedBox(
              height: _lineHeight,
            ),
            switch (inputJanState) {
              AsyncData(:final value) => value == null
                  ? const Center(
                      /// バーコードをスキャンまたは入力してください。
                      child: Text(kPleaseInputCode),
                    )
                  : _createTable(context, fixReasonList, value),
              AsyncError(:final error) => Text(
                  switch (error) {
                    UnknownException() => error.message,
                    _ => kGetGoodsError,
                  },
                ),
              AsyncLoading() => const SizedBox.shrink(),
            },
          ],
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: isFromMissingModule ?? false ? null : ScanFloatingIconButton(onScan: _onEditingComplete),
        bottomNavigationBar: BottomAppBar(
          shape: const CircularNotchedRectangle(),
          notchMargin: 8,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              /// 終了
              TextButton(
                onPressed: _onBackClick,
                child: Text(
                  kFinish,
                  style: texts.titleMedium?.copyWith(color: colors.primary),
                ),
              ),

              /// 一覧
              TextButton(
                onPressed: _onOverviewClick,
                child: Text(
                  kOverview,
                  style: texts.titleMedium?.copyWith(color: colors.primary),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _createTable(
    BuildContext context,
    List<ReasonOption> fixReasonList,
    InventoryGoods goodsInfo,
  ) {
    final fixReasonList = ref.watch(getReasonNameListProvider);
    final colors = Theme.of(context).colorScheme;
    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3), // 1列目は全体の50%
        1: FractionColumnWidth(0.7), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(6),
        ),
      ),
      children: [
        TableRow(
          children: [
            /// 商品名
            const _ItemNameText(kGoodsName),
            _ValueText(goodsInfo.name),
          ],
        ),
        TableRow(
          children: [
            /// 修正理由
            const _ItemNameText(kEditReason),
            Consumer(
              builder: (ctx, ref, _) {
                final selectedFixReasonCode = ref.watch(selectedFixReasonCodeStateProvider);

                return SelectReason(
                  reasonCode: selectedFixReasonCode,
                  onChanged: _onReasonSelected,
                  reasonList: fixReasonList,
                );
              },
            ),
          ],
        ),
        TableRow(
          children: [
            /// ケース
            const _ItemNameText(kBox),
            Consumer(
              builder: (ctx, ref, _) {
                final inputNumberState = ref.watch(goodsInputNumberNotifierProvider);

                return _InputNumberView(
                  inputNumber: inputNumberState.boxNumber,
                  number: goodsInfo.caseIrisu,
                  sum: inputNumberState.boxSum,
                  onFieldSubmitted: _updateBoxNumber,
                  readOnly: goodsInfo.productOrBundleFlag == GoodsType.singleGoods,
                );
              },
            ),
          ],
        ),
        TableRow(
          children: [
            /// パック
            const _ItemNameText(kPackage),
            Consumer(
              builder: (ctx, ref, _) {
                final inputNumberState = ref.watch(goodsInputNumberNotifierProvider);

                return _InputNumberView(
                  inputNumber: inputNumberState.packageNumber,
                  number: goodsInfo.packIrisu,
                  sum: inputNumberState.packageSum,
                  onFieldSubmitted: _updatePackageNumber,
                  readOnly: goodsInfo.productOrBundleFlag == GoodsType.singleGoods,
                );
              },
            ),
          ],
        ),
        TableRow(
          children: [
            /// 単品
            const _ItemNameText(kSingleGoods),
            if (goodsInfo.productOrBundleFlag == GoodsType.singleGoods)
              const _ValueText('')
            else
              ColoredBox(
                color: colors.button,
                child: Consumer(
                  builder: (ctx, ref, _) {
                    final inputNumberState = ref.watch(goodsInputNumberNotifierProvider);

                    return NumberTextField(
                      inputText: inputNumberState.singleGoodsNumber,
                      onFieldSubmitted: _updateSingleGoodsNumber,
                    );
                  },
                ),
              ),
          ],
        ),
        TableRow(
          children: [
            /// 本日納品予定数
            const _ItemNameText(kTodayReserveNumber),
            _ValueText(goodsInfo.deliveryExpectedNumber),
          ],
        ),
        TableRow(
          children: [
            /// 論理在庫数
            const _ItemNameText(kTheoryNumber),
            _ValueText(goodsInfo.retailStock.toString()),
          ],
        ),
        TableRow(
          children: [
            /// 実際在庫数
            const _ItemNameText(kReallyNumber),
            Consumer(
              builder: (ctx, ref, _) {
                final inputNumberState = ref.watch(goodsInputNumberNotifierProvider);
                if (goodsInfo.productOrBundleFlag == GoodsType.singleGoods) {
                  debugPrint(inputNumberState.sum);
                  return ColoredBox(
                    color: colors.button,
                    child: NumberTextField(
                      inputText: inputNumberState.sum,
                      onFieldSubmitted: _updateSum,
                    ),
                  );
                } else {
                  return _ValueText(inputNumberState.sum);
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _updateBoxNumber(String value) async {
    ref.read(goodsInputNumberNotifierProvider.notifier).updateBoxNumber(value);
    _checkIsShowReallyStockWarningAlert(() {
      ref.read(goodsInputNumberNotifierProvider.notifier).updateBoxNumber('');
    });
  }

  Future<void> _updatePackageNumber(String value) async {
    ref.read(goodsInputNumberNotifierProvider.notifier).updatePackageNumber(value);
    _checkIsShowReallyStockWarningAlert(() {
      ref.read(goodsInputNumberNotifierProvider.notifier).updatePackageNumber('');
    });
  }

  Future<void> _updateSingleGoodsNumber(String value) async {
    ref.read(goodsInputNumberNotifierProvider.notifier).updateSingleGoodsNumber(value);
    _checkIsShowReallyStockWarningAlert(() {
      ref.read(goodsInputNumberNotifierProvider.notifier).updateSingleGoodsNumber('');
    });
  }

  Future<void> _updateSum(String value) async {
    ref.read(goodsInputNumberNotifierProvider.notifier).updateSum(value);
    _checkIsShowReallyStockWarningAlert(() {
      ref.read(goodsInputNumberNotifierProvider.notifier).updateSum('');
    });
  }

  void _checkIsShowReallyStockWarningAlert(VoidCallback callback) {
    final sum = int.tryParse(ref.read(goodsInputNumberNotifierProvider).sum);
    final currentGoods = ref.read(inputJanStateProvider).value;
    if (currentGoods == null) return;
    if (sum == null) return;
    if (sum > 9999) {
      showAlert(
        kSumTooBig,
        buttons: [
          OutlinedButton(
            onPressed: () {
              context.pop();
              callback();
            },
            child: const Text(kNo),
          ),
        ],
      );
      return;
    }
    final referMaxNumber = currentGoods.retailStock == 0 ? 10 : currentGoods.retailStock.abs() * 10;
    final referMinNumber = currentGoods.retailStock == 0 ? 0 : currentGoods.retailStock.abs() / 10;
    if (sum >= referMaxNumber || sum <= referMinNumber) {
      showAlert(
        kAdjustStockWarning,
        buttons: [
          OutlinedButton(
            onPressed: () {
              context.pop();
              callback();
            },
            child: const Text(kNo),
          ),
          OutlinedButton(
            onPressed: () => context.pop(),
            child: const Text(kYes),
          ),
        ],
      );
    }
  }

  /// 理由を選択
  void _onReasonSelected(String? reasonCode) {
    ref.read(selectedFixReasonCodeStateProvider.notifier).update(reasonCode);
  }

  /// 新しいJANコードが入力された際には、入力前のチェックとJANのチェックを行います。
  Future<void> _onEditingComplete(String value) async {
    log('scrap input scan code value:[$value]');
    if (alertGlobalKey.currentContext != null) {
      _janTextController.text = ref.read(productCodeProvider);
      return;
    }
    // currentProductが空の場合、廃棄された入力をチェックする必要はありません。
    final currentProduct = ref.read(inputJanStateProvider).asData?.value;
    if (currentProduct != null) {
      final inputNumberString = ref.read(goodsInputNumberNotifierProvider);
      final inputNumber = int.tryParse(inputNumberString.sum);
      if (inputNumber == null) {
        log('invalid input number');
        _cleanInputData();
      }
    }

    if (_checkProductCodeNumeric(value) && isValidBarcodeLength(value.length)) {
      await ref.read(productCodeProvider.notifier).update(value);
    }
  }

  Future<void> _onOverviewClick() async {
    final currentGoods = ref.read(inputJanStateProvider).asData?.valueOrNull;
    final isNeedInsertGoods = await _validateInputs();
    switch (currentGoods) {
      case null:
        await _pushListPage();

      case _ when isNeedInsertGoods:
        _insertData();

      case _ when !isNeedInsertGoods:
        await _pushListPage();
    }
  }

  Future<void> _pushListPage() async {
    _cleanInputData();
    final result = await AdjustInventoryProductListRoute(
      isFromMissingModule: isFromMissingModule,
    ).push<PopResult>(context);
    if (result == null) return;
    final goods = result.goods;
    if (result.isFromEdit && goods != null) {
      final isSuccess = await ref.read(inputJanStateProvider.notifier).getUpdateGoods(goods: goods);
      if (isSuccess) {
        _janTextController.text = goods.productCode;
      }
      return;
    }
    if (!result.isFromEdit) {
      Future.delayed(const Duration(seconds: 1), () {
        context.pop(true);
      });
    }
  }

  void _insertData() {
    ref.read(insertDataProvider.notifier).insertData();
    showDialog<bool>(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final confirmGoodsState = ref.watch(insertDataProvider);

            return AlertDialog(
              key: alertGlobalKey,

              /// データ登録中、少々お待ちください。
              title: const Text(kInsertingGoods),
              content: switch (confirmGoodsState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                _ => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (confirmGoodsState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _onBackClick() async {
    final isNeedConfirm = await _validateInputs();
    final title = isNeedConfirm ? kExitAppWithoutConfirmData : kAreYouSureExitApp;
    if (!context.mounted) return;

    /// アプリケーションを終了します。よろしいでしょうか?
    final canPop = (isFromMissingModule ?? false)
        ? true
        : await showAlertDialog(
            context: context,
            title: title,
            cancelActionText: kNo,
            defaultActionText: kYes,
          );

    if ((canPop ?? false) && context.mounted) {
      Navigator.of(context).pop();
    }
  }

  /// 入力前の最後のチェック
  Future<bool> _validateInputs({bool isShowToast = false}) async {
    final selectedReasonCode = ref.read(selectedFixReasonCodeStateProvider);
    final stockNumberString = ref.read(goodsInputNumberNotifierProvider).sum;
    final stockNumber = int.tryParse(stockNumberString);

    if (selectedReasonCode == null) {
      if (!isShowToast) return false;

      /// 在庫修正理由を選択してください。
      return _showSnackBar(kPleaseSelectReason);
    }

    if (stockNumber == null) {
      if (!isShowToast) return false;

      /// 実際在庫数量が無効です。
      return _showSnackBar(kInvalidStockNumber);
    }

    return true;
  }

  /// ProductCodeの入力をチェック
  bool _checkProductCodeNumeric(String input) {
    if (input.isEmpty) {
      return true;
    }

    final numericPattern = RegExp(r'^[0-9]+$');

    final result = numericPattern.hasMatch(input);
    if (!result) {
      /// 正しい商品を入力してください。
      _showSnackBar(kInputCorrectGoodsCode);
    }

    return result;
  }

  void _cleanInputData() {
    _janTextController.text = '';
    ref.read(inputJanStateProvider.notifier).clear();
    ref.read(productCodeProvider.notifier).clear();
    ref.read(selectedFixReasonCodeStateProvider.notifier).clear();
    ref.read(goodsInputNumberNotifierProvider.notifier).clear();
  }

  /// SnackBarを表示する
  bool _showSnackBar(String text) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.error,
        content: Text(text),
      ),
    );

    return false;
  }

  void showAlert(String title, {List<Widget>? buttons}) {
    buttons ??= [
      OutlinedButton(
        onPressed: context.pop,
        child: const Text(kOk),
      ),
    ];
    showDialog<AlertDialog>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        actions: buttons,
      ),
    );
  }
}

class _InputNumberView extends StatelessWidget {
  const _InputNumberView({
    required this.onFieldSubmitted,
    this.inputNumber = '',
    this.number = '',
    this.sum = '',
    this.readOnly = false,
  });

  final bool readOnly;
  final String? inputNumber;
  final String? number;
  final String? sum;

  /// submitted
  final void Function(String)? onFieldSubmitted;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3),
        1: FractionColumnWidth(0.1),
        2: FractionColumnWidth(0.2),
        3: FractionColumnWidth(0.1),
        4: FractionColumnWidth(0.3),
      },
      border: TableBorder.all(
        color: colors.line,
      ),
      children: [
        TableRow(
          children: [
            if (readOnly)
              const _ValueText('')
            else
              ColoredBox(
                color: colors.button,
                child: NumberTextField(
                  inputText: inputNumber ?? '',
                  onFieldSubmitted: onFieldSubmitted,
                ),
              ),
            const _ValueText(kMultiplication),
            _ValueText(number ?? ''),
            const _ValueText(kEqual),
            _ValueText(sum ?? ''),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
        ),
      ),
    );
  }
}
