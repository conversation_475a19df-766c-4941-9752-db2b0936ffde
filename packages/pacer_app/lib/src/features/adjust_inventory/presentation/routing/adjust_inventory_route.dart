// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../form/adjust_inventory_page.dart';
import '../list/adjust_inventory_products_list_page.dart';

class AdjustInventoryRoute extends GoRouteData {
  const AdjustInventoryRoute({this.productCode});

  /// product code
  final String? productCode;

  @override
  Widget build(BuildContext context, GoRouterState state) => AdjustInventoryPage(
        productCode: productCode,
      );
}

class AdjustInventoryProductListRoute extends GoRouteData {
  const AdjustInventoryProductListRoute({this.isFromMissingModule});

  /// is from missing module
  final bool? isFromMissingModule;

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      AdjustInventoryProductsListPage(isFromMissingModule: isFromMissingModule);
}
