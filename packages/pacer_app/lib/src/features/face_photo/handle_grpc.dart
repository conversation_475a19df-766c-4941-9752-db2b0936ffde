import 'package:grpc/grpc.dart';

import '../../exceptions/app_exception.dart';

extension GrpcErrorX on GrpcError {
  AppException handleGrpcError() {
    final notFound = UnknownException('データが取得できません');
    switch (code) {
      case StatusCode.notFound:
        return notFound;
      case StatusCode.deadlineExceeded:
        return ParseOrderFailure(codeName);
      case StatusCode.invalidArgument:
        return WrongProductCodeException();
      case StatusCode.unknown:
        return notFound;
      case _:
        return ParseOrderFailure(codeName);
    }
  }
}
