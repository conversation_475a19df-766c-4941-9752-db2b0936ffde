// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_confirm_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$photoConfirmServiceHash() => r'b969e9fa49db5076bc7842b24a6133fab6255833';

///写真確認サービス
///
/// Copied from [photoConfirmService].
@ProviderFor(photoConfirmService)
final photoConfirmServiceProvider = Provider<PhotoConfirmService>.internal(
  photoConfirmService,
  name: r'photoConfirmServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$photoConfirmServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PhotoConfirmServiceRef = ProviderRef<PhotoConfirmService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
