import 'dart:typed_data';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../exceptions/app_exception.dart';
import '../../../authentication/data/auth_repository.dart';
import '../data/photo_confirm_repository.dart';

part 'photo_confirm_service.g.dart';

///写真確認サービス
@Riverpod(keepAlive: true)
PhotoConfirmService photoConfirmService(PhotoConfirmServiceRef ref) {
  return PhotoConfirmService(ref);
}

///写真確認サービス
class PhotoConfirmService {
  ///初期化
  PhotoConfirmService(this.ref);

  ///ref
  final Ref ref;

  ///画像をアップロードする
  Future<bool> uploadImage({
    required Uint8List file,
    required String fileName,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    try {
      final imageData = await ref.read(photoConfirmRepositoryProvider).uploadImage(
            file: file,
            fileName: fileName,
            user: caller,
          );
      final employeeMid = await ref.read(photoConfirmRepositoryProvider).getEmployeeMID(user: caller);
      final result = await ref.read(photoConfirmRepositoryProvider).updateImage(
            imageData: imageData,
            user: caller,
            employeeMid: employeeMid,
          );
      return result;
    } catch (e) {
      rethrow;
    }
  }
}
