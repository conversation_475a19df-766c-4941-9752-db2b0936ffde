// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_confirm_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$photoConfirmControllerHash() => r'5958e3adca59a1a774a8c2219221a2d92f0dc7d7';

///写真確認コントローラー
///
/// Copied from [PhotoConfirmController].
@ProviderFor(PhotoConfirmController)
final photoConfirmControllerProvider = AutoDisposeNotifierProvider<PhotoConfirmController, AsyncValue<bool?>>.internal(
  PhotoConfirmController.new,
  name: r'photoConfirmControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$photoConfirmControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoConfirmController = AutoDisposeNotifier<AsyncValue<bool?>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
