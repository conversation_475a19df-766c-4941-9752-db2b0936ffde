import 'dart:typed_data';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../application/photo_confirm_service.dart';

part 'photo_confirm_controller.g.dart';

///写真確認コントローラー
@riverpod
class PhotoConfirmController extends _$PhotoConfirmController {
  @override
  AsyncValue<bool?> build() {
    return const AsyncData(null);
  }

  ///写真をアップロードする
  Future<void> updateFile({
    required Uint8List file,
    required String fileName,
  }) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(
      () => ref.read(photoConfirmServiceProvider).uploadImage(
            file: file,
            fileName: fileName,
          ),
    );
  }
}
