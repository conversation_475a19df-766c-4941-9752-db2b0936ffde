import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image/image.dart' as img;

import '../../../../common_widgets/alert_dialogs.dart';
import '../../../../localization/app_localizations_context.dart';
import '../../../../routing/app_router.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/async_value_ui.dart';
import '../../camera/presentation/camera_controller.dart';
import '../../camera/presentation/routing/camera_route.dart';
import '../../employee_photo/presentation/employee_controller.dart';
import '../../employee_photo/presentation/employee_photo_controller.dart';
import '../../employee_photo/presentation/routing/employee_route.dart';
import 'photo_confirm_controller.dart';

///写真確認ページ
class PhotoConfirmPage extends ConsumerWidget {
  ///初期化
  const PhotoConfirmPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(photoConfirmControllerProvider);
    final image = ref.watch(cameraPageControllerProvider);
    final employeeName = ref.watch(employeeProvider);
    ref.listen(
      photoConfirmControllerProvider,
      (_, state) async {
        switch (state) {
          case AsyncError():
            state.showAlertDialogOnError(context);
          case AsyncData(value: false):
            await showAlertDialog(
              context: context,
              title: context.loc.updatePhotoFailed,
              defaultActionText: context.loc.confirm,
            );
          case AsyncData(value: true):
            ref.invalidate(employeePhotoProvider);
            const EmployeeRoute().go(context);
          case _:
        }
      },
    );
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop || state.isLoading) return;
        context.pop();
      },
      child: Scaffold(
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8),
              child: Text(employeeName.value?.name ?? ''),
            ),
            Flexible(
              child: Center(
                child: Image.file(
                  File(image.value?.path ?? ''),
                ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: BottomAppBar(
          child: Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: state.isLoading ? null : () => Navigator.of(context).pop(),
                  style: ButtonStyle(
                    foregroundColor: WidgetStateProperty.resolveWith((states) {
                      return states.contains(WidgetState.disabled)
                          ? Theme.of(context).colorScheme.disabled
                          : Theme.of(context).colorScheme.primary;
                    }),
                  ),
                  child: Text(context.loc.cancel),
                ),
              ),
              IconButton(
                onPressed: state.isLoading
                    ? null
                    : () {
                        ref.read(cameraPageControllerProvider.notifier).updateFile(null);
                        const CameraRoute().go(context);
                      },
                icon: const Icon(
                  Icons.camera_alt_outlined,
                  size: 50,
                ),
                style: ButtonStyle(
                  foregroundColor: WidgetStateProperty.resolveWith((states) {
                    return states.contains(WidgetState.disabled)
                        ? Theme.of(context).colorScheme.disabled
                        : Theme.of(context).colorScheme.primary;
                  }),
                ),
              ),
              Expanded(
                child: TextButton(
                  onPressed: switch ((image, state)) {
                    (AsyncData(:final value?), AsyncData()) => () async {
                        final file = img.decodeImage(await value.readAsBytes());
                        if (file == null) {
                          return;
                        }
                        final imageResize = img.copyResize(
                          file,
                          width: 150,
                          height: 200,
                          maintainAspect: true,
                        );
                        final imageData = img.encodeJpg(imageResize);
                        final imageUpload = Uint8List.fromList(imageData);
                        if (image.value?.name case final String nonNullName) {
                          await ref.read(photoConfirmControllerProvider.notifier).updateFile(
                                file: imageUpload,
                                fileName: nonNullName,
                              );
                        }
                      },
                    _ => null
                  },
                  child: switch (state) {
                    AsyncLoading() => Center(
                        child: SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ),
                    _ => Text(
                        context.loc.confirm,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
