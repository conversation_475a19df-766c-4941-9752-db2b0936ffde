import 'dart:typed_data';

import 'package:grpc/grpc.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/cloud_files/v1/cloud_files.pbgrpc.dart';
import 'package:shinise_core_client/common/v1/common.pb.dart';
import 'package:shinise_core_client/employee_photo/v1/employee_photo.pbgrpc.dart';

import '../../../../constants/environment.dart';
import '../../../../exceptions/app_exception.dart';
import '../../../../utils/client_interceptor.dart';
import '../../../authentication/domain/app_user.dart';
import '../../handle_grpc.dart';

part 'photo_confirm_repository.g.dart';

///写真確認リポジトリ
@Riverpod(keepAlive: true)
PhotoConfirmRepository photoConfirmRepository(PhotoConfirmRepositoryRef ref) {
  return PhotoConfirmRepository(ref: ref);
}

///写真確認リポジトリ
class PhotoConfirmRepository {
  ///初期化
  PhotoConfirmRepository({required this.ref});

  ///ref
  final Ref ref;

  final _options = CallOptions(timeout: const Duration(seconds: 60));

  final _uri = Env.getApiBaseUrl();

  ///画像をアップロードする
  Future<String> uploadImage({
    required Uint8List file,
    required String fileName,
    required AppUser user,
  }) async {
    final channel = ClientChannel(
      _uri.host,
      port: _uri.port,
    );
    final stub = CloudFilesServiceClient(
      channel,
      options: _options,
      interceptors: [ShiniseInterceptor(caller: user)],
    );
    final fileRequest = File(
      content: file,
      fileName: fileName,
    );
    final request = UploadFileRequest(
      employeeCode: user.userCode,
      userUploadFile: fileRequest,
    );
    final response = await stub.uploadFile(request);

    try {
      if (!response.hasData()) {
        return '';
      }
      return response.path;
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  ///画像を更新
  Future<bool> updateImage({
    required String imageData,
    required AppUser user,
    required String employeeMid,
  }) async {
    final channel = ClientChannel(
      _uri.host,
      port: _uri.port,
    );
    final stub = EmployeePhotoProtoServiceClient(
      channel,
      options: _options,
      interceptors: [ShiniseInterceptor(caller: user)],
    );

    final request = EmployeePhotoToolRequest(
      employeeCode: user.userCode,
      employeeMid: employeeMid,
      photoPath: imageData,
    );

    try {
      final response = await stub.employeePhotoTool(request);
      return response.success;
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  ///従業員の MID を取得する
  ///画像更新APIリクエストに必要
  Future<String> getEmployeeMID({
    required AppUser user,
  }) async {
    final channel = ClientChannel(
      _uri.host,
      port: _uri.port,
    );
    final stub = EmployeePhotoProtoServiceClient(
      channel,
      options: _options,
      interceptors: [ShiniseInterceptor(caller: user)],
    );
    final request = GetEmployeeMIDRequest(
      employeeCode: user.userCode,
    );

    try {
      final response = await stub.getEmployeeMID(request);
      return response.employeeMid;
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
