// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_confirm_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$photoConfirmRepositoryHash() => r'3c2a79f6e7c22a9ac5ca5a6e9708e6ace6a2eb8a';

///写真確認リポジトリ
///
/// Copied from [photoConfirmRepository].
@ProviderFor(photoConfirmRepository)
final photoConfirmRepositoryProvider = Provider<PhotoConfirmRepository>.internal(
  photoConfirmRepository,
  name: r'photoConfirmRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$photoConfirmRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PhotoConfirmRepositoryRef = ProviderRef<PhotoConfirmRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
