<?xml version="1.0" encoding="utf-8"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" d2Version="v0.6.3-HEAD" preserveAspectRatio="xMidYMid meet" viewBox="0 0 3077 1275"><svg id="d2-svg" class="d2-2990041089" width="3077" height="1275" viewBox="-101 -112 3077 1275"><rect x="-101.000000" y="-112.000000" width="3077.000000" height="1275.000000" rx="0.000000" class=" fill-N7" stroke-width="0" /><style type="text/css"><![CDATA[
.d2-2990041089 .text {
	font-family: "d2-2990041089-font-regular";
}
@font-face {
	font-family: d2-2990041089-font-regular;
	src: url("data:application/font-woff;base64,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");
}
.appendix-icon {
	filter: drop-shadow(0px 0px 32px rgba(31, 36, 58, 0.1));
}
.d2-2990041089 .text-bold {
	font-family: "d2-2990041089-font-bold";
}
@font-face {
	font-family: d2-2990041089-font-bold;
	src: url("data:application/font-woff;base64,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");
}
.d2-2990041089 .text-italic {
	font-family: "d2-2990041089-font-italic";
}
@font-face {
	font-family: d2-2990041089-font-italic;
	src: url("data:application/font-woff;base64,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");
}]]></style><style type="text/css"><![CDATA[.shape {
  shape-rendering: geometricPrecision;
  stroke-linejoin: round;
}
.connection {
  stroke-linecap: round;
  stroke-linejoin: round;
}
.blend {
  mix-blend-mode: multiply;
  opacity: 0.5;
}

		.d2-2990041089 .fill-N1{fill:#0A0F25;}
		.d2-2990041089 .fill-N2{fill:#676C7E;}
		.d2-2990041089 .fill-N3{fill:#9499AB;}
		.d2-2990041089 .fill-N4{fill:#CFD2DD;}
		.d2-2990041089 .fill-N5{fill:#DEE1EB;}
		.d2-2990041089 .fill-N6{fill:#EEF1F8;}
		.d2-2990041089 .fill-N7{fill:#FFFFFF;}
		.d2-2990041089 .fill-B1{fill:#0D32B2;}
		.d2-2990041089 .fill-B2{fill:#0D32B2;}
		.d2-2990041089 .fill-B3{fill:#E3E9FD;}
		.d2-2990041089 .fill-B4{fill:#E3E9FD;}
		.d2-2990041089 .fill-B5{fill:#EDF0FD;}
		.d2-2990041089 .fill-B6{fill:#F7F8FE;}
		.d2-2990041089 .fill-AA2{fill:#4A6FF3;}
		.d2-2990041089 .fill-AA4{fill:#EDF0FD;}
		.d2-2990041089 .fill-AA5{fill:#F7F8FE;}
		.d2-2990041089 .fill-AB4{fill:#EDF0FD;}
		.d2-2990041089 .fill-AB5{fill:#F7F8FE;}
		.d2-2990041089 .stroke-N1{stroke:#0A0F25;}
		.d2-2990041089 .stroke-N2{stroke:#676C7E;}
		.d2-2990041089 .stroke-N3{stroke:#9499AB;}
		.d2-2990041089 .stroke-N4{stroke:#CFD2DD;}
		.d2-2990041089 .stroke-N5{stroke:#DEE1EB;}
		.d2-2990041089 .stroke-N6{stroke:#EEF1F8;}
		.d2-2990041089 .stroke-N7{stroke:#FFFFFF;}
		.d2-2990041089 .stroke-B1{stroke:#0D32B2;}
		.d2-2990041089 .stroke-B2{stroke:#0D32B2;}
		.d2-2990041089 .stroke-B3{stroke:#E3E9FD;}
		.d2-2990041089 .stroke-B4{stroke:#E3E9FD;}
		.d2-2990041089 .stroke-B5{stroke:#EDF0FD;}
		.d2-2990041089 .stroke-B6{stroke:#F7F8FE;}
		.d2-2990041089 .stroke-AA2{stroke:#4A6FF3;}
		.d2-2990041089 .stroke-AA4{stroke:#EDF0FD;}
		.d2-2990041089 .stroke-AA5{stroke:#F7F8FE;}
		.d2-2990041089 .stroke-AB4{stroke:#EDF0FD;}
		.d2-2990041089 .stroke-AB5{stroke:#F7F8FE;}
		.d2-2990041089 .background-color-N1{background-color:#0A0F25;}
		.d2-2990041089 .background-color-N2{background-color:#676C7E;}
		.d2-2990041089 .background-color-N3{background-color:#9499AB;}
		.d2-2990041089 .background-color-N4{background-color:#CFD2DD;}
		.d2-2990041089 .background-color-N5{background-color:#DEE1EB;}
		.d2-2990041089 .background-color-N6{background-color:#EEF1F8;}
		.d2-2990041089 .background-color-N7{background-color:#FFFFFF;}
		.d2-2990041089 .background-color-B1{background-color:#0D32B2;}
		.d2-2990041089 .background-color-B2{background-color:#0D32B2;}
		.d2-2990041089 .background-color-B3{background-color:#E3E9FD;}
		.d2-2990041089 .background-color-B4{background-color:#E3E9FD;}
		.d2-2990041089 .background-color-B5{background-color:#EDF0FD;}
		.d2-2990041089 .background-color-B6{background-color:#F7F8FE;}
		.d2-2990041089 .background-color-AA2{background-color:#4A6FF3;}
		.d2-2990041089 .background-color-AA4{background-color:#EDF0FD;}
		.d2-2990041089 .background-color-AA5{background-color:#F7F8FE;}
		.d2-2990041089 .background-color-AB4{background-color:#EDF0FD;}
		.d2-2990041089 .background-color-AB5{background-color:#F7F8FE;}
		.d2-2990041089 .color-N1{color:#0A0F25;}
		.d2-2990041089 .color-N2{color:#676C7E;}
		.d2-2990041089 .color-N3{color:#9499AB;}
		.d2-2990041089 .color-N4{color:#CFD2DD;}
		.d2-2990041089 .color-N5{color:#DEE1EB;}
		.d2-2990041089 .color-N6{color:#EEF1F8;}
		.d2-2990041089 .color-N7{color:#FFFFFF;}
		.d2-2990041089 .color-B1{color:#0D32B2;}
		.d2-2990041089 .color-B2{color:#0D32B2;}
		.d2-2990041089 .color-B3{color:#E3E9FD;}
		.d2-2990041089 .color-B4{color:#E3E9FD;}
		.d2-2990041089 .color-B5{color:#EDF0FD;}
		.d2-2990041089 .color-B6{color:#F7F8FE;}
		.d2-2990041089 .color-AA2{color:#4A6FF3;}
		.d2-2990041089 .color-AA4{color:#EDF0FD;}
		.d2-2990041089 .color-AA5{color:#F7F8FE;}
		.d2-2990041089 .color-AB4{color:#EDF0FD;}
		.d2-2990041089 .color-AB5{color:#F7F8FE;}.appendix text.text{fill:#0A0F25}.md{--color-fg-default:#0A0F25;--color-fg-muted:#676C7E;--color-fg-subtle:#9499AB;--color-canvas-default:#FFFFFF;--color-canvas-subtle:#EEF1F8;--color-border-default:#0D32B2;--color-border-muted:#0D32B2;--color-neutral-muted:#EEF1F8;--color-accent-fg:#0D32B2;--color-accent-emphasis:#0D32B2;--color-attention-subtle:#676C7E;--color-danger-fg:red;}.sketch-overlay-B1{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-B2{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-B3{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B6{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AA2{fill:url(#streaks-dark);mix-blend-mode:overlay}.sketch-overlay-AA4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AA5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AB4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AB5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N1{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-N2{fill:url(#streaks-dark);mix-blend-mode:overlay}.sketch-overlay-N3{fill:url(#streaks-normal);mix-blend-mode:color-burn}.sketch-overlay-N4{fill:url(#streaks-normal);mix-blend-mode:color-burn}.sketch-overlay-N5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N6{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N7{fill:url(#streaks-bright);mix-blend-mode:darken}.light-code{display: block}.dark-code{display: none}]]></style><g id="Legend"><g class="shape" ><rect x="0.000000" y="29.000000" width="695.000000" height="549.000000" class=" stroke-B1 fill-B4" style="stroke-width:2;" /></g><text x="347.500000" y="16.000000" class="text fill-N1" style="text-anchor:middle;font-size:28px">Legend</text></g><g id="photoConfirmControllerProvider"><g class="shape" ><rect x="735.000000" y="426.000000" width="311.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="890.500000" y="464.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">photoConfirmControllerProvider</text><title>///写真確認コントローラー</title></g><g id="cameraPageControllerProvider"><g class="shape" ><rect x="1106.000000" y="426.000000" width="300.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1256.000000" y="464.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">cameraPageControllerProvider</text><title>カメラのページ</title></g><g id="employeeControllerProvider"><g class="shape" ><rect x="1893.000000" y="426.000000" width="282.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2034.000000" y="464.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">employeeControllerProvider</text><title>従業員情報管理者</title></g><g id="employeeServiceProvider"><g class="shape" ><rect x="1903.000000" y="100.000000" width="261.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2033.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">employeeServiceProvider</text><title>従業員情報サービス</title></g><g id="employeePhotoCheckControllerProvider"><g class="shape" ><rect x="1466.000000" y="426.000000" width="367.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1649.500000" y="464.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">employeePhotoCheckControllerProvider</text><title>///</title></g><g id="employeePhotoControllerProvider"><g class="shape" ><rect x="2224.000000" y="100.000000" width="324.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2386.000000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">employeePhotoControllerProvider</text><title>///従業員の写真コントローラー</title></g><g id="authRepositoryProvider"><g class="shape" ><rect x="2608.000000" y="100.000000" width="250.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2733.000000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">authRepositoryProvider</text><title>autoDisposeProvider</title></g><g id="PhotoConfirmPage"><g class="shape" ><ellipse rx="141.000000" ry="141.000000" cx="1073.000000" cy="921.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1073.000000" y="926.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">PhotoConfirmPage</text><title>///写真確認ページ</title></g><g id="CameraPage"><g class="shape" ><ellipse rx="110.000000" ry="110.000000" cx="1436.000000" cy="921.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1436.000000" y="926.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">CameraPage</text><title>///カメラとプレビュー写真</title></g><g id="EmployeePage"><g class="shape" ><ellipse rx="120.000000" ry="120.000000" cx="1832.000000" cy="921.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1832.000000" y="926.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">EmployeePage</text><title>社員情報ページ</title></g><g id="EmployeeBody"><g class="shape" ><ellipse rx="121.500000" ry="121.500000" cx="2553.500000" cy="458.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="2553.500000" y="464.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">EmployeeBody</text><title>従業員情報本体</title></g><g id="Legend.Type"><g class="shape" ><rect x="30.000000" y="370.000000" width="345.000000" height="178.000000" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="202.500000" y="358.000000" class="text fill-N1" style="text-anchor:middle;font-size:24px">Type</text></g><g id="Legend.Arrows"><g class="shape" ><rect x="415.000000" y="70.000000" width="252.000000" height="452.000000" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="541.000000" y="58.000000" class="text fill-N1" style="text-anchor:middle;font-size:24px">Arrows</text></g><g id="Legend.Type.Widget"><g class="shape" ><ellipse rx="59.000000" ry="59.000000" cx="119.000000" cy="459.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="119.000000" y="464.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Widget</text></g><g id="Legend.Type.Provider"><g class="shape" ><rect x="238.000000" y="426.000000" width="107.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="291.500000" y="464.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Provider</text></g><g id="Legend.Arrows.&#34;.&#34;"><g class="shape" ><rect x="512.000000" y="100.000000" width="49.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="536.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">.</text></g><g id="Legend.Arrows.&#34;..&#34;"><g class="shape" ><rect x="510.000000" y="426.000000" width="54.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="537.000000" y="464.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">..</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[0]"><marker id="mk-2177206569" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" class="connection fill-B2" stroke-width="2" /> </marker><path d="M 510.741604 165.054490 C 471.200012 213.899994 473.194000 314.700012 520.360311 421.839056" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2990041089)" /><text x="472.500000" y="298.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">read:</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[1]"><marker id="mk-3488378134" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" class="connection fill-B1" stroke-width="2" /> </marker><path d="M 536.500000 167.500000 C 536.500000 214.300003 536.500000 314.700012 536.500000 421.500000" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2990041089)" /><text x="537.000000" y="301.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">listen</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[2]"><marker id="mk-3519660172" markerWidth="16.000000" markerHeight="20.000000" refX="10.000000" refY="10.000000" viewBox="0.000000 0.000000 16.000000 20.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 16.000000,10.000000 0.000000,20.000000" class="connection fill-B1" stroke-width="4" /> </marker><path d="M 562.458127 163.772826 C 605.299988 213.500000 603.578003 314.700012 554.852185 419.156243" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2990041089)" /><text x="604.000000" y="296.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">watch:</text></g><g id="(photoConfirmControllerProvider -&gt; PhotoConfirmPage)[0]"><path d="M 870.201782 495.109947 C 809.335999 602.500000 793.750000 640.000000 793.750000 655.000000 C 793.750000 670.000000 826.200012 752.400024 950.239227 838.023381" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2990041089)" /></g><g id="(cameraPageControllerProvider -&gt; PhotoConfirmPage)[0]"><path d="M 1217.644006 494.559970 C 1103.364990 602.500000 1074.250000 640.000000 1074.250000 655.000000 C 1074.250000 670.000000 1074.199951 740.000000 1074.034991 773.000087" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2990041089)" /></g><g id="(employeeControllerProvider -&gt; PhotoConfirmPage)[0]"><path d="M 1995.743901 494.562983 C 1881.784058 602.500000 1852.750000 640.000000 1852.750000 655.000000 C 1852.750000 670.000000 1724.199951 761.400024 1216.800078 885.338993" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2990041089)" /></g><g id="(photoConfirmControllerProvider -&gt; PhotoConfirmPage)[1]"><path d="M 890.504680 494.499946 C 891.297974 602.500000 891.500000 640.000000 891.500000 655.000000 C 891.500000 670.000000 908.400024 747.599976 973.229532 815.114777" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2990041089)" /></g><g id="(cameraPageControllerProvider -&gt; PhotoConfirmPage)[1]"><path d="M 1223.297561 493.951053 C 1120.333984 602.500000 1094.250000 640.000000 1094.250000 655.000000 C 1094.250000 670.000000 1093.000000 740.200012 1088.486556 777.029702" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2990041089)" /></g><g id="(photoConfirmControllerProvider -&gt; PhotoConfirmPage)[2]"><path d="M 923.193511 493.940450 C 1027.760986 602.500000 1054.250000 640.000000 1054.250000 655.000000 C 1054.250000 670.000000 1055.199951 740.200012 1058.629052 777.017237" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2990041089)" /></g><g id="(cameraPageControllerProvider -&gt; CameraPage)[0]"><path d="M 1256.012026 495.499919 C 1256.797974 602.500000 1257.000000 640.000000 1257.000000 655.000000 C 1257.000000 670.000000 1277.800049 752.200012 1356.213944 835.891804" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2990041089)" /></g><g id="(cameraPageControllerProvider -&gt; CameraPage)[1]"><path d="M 1292.440453 493.887478 C 1405.199951 602.500000 1433.750000 640.000000 1433.750000 655.000000 C 1433.750000 670.000000 1434.000000 746.200012 1434.938279 807.000476" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2990041089)" /></g><g id="(cameraPageControllerProvider -&gt; CameraPage)[2]"><path d="M 1296.361382 493.808701 C 1421.968994 602.500000 1453.750000 640.000000 1453.750000 655.000000 C 1453.750000 670.000000 1452.199951 746.200012 1446.380973 807.018184" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2990041089)" /></g><g id="(employeeControllerProvider -&gt; EmployeePage)[0]"><path d="M 2034.012026 495.499919 C 2034.797974 602.500000 2035.000000 640.000000 2035.000000 655.000000 C 2035.000000 670.000000 2011.800049 751.799988 1924.101272 834.206565" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2990041089)" /></g><g id="(employeePhotoCheckControllerProvider -&gt; EmployeePage)[0]"><path d="M 1612.334178 494.596553 C 1501.895996 602.500000 1473.750000 640.000000 1473.750000 655.000000 C 1473.750000 670.000000 1524.199951 757.000000 1719.828276 861.696997" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2990041089)" /></g><g id="(employeeControllerProvider -&gt; EmployeePage)[1]"><path d="M 2040.582153 494.462376 C 2061.839111 602.500000 2067.250000 640.000000 2067.250000 655.000000 C 2067.250000 670.000000 2038.800049 753.000000 1928.110634 842.485253" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2990041089)" /></g><g id="(employeePhotoCheckControllerProvider -&gt; EmployeePage)[1]"><path d="M 1649.504680 494.499946 C 1650.297974 602.500000 1650.500000 640.000000 1650.500000 655.000000 C 1650.500000 670.000000 1670.199951 750.799988 1746.249408 831.095823" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2990041089)" /></g><g id="(employeePhotoCheckControllerProvider -&gt; EmployeePage)[2]"><path d="M 1687.020463 493.865287 C 1803.311035 602.500000 1832.750000 640.000000 1832.750000 655.000000 C 1832.750000 670.000000 1832.800049 744.200012 1832.985919 797.000025" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2990041089)" /></g><g id="(employeePhotoControllerProvider -&gt; EmployeeBody)[0]"><path d="M 2325.387862 166.975376 C 2241.600098 214.300003 2265.000000 310.200012 2438.778202 399.792318" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2990041089)" /></g><g id="(authRepositoryProvider -&gt; EmployeeBody)[0]"><path d="M 2733.250000 168.500000 C 2733.250000 214.300003 2714.800049 304.399994 2646.092537 369.197285" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-2990041089)" /></g><g id="(employeePhotoControllerProvider -&gt; EmployeeBody)[1]"><path d="M 2445.483459 166.497556 C 2528.550049 214.300003 2550.000000 297.000000 2550.900031 333.001249" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-2990041089)" /></g><g id="(employeeServiceProvider -&gt; employeeControllerProvider)[0]"><path d="M 2033.750000 167.500000 C 2033.750000 214.300003 2033.750000 314.700012 2033.750000 421.500000" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-2990041089)" /></g><g transform="translate(1030 410)" class="appendix-icon"><title>///写真確認コントローラー</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1390 410)" class="appendix-icon"><title>カメラのページ</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2159 410)" class="appendix-icon"><title>従業員情報管理者</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2148 84)" class="appendix-icon"><title>従業員情報サービス</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1817 410)" class="appendix-icon"><title>///</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2532 84)" class="appendix-icon"><title>///従業員の写真コントローラー</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2842 84)" class="appendix-icon"><title>autoDisposeProvider</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1157 805)" class="appendix-icon"><title>///写真確認ページ</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1498 827)" class="appendix-icon"><title>///カメラとプレビュー写真</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1901 820)" class="appendix-icon"><title>社員情報ページ</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2623 357)" class="appendix-icon"><title>従業員情報本体</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><mask id="d2-2990041089" maskUnits="userSpaceOnUse" x="-101" y="-112" width="3077" height="1275">
<rect x="-101" y="-112" width="3077" height="1275" fill="white"></rect>
<rect x="306.000000" y="-12.000000" width="83" height="36" fill="rgba(0,0,0,0.75)"></rect>
<rect x="773.500000" y="448.500000" width="234" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1144.500000" y="448.500000" width="223" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1931.500000" y="448.500000" width="205" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1941.500000" y="122.500000" width="184" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1504.500000" y="448.500000" width="290" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2262.500000" y="122.500000" width="247" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2646.500000" y="122.500000" width="173" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1006.000000" y="910.500000" width="134" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1391.000000" y="910.500000" width="90" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1780.000000" y="910.500000" width="104" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="2500.500000" y="448.000000" width="106" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="178.000000" y="334.000000" width="49" height="31" fill="rgba(0,0,0,0.75)"></rect>
<rect x="506.000000" y="34.000000" width="70" height="31" fill="rgba(0,0,0,0.75)"></rect>
<rect x="94.000000" y="448.500000" width="50" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="260.500000" y="448.500000" width="62" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="534.500000" y="122.500000" width="4" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="532.500000" y="448.500000" width="9" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="455.000000" y="282.000000" width="35" height="21" fill="black"></rect>
<rect x="519.000000" y="285.000000" width="36" height="21" fill="black"></rect>
<rect x="582.000000" y="280.000000" width="44" height="21" fill="black"></rect>
</mask></svg></svg>