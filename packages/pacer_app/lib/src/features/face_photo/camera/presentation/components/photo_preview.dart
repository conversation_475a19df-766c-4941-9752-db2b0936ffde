import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../localization/app_localizations_context.dart';
import '../../../../../routing/app_router.dart';
import '../../../photo_confirm/presentation/routing/photo_confirm_route.dart';

///写真のプレビュー
class PhotoPreview extends ConsumerWidget {
  ///初期化
  const PhotoPreview({super.key, this.fileImage, required this.retakePhoto});

  ///写真
  final XFile? fileImage;

  /// 再撮影
  final VoidCallback retakePhoto;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        Expanded(
          child: Image.file(
            File(fileImage?.path ?? ''),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(
                        size: 40,
                        Icons.close,
                        color: Colors.red,
                      ),
                    ),
                    Text(
                      context.loc.cancel,
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  children: [
                    IconButton(
                      onPressed: retakePhoto,
                      icon: const Icon(
                        size: 40,
                        Icons.refresh,
                        color: Colors.blue,
                      ),
                    ),
                    Text(
                      context.loc.retake,
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  children: [
                    IconButton(
                      onPressed: () {
                        const PhotoConfirmRoute().go(context);
                      },
                      icon: const Icon(
                        size: 40,
                        Icons.done,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      context.loc.confirm,
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
