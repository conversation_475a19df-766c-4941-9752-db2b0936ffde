// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'camera_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cameraPageControllerHash() => r'a32fccb6f9efadb8b96e5b10d97dfd829a8e5f0e';

/// カメラのページ
///
/// Copied from [CameraPageController].
@ProviderFor(CameraPageController)
final cameraPageControllerProvider = AutoDisposeNotifierProvider<CameraPageController, AsyncValue<XFile?>>.internal(
  CameraPageController.new,
  name: r'cameraPage<PERSON>ontrollerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$cameraPageControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CameraPageController = AutoDisposeNotifier<AsyncValue<XFile?>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
