import 'package:camera/camera.dart';
import 'package:flutter/material.dart';

import '../../../../../localization/app_localizations_context.dart';
import '../../config/image_type.dart';

/// カメラ本体
class CameraBody extends StatefulWidget {
  /// 初期化
  const CameraBody({super.key, required this.takePhoto});

  /// 写真を更新
  final ValueChanged<XFile?> takePhoto;
  @override
  State<CameraBody> createState() => _CameraBodyState();
}

class _CameraBodyState extends State<CameraBody> with WidgetsBindingObserver {
  ///写真の最大サイズ：41MBぐらい
  static const maxPhotoSize = 41943040;
  CameraController? controller;
  XFile? file;

  Future<void> _initializeCameraController() async {
    controller = CameraController(
      const CameraDescription(
        name: '0',
        lensDirection: CameraLensDirection.back,
        sensorOrientation: 0,
      ),
      ResolutionPreset.low,
      imageFormatGroup: const ImageType.jpg().imageFormatGroup,
      enableAudio: false,
    );
    await controller?.initialize();
    setState(() {});
    controller!.addListener(() {
      if (controller!.value.hasError) {
        throw Exception(controller!.value.errorDescription);
      }
    });
  }

  Future<void> takePicture() async {
    if (controller == null || !controller!.value.isInitialized) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(context.loc.startCamera)));
    }

    file = await controller!.takePicture();
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _initializeCameraController();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      controller = null;
    } else if (state == AppLifecycleState.resumed) {
      _initializeCameraController();
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return switch (controller) {
      final controller? => Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Center(child: CameraPreview(controller)),
            ),
            InkWell(
              onTap: () async {
                await takePicture();
                if (file case final XFile nonNullFile?) {
                  final lengthImage = await nonNullFile.length();
                  if (lengthImage > maxPhotoSize && mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(context.loc.photoTooLarge),
                      ),
                    );
                  }
                  widget.takePhoto(file);
                }
              },
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Icon(
                  Icons.camera_alt_outlined,
                  color: Theme.of(context).colorScheme.primary,
                  size: 50,
                ),
              ),
            ),
          ],
        ),
      _ => const Center(child: CircularProgressIndicator()),
    };
  }
}
