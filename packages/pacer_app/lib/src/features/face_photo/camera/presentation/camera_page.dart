import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'camera_controller.dart';
import 'components/camera_body.dart';
import 'components/photo_preview.dart';

///カメラとプレビュー写真
class CameraPage extends ConsumerWidget {
  ///カメラとプレビュー写真
  const CameraPage({super.key});

  @override
  Widget build(
    BuildContext context,
    WidgetRef ref,
  ) {
    final fileImage = ref.watch(cameraPageControllerProvider);
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: fileImage.value == null
            ? CameraBody(
                takePhoto: (file) {
                  ref.read(cameraPageControllerProvider.notifier).updateFile(file);
                },
              )
            : PhotoPreview(
                fileImage: fileImage.value,
                retakePhoto: () => ref.read(cameraPageControllerProvider.notifier).updateFile(null),
              ),
      ),
    );
  }
}
