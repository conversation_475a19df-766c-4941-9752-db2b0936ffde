Legend: {
  Type: {
    Widget.shape: circle
    Provider.shape: rectangle
  }
  Arrows: {
    "." -> "..": read: {style.stroke-dash: 4}
    "." -> "..": listen
    "." -> "..": watch: {style.stroke-width: 4}
  }
}

photoConfirmControllerProvider: "photoConfirmControllerProvider"
photoConfirmControllerProvider.shape: rectangle
photoConfirmControllerProvider.tooltip: "///写真確認コントローラー"
cameraPageControllerProvider: "cameraPageControllerProvider"
cameraPageControllerProvider.shape: rectangle
cameraPageControllerProvider.tooltip: "カメラのページ"
employeeControllerProvider: "employeeControllerProvider"
employeeControllerProvider.shape: rectangle
employeeControllerProvider.tooltip: "従業員情報管理者"
employeeServiceProvider: "employeeServiceProvider"
employeeServiceProvider.shape: rectangle
employeeServiceProvider.tooltip: "従業員情報サービス"
employeePhotoCheckControllerProvider: "employeePhotoCheckControllerProvider"
employeePhotoCheckControllerProvider.shape: rectangle
employeePhotoCheckControllerProvider.tooltip: "///"
employeePhotoControllerProvider: "employeePhotoControllerProvider"
employeePhotoControllerProvider.shape: rectangle
employeePhotoControllerProvider.tooltip: "///従業員の写真コントローラー"
authRepositoryProvider: "authRepositoryProvider"
authRepositoryProvider.shape: rectangle
authRepositoryProvider.tooltip: "autoDisposeProvider"
PhotoConfirmPage.shape: circle
PhotoConfirmPage.tooltip: "///写真確認ページ"
CameraPage.shape: circle
CameraPage.tooltip: "///カメラとプレビュー写真"
EmployeePage.shape: circle
EmployeePage.tooltip: "社員情報ページ"
EmployeeBody.shape: circle
EmployeeBody.tooltip: "従業員情報本体"

photoConfirmControllerProvider -> PhotoConfirmPage: {style.stroke-width: 4}
cameraPageControllerProvider -> PhotoConfirmPage: {style.stroke-width: 4}
employeeControllerProvider -> PhotoConfirmPage: {style.stroke-width: 4}
photoConfirmControllerProvider -> PhotoConfirmPage
cameraPageControllerProvider -> PhotoConfirmPage: {style.stroke-dash: 4}
photoConfirmControllerProvider -> PhotoConfirmPage: {style.stroke-dash: 4}
cameraPageControllerProvider -> CameraPage: {style.stroke-width: 4}
cameraPageControllerProvider -> CameraPage: {style.stroke-dash: 4}
cameraPageControllerProvider -> CameraPage: {style.stroke-dash: 4}
employeeControllerProvider -> EmployeePage: {style.stroke-width: 4}
employeePhotoCheckControllerProvider -> EmployeePage: {style.stroke-width: 4}
employeeControllerProvider -> EmployeePage
employeePhotoCheckControllerProvider -> EmployeePage
employeePhotoCheckControllerProvider -> EmployeePage: {style.stroke-dash: 4}
employeePhotoControllerProvider -> EmployeeBody: {style.stroke-width: 4}
authRepositoryProvider -> EmployeeBody: {style.stroke-width: 4}
employeePhotoControllerProvider -> EmployeeBody
employeeServiceProvider -> employeeControllerProvider: {style.stroke-dash: 4}
