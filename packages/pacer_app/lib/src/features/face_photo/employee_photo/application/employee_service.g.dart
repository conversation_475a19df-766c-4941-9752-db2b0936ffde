// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$employeeServiceHash() => r'cd11e7f1e275aa9622cebc4e528d329ce298ffd0';

/// 従業員情報サービス
///
/// Copied from [employeeService].
@ProviderFor(employeeService)
final employeeServiceProvider = AutoDisposeProvider<EmployeeService>.internal(
  employeeService,
  name: r'employeeServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$employeeServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef EmployeeServiceRef = AutoDisposeProviderRef<EmployeeService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
