import 'dart:typed_data';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../exceptions/app_exception.dart';
import '../../../authentication/data/auth_repository.dart';
import '../../photo_confirm/data/photo_confirm_repository.dart';
import '../data/employee_repository.dart';
import '../domain/employee.dart';

part 'employee_service.g.dart';

/// 従業員情報サービス
@riverpod
EmployeeService employeeService(EmployeeServiceRef ref) {
  return EmployeeService(ref);
}

/// 従業員情報サービス
class EmployeeService {
  ///初期化
  EmployeeService(this.ref);

  /// riverpod ref
  final Ref ref;

  /// 従業員情報を取得する
  Future<Employee?> getEmployee() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    return ref.read(employeeRepositoryProvider).getEmployee(
          appUser: caller,
        );
  }

  ///従業員の写真を取得する
  Future<Uint8List?> getEmployeePhoto() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    return ref.read(employeeRepositoryProvider).getEmployeePhoto(
          appUser: caller,
        );
  }

  /// 写真存在するかとかチェック
  Future<bool> checkPhotoExist() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final employeeMid = await ref.read(photoConfirmRepositoryProvider).getEmployeeMID(user: caller);
    return ref.read(employeeRepositoryProvider).checkPhotoExist(
          appUser: caller,
          employeeMid: employeeMid,
          photoUrl: '',
        );
  }
}
