import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../localization/app_localizations_context.dart';
import '../../../../../utils/async_value_ui.dart';
import '../../../../authentication/data/auth_repository.dart';
import '../../domain/employee.dart';
import '../employee_photo_controller.dart';

/// 従業員情報本体
class EmployeeBody extends ConsumerWidget {
  /// 初期化
  const EmployeeBody({super.key, this.employee});

  /// 従業員情報
  final Employee? employee;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final employeePhoto = ref.watch(employeePhotoProvider);
    final currentUser = ref.watch(authRepositoryProvider).currentUser;
    ref.listen(employeePhotoProvider, (_, state) {
      state.showAlertDialogOnError(context);
    });
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          Row(
            children: [
              SizedBox(
                width: 140,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      context.loc.employeeCodeTitle,
                      textAlign: TextAlign.right,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      context.loc.employeeNameTitle,
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 4),
              IntrinsicWidth(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 4,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.grey,
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(currentUser?.userCode ?? ''),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 4,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.grey,
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(employee?.name ?? ''),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          switch (employeePhoto) {
            AsyncData(:final value, :final isLoading) => Flexible(
                child: isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Container(
                        constraints: const BoxConstraints(maxWidth: 250),
                        child: Image.memory(
                          value ?? Uint8List.fromList([]),
                          fit: BoxFit.contain,
                        ),
                      ),
              ),
            AsyncError() => const SizedBox.shrink(),
            _ => const Center(child: CircularProgressIndicator()),
          },
          const SizedBox(height: 4),
          Text(context.loc.employeeNote),
        ],
      ),
    );
  }
}
