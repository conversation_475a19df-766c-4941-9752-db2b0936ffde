import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../common_widgets/alert_dialogs.dart';
import '../../../../common_widgets/pacer_back_button.dart';
import '../../../../localization/app_localizations_context.dart';
import '../../../../routing/app_router.dart';
import '../../../../utils/async_value_ui.dart';
import '../../camera/presentation/routing/camera_route.dart';
import 'components/employee_app_bar.dart';
import 'components/employee_body.dart';
import 'employee_controller.dart';

/// 社員情報ページ
class EmployeePage extends ConsumerWidget {
  /// 初期化
  const EmployeePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final employee = ref.watch(employeeProvider);
    final employeePhotoCheck = ref.watch(employeePhotoCheckControllerProvider);
    ref
      ..listen(employeeProvider, (_, state) => state.showAlertDialogOnError(context))
      ..listen(
        employeePhotoCheckControllerProvider,
        (_, state) {
          state.showAlertDialogOnError(context);
          switch (state) {
            case AsyncData(:final value):
              if (value == false) {
                const CameraRoute().go(context);
              } else {
                showAlertDialog(
                  context: context,
                  title: context.loc.employeeUploadImage,
                );
              }
            default:
              return;
          }
        },
      );

    return Scaffold(
      appBar: const EmployeeAppBar(),
      body: SafeArea(
        child: switch (employee) {
          AsyncData(:final value) => EmployeeBody(
              employee: value,
            ),
          AsyncError() => const SizedBox.shrink(),
          _ => const Center(child: CircularProgressIndicator())
        },
      ),
      bottomNavigationBar: BottomAppBar(
        child: Stack(
          children: [
            const PacerBackButton(),
            Center(
              child: Ink(
                height: 50,
                decoration: ShapeDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  shape: const CircleBorder(),
                ),
                child: IconButton(
                  onPressed: switch (employee) {
                    AsyncData(:final value) => () {
                        if (value?.isNewEmployee ?? false) {
                          const CameraRoute().go(context);
                          return;
                        }
                        ref.read(employeePhotoCheckControllerProvider.notifier).checkPhotoExist();
                      },
                    _ => null,
                  },
                  icon: switch (employeePhotoCheck) {
                    AsyncLoading() => const CircularProgressIndicator(),
                    _ => Icon(
                        Icons.camera_alt_outlined,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
