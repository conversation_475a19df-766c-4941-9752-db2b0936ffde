// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee_photo_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$employeePhotoHash() => r'25270385c188a14b837fb4ed3c49f8e7485b9ae6';

///従業員の写真
///
/// Copied from [employeePhoto].
@ProviderFor(employeePhoto)
final employeePhotoProvider = AutoDisposeFutureProvider<Uint8List?>.internal(
  employeePhoto,
  name: r'employeePhotoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$employeePhotoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef EmployeePhotoRef = AutoDisposeFutureProviderRef<Uint8List?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
