import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../application/employee_service.dart';
import '../domain/employee.dart';

part 'employee_controller.g.dart';

/// 従業員情報管理
@riverpod
Future<Employee?> employee(EmployeeRef ref) {
  return ref.read(employeeServiceProvider).getEmployee();
}

@riverpod
class EmployeePhotoCheckController extends _$EmployeePhotoCheckController {
  @override
  AsyncValue<bool?> build() {
    return const AsyncData(null);
  }

  /// 写真存在するかとかチェック
  Future<void> checkPhotoExist() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => ref.read(employeeServiceProvider).checkPhotoExist());
  }
}
