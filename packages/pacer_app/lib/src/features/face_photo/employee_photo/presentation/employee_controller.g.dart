// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$employeeHash() => r'0a010c4541db44755447c68e81435b39d1460037';

/// 従業員情報管理
///
/// Copied from [employee].
@Provider<PERSON>or(employee)
final employeeProvider = AutoDisposeFutureProvider<Employee?>.internal(
  employee,
  name: r'employeeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$employeeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef EmployeeRef = AutoDisposeFutureProviderRef<Employee?>;
String _$employeePhotoCheckControllerHash() => r'f6d5e47b2bbd357ce6b9d260b378c076fa46cc89';

/// See also [EmployeePhotoCheckController].
@ProviderFor(EmployeePhotoCheckController)
final employeePhotoCheckControllerProvider =
    AutoDisposeNotifierProvider<EmployeePhotoCheckController, AsyncValue<bool?>>.internal(
  EmployeePhotoCheckController.new,
  name: r'employeePhotoCheckControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$employeePhotoCheckControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EmployeePhotoCheckController = AutoDisposeNotifier<AsyncValue<bool?>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
