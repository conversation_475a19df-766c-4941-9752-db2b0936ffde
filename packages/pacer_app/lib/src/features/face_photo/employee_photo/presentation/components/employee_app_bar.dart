import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../localization/app_localizations_context.dart';

/// フェイス写真
class EmployeeAppBar extends ConsumerWidget implements PreferredSizeWidget {
  /// 初期化
  const EmployeeAppBar({super.key});

  @override
  Size get preferredSize => const Size(double.infinity, kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PacerAppBar(
      title: Text(
        context.loc.employeeTitle,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Theme.of(context).colorScheme.onPrimary),
      ),
      context: context,
    );
  }
}
