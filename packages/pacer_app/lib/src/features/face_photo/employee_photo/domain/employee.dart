import 'package:shinise_core_client/employee_photo/v1/employee_photo.pbgrpc.dart';

///従業員情報
class Employee {
  ///初期化
  const Employee({
    required this.isNewEmployee,
    required this.name,
  });

  ///grpc応答から初期化する
  factory Employee.fromGrpc(GetEmployeeInfoResponse response) {
    /// 新規従業員
    const newEmployeeType = '4';

    return Employee(
      isNewEmployee: response.employeeType == newEmployeeType,
      name: response.employeeName,
    );
  }

  /// 従業員の種類 例: '4'
  final bool isNewEmployee;

  /// 社員名
  final String name;
}
