// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fake_employee_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fakeEmployeeRepositoryHash() => r'0b8fa363584eabc2f78d50afcd25f67c59b50eb0';

/// See also [fakeEmployeeRepository].
@ProviderFor(fakeEmployeeRepository)
final fakeEmployeeRepositoryProvider = Provider<FakeEmployeeRepository>.internal(
  fakeEmployeeRepository,
  name: r'fakeEmployeeRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fakeEmployeeRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FakeEmployeeRepositoryRef = ProviderRef<FakeEmployeeRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
