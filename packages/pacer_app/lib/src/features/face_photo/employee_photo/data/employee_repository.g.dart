// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$employeeRepositoryHash() => r'735247c541363cdd89a62362b3dbaa0a5a738d98';

///従業員マネージャー
///
/// Copied from [employeeRepository].
@ProviderFor(employeeRepository)
final employeeRepositoryProvider = Provider<EmployeeReponsitory>.internal(
  employeeRepository,
  name: r'employeeRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$employeeRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef EmployeeRepositoryRef = ProviderRef<EmployeeReponsitory>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
