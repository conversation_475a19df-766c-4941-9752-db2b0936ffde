import 'dart:typed_data';

import 'package:grpc/grpc.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/employee_photo/v1/employee_photo.pbgrpc.dart';

import '../../../../constants/environment.dart';
import '../../../../exceptions/app_exception.dart';
import '../../../../utils/client_interceptor.dart';
import '../../../authentication/domain/app_user.dart';
import '../../handle_grpc.dart';
import '../domain/employee.dart';

part 'employee_repository.g.dart';

///従業員マネージャー
@Riverpod(keepAlive: true)
EmployeeReponsitory employeeRepository(EmployeeRepositoryRef ref) {
  return EmployeeReponsitory(ref);
}

///従業員マネージャー
class EmployeeReponsitory {
  ///初期化
  EmployeeReponsitory(this.ref);

  ///riverpod ref
  final Ref ref;

  final _employee = const Employee(isNewEmployee: true, name: '');
  final _uri = Env.getApiBaseUrl();

  ///従業員を獲得する
  Employee? get employee => _employee;

  ///従業員を獲得する
  Future<Employee> getEmployee({
    required AppUser appUser,
  }) async {
    final channel = ClientChannel(
      _uri.host,
      port: _uri.port,
    );

    final stub = EmployeePhotoProtoServiceClient(
      channel,
      options: CallOptions(
        timeout: const Duration(seconds: 20),
        metadata: {'certificate': 'n', 'certificate_key': 'n'},
      ),
    );

    try {
      final request = GetEmployeeInfoRequest(employeeCode: appUser.userCode);

      final response = await stub.getEmployeeInfo(request);
      return Employee.fromGrpc(response);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  ///従業員の写真を取得する
  Future<Uint8List> getEmployeePhoto({
    required AppUser appUser,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = EmployeePhotoProtoServiceClient(
      channel,
      options: CallOptions(
        timeout: const Duration(seconds: 20),
        metadata: {'certificate': 'n', 'certificate_key': 'n'},
      ),
    );

    try {
      final request = ImageViewRequest(employeeCode: appUser.userCode);

      final response = await stub.imageView(request);
      return Uint8List.fromList(response.image);
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 写真存在するかとかチェック
  Future<bool> checkPhotoExist({
    required AppUser appUser,
    required String employeeMid,
    required String photoUrl,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final stub = EmployeePhotoProtoServiceClient(
      channel,
      options: CallOptions(timeout: const Duration(seconds: 20)),
      interceptors: [ShiniseInterceptor(caller: appUser)],
    );

    try {
      final request = GetPhotoExistCheckRequest(employeeCode: appUser.userCode);

      return (await stub.getPhotoExistCheck(request)).existPhoto;
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
