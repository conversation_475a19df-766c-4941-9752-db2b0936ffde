import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/consume/v1/consume.pb.dart';

/// 商品
class GoodsHistory extends Equatable {
  /// 標準コンストラクタ
  const GoodsHistory({
    required this.id,
    required this.productName,
    required this.productCode,
    required this.quantity,
    required this.transDate,
    required this.transferDeptCode,
    required this.transferDeptName,
    required this.state,
    required this.recordNo,
  });

  /// Grpcの結果を構築します
  factory GoodsHistory.fromGrpc(GetProdHistoryResponse_ProdHistory goodsInfo) => GoodsHistory(
        id: goodsInfo.id,
        productName: goodsInfo.productName,
        productCode: goodsInfo.productCode,
        quantity: goodsInfo.quantity,
        transDate: goodsInfo.transDate,
        transferDeptCode: goodsInfo.transferDeptCode,
        transferDeptName: goodsInfo.transferDeptName,
        state: goodsInfo.state,
        recordNo: goodsInfo.recNo,
      );

  /// id
  final int id;

  /// 商品名称
  final String productName;

  /// 商品コード
  final String productCode;

  /// 消耗品数量
  final int quantity;

  /// 振替日（e.g: 20230614）
  final int transDate;

  /// 部門コード
  final String transferDeptCode;

  /// 部門名称
  final String transferDeptName;

  /// レコード状態 Status value, api request parameters
  final int state;

  /// レコード番号
  final String recordNo;

  @override
  List<Object?> get props => [
        id,
        productName,
        productCode,
        quantity,
        transDate,
        transferDeptCode,
        transferDeptName,
        state,
        recordNo,
      ];
}
