import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/consume/v1/consume.pb.dart';

/// 商品
class Goods extends Equatable {
  /// 標準コンストラクタ
  const Goods({
    required this.productName,
    required this.specName,
    required this.departmentCode,
    required this.departmentId,
    required this.departmentName,
    required this.brandName,
    required this.productCode,
    required this.cost,
    required this.price,
    required this.janId,
    required this.serverDate,
  });

  /// Grpcの結果を構築します
  factory Goods.fromGrpc(GetProdInfoResponse_ProductInfo goodsInfo) => Goods(
        productName: goodsInfo.productNameRead,
        specName: goodsInfo.specNameRead,
        departmentCode: goodsInfo.departmentCode,
        departmentId: goodsInfo.departmentId,
        departmentName: goodsInfo.departmentName,
        brandName: goodsInfo.brandName,
        productCode: goodsInfo.productCode,
        cost: goodsInfo.cost,
        price: goodsInfo.price,
        janId: goodsInfo.janId,
        serverDate: goodsInfo.opeIymd,
      );

  /// 商品名称
  final String productName;

  /// 規格
  final String specName;

  /// 部門CD
  final int departmentCode;

  /// 部門ID
  final int departmentId;

  /// 部門名
  final String departmentName;

  /// ブランド名
  final String brandName;

  /// 商品コード
  final String productCode;

  /// 原価（税込）（小数点１桁）
  final double cost;

  /// 売価（税込）
  final int price;

  /// janId 基幹用商品ID
  final int janId;

  /// サーバー日付
  final String serverDate;

  @override
  List<Object?> get props => [
        productName,
        specName,
        departmentCode,
        departmentId,
        departmentName,
        brandName,
        productCode,
        cost,
        price,
        janId,
        serverDate,
      ];
}
