import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/consume/v1/consume.pb.dart';
import 'goods.dart';
import 'reason_option.dart';

/// 商品情報取得
class GoodsCorrelation extends Equatable {
  /// 標準コンストラクタ
  const GoodsCorrelation({
    required this.goodsInfo,
    required this.locations,
    required this.usages,
  });

  /// Grpcの結果を構築します
  factory GoodsCorrelation.fromGrpc(
    GetProdInfoResponse_ProductInfo goodsInfo,
    List<GetProdInfoResponse_Location> location,
    List<GetProdInfoResponse_Usage> usage,
  ) =>
      GoodsCorrelation(
        goodsInfo: Goods.fromGrpc(goodsInfo),
        locations: location
            .map(
              (e) => ReasonOption.fromGrpc(
                e.transferDeptCode,
                e.siteCode,
                e.siteName,
              ),
            )
            .toList(),
        usages: usage
            .map(
              (e) => ReasonOption.fromGrpc(
                0,
                e.usedCode,
                e.usedName,
              ),
            )
            .toList(),
      );

  /// 商品ID
  final Goods goodsInfo;

  /// 使用場所
  final List<ReasonOption> locations;

  /// 使用用途
  final List<ReasonOption> usages;

  @override
  List<Object?> get props => [
        goodsInfo,
        locations,
        usages,
      ];
}
