import 'package:equatable/equatable.dart';

/// 理由取得
class ReasonOption extends Equatable {
  /// init
  const ReasonOption({
    required this.code,
    required this.name,
    required this.id,
  });

  /// Grpcの結果を構築します
  factory ReasonOption.fromGrpc(
    int id,
    int code,
    String name,
  ) =>
      ReasonOption(
        id: id,
        code: code,
        name: name,
      );

  /// コード
  final int code;

  /// common reason name
  final String name;

  /// id (option)
  final int id;

  @override
  List<Object?> get props => [
        id,
        code,
        name,
      ];
}
