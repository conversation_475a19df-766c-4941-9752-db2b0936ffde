// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consumable_adjust_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$consumableAdjustServiceHash() => r'f73aa5b38ca69e2f89795ed019ae05e71a629432';

/// 消耗品振替 service
///
/// Copied from [consumableAdjustService].
@ProviderFor(consumableAdjustService)
final consumableAdjustServiceProvider = Provider<ConsumableAdjustService>.internal(
  consumableAdjustService,
  name: r'consumableAdjustServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$consumableAdjustServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ConsumableAdjustServiceRef = ProviderRef<ConsumableAdjustService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
