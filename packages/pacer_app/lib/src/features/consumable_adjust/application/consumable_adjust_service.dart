import 'dart:developer';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/consumable_adjust_repository.dart';
import '../domain/goods.dart';
import '../domain/goods_correlation.dart';
import '../domain/goods_history.dart';

part 'consumable_adjust_service.g.dart';

/// 消耗品振替 service
@Riverpod(keepAlive: true)
ConsumableAdjustService consumableAdjustService(
  ConsumableAdjustServiceRef ref,
) =>
    ConsumableAdjustService(ref);

/// 消耗品振替 service
class ConsumableAdjustService {
  /// init
  ConsumableAdjustService(this.ref);

  /// ref
  final Ref ref;

  /// get authority
  FutureOr<bool> getAuthority() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref.read(ConsumableAdjustRepositoryProvider(caller: caller)).getAuthority(
          storeCode: store.code,
          storeId: store.id,
        );
  }

  /// get goods list
  FutureOr<List<GoodsHistory>> getGoodsList() async {
    ref
      ..onCancel(() => log('cancel: getGoodsList'))
      ..onResume(() => log('resume: getGoodsList'))
      ..onDispose(() => log('dispose: getGoodsList'));

    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref.read(ConsumableAdjustRepositoryProvider(caller: caller)).getGoodsHistory(
          groupWorkId: '0',
          storeCode: store.code,
          storeId: store.id,
        );
  }

  /// get goods info by productCode
  FutureOr<GoodsCorrelation> getGoodsInfo({
    required String productCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref.read(ConsumableAdjustRepositoryProvider(caller: caller)).getGoodsInfo(
          productCode: productCode,
          storeCode: store.code,
          storeId: store.id,
        );
  }

  /// insert goods data
  FutureOr<bool> insertGoods({
    required Goods goodsInfo,
    required int quantity,
    required int useCode,
    required int transferDeptCode,
    required int siteCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref.read(ConsumableAdjustRepositoryProvider(caller: caller)).insertGoods(
          goodsInfo: goodsInfo,
          quantity: quantity,
          useCode: useCode,
          transferDeptCode: transferDeptCode,
          siteCode: siteCode,
          storeCode: store.code,
          storeId: store.id,
        );
  }

  /// delete goods
  FutureOr<bool> deleteGoods({
    required GoodsHistory goods,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final store = caller.clockInStore;

    return ref.read(ConsumableAdjustRepositoryProvider(caller: caller)).deleteGoods(
          id: goods.id,
          transDate: goods.transDate.toString(),
          recNo: goods.recordNo,
          state: goods.state,
          storeCode: store.code,
          storeId: store.id,
        );
  }
}
