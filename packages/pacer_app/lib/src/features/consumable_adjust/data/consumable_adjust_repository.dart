import 'dart:math';

import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/consume/v1/consume.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../../utils/device_ip_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/goods.dart';
import '../domain/goods_correlation.dart';
import '../domain/goods_history.dart';

part 'consumable_adjust_repository.g.dart';

/// 消耗品振替 repository
@Riverpod(keepAlive: true)
ConsumableAdjustRepository consumableAdjustRepository(
  ConsumableAdjustRepositoryRef ref, {
  required AppUser? caller,
}) =>
    ConsumableAdjustRepository(caller: caller);

/// 消耗品振替
class ConsumableAdjustRepository {
  /// init
  ConsumableAdjustRepository({
    required this.caller,
  });

  /// 操作を行うユーザー
  final AppUser? caller;

  final _uri = Env.getApiBaseUrl();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));

  /// 権限取得
  Future<bool> getAuthority({
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = ConsumeServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getAuthority(
        GetAuthorityRequest(
          storeCode: storeCode,
          storeId: int.tryParse(storeId ?? ''),
        ),
      );

      return switch (resp) {
        GetAuthorityResponse(code: == '000') => resp.authority,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 商品情報取得
  Future<GoodsCorrelation> getGoodsInfo({
    required String productCode,
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = ConsumeServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getProdInfo(
        GetProdInfoRequest(
          productCode: productCode,
          storeCode: storeCode,
          storeId: int.tryParse(storeId ?? ''),
        ),
      );

      return switch (resp) {
        GetProdInfoResponse(code: == '000') => GoodsCorrelation.fromGrpc(
            resp.productInfo,
            resp.location,
            resp.usage,
          ),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 消耗品商品情報登録
  Future<bool> insertGoods({
    required Goods goodsInfo,
    required int quantity,
    required int useCode,
    required int transferDeptCode,
    required int siteCode,
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = ConsumeServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.insertProdInfo(
        InsertProdInfoRequest(
          janId: goodsInfo.janId,
          quantity: quantity,
          transferDeptCode: transferDeptCode,
          transferDeptName: '',
          siteCode: siteCode,
          outItGrp: goodsInfo.departmentId,
          costInTax: goodsInfo.cost,
          price: goodsInfo.price,
          systemDate: goodsInfo.serverDate,
          productCode: goodsInfo.productCode,
          useCode: useCode,
          storeCode: storeCode,
          storeId: int.tryParse(storeId ?? ''),
        ),
      );

      return switch (resp) {
        InsertProdInfoResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 消耗品商品一覧検索
  Future<List<GoodsHistory>> getGoodsHistory({
    required String groupWorkId,
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = ConsumeServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.getProdHistory(
        GetProdHistoryRequest(
          groupWorkId: groupWorkId,
          storeCode: storeCode,
          storeId: int.tryParse(storeId ?? ''),
        ),
      );

      return switch (resp) {
        GetProdHistoryResponse(code: == '000') => resp.prodHistory
            .map(
              GoodsHistory.fromGrpc,
            )
            .toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 消耗品商品削除
  Future<bool> deleteGoods({
    required int id,
    required String transDate,
    required String recNo,
    required int state,
    required String storeCode,
    String? storeId,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final user = caller;
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final stub = ConsumeServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
      ],
    );

    try {
      final resp = await stub.deleteProd(
        DeleteProdRequest(
          id: id,
          transDate: transDate,
          recNo: recNo,
          state: state,
          storeCode: storeCode,
          storeId: int.tryParse(storeId ?? ''),
        ),
      );

      return switch (resp) {
        DeleteProdResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}

extension on GrpcError {
  AppException handle() {
    return switch (this) {
      GrpcError(code: StatusCode.notFound) => ProductNotFoundException(),
      GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
      GrpcError(code: StatusCode.invalidArgument) => WrongProductCodeException(),
      GrpcError(code: _) => UnknownException(e.toString()),
    };
  }
}
