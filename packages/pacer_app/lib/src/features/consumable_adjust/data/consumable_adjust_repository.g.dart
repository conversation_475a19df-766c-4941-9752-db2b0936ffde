// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consumable_adjust_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$consumableAdjustRepositoryHash() => r'cb5c3c7da1d40162cfe462535eac5201e6125ec5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// 消耗品振替 repository
///
/// Copied from [consumableAdjustRepository].
@ProviderFor(consumableAdjustRepository)
const consumableAdjustRepositoryProvider = ConsumableAdjustRepositoryFamily();

/// 消耗品振替 repository
///
/// Copied from [consumableAdjustRepository].
class ConsumableAdjustRepositoryFamily extends Family {
  /// 消耗品振替 repository
  ///
  /// Copied from [consumableAdjustRepository].
  const ConsumableAdjustRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'consumableAdjustRepositoryProvider';

  /// 消耗品振替 repository
  ///
  /// Copied from [consumableAdjustRepository].
  ConsumableAdjustRepositoryProvider call({
    required AppUser? caller,
  }) {
    return ConsumableAdjustRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  ConsumableAdjustRepositoryProvider getProviderOverride(
    covariant ConsumableAdjustRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(ConsumableAdjustRepository Function(ConsumableAdjustRepositoryRef ref) create) {
    return _$ConsumableAdjustRepositoryFamilyOverride(this, create);
  }
}

class _$ConsumableAdjustRepositoryFamilyOverride implements FamilyOverride {
  _$ConsumableAdjustRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final ConsumableAdjustRepository Function(ConsumableAdjustRepositoryRef ref) create;

  @override
  final ConsumableAdjustRepositoryFamily overriddenFamily;

  @override
  ConsumableAdjustRepositoryProvider getProviderOverride(
    covariant ConsumableAdjustRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// 消耗品振替 repository
///
/// Copied from [consumableAdjustRepository].
class ConsumableAdjustRepositoryProvider extends Provider<ConsumableAdjustRepository> {
  /// 消耗品振替 repository
  ///
  /// Copied from [consumableAdjustRepository].
  ConsumableAdjustRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => consumableAdjustRepository(
            ref as ConsumableAdjustRepositoryRef,
            caller: caller,
          ),
          from: consumableAdjustRepositoryProvider,
          name: r'consumableAdjustRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$consumableAdjustRepositoryHash,
          dependencies: ConsumableAdjustRepositoryFamily._dependencies,
          allTransitiveDependencies: ConsumableAdjustRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  ConsumableAdjustRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    ConsumableAdjustRepository Function(ConsumableAdjustRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ConsumableAdjustRepositoryProvider._internal(
        (ref) => create(ref as ConsumableAdjustRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  ProviderElement<ConsumableAdjustRepository> createElement() {
    return _ConsumableAdjustRepositoryProviderElement(this);
  }

  ConsumableAdjustRepositoryProvider _copyWith(
    ConsumableAdjustRepository Function(ConsumableAdjustRepositoryRef ref) create,
  ) {
    return ConsumableAdjustRepositoryProvider._internal(
      (ref) => create(ref as ConsumableAdjustRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is ConsumableAdjustRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ConsumableAdjustRepositoryRef on ProviderRef<ConsumableAdjustRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _ConsumableAdjustRepositoryProviderElement extends ProviderElement<ConsumableAdjustRepository>
    with ConsumableAdjustRepositoryRef {
  _ConsumableAdjustRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as ConsumableAdjustRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
