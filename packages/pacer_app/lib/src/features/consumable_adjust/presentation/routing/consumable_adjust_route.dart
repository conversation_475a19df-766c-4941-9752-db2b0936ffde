// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../pages/form/consumable_adjust_page.dart';
import '../pages/list/consumable_adjust_list_page.dart';

class ConsumableAdjustRoute extends GoRouteData {
  const ConsumableAdjustRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const ConsumableAdjustPage();
}

class ConsumableAdjustListRoute extends GoRouteData {
  const ConsumableAdjustListRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const ConsumableAdjustListPage();
}
