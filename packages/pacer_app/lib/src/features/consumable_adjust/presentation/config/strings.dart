// ignore_for_file: public_member_api_docs

/// 消耗品振替/入力
const String kConsumableAdjustTitle = '消耗品振替入力';
const String kReset = '取消';
const String kOverview = '一覧';
const String kNo = 'いいえ';
const String kYes = 'はい';
const String kOk = 'ok';
const String kPleaseSelect = '選択してください';
const String kGoodsName = '商品名';
const String kSpec = '規格';
const String kBrand = 'ブランド';
const String kLocation = '使用場所';
const String kUsages = '使用用途';
const String kNumber = '数量';
const String kJAN = 'JAN';
const String kChangeBeforeDept = '振替先部門';
const String kOutOfWarehouseGoods = '出庫商品名称';

/// 消耗品申請一覧
const String kConsumableAdjustListTitle = '消耗品申請一覧';
const String kDelete = '行削除';
const String kToLastLine = '最終行';
const String kClose = '閉じる';

const String kPleaseInputCode = 'バーコードをスキャンまたは入力してください。';
const String kBackTip = '消耗品振替を中止し、メニューに戻ります。よろしいですか？';
const String kRequestError = '取得に失敗しました';
const String kGetGoodsError = '商品情報を取得する際にエラーが発生しました。';
const String kInputCorrectGoodsCode = '正しい商品を入力してください。';
const String kAuthorityTip = '社員のみ使用可能です。';
const String kDeleteSelectItem = '指定されている行を削除します。よろしいですか？';
const String kNoGoods = '対象商品がありません。';
const String kItHasBeenDelete = '削除しました。';
const String kUnableDeleteError = '削除できません、何らかのエラーが発生しました。';
const String kDeleting = '削除中...';
const String kIsRegisterInfo = '今入力している内容で登録します。よろしいですか？';
const String kPleaseSelectLocation = '使用場所を選択してください。';
const String kPleaseSelectUsage = '使用用途を選択してください。';
const String kPleaseInputNumber = '数量を入力してください。';
const String kInputZeroWarning = '数量0は登録することができません。';
const String kLoginCurrentInfo = '今入力している内容で登録します。よろしいでしょうか?';
