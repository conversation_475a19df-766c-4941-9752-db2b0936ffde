import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../config/strings.dart';
import '../../../widgets/show_alert.dart';

/// 数値入力時の最大文字数
const int maxCheckWeekNumberLength = 5;

int _lastTextLength = 0;

/// number text field
class NumberTextField extends HookConsumerWidget {
  /// constructor
  const NumberTextField({
    super.key,
    required this.inputText,
    required this.onFieldSubmitted,
    this.onChanged,
    this.hintText = '',
    this.autofocus = false,
    this.onPressed,
    this.focusNode,
  });

  /// ヒントテキスト
  final String hintText;

  /// 入力テキスト
  final String inputText;

  /// 入力時のコールバック
  final void Function(String)? onChanged;

  /// submit call back
  final void Function(String)? onFieldSubmitted;

  /// ボタン押下時のコールバック
  final VoidCallback? onPressed;

  /// オートフォーカス
  final bool autofocus;

  /// フォーカスノード
  final FocusNode? focusNode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _lastTextLength = 0;
    final textEditingController = useTextEditingController();
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    useEffect(
      () {
        textEditingController.text = inputText;

        return;
      },
    );

    return Focus(
      child: TextFormField(
        focusNode: focusNode,
        controller: textEditingController,
        keyboardType: TextInputType.number,
        maxLength: maxCheckWeekNumberLength,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        validator: (text) => validateInput(text: text),
        decoration: InputDecoration(
          filled: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          fillColor: colors.button,
          focusColor: colors.button,
          hintText: hintText,
          hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
          counterText: '',
        ),
        onChanged: (_) => changeCheckFormatText(context, textEditingController),
        onTap: () => changeNumber(textEditingController),
        onFieldSubmitted: submit,
        autofocus: autofocus,
      ),
      onFocusChange: (hasFocus) {
        if (!hasFocus) {
          changeFormatText(textEditingController);
        }
      },
    );
  }

  /// 数値に変換する
  void changeNumber(TextEditingController controller) {
    onPressed?.call();
    controller.text = controller.text.replaceAll(RegExp(r'\D'), '');
  }

  /// change check
  void changeCheckFormatText(
    BuildContext context,
    TextEditingController controller,
  ) {
    controller.text = controller.text.replaceAll(RegExp(r'\D'), '');
    _checkInputNumberRepetition(context, controller);
    onChanged?.call(controller.text);
    _lastTextLength = controller.text.length;
  }

  /// 入力値を変換する
  void changeFormatText(TextEditingController controller) {
    onChanged?.call(controller.text);
  }

  /// done
  void submit(String inputValue) {
    final value = inputValue.replaceAll(RegExp(r'\D'), '');
    onFieldSubmitted?.call(value);
  }

  void _checkInputNumberRepetition(
    BuildContext context,
    TextEditingController controller,
  ) {
    final inputValue = controller.text;
    if (inputValue.length <= 1) return;
    if (inputValue.length < _lastTextLength) return;
    final charArray = inputValue.split('');
    if (charArray.last == charArray[charArray.length - 2]) {
      showAlert(
        context,
        '数量は$inputValueで入力しますか？',
        buttons: [
          OutlinedButton(
            onPressed: () {
              context.pop();
              controller.text = inputValue.substring(0, inputValue.length - 1);
            },
            child: const Text(kNo),
          ),
          OutlinedButton(
            onPressed: () => context.pop(),
            child: const Text(kYes),
          ),
        ],
      );
    }
  }

  /// 入力値の正当性をチェックする
  String? validateInput({
    required String? text,
  }) {
    if (text == '') {
      return null;
    }
    if (!isNumeric(text)) {
      return '数字で入力してください';
    }

    return null;
  }

  /// 数値かどうかを判定する
  bool isNumeric(String? value) {
    if (value == null || value.isEmpty) {
      return false;
    }

    return int.tryParse(value) != null;
  }
}
