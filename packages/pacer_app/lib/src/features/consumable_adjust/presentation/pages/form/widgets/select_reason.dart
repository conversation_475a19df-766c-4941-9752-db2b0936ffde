import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/reason_option.dart';
import '../../../config/strings.dart';

/// select widget
class SelectReason extends StatelessWidget {
  /// 標準コンストラクタ
  const SelectReason({
    super.key,
    required this.reasonList,
    required this.reasonCode,
    required this.onChanged,
  });

  /// reason array
  final List<ReasonOption> reasonList;

  /// select reason code
  final int? reasonCode;

  /// click reason func
  final void Function(ReasonOption?) onChanged;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final anchorKey = GlobalKey();
    final reasonDescription = reasonList
            .firstWhereOrNull(
              (e) => e.code == reasonCode,
            )
            ?.name ??
        '';

    return ColoredBox(
      color: colors.button,
      child: TextField(
        key: anchor<PERSON>ey,
        controller: TextEditingController(
          text: '${reasonCode ?? ''}$reasonDescription',
        ),
        onTap: () => showMenu(
          context: context,
          color: colors.button,
          position: RelativeRect.fromLTRB(
            100,
            _getOffsetY(anchorKey),
            80,
            100,
          ),
          items: reasonList
              .map(
                (reason) => PopupMenuItem(
                  onTap: () => onChanged(reason),
                  value: reason.code,
                  child: Wrap(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ConstrainedBox(
                            constraints: const BoxConstraints(minWidth: 30),
                            child: Text(reason.code.toString()),
                          ),
                          const SizedBox(width: 10),
                          Expanded(child: Text(reason.name)),
                        ],
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
        ),
        readOnly: true,
        decoration: InputDecoration(
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colors.primary.withOpacity(0),
            ),
          ),
          contentPadding: const EdgeInsets.all(10),
          hintText: kPleaseSelect,
          hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
          prefixIconConstraints: const BoxConstraints(),
          suffixIcon: const Icon(Icons.arrow_drop_down),
          border: const OutlineInputBorder(),
          errorStyle: const TextStyle(fontSize: 0, height: 0),
        ),
      ),
    );
  }

  double _getOffsetY(GlobalKey anchorKey) {
    final renderObject = anchorKey.currentContext?.findRenderObject();
    if (renderObject == null) {
      return 200;
    }
    final renderBox = renderObject as RenderBox;

    return renderBox.localToGlobal(Offset(0, renderBox.size.height)).dy;
  }
}
