import 'package:flutter/material.dart';

import '../../../domain/goods_history.dart';

/// 消耗品申請一覧表示
class ConsumableAdjustListCell extends StatelessWidget {
  /// 標準コンストラクタ
  const ConsumableAdjustListCell({
    super.key,
    required this.goods,
    required this.isSelected,
    required this.onPressed,
  });

  /// 商品
  final GoodsHistory goods;

  /// 選択されているかどうか
  final bool isSelected;

  /// クリックイベントのコールバック
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final containerColor = isSelected ? colors.primaryContainer : colors.surface;

    final textColor = isSelected ? colors.onPrimaryContainer : colors.onSurface;

    final textTheme = texts.bodyMedium?.copyWith(
      color: textColor,
      height: 2,
    );

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: containerColor,
          border: Border.fromBorderSide(
            BorderSide(
              color: colors.outlineVariant,
            ),
          ),
          borderRadius: const BorderRadius.all(Radius.circular(10)),
        ),
        child: Column(
          children: [
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        goods.productCode,
                        textAlign: TextAlign.center,
                        style: textTheme,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${goods.transferDeptCode}${goods.transferDeptName}',
                      textAlign: TextAlign.center,
                      style: textTheme,
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: colors.outlineVariant,
            ),
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        goods.productName,
                        textAlign: TextAlign.center,
                        style: textTheme,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    child: Text(
                      '${goods.quantity}',
                      textAlign: TextAlign.center,
                      style: textTheme,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
