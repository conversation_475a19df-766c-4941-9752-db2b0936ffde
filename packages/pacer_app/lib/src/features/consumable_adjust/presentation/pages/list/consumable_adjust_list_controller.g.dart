// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consumable_adjust_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$consumableAdjustListHash() => r'375db8173e2746944b960ec481c8a04f5a2bfe71';

/// goods list
///
/// Copied from [ConsumableAdjustList].
@ProviderFor(ConsumableAdjustList)
final consumableAdjustListProvider =
    AutoDisposeAsyncNotifierProvider<ConsumableAdjustList, List<GoodsHistory>>.internal(
  ConsumableAdjustList.new,
  name: r'consumableAdjustListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$consumableAdjustListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConsumableAdjustList = AutoDisposeAsyncNotifier<List<GoodsHistory>>;
String _$selectGoodsStateHash() => r'5bbb7d3b28caa0b9f3197b9feb8548604a7315e1';

/// 选中商品
///
/// Copied from [SelectGoodsState].
@ProviderFor(SelectGoodsState)
final selectGoodsStateProvider = AutoDisposeNotifierProvider<SelectGoodsState, GoodsHistory?>.internal(
  SelectGoodsState.new,
  name: r'selectGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectGoodsState = AutoDisposeNotifier<GoodsHistory?>;
String _$deleteGoodsStateHash() => r'cf5575af09353a44fd0eab2a06688a3f02551afe';

/// 商品の削除
///
/// Copied from [DeleteGoodsState].
@ProviderFor(DeleteGoodsState)
final deleteGoodsStateProvider = AutoDisposeAsyncNotifierProvider<DeleteGoodsState, bool>.internal(
  DeleteGoodsState.new,
  name: r'deleteGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deleteGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteGoodsState = AutoDisposeAsyncNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
