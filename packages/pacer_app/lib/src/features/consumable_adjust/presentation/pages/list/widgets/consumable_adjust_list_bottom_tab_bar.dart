import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../config/strings.dart';
import '../../../widgets/bottom_tab_bar.dart';
import '../../../widgets/outline_round_text_button.dart';
import '../../../widgets/show_alert.dart';
import '../consumable_adjust_list_controller.dart';

/// 消耗品申請一覧 tab bar
class ConsumableAdjustListBottomTabBar extends ConsumerWidget {
  /// constructor
  const ConsumableAdjustListBottomTabBar({
    super.key,
    required this.onScrollToLastLineCallBack,
  });

  /// scroll to end
  final VoidCallback onScrollToLastLineCallBack;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BottomTabBar(
      leading: const PacerBackButton(),
      actions: [
        OutlineRoundTextButton(
          kDelete,
          onPressed: () => _onDeleteClick(context, ref),
        ),
        const SizedBox(width: 8),
        OutlineRoundTextButton(
          kToLastLine,
          onPressed: onScrollToLastLineCallBack,
        ),
      ],
    );
  }

  Future<void> _onDeleteClick(
    BuildContext context,
    WidgetRef ref,
  ) async {
    /// 指定されている行を削除します。よろしいですか？
    showAlert(
      context,
      kDeleteSelectItem,
      buttons: [
        OutlinedButton(
          onPressed: context.pop,
          child: const Text(kNo),
        ),
        OutlinedButton(
          onPressed: () => _deleteSelectedGoods(context, ref),
          child: const Text(kYes),
        ),
      ],
    );
  }

  void _deleteSelectedGoods(
    BuildContext context,
    WidgetRef ref,
  ) {
    context.pop();
    final deletingGoods = ref.read(selectGoodsStateProvider);
    if (deletingGoods == null) return;
    ref.read(deleteGoodsStateProvider.notifier).deleteGoods(deletingGoods);

    /// 削除進度のダイアログを表示
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final deleteGoodsState = ref.watch(deleteGoodsStateProvider);

            return AlertDialog(
              /// 削除中...
              title: const Text(kDeleting),
              content: switch (deleteGoodsState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 削除できません、何らかのエラーが発生しました。
                      _ => kUnableDeleteError,
                    },
                    style: errorTextStyle,
                  ),
                _ => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (deleteGoodsState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}
