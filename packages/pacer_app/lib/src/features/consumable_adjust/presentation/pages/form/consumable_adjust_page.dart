import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../exceptions/app_exception.dart';
import '../../../../../routing/app_router.dart';
import '../../../../../utils/async_util.dart';
import '../../../../../utils/async_value_ui.dart';
import '../../../../../utils/validator.dart';
import '../../../../app/application/global_loading_service.dart';
import '../../../../device/presentation/scan_window.dart';
import '../../config/strings.dart';
import '../../routing/consumable_adjust_route.dart';
import '../../widgets/jan_text_field.dart';
import '../../widgets/show_alert.dart';
import 'consumable_adjust_controller.dart';
import 'widgets/consumable_adjust_bottom_tab_bar.dart';
import 'widgets/consumable_adjust_table.dart';

/// 消耗品振替入力
class ConsumableAdjustPage extends HookConsumerWidget {
  /// init
  const ConsumableAdjustPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    /// JAN入力コントローラー
    final janTextController = useTextEditingController();
    final goodsInfo = ref.watch(inputJanStateProvider);
    final insertDataIsFromBack = useState(false);

    useEffect(
      () {
        delayed(() {
          ref.read(getAuthorityStateProvider.notifier).getAuthority();
        });
        return null;
      },
      [],
    );

    ref
      ..listen(getAuthorityStateProvider, (previous, next) {
        next.showSnackBarOnError(context);
        if (next.hasError) {
          _cleanInputData(ref, janTextController);
          return;
        }
        final isAuthority = next.asData?.value;
        if (isAuthority == null) return;
        if (!isAuthority) {
          showAlert(
            context,
            kAuthorityTip,
            buttons: [
              OutlinedButton(
                onPressed: () {
                  context
                    ..pop()
                    ..pop();
                },
                child: const Text(kOk),
              ),
            ],
          );
        }
      })
      ..listen(inputJanStateProvider, (previous, next) {
        next.showSnackBarOnError(context);
        if (next.hasError) {
          _cleanInputData(ref, janTextController);
          return;
        }
        final code = next.value?.goodsInfo.productCode;
        if (code == null) return;
        janTextController.text = code;
      })
      ..listen(insertDataProvider, (previous, next) {
        next.showSnackBarOnError(context);
        if (next.hasError) {
          _cleanInputData(ref, janTextController);
          return;
        }
        final result = next.asData?.value ?? false;
        if (result) {
          if (insertDataIsFromBack.value) {
            context.pop();
          } else {
            _pushListPage(ref, janTextController);
          }
        }
      });

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop || ref.watch(globalLoadingServiceProvider)) return;
        await _onBackPressed(ref, insertDataIsFromBack);
      },
      child: Scaffold(
        appBar: PacerAppBar(
          context: context,
          title: const Text(kConsumableAdjustTitle),
        ),
        body: ListView(
          padding: const EdgeInsets.symmetric(horizontal: _edge),
          children: [
            const SizedBox(
              height: _lineHeight,
            ),
            JanTextField(
              kJAN,
              controller: janTextController,
              notifyPagePath: '/consumable_adjust',
              onFieldSubmitted: (value) => _onEditingComplete(ref, value, janTextController),
            ),
            const SizedBox(
              height: _lineHeight,
            ),
            switch (goodsInfo) {
              AsyncData(:final value) => value == null
                  ? const Center(
                      /// バーコードをスキャンまたは入力してください。
                      child: Text(kPleaseInputCode),
                    )
                  : ConsumableAdjustTable(
                      productName: value.goodsInfo.productName,
                      brandName: value.goodsInfo.brandName,
                      specName: value.goodsInfo.specName,
                    ),
              AsyncError(:final error) => Text(
                  switch (error) {
                    UnknownException() => error.message,
                    _ => kGetGoodsError,
                  },
                ),
              AsyncLoading() => const SizedBox.shrink(),
            },
          ],
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: ScanFloatingIconButton(
          onScan: (String value) => _onEditingComplete(ref, value, janTextController),
        ),
        bottomNavigationBar: ConsumableAdjustBottomTabBar(
          onBackCallBack: () => _onBackPressed(ref, insertDataIsFromBack),
          onResetCallBack: () => _cleanInputData(ref, janTextController),
          onPushListCallBack: () => _onOverviewClick(ref, janTextController, insertDataIsFromBack),
        ),
      ),
    );
  }

  InputInfoState _checkInputInfoState(WidgetRef ref) {
    final currentProduct = ref.read(inputJanStateProvider).value;
    if (currentProduct == null) return InputInfoState.noInputJan;
    final inputNumber = int.tryParse(ref.read(inputNumberStateProvider));
    final selectUsageInfo = ref.read(selectedUsageReasonCodeStateProvider);
    final selectLocationInfo = ref.read(selectedLocationReasonCodeStateProvider);
    if (inputNumber == null || inputNumber == 0 || selectUsageInfo == null || selectLocationInfo == null) {
      return InputInfoState.inputJanWithNoInfo;
    }

    return InputInfoState.inputJanWithInputAllInfo;
  }

  /// 戻るボタンの処理
  Future<void> _onBackPressed(
    WidgetRef ref,
    ValueNotifier<bool> insertDataIsFromBack,
  ) async {
    switch (_checkInputInfoState(ref)) {
      case InputInfoState.noInputJan:
        _checkShowWarningAlert(
          ref.context,
          kBackTip,
          sureCallback: () {
            ref.context.pop();
          },
        );
      case InputInfoState.inputJanWithNoInfo:
        _checkShowWarningAlert(
          ref.context,
          kBackTip,
          sureCallback: () {
            ref.context.pop();
          },
        );
      case InputInfoState.inputJanWithInputAllInfo:
        _checkShowWarningAlert(
          ref.context,
          kIsRegisterInfo,
          cancelCallback: () {
            ref.context.pop();
          },
          sureCallback: () {
            insertDataIsFromBack.value = true;
            _insertData(ref);
          },
        );
    }
  }

  void _checkShowSpecificWarningAlert(
    WidgetRef ref,
  ) {
    final selectLocationInfo = ref.read(selectedLocationReasonCodeStateProvider);
    if (selectLocationInfo == null) {
      return showAlert(ref.context, kPleaseSelectLocation);
    }

    final selectUsageInfo = ref.read(selectedUsageReasonCodeStateProvider);
    if (selectUsageInfo == null) {
      return showAlert(ref.context, kPleaseSelectUsage);
    }

    final inputNumber = int.tryParse(ref.read(inputNumberStateProvider));
    if (inputNumber == null) return showAlert(ref.context, kPleaseInputNumber);

    if (inputNumber == 0) return showAlert(ref.context, kInputZeroWarning);
  }

  void _checkShowWarningAlert(
    BuildContext ctx,
    String title, {
    VoidCallback? sureCallback,
    VoidCallback? cancelCallback,
  }) {
    showAlert(
      ctx,
      title,
      buttons: [
        OutlinedButton(
          onPressed: () {
            ctx.pop();
            cancelCallback?.call();
          },
          child: const Text(kNo),
        ),
        OutlinedButton(
          onPressed: () {
            ctx.pop();
            sureCallback?.call();
          },
          child: const Text(kYes),
        ),
      ],
    );
  }

  /// 新しいJANコードが入力された際には、入力前のチェックとJANのチェックを行います。
  Future<void> _onEditingComplete(
    WidgetRef ref,
    String value,
    TextEditingController controller,
  ) async {
    log('consumable input scan code value:[$value]');
    final currentProduct = ref.read(inputJanStateProvider).value;
    if (currentProduct != null) {
      final inputInfoState = _checkInputInfoState(ref);
      if (inputInfoState == InputInfoState.inputJanWithNoInfo) {
        _checkShowSpecificWarningAlert(ref);
        return;
      }
    }

    if (_checkProductCodeNumeric(ref, value) && isValidBarcodeLength(value.length)) {
      await ref.read(productCodeProvider.notifier).update(value);
    } else {
      _showSnackBar(ref, kInputCorrectGoodsCode);
      _cleanInputData(ref, controller);
    }
  }

  /// ProductCodeの入力をチェック
  bool _checkProductCodeNumeric(WidgetRef ref, String input) {
    if (input.isEmpty) {
      return true;
    }

    final numericPattern = RegExp(r'^[0-9]+$');

    final result = numericPattern.hasMatch(input);
    if (!result) {
      /// 正しい商品を入力してください。
      _showSnackBar(ref, kInputCorrectGoodsCode);
    }

    return result;
  }

  Future<void> _onOverviewClick(
    WidgetRef ref,
    TextEditingController controller,
    ValueNotifier<bool> insertDataIsFromBack,
  ) async {
    final currentGoods = ref.read(inputJanStateProvider).value;
    final isNeedInsertGoods = _checkInputInfoState(ref) == InputInfoState.inputJanWithInputAllInfo;
    switch (currentGoods) {
      case null:
        _pushListPage(ref, controller);

      case _ when isNeedInsertGoods:
        _checkShowWarningAlert(
          ref.context,
          kLoginCurrentInfo,
          cancelCallback: () {
            _cleanInputData(ref, controller);
            _pushListPage(ref, controller);
          },
          sureCallback: () {
            insertDataIsFromBack.value = false;
            _insertData(ref);
          },
        );

      case _ when !isNeedInsertGoods:
        _checkShowSpecificWarningAlert(ref);
    }
  }

  void _pushListPage(WidgetRef ref, TextEditingController controller) {
    _cleanInputData(ref, controller);
    const ConsumableAdjustListRoute().go(ref.context);
  }

  void _insertData(WidgetRef ref) {
    ref.read(globalLoadingServiceProvider.notifier).wrap(
          ref.read(insertDataProvider.notifier).insertData(),
        );
  }

  void _cleanInputData(WidgetRef ref, TextEditingController controller) {
    controller.text = '';
    ref.read(inputJanStateProvider.notifier).clear();
    ref.read(productCodeProvider.notifier).clear();
    ref.read(selectedUsageReasonCodeStateProvider.notifier).clear();
    ref.read(selectedLocationReasonCodeStateProvider.notifier).clear();
    ref.read(inputNumberStateProvider.notifier).clear();
  }

  /// SnackBarを表示する
  bool _showSnackBar(WidgetRef ref, String text) {
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(ref.context).colorScheme.error,
        content: Text(text),
      ),
    );

    return false;
  }
}

/// Input information state
enum InputInfoState {
  /// 0：no input jan
  noInputJan(value: 0),

  /// 1：input jan, no input info(location, usage, number)
  inputJanWithNoInfo(value: 1),

  /// 2：input jan, input info(location, usage, number)
  inputJanWithInputAllInfo(value: 2);

  const InputInfoState({
    required this.value,
  });

  factory InputInfoState.fromValue(int value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => InputInfoState.noInputJan,
    );
  }

  /// value
  final int value;
}
