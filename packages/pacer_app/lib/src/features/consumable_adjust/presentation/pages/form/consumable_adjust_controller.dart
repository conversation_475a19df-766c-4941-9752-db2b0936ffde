import 'dart:developer';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../app/application/global_loading_service.dart';
import '../../../application/consumable_adjust_service.dart';
import '../../../domain/goods_correlation.dart';
import '../../../domain/reason_option.dart';

part 'consumable_adjust_controller.g.dart';

/// get authority
@riverpod
class GetAuthorityState extends _$GetAuthorityState {
  /// build
  @override
  FutureOr<bool?> build() async {
    ref.showGlobalLoading();
    return null;
  }

  /// get authority
  Future<void> getAuthority() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async => ref.read(consumableAdjustServiceProvider).getAuthority(),
    );
  }
}

/// select reason
@riverpod
class SelectedUsageReasonCodeState extends _$SelectedUsageReasonCodeState {
  /// build
  @override
  ReasonOption? build() => null;

  /// update selected reason
  void update(ReasonOption? selectInfo) {
    log('usage select: ${selectInfo?.code}');
    state = selectInfo;
  }

  /// clean selected reason
  void clear() {
    state = null;
  }
}

/// select reason
@riverpod
class SelectedLocationReasonCodeState extends _$SelectedLocationReasonCodeState {
  /// build
  @override
  ReasonOption? build() => null;

  /// update selected reason
  void update(ReasonOption? selectInfo) {
    log('location select: ${selectInfo?.code}');
    state = selectInfo;
  }

  /// clean selected reason
  void clear() {
    state = null;
  }
}

/// jan
@riverpod
class ProductCode extends _$ProductCode {
  /// jan valid length
  static final List<int> toProcessJanCodeLengths = [20, 26];

  /// build
  @override
  String build() {
    return '';
  }

  ///
  Future<void> update(String value) async {
    state = _janHandle(value).replaceFirst(RegExp('^0+'), '');
  }

  /// clear value
  void clear() {
    state = '';
  }

  String _janHandle(String value) {
    if (toProcessJanCodeLengths.contains(value.length)) {
      final result = value.substring(0, 13);
      log('taking the first 13 characters ; $result');

      return result;
    }

    return value;
  }
}

/// input goods number
@riverpod
class InputNumberState extends _$InputNumberState {
  /// init
  @override
  String build() => '';

  /// update number
  void updateNumber(String number) {
    log('input number: $number');
    state = number;
  }

  /// clear
  void clear() {
    state = '';
  }
}

/// insert data
@riverpod
class InsertData extends _$InsertData {
  /// init
  @override
  FutureOr<bool?> build() => null;

  /// insert goods
  FutureOr<void> insertData() async {
    final oldProduct = ref.read(inputJanStateProvider).value;
    if (oldProduct == null) return;
    final selectedUsage = ref.read(selectedUsageReasonCodeStateProvider);
    if (selectedUsage == null) return;
    final selectedLocation = ref.read(selectedLocationReasonCodeStateProvider);
    if (selectedLocation == null) return;
    final inputNumberState = ref.read(inputNumberStateProvider);
    final numberValue = int.tryParse(inputNumberState);
    if (numberValue == null) return;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(consumableAdjustServiceProvider).insertGoods(
              goodsInfo: oldProduct.goodsInfo,
              quantity: numberValue,
              useCode: selectedUsage.code,
              transferDeptCode: selectedLocation.id,
              siteCode: selectedLocation.code,
            );
      },
    );
  }
}

/// jan input
@riverpod
class InputJanState extends _$InputJanState {
  /// build
  @override
  FutureOr<GoodsCorrelation?> build() {
    ref.showGlobalLoading();
    final scanProductCode = ref.watch(productCodeProvider);
    final service = ref.watch(consumableAdjustServiceProvider);

    if (scanProductCode.isEmpty) return null;

    /// oldProductが空の場合、製品情報の初回読み込みを表します。
    /// もしproductがnullの場合、getProductInfoインターフェースを直接使用します。
    final oldProduct = state.value;
    if (oldProduct == null) {
      return service.getGoodsInfo(productCode: scanProductCode);
    }

    return insertDataAndFetchNextProductInfo(oldProduct: oldProduct);
  }

  /// もしnextProductCodeに空が渡された場合、nullを返します。
  Future<GoodsCorrelation?> insertDataAndFetchNextProductInfo({
    required GoodsCorrelation oldProduct,
  }) async {
    final service = ref.watch(consumableAdjustServiceProvider);
    final scanProductCode = ref.watch(productCodeProvider);
    final selectedUsage = ref.read(selectedUsageReasonCodeStateProvider);
    if (selectedUsage == null) return null;
    final selectedLocation = ref.read(selectedLocationReasonCodeStateProvider);
    if (selectedLocation == null) return null;

    final inputNumberState = ref.read(inputNumberStateProvider);
    final numberValue = int.tryParse(inputNumberState);
    if (numberValue == null) return null;

    /// upload previous goods
    final isInsertSuccess = await service.insertGoods(
      goodsInfo: oldProduct.goodsInfo,
      quantity: numberValue,
      useCode: selectedUsage.code,
      transferDeptCode: selectedLocation.id,
      siteCode: selectedLocation.code,
    );

    if (isInsertSuccess) {
      /// clean history input data
      ref.read(inputNumberStateProvider.notifier).clear();
      ref.read(selectedUsageReasonCodeStateProvider.notifier).clear();
      ref.read(selectedLocationReasonCodeStateProvider.notifier).clear();

      if (scanProductCode.isEmpty) return null;

      return service.getGoodsInfo(productCode: scanProductCode);
    } else {
      return oldProduct;
    }
  }

  /// insert data
  Future<bool> insertCurrentGoods() async {
    state = const AsyncLoading();

    final oldProduct = state.value;
    if (oldProduct == null) return true;

    final result = await AsyncValue.guard(
      () => insertDataAndFetchNextProductInfo(oldProduct: oldProduct),
    );
    state = result;
    if (result.hasError) {
      return false;
    }

    return true;
  }

  /// clear
  void clear() {
    state = const AsyncData(null);
  }
}
