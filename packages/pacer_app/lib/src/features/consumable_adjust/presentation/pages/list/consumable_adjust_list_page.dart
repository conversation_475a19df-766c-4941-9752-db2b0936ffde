import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../common_widgets/snack_bars.dart';
import '../../../../../exceptions/app_exception.dart';
import '../../config/strings.dart';
import '../../widgets/show_alert.dart';
import 'consumable_adjust_list_cell.dart';
import 'consumable_adjust_list_controller.dart';
import 'widgets/consumable_adjust_list_bottom_tab_bar.dart';

/// 消耗品申請一覧
class ConsumableAdjustListPage extends HookConsumerWidget {
  /// init
  const ConsumableAdjustListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = useScrollController();
    final goodsArray = ref.watch(consumableAdjustListProvider);

    ref
      ..listen(deleteGoodsStateProvider, (previous, next) {
        final result = next.asData?.value;
        if (result != null) {
          /// 削除しました。
          showSnackBar(context, kItHasBeenDelete);
          _refreshPage(context, ref);
        }
      })
      ..listen(consumableAdjustListProvider, (previous, next) {
        final result = next.asData?.value;
        if (result != null && result.isEmpty) {
          showAlert(
            context,
            kNoGoods,
            buttons: [
              OutlinedButton(
                onPressed: () => _onClose(context),
                child: const Text(kOk),
              ),
            ],
          );
        }
      });

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kConsumableAdjustListTitle),
      ),
      body: switch (goodsArray) {
        AsyncData(:final value) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            child: Column(
              children: [
                _Header(),
                Expanded(
                  child: ListView.separated(
                    controller: scrollController,
                    itemBuilder: (context, index) {
                      final cellInfo = value[index];

                      return Consumer(
                        builder: (ctx, ref, _) {
                          final selectedGoods = ref.watch(selectGoodsStateProvider);

                          return ConsumableAdjustListCell(
                            goods: cellInfo,
                            isSelected: selectedGoods == cellInfo,
                            onPressed: () => ref.read(selectGoodsStateProvider.notifier).update(cellInfo),
                          );
                        },
                      );
                    },
                    separatorBuilder: (context, _) => const SizedBox(
                      height: 12,
                    ),
                    itemCount: value.length,
                  ),
                ),
              ],
            ),
          ),
        AsyncError(:final error) => Text(
            switch (error) {
              UnknownException() => error.message,

              /// 取得に失敗しました
              _ => kRequestError,
            },
          ),
        _ => const Center(
            child: CircularProgressIndicator(),
          ),
      },
      bottomNavigationBar: ConsumableAdjustListBottomTabBar(
        onScrollToLastLineCallBack: () => _scrollToLastLine(scrollController, ref),
      ),
    );
  }

  void _scrollToLastLine(ScrollController scrollController, WidgetRef ref) {
    final goodsArray = ref.read(consumableAdjustListProvider).value;
    if (goodsArray == null) return;

    scrollController.animateTo(
      scrollController.position.maxScrollExtent,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOut,
    );
    ref.read(selectGoodsStateProvider.notifier).update(goodsArray.last);
  }

  void _onClose(BuildContext context) {
    /// close alert
    context.pop();

    /// close page
    Navigator.of(context).pop();
  }

  void _refreshPage(BuildContext context, WidgetRef ref) {
    /// close alert
    context.pop();
    ref.read(selectGoodsStateProvider.notifier).clear();
    ref.read(consumableAdjustListProvider.notifier).reload();
  }
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyMedium?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Container(
      decoration: BoxDecoration(
        color: colors.surfaceContainerHighest,
        border: Border.fromBorderSide(BorderSide(color: colors.outlineVariant)),
      ),
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          IntrinsicHeight(
            child: Row(
              children: [
                /// JAN
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      kJAN,
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),

                /// 振替先部門
                Expanded(
                  flex: 2,
                  child: Text(
                    kChangeBeforeDept,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
          Divider(
            height: 1,
            thickness: 1,
            color: colors.outlineVariant,
          ),
          IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),

                    /// 出庫商品名称
                    child: Text(
                      kOutOfWarehouseGoods,
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  /// 数量
                  child: Text(
                    kNumber,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
