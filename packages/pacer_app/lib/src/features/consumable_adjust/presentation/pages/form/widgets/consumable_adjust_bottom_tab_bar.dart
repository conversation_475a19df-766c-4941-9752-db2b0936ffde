import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../config/strings.dart';
import '../../../widgets/bottom_tab_bar.dart';
import '../../../widgets/outline_round_text_button.dart';

/// 消耗品振替入力 tab bar
class ConsumableAdjustBottomTabBar extends ConsumerWidget {
  /// constructor
  const ConsumableAdjustBottomTabBar({
    super.key,
    required this.onBackCallBack,
    required this.onResetCallBack,
    required this.onPushListCallBack,
  });

  /// back
  final VoidCallback onBackCallBack;

  /// reset page
  final VoidCallback onResetCallBack;

  /// push list page
  final VoidCallback onPushListCallBack;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;

    return BottomTabBar(
      leading: TextButton(
        onPressed: onBackCallBack,
        child: Text(
          '終了',
          style: texts.titleMedium,
        ),
      ),
      actions: [
        OutlineRoundTextButton(
          kReset,
          onPressed: onResetCallBack,
        ),
        const SizedBox(width: 8),
        OutlineRoundTextButton(
          kOverview,
          onPressed: onPushListCallBack,
        ),
      ],
    );
  }
}
