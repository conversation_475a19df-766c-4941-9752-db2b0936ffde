import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../application/consumable_adjust_service.dart';
import '../../../domain/goods_history.dart';

part 'consumable_adjust_list_controller.g.dart';

/// goods list
@riverpod
class ConsumableAdjustList extends _$ConsumableAdjustList {
  @override
  FutureOr<List<GoodsHistory>> build() async {
    return ref.watch(consumableAdjustServiceProvider).getGoodsList();
  }

  /// upload
  void reload() {
    state = const AsyncValue.loading();

    ref.invalidateSelf();
  }
}

/// 选中商品
@riverpod
class SelectGoodsState extends _$SelectGoodsState {
  /// init
  @override
  GoodsHistory? build() {
    final goodsArray = ref.watch(consumableAdjustListProvider).asData?.value ?? [];

    return goodsArray.isEmpty ? null : goodsArray.first;
  }

  /// update selected goods
  Future<void> update(GoodsHistory? goods) async {
    log('scrap reason select: $goods');
    state = goods;
  }

  /// clean selected goods
  void clear() {
    state = null;
  }
}

/// 商品の削除
@riverpod
class DeleteGoodsState extends _$DeleteGoodsState {
  /// init
  @override
  FutureOr<bool> build() => false;

  /// delete goods
  FutureOr<void> deleteGoods(GoodsHistory goods) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(consumableAdjustServiceProvider).deleteGoods(goods: goods);
      },
    );
  }
}
