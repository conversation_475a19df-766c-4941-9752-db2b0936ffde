// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consumable_adjust_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getAuthorityStateHash() => r'2b4ab4b42415ae76eb898a50a865e1b3236dc98b';

/// get authority
///
/// Copied from [GetAuthorityState].
@ProviderFor(GetAuthorityState)
final getAuthorityStateProvider = AutoDisposeAsyncNotifierProvider<GetAuthorityState, bool?>.internal(
  GetAuthorityState.new,
  name: r'getAuthorityStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getAuthorityStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetAuthorityState = AutoDisposeAsyncNotifier<bool?>;
String _$selectedUsageReasonCodeStateHash() => r'7f10c366c02da4cfd24f8cae83bf4b7d591b9ed1';

/// select reason
///
/// Copied from [SelectedUsageReasonCodeState].
@ProviderFor(SelectedUsageReasonCodeState)
final selectedUsageReasonCodeStateProvider =
    AutoDisposeNotifierProvider<SelectedUsageReasonCodeState, ReasonOption?>.internal(
  SelectedUsageReasonCodeState.new,
  name: r'selectedUsageReasonCodeStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectedUsageReasonCodeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedUsageReasonCodeState = AutoDisposeNotifier<ReasonOption?>;
String _$selectedLocationReasonCodeStateHash() => r'250b2f64febf875cf1ceed177b1312250140e130';

/// select reason
///
/// Copied from [SelectedLocationReasonCodeState].
@ProviderFor(SelectedLocationReasonCodeState)
final selectedLocationReasonCodeStateProvider =
    AutoDisposeNotifierProvider<SelectedLocationReasonCodeState, ReasonOption?>.internal(
  SelectedLocationReasonCodeState.new,
  name: r'selectedLocationReasonCodeStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$selectedLocationReasonCodeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedLocationReasonCodeState = AutoDisposeNotifier<ReasonOption?>;
String _$productCodeHash() => r'494244d5581d6cd905f1dc08525f6232009b98a1';

/// jan
///
/// Copied from [ProductCode].
@ProviderFor(ProductCode)
final productCodeProvider = AutoDisposeNotifierProvider<ProductCode, String>.internal(
  ProductCode.new,
  name: r'productCodeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$productCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProductCode = AutoDisposeNotifier<String>;
String _$inputNumberStateHash() => r'f1d04959b15d197012792c90b1ed8f0ba9ce9718';

/// input goods number
///
/// Copied from [InputNumberState].
@ProviderFor(InputNumberState)
final inputNumberStateProvider = AutoDisposeNotifierProvider<InputNumberState, String>.internal(
  InputNumberState.new,
  name: r'inputNumberStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputNumberStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputNumberState = AutoDisposeNotifier<String>;
String _$insertDataHash() => r'ecaf1df62dda32c091ef09ff2e6a4390318a98fb';

/// insert data
///
/// Copied from [InsertData].
@ProviderFor(InsertData)
final insertDataProvider = AutoDisposeAsyncNotifierProvider<InsertData, bool?>.internal(
  InsertData.new,
  name: r'insertDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$insertDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InsertData = AutoDisposeAsyncNotifier<bool?>;
String _$inputJanStateHash() => r'ece2fdeeb006e593c021da089d5021571a23c56d';

/// jan input
///
/// Copied from [InputJanState].
@ProviderFor(InputJanState)
final inputJanStateProvider = AutoDisposeAsyncNotifierProvider<InputJanState, GoodsCorrelation?>.internal(
  InputJanState.new,
  name: r'inputJanStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputJanStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputJanState = AutoDisposeAsyncNotifier<GoodsCorrelation?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
