import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/reason_option.dart';
import '../../../config/strings.dart';
import '../consumable_adjust_controller.dart';
import 'number_text_field.dart';
import 'select_reason.dart';

/// 消耗品振替 table
class ConsumableAdjustTable extends ConsumerWidget {
  /// constructor
  const ConsumableAdjustTable({
    super.key,
    required this.productName,
    required this.brandName,
    required this.specName,
  });

  /// 商品名
  final String productName;

  /// ブランド名
  final String brandName;

  /// 規格
  final String specName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    ref.listen(inputNumberStateProvider, (previous, next) {
      log('input number: $next');
    });

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3), // 1列目は全体の50%
        1: FractionColumnWidth(0.7), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(6),
        ),
      ),
      children: [
        TableRow(
          children: [
            /// 商品名
            const _ItemNameText(kGoodsName),
            _ValueText(productName),
          ],
        ),
        TableRow(
          children: [
            /// 規格
            const _ItemNameText(kSpec),
            _ValueText(specName),
          ],
        ),
        TableRow(
          children: [
            /// ブランド
            const _ItemNameText(kBrand),
            _ValueText(brandName),
          ],
        ),
        TableRow(
          children: [
            /// 使用場所
            const _ItemNameText(kLocation),
            Consumer(
              builder: (ctx, ref, _) {
                final selectedInfo = ref.watch(selectedLocationReasonCodeStateProvider);

                return SelectReason(
                  reasonCode: selectedInfo?.code,
                  onChanged: (value) => _onLocationSelected(value, ref),
                  reasonList: ref.read(inputJanStateProvider).value?.locations ?? [],
                );
              },
            ),
          ],
        ),
        TableRow(
          children: [
            /// 使用用途
            const _ItemNameText(kUsages),
            Consumer(
              builder: (ctx, ref, _) {
                final selectedInfo = ref.watch(selectedUsageReasonCodeStateProvider);

                return SelectReason(
                  reasonCode: selectedInfo?.code,
                  onChanged: (value) => _onUsageSelected(value, ref),
                  reasonList: ref.read(inputJanStateProvider).value?.usages ?? [],
                );
              },
            ),
          ],
        ),
        TableRow(
          children: [
            /// 数量
            const _ItemNameText(kNumber),
            ColoredBox(
              color: colors.button,
              child: NumberTextField(
                inputText: '',
                onFieldSubmitted: (value) => ref.read(inputNumberStateProvider.notifier).updateNumber(value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 使用用途を選択
  void _onUsageSelected(ReasonOption? info, WidgetRef ref) {
    ref.read(selectedUsageReasonCodeStateProvider.notifier).update(info);
  }

  /// 使用場所を選択
  void _onLocationSelected(ReasonOption? info, WidgetRef ref) {
    ref.read(selectedLocationReasonCodeStateProvider.notifier).update(info);
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
        ),
      ),
    );
  }
}
