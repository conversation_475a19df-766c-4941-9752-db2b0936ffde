import 'package:flutter/material.dart';

import '../../../../common_widgets/pacer_back_button.dart';

/// BottomTabBar
class BottomTabBar extends StatelessWidget {
  /// init
  const BottomTabBar({
    super.key,
    this.leading,
    this.actions = const [],
  });

  /// leading widget
  final Widget? leading;

  /// buttons
  final List<Widget> actions;

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      padding: const EdgeInsets.only(left: 12, right: 9),
      child: Row(
        children: [
          leading ?? const PacerBackButton(),
          const Spacer(),
          ...actions,
        ],
      ),
    );
  }
}
