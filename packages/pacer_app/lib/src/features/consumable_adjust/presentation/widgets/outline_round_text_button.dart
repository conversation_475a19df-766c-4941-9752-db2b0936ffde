// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

class OutlineRoundTextButton extends StatelessWidget {
  const OutlineRoundTextButton(
    this.text, {
    this.buttonColor,
    this.textColor,
    this.borderColor,
    this.width,
    this.height,
    super.key,
    this.fontSize = 16,
    this.padding,
    this.onPressed,
  });

  final String text;
  final Color? buttonColor;
  final Color? textColor;
  final Color? borderColor;
  final double? width;
  final double? height;
  final double? fontSize;
  final VoidCallback? onPressed;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return SizedBox(
      width: width,
      height: height,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          padding: padding,
          backgroundColor: buttonColor ?? Colors.transparent,
          side: BorderSide(
            color: onPressed != null ? borderColor ?? colors.primary.withOpacity(0.2) : colors.outline.withOpacity(0.3),
          ),
          shape: const StadiumBorder(),
        ),
        child: Text(
          text,
          style: texts.titleMedium?.copyWith(
            fontSize: fontSize,
            color: onPressed != null ? colors.primary : colors.outline,
          ),
        ),
      ),
    );
  }
}
