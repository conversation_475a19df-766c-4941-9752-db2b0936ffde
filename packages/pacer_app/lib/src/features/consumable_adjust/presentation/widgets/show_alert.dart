import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../config/strings.dart';

/// alert
void showAlert(BuildContext context, String title, {List<Widget>? buttons}) {
  buttons ??= [
    OutlinedButton(
      onPressed: context.pop,
      child: const Text(kOk),
    ),
  ];
  showDialog<AlertDialog>(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(title),
      actions: buttons,
    ),
  );
}
