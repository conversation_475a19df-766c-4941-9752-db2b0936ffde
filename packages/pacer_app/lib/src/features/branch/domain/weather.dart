import 'package:clock/clock.dart';
import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/order/v1/v1.dart';

import '../../../constants/weathers.dart';

/// 店舗の天気情報
class StoreWeather extends Equatable {
  /// init
  const StoreWeather({
    required this.storeCode,
    required this.date,
    required this.weather,
    required this.maxTemp,
    required this.minTemp,
  });

  /// fakeデータ
  factory StoreWeather.fake() {
    return StoreWeather(
      storeCode: '0000',
      date: DateTime.now(),
      weather: Weather.cloudy,
      maxTemp: '100',
      minTemp: '0',
    );
  }

  //// レスポンスから変換
  factory StoreWeather.fromResponse(GetSystemDateResponse_Table1_Row e) {
    return StoreWeather(
      storeCode: e.branchCd,
      date: DateTime.tryParse(e.weatherDate) ?? clock.now(),
      weather: Weather.getByTitle(e.weatherName) ?? Weather.cloudy,
      maxTemp: e.temperatureHighest,
      minTemp: e.temperatureLowest,
    );
  }

  /// 店舗コード
  final String storeCode;

  /// 日付
  final DateTime date;

  /// 天気
  final Weather weather;

  /// 最高気温
  final String maxTemp;

  /// 最低気温
  final String minTemp;

  @override
  List<Object?> get props => [storeCode, date, weather, maxTemp, minTemp];
}
