import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';

/// 店舗、支店
class Branch extends Equatable {
  /// init
  const Branch._({
    required this.name,
    required this.code,
    this.id,
    this.positionAtLogin,
  });

  /// インスタンス作成
  /// 明治屋対応
  /// shopcdが5桁　+ 頭が26の場合、後ろ3桁が店舗コード
  /// 例:26012 変換後：12
  factory Branch.build({
    required String name,
    required String code,
    String? id,
    Position? positionAtLogin,
  }) {
    if (code.length == 5 && code.startsWith('26')) {
      code = int.parse(code.substring(2)).toString();
    }
    return Branch._(
      name: name,
      code: code,
      id: id,
      positionAtLogin: positionAtLogin,
    );
  }

  /// fake
  factory Branch.fake() {
    return const Branch._(name: 'ローカルテスト', code: '27', id: '336');
  }

  /// 店舗名
  final String name;

  /// 店舗コード
  final String code;

  /// 店舗ID
  final String? id;

  /// ログインした時の位置情報
  final Position? positionAtLogin;

  /// update id
  Branch setId(String id) {
    return Branch._(
      name: name,
      code: code,
      id: id,
      positionAtLogin: positionAtLogin,
    );
  }

  /// copy
  Branch setPosition(Position position) {
    return Branch._(name: name, code: code, id: id, positionAtLogin: position);
  }

  Branch copyWith({
    String? name,
    String? code,
    String? id,
    Position? positionAtLogin,
  }) {
    return Branch._(
      name: name ?? this.name,
      code: code ?? this.code,
      id: id ?? this.id,
      positionAtLogin: positionAtLogin ?? this.positionAtLogin,
    );
  }

  @override
  List<Object> get props => [name, code];
}
