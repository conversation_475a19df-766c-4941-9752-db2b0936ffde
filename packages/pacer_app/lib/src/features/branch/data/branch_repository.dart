import 'dart:async';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:grpc/grpc.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/order/v1/v1.dart';
import 'package:shinise_core_client/player/v1/v1.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/branch.dart';
import '../domain/weather.dart';
import 'store_response.dart';

part 'branch_repository.g.dart';

/// 店舗や倉庫情報のRepositoryプロバイダー
@Riverpod(keepAlive: true)
BranchRepository branchRepository(BranchRepositoryRef ref) => BranchRepository();

/// 店舗や倉庫情報のRepository
class BranchRepository {
  final _dio = Dio(
    BaseOptions(
      connectTimeout: const Duration(seconds: 5000),
      headers: {'x-api-key': Env.tengenKey},
    ),
  );

  /// 対象の支店を検索
  Future<Branch?> fetchTargetSite({required bool isITG400}) async {
    const limit = Duration(seconds: 20);

    try {
      switch (isITG400) {
        case true:
          final ip = await NetworkInfo().getWifiIP();

          // まずはIPで検索。ヒットしたら返す
          if (ip != null) {
            final ipSite = await _fetchBranchByIP(ip: ip);
            return ipSite;
          }
          return null;

        case false:
          final ip = await NetworkInfo().getWifiIP();
          // ignore: deprecated_member_use
          final position = await Geolocator.getCurrentPosition(timeLimit: limit);

          // まずはIPで検索。ヒットしたら返す
          if (ip != null) {
            final ipSite = await _fetchBranchByIP(ip: ip);

            if (ipSite != null) {
              return ipSite.setPosition(position);
            }

            log('IPに一致する店舗はありませんでした。続いて位置情報での検索を行います');
          }
          final siteByPositon = await _fetchNeabySite(
            latitude: position.latitude,
            longitude: position.longitude,
          );

          return siteByPositon.setPosition(position);
      }
    } catch (e) {
      throw UnknownException(e.toString());
    }
  }

  /// 最寄りの店舗 取得
  Future<Branch> _fetchNeabySite({
    required double latitude,
    required double longitude,
  }) async {
    final uri = '${Env.tengenURL}/api/v1/stores/locations?latitude=$latitude&longitude=$longitude&company_cd=1&limit=1';
    try {
      final body = (await _dio.get<Map<String, Object?>>(uri)).data;

      return switch (body) {
        {
          'data': [
            {
              'store_cd': final int storeCode,
              'store_name': final String storeName,
            },
            ..._
          ]
        } =>
          Branch.build(name: storeName, code: storeCode.toString()),

        /// 不正なデータ形式の場合
        _ => throw UnknownException('不正なデータ形式を受け取りました。')
      };
    } catch (e) {
      ///
      Error.throwWithStackTrace(
        switch (e) {
          AppException => e,
          _ => UnknownException('ネットワークのエラーです。再度お試しください'),
        },
        StackTrace.current,
      );
    }
  }

  /// 店舗一覧 取得
  Future<List<Branch>> listSites() async {
    const uri = '${Env.tengenURL}/api/v1/stores?company_cd=1';

    try {
      final response = await _dio.get<Map<String, dynamic>>(uri);

      if (response.statusCode != 200) {
        throw ParseAuthFailure('status code is not 200');
      }

      final body = StoreResponse.fromJson(response.data ?? {});

      return body.data.map((e) => Branch.build(name: e.name, code: e.code.toString())).toList();
    } catch (e) {
      throw UnknownException(e.toString());
    }
  }

  /// IPで店舗情報を検索する
  /// IP例: ***********
  Future<Branch?> _fetchBranchByIP({required String ip}) async {
    final uri = Env.getApiBaseUrl();

    final channel = ClientChannel(uri.host, port: uri.port);
    log('request ip $ip');
    final subNet = ip.substring(0, ip.lastIndexOf('.'));
    final req = GetInfoRequest(ip: subNet);
    log('fetchBranchByIP request $req');

    final stub = PlayerServiceClient(
      channel,
      options: CallOptions(
        /// 何か知らないとエラーになるから渡すが、本来必要ない
        metadata: {'certificate': 'n', 'certificate_key': 'n'},
        timeout: const Duration(seconds: 20),
      ),
    );

    try {
      final response = await stub.getInfo(req);
      log('fetchBranchByIP response $response');

      return switch (response) {
        /// 成功コードじゃないならエラー
        GetInfoResponse(code: != '000') => throw InternalException(),

        /// 成功コード
        GetInfoResponse(table0: GetInfoResponse_Table0(rows: [final branch, ...])) =>
          Branch.build(name: branch.storeName, code: branch.storeCode),
        _ => null,
      };
    } on Exception catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError() => ParseWeatherFailure(e.toString()),
          _ => ParseWeatherFailure(e.toString()),
        },
        StackTrace.current,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 店舗の天気情報を取得
  Future<List<StoreWeather>> fetchStoreWeathers(
    String storeCode,
    AppUser? caller,
  ) async {
    log('call fetchStoreWeathers');
    final uri = Env.getApiBaseUrl();

    final channel = ClientChannel(uri.host, port: uri.port);
    final stub = OrderServiceClient(
      channel,
      options: CallOptions(timeout: const Duration(seconds: 20)),
      interceptors: [ShiniseInterceptor(caller: caller)],
    );
    try {
      final response = await stub.getSystemDate(GetSystemDateRequest(storeCode: storeCode));
      debugPrint(response.toString());

      return response.table1.rows.map(StoreWeather.fromResponse).toList();
    } catch (e) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
          GrpcError() => ParseWeatherFailure(e.toString()),
          _ => UnknownException(e.toString()),
        },
        StackTrace.current,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
