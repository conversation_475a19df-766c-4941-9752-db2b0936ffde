// ignore_for_file: public_member_api_docs

import 'package:equatable/equatable.dart';

class StoreResponse extends Equatable {
  const StoreResponse({
    required this.message,
    required this.data,
  });

  factory StoreResponse.fromJson(Map<String, dynamic> json) => StoreResponse(
        message: json['message'] as String,
        data: (json['data'] as List<dynamic>).map((e) => StoreData.fromJson(e as Map<String, dynamic>)).toList(),
      );
  final String message;
  final List<StoreData> data;

  @override
  List<Object?> get props => [message, data];
}

class StoreData extends Equatable {
  const StoreData({
    required this.code,
    required this.companyCode,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
  });

  factory StoreData.fromJson(Map<String, dynamic> json) => StoreData(
        code: json['store_cd'] as int,
        companyCode: json['company_cd'] as int,
        name: json['store_name'] as String,
        address: json['address'] as String,
        latitude: json['latitude'] as double,
        longitude: json['longitude'] as double,
      );
  final int code;
  final int companyCode;
  final String name;
  final String address;
  final double latitude;
  final double longitude;

  @override
  List<Object?> get props => [
        code,
        companyCode,
        name,
        address,
        latitude,
        longitude,
      ];
}
