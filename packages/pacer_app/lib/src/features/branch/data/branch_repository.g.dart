// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$branchRepositoryHash() => r'7a7376f635bee669642365e31bbf3c16679a39f7';

/// 店舗や倉庫情報のRepositoryプロバイダー
///
/// Copied from [branchRepository].
@ProviderFor(branchRepository)
final branchRepositoryProvider = Provider<BranchRepository>.internal(
  branchRepository,
  name: r'branchRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$branchRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef BranchRepositoryRef = ProviderRef<BranchRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
