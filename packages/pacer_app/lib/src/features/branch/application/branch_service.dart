import 'dart:async';

import 'package:geolocator/geolocator.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../../device/data/pacer_repository.dart';
import '../../device/domain/extension.dart';
import '../data/branch_repository.dart';
import '../domain/branch.dart';
import '../domain/weather.dart';

part 'branch_service.g.dart';

/// 店舗の天気予報を取得する 2時間キャッシュ
@riverpod
Future<List<StoreWeather>> fetchWeathers(FetchWeathersRef ref) async {
  final caller = ref.watch(authRepositoryProvider).currentUser;
  final branchRepository = ref.watch(branchRepositoryProvider);

  final store = caller?.clockInStore;
  if (store == null) throw UnknownException('店舗未選択');

  final link = ref.keepAlive();
  final timer = Timer(const Duration(hours: 2), link.close);
  ref.onDispose(timer.cancel);

  return branchRepository.fetchStoreWeathers(store.code, caller);
}

/// 現在地
@riverpod
Future<Position> getCurrentPosition(GetCurrentPositionRef ref) async {
  if (ref.read(deviceInfoProvider).requireValue.isITG400) {
    return Position.fromMap({'latitude': 0, 'longitude': 0});
  }

  // ignore: deprecated_member_use
  return Geolocator.getCurrentPosition(timeLimit: const Duration(seconds: 20));
}

/// 支店情報一覧
@Riverpod()
Future<List<Branch>> listSites(ListSitesRef ref) async => ref.read(branchRepositoryProvider).listSites();

/// 現在の支店
/// WifiのIPで検索。なければ、現在地で検索
/// ITG400の場合は、IPで見つからないならそこで終了
@Riverpod()
Future<Branch?> currentSite(CurrentSiteRef ref) async {
  final ip = await NetworkInfo().getWifiIP();
  final isITG400 = ref.read(deviceInfoProvider).requireValue.isITG400;

  if (ref.read(deviceInfoProvider).requireValue.isITG400) {
    if (ip == null) throw UnknownException('店舗Wifiに接続してください');
  }

  return ref.watch(branchRepositoryProvider).fetchTargetSite(isITG400: isITG400);
}
