// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchWeathersHash() => r'439b467bb7fa4daf5bc87307f9fa408cfca54f3d';

/// 店舗の天気予報を取得する 2時間キャッシュ
///
/// Copied from [fetchWeathers].
@ProviderFor(fetchWeathers)
final fetchWeathersProvider = AutoDisposeFutureProvider<List<StoreWeather>>.internal(
  fetchWeathers,
  name: r'fetchWeathersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fetchWeathersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchWeathersRef = AutoDisposeFutureProviderRef<List<StoreWeather>>;
String _$getCurrentPositionHash() => r'7b0df69babbbb4ff6731c7bd0847a0a564d85d08';

/// 現在地
///
/// Copied from [getCurrentPosition].
@ProviderFor(getCurrentPosition)
final getCurrentPositionProvider = AutoDisposeFutureProvider<Position>.internal(
  getCurrentPosition,
  name: r'getCurrentPositionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getCurrentPositionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef GetCurrentPositionRef = AutoDisposeFutureProviderRef<Position>;
String _$listSitesHash() => r'3ff88578b9afcd5408e68c5de5c4b02b89ef197d';

/// 支店情報一覧
///
/// Copied from [listSites].
@ProviderFor(listSites)
final listSitesProvider = AutoDisposeFutureProvider<List<Branch>>.internal(
  listSites,
  name: r'listSitesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$listSitesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ListSitesRef = AutoDisposeFutureProviderRef<List<Branch>>;
String _$currentSiteHash() => r'aa41d1db507519cd91b65772b3180016026b6a6f';

/// 現在の支店
/// WifiのIPで検索。なければ、現在地で検索
/// ITG400の場合は、IPで見つからないならそこで終了
///
/// Copied from [currentSite].
@ProviderFor(currentSite)
final currentSiteProvider = AutoDisposeFutureProvider<Branch?>.internal(
  currentSite,
  name: r'currentSiteProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$currentSiteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentSiteRef = AutoDisposeFutureProviderRef<Branch?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
