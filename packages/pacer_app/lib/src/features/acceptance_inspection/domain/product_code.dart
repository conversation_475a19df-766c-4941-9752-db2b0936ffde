import '../../../utils/string_extensions_itf14.dart';

/// String extension
extension ProductCode on String {
  /// check product code length
  bool isValidProductCodeLength() => (length >= 6 && length <= 18) || length == 20 || length == 26;

  /// verify product code
  bool isValidProductCode() => isValidProductCodeLength() && RegExp(r'^[0-9]+$').hasMatch(this);

  /// try parse product code
  String? productCodeTryParse() {
    if (isValidProductCode()) return productCodeParse();

    return null;
  }

  /// parse product code
  ///
  /// [processNONPLU]がtrueの場合、NONPLUコードであれば、価格部分を0に変換して返却します。
  ///
  /// [processNONPLU]がfalseの場合、NONPLUコードはそのまま返却します。
  // TODO: rename to parseProductCode
  String productCodeParse({bool processNONPLU = true}) {
    final productCode = this;
    return switch (productCode) {
      // 最初の2桁が25の場合後ろから6桁を000000に置き換えて返す
      String(length: 13) when processNONPLU && productCode.startsWith('25') => '${productCode.substring(0, 7)}000000',
      // 商品コードが20桁または26桁の場合は先頭13桁を返す
      String(length: 20 || 26) => productCode.substring(0, 13).productCodeParse(),
      // 商品コードがITF(14桁)の場合は変換して返す
      String(length: 14) => productCode.ean13FromITF14,
      // 13桁入力で先頭が0ではない場合はそのまま返す
      String(length: 13) when !productCode.startsWith('0') => productCode,
      // 上記以外の場合は先頭の0を削除して返す
      _ => int.tryParse(productCode)?.toString() ?? '',
    };
  }
}
