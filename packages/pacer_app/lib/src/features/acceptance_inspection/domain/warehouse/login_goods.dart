import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/transfer_bll/v1/transferbll.pb.dart';

import '../../presentation/utils/utils.dart';

/// 確認登録の伝票明細リスト抽出
class LoginGoods extends Equatable {
  /// 標準コンストラクタ
  const LoginGoods({
    required this.id,
    required this.productCode,
    required this.productNameRead,
    required this.subName,
    required this.quantity,
    required this.receiveNum,
    this.changedReceiveNum,
  });

  /// Grpcの結果を構築します
  factory LoginGoods.fromGrpc(
    GetConfirmLoginProductCdResponse_ConfirmInfo info,
  ) =>
      LoginGoods(
        id: info.id,
        productCode: info.productCode,
        productNameRead: info.productNameRead,
        subName: info.subName,
        quantity: info.quantity,
        receiveNum: info.receiveNum,
        changedReceiveNum: checkDoubleToString(info.receiveNum),
      );

  /// copy
  LoginGoods copyWith({
    String? changedReceiveNum,
  }) {
    return LoginGoods(
      id: id,
      productCode: productCode,
      productNameRead: productNameRead,
      subName: subName,
      quantity: quantity,
      receiveNum: receiveNum,
      changedReceiveNum: changedReceiveNum ?? this.changedReceiveNum,
    );
  }

  /// check input number
  bool isNumberExceedPlannedLimit(String inputNumber) {
    return (double.tryParse(inputNumber) ?? 0.0) > quantity;
  }

  /// 振替テーブルの連番ID
  final int id;

  /// 商品コード
  final String productCode;

  /// 商品名
  final String productNameRead;

  /// 規格
  final String subName;

  /// 納品予定数（小数点１桁）
  final double quantity;

  /// 検品数（小数点１桁）
  final double receiveNum;

  /// 検品数 修正後
  final String? changedReceiveNum;

  @override
  List<Object?> get props => [
        id,
        productCode,
        productNameRead,
        subName,
        quantity,
        receiveNum,
        changedReceiveNum,
      ];
}
