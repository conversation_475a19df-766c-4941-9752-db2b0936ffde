import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// history info
class HistoryInfo extends Equatable {
  /// 標準コンストラクタ
  const HistoryInfo({
    required this.productName,
    required this.specName,
    required this.slipNo,
    required this.deliveryNum,
    required this.lineNo,
    required this.brandName,
    required this.productCode,
  });

  /// Grpcの結果を構築します
  factory HistoryInfo.fromGrpc(
    GetScanResponse_ScanInfo info,
  ) =>
      HistoryInfo(
        productName: info.productName,
        slipNo: info.slipNo,
        specName: info.specName,
        brandName: info.brandName,
        deliveryNum: info.deliveryNum,
        lineNo: info.lineNo,
        productCode: info.productCode,
      );

  /// 商品名称
  final String productName;

  /// 商品code
  final String productCode;

  /// 伝票番号
  final String slipNo;

  /// 規格
  final String specName;

  /// ブランド
  final String brandName;

  /// 納品数
  final int deliveryNum;

  /// 伝票明細番号
  final int lineNo;

  @override
  List<Object?> get props => [
        productName,
        productCode,
        slipNo,
        specName,
        brandName,
        deliveryNum,
        lineNo,
      ];
}
