import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/transfer_bll/v1/transferbll.pb.dart';

/// 伝票番号リスト
class UpdateReceiveNumberParam extends Equatable {
  /// 標準コンストラクタ
  const UpdateReceiveNumberParam({
    required this.id,
    required this.receiveNum,
  });

  /// transform model
  UpdateReceiveNumNewRequest_ReceiveNumberInfo transform() => UpdateReceiveNumNewRequest_ReceiveNumberInfo(
        id: id,
        receiveNum: receiveNum,
      );

  /// 振替テーブルの連番ID
  final int id;

  /// 検品数
  final double receiveNum;

  @override
  List<Object?> get props => [
        id,
        receiveNum,
      ];
}
