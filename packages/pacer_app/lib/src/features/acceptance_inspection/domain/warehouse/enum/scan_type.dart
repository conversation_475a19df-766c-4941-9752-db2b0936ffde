/// 商品存在フラグ
enum ScanType {
  /// 0:入庫対象外
  notSubjectToReceipt(value: 0),

  /// 1:入庫対象
  subjectToReceipt(value: 1),

  /// 2:入庫対象、この商品は伝票番号には存在しません
  subjectUnknown(value: 2);

  const ScanType({
    required this.value,
  });

  factory ScanType.fromValue(int value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => ScanType.notSubjectToReceipt,
    );
  }

  /// value
  final int value;
}
