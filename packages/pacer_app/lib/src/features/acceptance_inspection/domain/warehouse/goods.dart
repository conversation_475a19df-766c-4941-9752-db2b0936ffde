import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/transfer_bll/v1/transferbll.pb.dart';

import '../../presentation/utils/utils.dart';
import 'enum/inspection_status.dart';

/// 商品
class Goods extends Equatable {
  /// 標準コンストラクタ
  const Goods({
    required this.productName,
    required this.specName,
    required this.productColor,
    required this.productSize,
    required this.quantity,
    required this.receiveNum,
    required this.totalSlipCount,
    required this.unInspectedSlipCount,
    required this.inspectionStatus,
    required this.productCode,
    required this.slipCode,
  });

  /// Grpcの結果を構築します
  factory Goods.fromGrpc(
    GetProdInfoResponse goodsInfo,
    String productCode,
    String slipCode,
  ) =>
      Goods(
        productName: goodsInfo.productInfo.first.productName,
        specName: goodsInfo.productInfo.first.subName,
        productColor: goodsInfo.productInfo.first.productColor,
        productSize: goodsInfo.productInfo.first.productSize,
        quantity: goodsInfo.productInfo.first.quantity,
        receiveNum: goodsInfo.productInfo.first.receiveNum,
        totalSlipCount: goodsInfo.productInfo.first.totalSlipCount,
        unInspectedSlipCount: goodsInfo.productInfo.first.uninspectedSlipCount,
        inspectionStatus:
            goodsInfo.productInfo.first.isReceive ? InspectionStatus.wasInspected : InspectionStatus.notInspected,
        productCode: productCode,
        slipCode: slipCode,
      );

  /// check input number
  bool isNumberExceedPlannedLimit(String inputNumber, {bool isAllOne = false}) {
    final inputValue = double.tryParse(inputNumber) ?? 0.0;
    final referenceValue = isAllOne ? inputValue + receiveNum : inputValue;
    return referenceValue > quantity;
  }

  /// prefill value
  String prefillValue() {
    final value = receiveNum > 0 ? receiveNum : quantity;
    return checkDoubleToString(value);
  }

  /// 商品名称
  final String productName;

  /// 商品code
  final String productCode;

  /// slip code
  final String slipCode;

  /// 規格
  final String specName;

  /// カラー
  final String productColor;

  /// サイズ
  final String productSize;

  /// 納品予定数（小数点１桁）
  final double quantity;

  /// 検品数（小数点１桁）
  final double receiveNum;

  /// 検品進捗(伝票明細の総個数)
  final int totalSlipCount;

  /// 検品進捗(検品済明細個数)
  final int unInspectedSlipCount;

  /// 売価（税込）
  final InspectionStatus inspectionStatus;

  @override
  List<Object?> get props => [
        productName,
        productCode,
        slipCode,
        specName,
        productColor,
        productSize,
        quantity,
        receiveNum,
        totalSlipCount,
        unInspectedSlipCount,
        inspectionStatus,
      ];
}
