/// 検品状態
enum ReceiveStatus {
  /// 0：未検品
  notInspected(value: 0),

  /// 1：検品済
  inspectionCompleted(value: 1),

  /// 2：検品確定済
  confirmInspectionCompleted(value: 2);

  const ReceiveStatus({
    required this.value,
  });

  factory ReceiveStatus.fromValue(int value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => ReceiveStatus.notInspected,
    );
  }

  /// value
  final int value;
}
