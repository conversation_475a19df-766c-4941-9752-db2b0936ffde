import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/transfer_bll/v1/transferbll.pb.dart';

import 'enum/receive_status.dart';
import 'enum/ships_type.dart';

/// 伝票番号が検品済みかどうかを判断する
class SlipCodeJudge extends Equatable {
  /// 標準コンストラクタ
  const SlipCodeJudge({
    required this.receiveStatus,
    required this.shipsType,
  });

  /// Grpcの結果を構築します
  factory SlipCodeJudge.fromGrpc(GetSlipNoJudgeResponse info) => SlipCodeJudge(
        receiveStatus: ReceiveStatus.fromValue(info.receiveFlag.value),
        shipsType: ShipsType.fromValue(info.shipsType),
      );

  /// 検品状態 0:未検品 1:検品済 2:検品確定済
  final ReceiveStatus receiveStatus;

  /// 振替タイプコード shipsType＝1 TLS便 shipsType＝2社員便
  final ShipsType shipsType;

  @override
  List<Object?> get props => [
        receiveStatus,
        shipsType,
      ];
}
