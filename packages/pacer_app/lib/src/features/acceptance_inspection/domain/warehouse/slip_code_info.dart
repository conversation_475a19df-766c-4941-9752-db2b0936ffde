import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/transfer_bll/v1/transferbll.pb.dart';

import 'enum/receive_status.dart';
import 'enum/ships_type.dart';

/// JANより 伝票番号情報を取る
class SlipCodeInfo extends Equatable {
  /// 標準コンストラクタ
  const SlipCodeInfo({
    required this.storeCode,
    required this.storeName,
    required this.slipNumber,
    required this.shipsType,
    required this.shipsDate,
    required this.shipsTypeCode,
    required this.receiveFlag,
  });

  /// Grpcの結果を構築します
  factory SlipCodeInfo.fromGrpc(ShipsGetSlipNoResponse_SlipNumberInfo info) => SlipCodeInfo(
        storeCode: info.storeCode,
        storeName: info.storeName,
        slipNumber: info.slipNumber,
        shipsType: info.shipsType,
        shipsDate: info.shipsDate.length > 10 ? info.shipsDate.substring(0, 10) : info.shipsDate,
        shipsTypeCode: ShipsType.fromValue(info.shipsTypeCode),
        receiveFlag: ReceiveStatus.fromValue(info.receiveFlag.value),
      );

  /// Grpcの結果を構築します
  factory SlipCodeInfo.fromGetSlipNoScanGrpc(
    ShipsGetSlipNoScanResponse info,
  ) =>
      SlipCodeInfo(
        storeCode: info.storeCode,
        storeName: info.storeName,
        slipNumber: info.slipNumber.toString(),
        shipsType: info.shipsStyle,
        shipsDate: info.shipsDate.length > 10 ? info.shipsDate.substring(0, 10) : info.shipsDate,
        shipsTypeCode: ShipsType.fromValue(info.shipsTypeInt),
        receiveFlag: ReceiveStatus.fromValue(info.receiveFlag.value),
      );

  /// 入庫先の店舗コード（出庫店舗）
  final String storeCode;

  /// 入庫先の店舗名
  final String storeName;

  /// 伝票番号
  final String slipNumber;

  /// 振替タイプ
  final String shipsType;

  /// 振替日
  final String shipsDate;

  /// 振替タイプコード
  final ShipsType shipsTypeCode;

  /// 検品状態 0:未検品 1:検品済 2:検品確定済
  final ReceiveStatus receiveFlag;

  @override
  List<Object?> get props => [
        storeCode,
        storeName,
        slipNumber,
        shipsType,
        shipsDate,
        shipsTypeCode,
        receiveFlag,
      ];
}
