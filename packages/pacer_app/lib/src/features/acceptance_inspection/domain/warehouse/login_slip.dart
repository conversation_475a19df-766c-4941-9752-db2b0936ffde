import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/transfer_bll/v1/transferbll.pb.dart';

import '../../presentation/config/strings.dart';

/// 確認登録の伝票番号リスト抽出
class LoginSlip extends Equatable {
  /// 標準コンストラクタ
  const LoginSlip({
    required this.slipNumber,
    required this.confirmFlagString,
    required this.isCanSelect,
  });

  /// Grpcの結果を構築します
  factory LoginSlip.fromGrpc(GetConfirmLoginSlipResponse_ConfirmInfo info) => LoginSlip(
        slipNumber: info.slipNumber,
        confirmFlagString: info.confirmFlag,
        isCanSelect: info.confirmFlag.trim() != kUnfinished,
      );

  /// 伝票番号
  final String slipNumber;

  /// 伝票状態（未完了、済(差異)、済）
  final String confirmFlagString;

  /// 選択することは可能ですか
  final bool isCanSelect;

  @override
  List<Object?> get props => [
        slipNumber,
        confirmFlagString,
        isCanSelect,
      ];
}
