import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/transfer_bll/v1/transferbll.pb.dart';

import 'enum/scan_type.dart';
import 'slip_code_info.dart';

/// 伝票番号より 商品基本情報取得
class ScanInfo extends Equatable {
  /// 標準コンストラクタ
  const ScanInfo({
    required this.slipInfo,
    required this.scanFlag,
  });

  /// Grpcの結果を構築します
  factory ScanInfo.fromGrpc(
    ShipsGetSlipNoScanResponse info,
  ) =>
      ScanInfo(
        slipInfo: SlipCodeInfo.fromGetSlipNoScanGrpc(info),
        scanFlag: ScanType.fromValue(info.scanFlag.value),
      );

  /// 商品
  final SlipCodeInfo slipInfo;

  /// 商品存在フラグ 0:入庫対象外 1:入庫対象 2:入庫対象、この商品は伝票番号には存在しません
  final ScanType scanFlag;

  @override
  List<Object?> get props => [
        slipInfo,
        scanFlag,
      ];
}
