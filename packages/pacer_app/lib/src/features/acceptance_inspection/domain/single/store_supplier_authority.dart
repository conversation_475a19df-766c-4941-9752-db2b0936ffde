import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 当日日付、時間、週取得
class StoreAndSupplierAuthority extends Equatable {
  /// 標準コンストラクタ
  const StoreAndSupplierAuthority({
    required this.storeLimit,
    required this.supplierLimit,
  });

  /// Grpcの結果を構築します
  factory StoreAndSupplierAuthority.fromGrpc(
    GetStoreAndVenderCategoryResponse resp,
  ) =>
      StoreAndSupplierAuthority(
        storeLimit: resp.storeLimit,
        supplierLimit: resp.venderLimit,
      );

  /// 店舗制御
  /// true⇒店舗制御テーブルを参照しない、全店舗　新流れ
  /// false⇒店舗制御テーブルを参照する
  final bool storeLimit;

  /// ベンダー制御
  /// true⇒ベンダー制御テーブルを参照しない、全てベンダー　新流れ;
  /// false⇒ベンダー制御テーブルを参照する
  final bool supplierLimit;

  @override
  List<Object?> get props => [
        storeLimit,
        supplierLimit,
      ];
}
