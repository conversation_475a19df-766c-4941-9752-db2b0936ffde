import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 同じ商品で伝票番号データ
class SelectSlipInfo extends Equatable {
  /// 標準コンストラクタ
  const SelectSlipInfo({
    required this.slipNo,
    required this.lineNo,
    required this.productCode,
    required this.productName,
    required this.orderNum,
    required this.asnNum,
    required this.deliveryNum,
    required this.isInspected,
    required this.deliveryDate,
    required this.vendorCode,
  });

  /// Grpcの結果を構築します
  factory SelectSlipInfo.fromGrpc(GetSlipInfoDataResponse_SlipInfo info) {
    final date = DateTime.tryParse(info.deliveryDate);
    return SelectSlipInfo(
      slipNo: info.slipNo,
      lineNo: info.lineNo,
      productCode: info.productCode,
      productName: info.productName,
      orderNum: info.orderNum,
      asnNum: info.asnNum,
      deliveryNum: info.deliveryNum,
      isInspected: info.isInspected,
      deliveryDate: '${date?.month}月${date?.day}日',
      vendorCode: info.vendorCode,
    );
  }

  /// 伝票番号
  final String slipNo;

  /// 伝票明細番号
  final int lineNo;

  /// 商品コード
  final String productCode;

  /// 商品名
  final String productName;

  /// 発注数
  final double orderNum;

  /// 納品予定数（ASN数）
  final double asnNum;

  /// 納品数
  final double deliveryNum;

  /// 一時テーブルの検品フラグ　false：未検品　true：検品済み
  final bool isInspected;

  /// 納品予定日
  final String deliveryDate;

  /// ベンダーコード
  final String vendorCode;

  @override
  List<Object?> get props => [
        slipNo,
        lineNo,
        productCode,
        productName,
        orderNum,
        asnNum,
        deliveryNum,
        isInspected,
        deliveryDate,
        vendorCode,
      ];
}
