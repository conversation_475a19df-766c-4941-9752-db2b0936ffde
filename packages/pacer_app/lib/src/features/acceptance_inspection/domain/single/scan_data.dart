import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 訂正登録画面の商品取得
class ScanData extends Equatable {
  /// 標準コンストラクタ
  const ScanData({
    required this.id,
    required this.slipNo,
    required this.lineNo,
    required this.productCode,
    required this.productName,
    required this.specName,
    required this.brandName,
    required this.colorAndSize,
    required this.orderNum,
    required this.asnNum,
    required this.deliveryNum,
    required this.packNum,
    required this.caseNum,
    required this.isDeleted,
    required this.isBread,
  });

  /// Grpcの結果を構築します
  factory ScanData.fromGrpc(GetScanDataResponse_ScanData info) => ScanData(
        id: info.id,
        slipNo: info.slipNo,
        lineNo: info.lineNo,
        productCode: info.productCode,
        productName: info.productName,
        specName: info.specName,
        brandName: info.brandName,
        colorAndSize: info.colorAndSize,
        orderNum: info.orderNum,
        asnNum: info.asnNum,
        deliveryNum: info.deliveryNum,
        packNum: info.packNum,
        caseNum: info.caseNum,
        isDeleted: info.isDeleted,
        isBread: info.isBread,
      );

  /// ID
  final String id;

  /// 伝票番号
  final String slipNo;

  /// 伝票明細番号
  final int lineNo;

  /// 商品コード
  final String productCode;

  /// 商品名
  final String productName;

  /// 規格
  final String specName;

  /// ブランド
  final String brandName;

  /// カラー　と　サイズ
  final String colorAndSize;

  /// 発注数
  final double orderNum;

  /// 納品予定数（ASN数）
  final double asnNum;

  /// 納品数
  final double deliveryNum;

  /// 入数
  final int packNum;

  /// ケース数
  final int caseNum;

  /// 削除フラグ　false：未削除　true：削除
  final bool isDeleted;

  /// ベンダーフラグ　true：パン　false：通常
  final bool isBread;

  @override
  List<Object?> get props => [
        id,
        slipNo,
        lineNo,
        productCode,
        productName,
        specName,
        brandName,
        colorAndSize,
        orderNum,
        asnNum,
        deliveryNum,
        packNum,
        caseNum,
        isDeleted,
        isBread,
      ];
}
