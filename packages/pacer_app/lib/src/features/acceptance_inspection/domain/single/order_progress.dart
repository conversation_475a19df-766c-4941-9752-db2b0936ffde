import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 検品中の進捗
class OrderProgress extends Equatable {
  /// 標準コンストラクタ
  const OrderProgress({
    required this.orderCount,
    required this.scanCount,
    required this.scanId,
  });

  /// Grpcの結果を構築します
  factory OrderProgress.fromGrpc(
    GetOrderScanCountResponse info,
  ) =>
      OrderProgress(
        orderCount: info.orderCount,
        scanCount: info.scanCount,
        scanId: info.scanId,
      );

  /// 一時テーブルの検品用のデータ件数
  final int orderCount;

  /// 一時テーブルのporscantemp_XXX_XXX_XXX_XXXの最大ID+1
  final int scanCount;

  /// ID
  final int scanId;

  @override
  List<Object?> get props => [
        orderCount,
        scanCount,
        scanId,
      ];
}
