import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// request params
class UpdateOrderParam extends Equatable {
  /// 標準コンストラクタ
  const UpdateOrderParam({
    required this.specName,
    required this.brandName,
    required this.colorAndSize,
    required this.packNum,
    required this.productName,
    required this.orderNum,
    required this.deliveryNum,
  });

  /// transform model
  UpdateOrderDataRequest_Info transform() => UpdateOrderDataRequest_Info(
        specName: specName,
        brandName: brandName,
        colorAndSize: colorAndSize,
        packNum: packNum,
        productName: productName,
        orderNum: orderNum,
        deliveryNum: deliveryNum,
      );

  /// 規格
  final String specName;

  /// ブランド
  final String brandName;

  /// カラーとサイズ
  final String colorAndSize;

  /// 入数
  final int packNum;

  /// 商品名
  final String productName;

  /// 発注数
  final int orderNum;

  /// 納品予定数
  final int deliveryNum;

  @override
  List<Object?> get props => [
        specName,
        brandName,
        colorAndSize,
        packNum,
        productName,
        orderNum,
        deliveryNum,
      ];
}
