import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 明細納品数
class UpdateReceiveNumberParam extends Equatable {
  /// 標準コンストラクタ
  const UpdateReceiveNumberParam({
    required this.lineNo,
    required this.deliveryNum,
    required this.isSuspended,
  });

  /// transform model
  CorrectedData transform() => CorrectedData(
        lineNo: lineNo,
        deliveryNum: deliveryNum,
        isSuspended: isSuspended,
      );

  /// 伝票明細番号
  final String lineNo;

  /// 納品数
  final double deliveryNum;

  /// 保留フラグ　true：保留あり　false：保留なし
  final bool isSuspended;

  @override
  List<Object?> get props => [
        lineNo,
        deliveryNum,
        isSuspended,
      ];
}
