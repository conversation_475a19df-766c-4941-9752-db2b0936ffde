import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

import '../../presentation/utils/utils.dart';

/// 修正データ取得
class CorrectDetails extends Equatable {
  /// 標準コンストラクタ
  const CorrectDetails({
    required this.lineNo,
    required this.asnNum,
    required this.deliveryNum,
    required this.productName,
    required this.isSuspended,
    required this.isError,
    required this.isDivMeat,
    required this.isFresh,
    required this.orderNum,
    required this.changedDeliverNum,
  });

  /// Grpcの結果を構築します
  factory CorrectDetails.fromGrpc(
    GetCorrectDetailsResponse_CorrectDetails info,
  ) =>
      CorrectDetails(
        lineNo: info.lineNo,
        asnNum: info.asnNum,
        deliveryNum: info.deliveryNum,
        productName: info.productName,
        isSuspended: info.isSuspended,
        isError: info.isError,
        isDivMeat: info.isDivMeat,
        isFresh: info.isFresh,
        orderNum: info.orderNum,
        changedDeliverNum: checkDoubleToString(info.deliveryNum),
      );

  /// check input number
  bool isNumberExceedPlannedLimit(String inputNumber) {
    return (double.tryParse(inputNumber) ?? 0.0) > orderNum;
  }

  /// copy
  CorrectDetails copyWith({
    String? changedDeliveryNumber,
    bool? isError,
  }) {
    return CorrectDetails(
      lineNo: lineNo,
      asnNum: asnNum,
      deliveryNum: deliveryNum,
      productName: productName,
      isSuspended: isSuspended,
      isError: isError ?? this.isError,
      isDivMeat: isDivMeat,
      isFresh: isFresh,
      orderNum: orderNum,
      changedDeliverNum: changedDeliveryNumber ?? changedDeliverNum,
    );
  }

  /// 伝票明細番号
  final int lineNo;

  /// 納品予定数（ASN数）
  final double asnNum;

  /// 納品数
  final double deliveryNum;

  /// 商品名
  final String productName;

  /// 保留フラグ　true：保留あり　false：保留なし
  final bool isSuspended;

  /// false：誤差なし　true：誤差あり
  final bool isError;

  /// true：精肉　false：精肉以外
  final bool isDivMeat;

  /// 生鮮フラグ　true：生鮮　false：非生鮮
  final bool isFresh;

  /// 発注数
  final double orderNum;

  /// 納品数 修改
  final String? changedDeliverNum;

  @override
  List<Object?> get props => [
        lineNo,
        asnNum,
        deliveryNum,
        productName,
        isSuspended,
        isError,
        isDivMeat,
        isFresh,
        orderNum,
        changedDeliverNum,
      ];
}
