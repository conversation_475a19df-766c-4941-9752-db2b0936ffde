/// order data type
enum OrderType {
  /// 0 unspecified
  unspecified(value: 0),

  /// 1：当日か翌日　追加スキャンデータが存在
  add(value: 1),

  /// 2：当日未スキャンデータが存在
  today(value: 2),

  /// 3：翌日未スキャンデータが存在
  nextDay(value: 3),

  /// 4：当日と翌日未スキャンデータ
  todayAndNextDay(value: 4),

  /// 5：スキャン済み
  scanned(value: 5);

  const OrderType({
    required this.value,
  });

  factory OrderType.fromValue(int value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => OrderType.unspecified,
    );
  }

  /// value
  final int value;
}
