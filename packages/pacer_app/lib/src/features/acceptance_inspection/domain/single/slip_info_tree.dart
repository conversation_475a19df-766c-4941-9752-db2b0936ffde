import 'package:equatable/equatable.dart';
import 'select_slip_info.dart';

///
class SlipInfoTree extends Equatable {
  /// 標準コンストラクタ
  const SlipInfoTree({
    required this.slipNo,
    required this.time,
    required this.goodsList,
  });

  /// copy
  SlipInfoTree copyWith({
    List<SelectSlipInfo>? list,
  }) {
    return SlipInfoTree(
      slipNo: slipNo,
      time: time,
      goodsList: list ?? goodsList,
    );
  }

  /// 伝票番号
  final String slipNo;

  /// time
  final String time;

  /// goods list
  final List<SelectSlipInfo> goodsList;

  @override
  List<Object?> get props => [
        slipNo,
        time,
        goodsList,
      ];
}
