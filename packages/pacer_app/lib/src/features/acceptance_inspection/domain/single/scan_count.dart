import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 単品検品の該当伝票の検品進捗取得
class ScanCount extends Equatable {
  /// 標準コンストラクタ
  const ScanCount({
    required this.slipCount,
    required this.scanCount,
    required this.idCount,
  });

  /// Grpcの結果を構築します
  factory ScanCount.fromGrpc(
    GetScanCountDataResponse info,
  ) =>
      ScanCount(
        slipCount: info.slipCount,
        scanCount: info.scanCount,
        idCount: info.idCount,
      );

  /// Grpcの結果を構築します
  factory ScanCount.fromOrderGrpc(
    GetOrderDataForSlipResponse_SlipCount info,
  ) =>
      ScanCount(
        slipCount: info.slipCount,
        scanCount: info.scanCount,
        idCount: info.idCount,
      );

  /// 該当伝票の全部の商品件数
  final int slipCount;

  /// 検品済み商品件数
  final int scanCount;

  /// 次の商品件数
  final int idCount;

  @override
  List<Object?> get props => [
        slipCount,
        scanCount,
        idCount,
      ];
}
