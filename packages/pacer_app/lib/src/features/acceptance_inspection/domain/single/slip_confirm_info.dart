import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

import '../../presentation/config/strings.dart';

/// 伝票とステータス取得
class SlipConfirmInfo extends Equatable {
  /// 標準コンストラクタ
  const SlipConfirmInfo({
    required this.slipNo,
    required this.statusName,
    required this.isSuspended,
    required this.isError,
    required this.isHavePorVendor,
    required this.isCanSelect,
    required this.cellBackgroundColor,
  });

  /// Grpcの結果を構築します
  factory SlipConfirmInfo.fromGrpc(
    GetInputCheckInfoVendorResponse_InputCheckInfo info,
  ) {
    final unSelectStatusList = [
      kStatusUnInspected,
      kStatusCompleted,
      kStatusCompletedAlternative,
    ];

    return SlipConfirmInfo(
      slipNo: info.slipNo,
      statusName: info.status,
      isSuspended: info.isSuspended,
      isError: info.isError,
      isHavePorVendor: info.havePorVendor,
      isCanSelect: !unSelectStatusList.contains(info.status.trim()) &&
          !(info.status.trim() == kStatusConfirmed && info.slipNo == '9999999'),
      cellBackgroundColor: info.isError ? 0xFFF44336 : 0xFFFFFFFF,
    );
  }

  /// copy
  SlipConfirmInfo copyWith() {
    return SlipConfirmInfo(
      slipNo: slipNo,
      statusName: statusName,
      isSuspended: !isSuspended,
      isError: isError,
      isHavePorVendor: isHavePorVendor,
      isCanSelect: isCanSelect,
      cellBackgroundColor: cellBackgroundColor,
    );
  }

  /// 伝票番号
  final String slipNo;

  /// 未検品、未完了、確認済、済(差異)、済(代替)、済
  final String statusName;

  /// 保留フラグ　false：保留なし　true：保留あり
  final bool isSuspended;

  /// 誤算フラグ　false：誤差なし　true：誤差あり
  final bool isError;

  /// porベンダーのベンダーコードあり・なし　false：なし　true：あり
  final bool isHavePorVendor;

  /// 選択することは可能ですか
  final bool isCanSelect;

  /// background color
  final int cellBackgroundColor;

  @override
  List<Object?> get props => [
        slipNo,
        statusName,
        isSuspended,
        isError,
        isHavePorVendor,
        isCanSelect,
      ];
}
