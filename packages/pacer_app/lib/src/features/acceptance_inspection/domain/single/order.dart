import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

import 'scan_count.dart';
import 'slip_info.dart';

/// order info
class Order extends Equatable {
  /// 標準コンストラクタ
  const Order({
    required this.scanCountInfo,
    required this.slipList,
  });

  /// Grpcの結果を構築します
  factory Order.fromGrpc(
    GetOrderDataForSlipResponse resp,
  ) =>
      Order(
        scanCountInfo: ScanCount.fromOrderGrpc(resp.slipCount),
        slipList: resp.slipData.map(SlipInfo.fromOrderGrpc).toList(),
      );

  /// scan info
  final ScanCount scanCountInfo;

  /// slips list
  final List<SlipInfo> slipList;

  @override
  List<Object?> get props => [
        scanCountInfo,
        slipList,
      ];
}
