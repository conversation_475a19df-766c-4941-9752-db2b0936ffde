import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 商品
class Goods extends Equatable {
  /// 標準コンストラクタ
  const Goods({
    required this.productName,
    required this.specName,
    required this.productCode,
    required this.brandName,
    required this.colorAndSize,
    required this.vendorCode,
    required this.isBread,
    required this.packNum,
  });

  /// Grpcの結果を構築します
  factory Goods.fromGrpc(
    ProductAndVendorInfoResponse_ProductInfo goodsInfo,
  ) =>
      Goods(
        productName: goodsInfo.productName,
        specName: goodsInfo.specName,
        productCode: goodsInfo.productCode,
        brandName: goodsInfo.brandName,
        colorAndSize: goodsInfo.colorAndSize,
        vendorCode: goodsInfo.vendorCode,
        isBread: goodsInfo.isBread,
        packNum: goodsInfo.packNum,
      );

  /// copy
  Goods copyWith(
    String? vendorCode,
  ) =>
      Goods(
        productName: productName,
        specName: specName,
        productCode: productCode,
        brandName: brandName,
        colorAndSize: colorAndSize,
        vendorCode: vendorCode ?? this.vendorCode,
        isBread: isBread,
        packNum: packNum,
      );

  /// 商品名称
  final String productName;

  /// 商品code
  final String productCode;

  /// ブランド
  final String brandName;

  /// 規格
  final String specName;

  /// カラー、サイズ
  final String colorAndSize;

  /// 入数
  final int packNum;

  /// ベンダーフラグ　true：パン　false：通常
  final bool isBread;

  /// ベンダーコード
  final String vendorCode;

  @override
  List<Object?> get props => [
        productName,
        productCode,
        brandName,
        specName,
        colorAndSize,
        packNum,
        isBread,
        vendorCode,
      ];
}
