import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

import 'enum/order_type.dart';
import 'goods.dart';
import 'slip_info.dart';

/// 単品検品の商品情報取得
class GoodsAndVendorInfo extends Equatable {
  /// 標準コンストラクタ
  const GoodsAndVendorInfo({
    required this.unInspectedCount,
    required this.checkScannedSlipMessage,
    required this.checkOrderData,
    required this.goods,
    required this.slipList,
  });

  /// Grpcの結果を構築します
  factory GoodsAndVendorInfo.fromGrpc(
    ProductAndVendorInfoResponse resp,
  ) =>
      GoodsAndVendorInfo(
        unInspectedCount: resp.uninspectedCount,
        checkScannedSlipMessage: resp.checkScannedSlipMessage,
        checkOrderData: OrderType.fromValue(resp.checkOrderData.value),
        goods: Goods.fromGrpc(resp.productInfo),
        slipList: resp.slipInfo.map(SlipInfo.fromGrpc).toList(),
      );

  /// copy
  GoodsAndVendorInfo copyWith(
    Goods? goods,
  ) =>
      GoodsAndVendorInfo(
        unInspectedCount: unInspectedCount,
        checkScannedSlipMessage: checkScannedSlipMessage,
        checkOrderData: checkOrderData,
        goods: goods ?? this.goods,
        slipList: slipList,
      );

  /// 未検品件数
  final int unInspectedCount;

  /// 翌日商品の伝票を未スキャン数
  final String checkScannedSlipMessage;

  /// order data type
  final OrderType checkOrderData;

  /// goods info
  final Goods goods;

  /// slips list
  final List<SlipInfo> slipList;

  @override
  List<Object?> get props => [
        unInspectedCount,
        checkScannedSlipMessage,
        checkOrderData,
        goods,
        slipList,
      ];
}
