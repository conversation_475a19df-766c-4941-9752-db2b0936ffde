import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 当日日付、時間、週取得
class SystemDate extends Equatable {
  /// 標準コンストラクタ
  const SystemDate({
    required this.date,
    required this.time,
    required this.weekIndex,
  });

  /// Grpcの結果を構築します
  factory SystemDate.fromGrpc(
    GetSystemDateResponse_SystemDate info,
  ) =>
      SystemDate(
        date: info.date.replaceAll('-', '/'),
        time: info.time,
        weekIndex: info.weekIndex,
      );

  /// 当日
  final String date;

  /// 取得時点の時間
  final String time;

  /// 曜日
  final int weekIndex;

  @override
  List<Object?> get props => [
        date,
        time,
        weekIndex,
      ];
}
