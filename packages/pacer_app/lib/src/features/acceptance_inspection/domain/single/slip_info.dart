import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 伝票番号情報
class SlipInfo extends Equatable {
  /// 標準コンストラクタ
  const SlipInfo({
    required this.slipNo,
    required this.lineNo,
    required this.productCode,
    required this.orderNum,
    required this.asnNum,
    required this.deliveryNum,
    required this.isInspected,
  });

  /// Grpcの結果を構築します
  factory SlipInfo.fromGrpc(ProductAndVendorInfoResponse_SlipInfo info) => SlipInfo(
        slipNo: info.slipNo,
        lineNo: info.lineNo,
        productCode: info.productCode,
        orderNum: info.orderNum,
        asnNum: info.asnNum,
        deliveryNum: info.deliveryNum,
        isInspected: info.isInspected,
      );

  /// Grpcの結果を構築します
  factory SlipInfo.fromOrderGrpc(GetOrderDataForSlipResponse_SlipData info) => SlipInfo(
        slipNo: info.slipNo,
        lineNo: info.lineNo,
        productCode: info.productCode,
        orderNum: info.orderNum,
        asnNum: info.asnNum,
        deliveryNum: info.deliveryNum,
        isInspected: false,
      );

  /// 伝票番号
  final String slipNo;

  /// 伝票明細番号
  final int lineNo;

  /// 商品コード
  final String productCode;

  /// 発注数
  final double orderNum;

  /// 納品予定数（ASN数）
  final double asnNum;

  /// 納品数
  final double deliveryNum;

  /// 一時テーブルの検品フラグ　false：未検品　true：検品済み
  final bool isInspected;

  @override
  List<Object?> get props => [
        slipNo,
        lineNo,
        productCode,
        orderNum,
        asnNum,
        deliveryNum,
        isInspected,
      ];
}
