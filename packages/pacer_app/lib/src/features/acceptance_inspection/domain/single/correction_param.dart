import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 誤差フラグ更新
class CorrectionParam extends Equatable {
  /// 標準コンストラクタ
  const CorrectionParam({
    required this.lineNo,
    required this.isError,
  });

  /// transform model
  UpdateCorrectedDataFlgRequest_ErrorData transform() => UpdateCorrectedDataFlgRequest_ErrorData(
        lineNo: lineNo,
        isError: isError,
      );

  /// 伝票明細番号
  final String lineNo;

  /// 誤差フラグ
  final bool isError;

  @override
  List<Object?> get props => [
        lineNo,
        isError,
      ];
}
