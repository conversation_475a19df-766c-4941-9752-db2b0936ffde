import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 単品検品の初期データ件数取得
class InitialSingleData extends Equatable {
  /// 標準コンストラクタ
  const InitialSingleData({
    required this.orderDataCount,
    required this.scanDataCount,
  });

  /// Grpcの結果を構築します
  factory InitialSingleData.fromGrpc(
    InputCheckSingleDataCreateResponse resp,
  ) =>
      InitialSingleData(
        orderDataCount: resp.orderDataCnt,
        scanDataCount: resp.scanDataCnt,
      );

  /// 一時テーブルの検品用のデータ件数
  final int orderDataCount;

  /// 一時テーブルの検品済みのデータ件数
  final int scanDataCount;

  @override
  List<Object?> get props => [
        orderDataCount,
        scanDataCount,
      ];
}
