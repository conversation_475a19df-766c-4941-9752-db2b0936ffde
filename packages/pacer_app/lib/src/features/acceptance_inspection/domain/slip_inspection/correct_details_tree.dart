import 'package:equatable/equatable.dart';

import 'correct_details.dart';

///
class CorrectDetailsTree extends Equatable {
  /// 標準コンストラクタ
  const CorrectDetailsTree({
    required this.slipNo,
    required this.sum,
    required this.slipInspectionList,
  });

  /// copy
  CorrectDetailsTree copyWith({
    List<CorrectDetails>? list,
    double? sum,
  }) {
    return CorrectDetailsTree(
      slipNo: slipNo,
      sum: sum ?? this.sum,
      slipInspectionList: list ?? slipInspectionList,
    );
  }

  /// 伝票番号
  final String slipNo;

  /// sum
  final double sum;

  /// slip list
  final List<CorrectDetails> slipInspectionList;

  @override
  List<Object?> get props => [
        slipNo,
        sum,
        slipInspectionList,
      ];
}
