import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

import '../../presentation/utils/utils.dart';

/// 伝票検品データ取得
class CorrectDetails extends Equatable {
  /// 標準コンストラクタ
  const CorrectDetails({
    required this.slipNo,
    required this.lineNo,
    required this.asnNum,
    required this.deliveryNum,
    required this.productCode,
    required this.productName,
    required this.isSuspended,
    required this.isError,
    required this.isDivMeat,
    required this.isFresh,
    required this.orderNum,
    required this.changedDeliverNum,
    required this.isSelect,
  });

  /// Grpcの結果を構築します
  factory CorrectDetails.fromGrpc(
    GetCorrectDetailsAllResponse_CorrectDetail info,
  ) =>
      CorrectDetails(
        slipNo: info.slipNo,
        lineNo: info.lineNo,
        asnNum: info.asnNum,
        deliveryNum: info.deliveryNum,
        productCode: info.productCode,
        productName: info.productInfo,
        isSuspended: info.isSuspended,
        isError: info.isError,
        isDivMeat: info.isDivMeat,
        isFresh: info.isFresh,
        orderNum: info.orderNum,
        changedDeliverNum: checkDoubleToString(info.deliveryNum),
        isSelect: false,
      );

  /// check input number
  bool isNumberExceedPlannedLimit(String inputNumber) {
    return (double.tryParse(inputNumber) ?? 0.0) > orderNum;
  }

  /// copy
  CorrectDetails copyWith({
    String? changedDeliveryNumber,
    bool? isSelect,
  }) {
    return CorrectDetails(
      slipNo: slipNo,
      lineNo: lineNo,
      asnNum: asnNum,
      deliveryNum: deliveryNum,
      productCode: productCode,
      productName: productName,
      isSuspended: isSuspended,
      isError: isError,
      isDivMeat: isDivMeat,
      isFresh: isFresh,
      orderNum: orderNum,
      changedDeliverNum: changedDeliveryNumber ?? changedDeliverNum,
      isSelect: isSelect ?? this.isSelect,
    );
  }

  /// 伝票番号
  final String slipNo;

  /// 伝票明細番号
  final int lineNo;

  /// 商品code
  final String productCode;

  /// 発注数
  final double orderNum;

  /// 納品予定数（ASN数）
  final String asnNum;

  /// 商品名
  final String productName;

  /// 納品数
  final double deliveryNum;

  /// 保留フラグ　true：保留あり　false：保留なし
  final String isSuspended;

  /// false：誤差なし　true：誤差あり
  final bool isError;

  /// true：精肉　false：精肉以外
  final bool isDivMeat;

  /// 生鮮フラグ　true：生鮮　false：非生鮮
  final bool isFresh;

  /// 納品数 修改
  final String? changedDeliverNum;

  /// select
  final bool isSelect;

  @override
  List<Object?> get props => [
        slipNo,
        lineNo,
        asnNum,
        deliveryNum,
        productCode,
        productName,
        isSuspended,
        isError,
        isDivMeat,
        isFresh,
        orderNum,
        changedDeliverNum,
      ];
}
