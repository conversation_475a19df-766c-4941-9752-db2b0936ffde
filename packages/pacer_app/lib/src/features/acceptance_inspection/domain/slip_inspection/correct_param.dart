import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 誤差フラグ更新
class CorrectionParam extends Equatable {
  /// 標準コンストラクタ
  const CorrectionParam({
    required this.sequenceNumber,
    required this.lineNo,
    required this.deliveryNum,
    required this.isSuspended,
  });

  /// transform model
  UpdateCorrectedDataToSeeRequest_OrderDetails transform() => UpdateCorrectedDataToSeeRequest_OrderDetails(
        sequenceNumber: sequenceNumber,
        lineNo: lineNo,
        deliveryNum: deliveryNum,
        isSuspended: isSuspended,
      );

  /// 伝票順番0
  final String sequenceNumber;

  /// 伝票明細番号
  final String lineNo;

  /// 納品数
  final double deliveryNum;

  /// 保留
  final bool isSuspended;

  @override
  List<Object?> get props => [
        sequenceNumber,
        lineNo,
        deliveryNum,
        isSuspended,
      ];
}
