import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 伝票検品の一時テーブルのベンダー
class SupportInfo extends Equatable {
  /// 標準コンストラクタ
  const SupportInfo({
    required this.registeredCount,
    required this.childCount,
  });

  /// grpc応答から初期化する
  factory SupportInfo.fromGrpc(
    GetSupportListResponse_SupportInfo response,
  ) {
    return SupportInfo(
      registeredCount: response.registeredCount,
      childCount: response.childCount,
    );
  }

  /// grpc応答から初期化する
  factory SupportInfo.fromSlipConfirmGrpc(
    GetSupportListConfirmResponse_SupportInfo response,
  ) {
    return SupportInfo(
      registeredCount: response.registeredCount,
      childCount: response.childCount,
    );
  }

  /// 登録ＩＰの件数
  final int registeredCount;

  /// 子ＩＰ件数
  final int childCount;

  @override
  List<Object?> get props => [
        registeredCount,
        childCount,
      ];
}
