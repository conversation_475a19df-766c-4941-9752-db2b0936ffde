import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// ベンダーコードにより伝票取得して一時テーブルに追加
class InputCheckInfo extends Equatable {
  /// 標準コンストラクタ
  const InputCheckInfo({
    required this.orderDataCount,
    required this.scanDataCount,
  });

  /// Grpcの結果を構築します
  factory InputCheckInfo.fromGrpc(
    InputCheckBeginClickResponse info,
  ) =>
      InputCheckInfo(
        orderDataCount: info.orderDataCount,
        scanDataCount: info.scanDataCount,
      );

  /// 一時テーブルの検品用のデータ件数
  final int orderDataCount;

  /// 一時テーブルのporscantemp_XXX_XXX_XXX_XXXの最大ID+1
  final int scanDataCount;

  @override
  List<Object?> get props => [
        orderDataCount,
        scanDataCount,
      ];
}
