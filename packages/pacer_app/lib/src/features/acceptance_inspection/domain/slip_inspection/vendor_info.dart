import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 商品コードによりベンダー名
class VendorInfo extends Equatable {
  /// 標準コンストラクタ
  const VendorInfo({
    required this.vendorCode,
    required this.vendorName,
    required this.jan,
  });

  /// grpc応答から初期化する
  factory VendorInfo.fromGrpc(
    GetJanVendorSearchResponse response,
  ) {
    return VendorInfo(
      vendorCode: response.vendorCode,
      vendorName: response.vendorName,
      jan: response.jan,
    );
  }

  /// 仕入先（＝ベンダーコード）
  final int vendorCode;

  /// 仕入先名（＝ベンダー名）
  final String vendorName;

  /// 商品コード
  final String jan;

  @override
  List<Object?> get props => [
        vendorCode,
        vendorName,
        jan,
      ];
}
