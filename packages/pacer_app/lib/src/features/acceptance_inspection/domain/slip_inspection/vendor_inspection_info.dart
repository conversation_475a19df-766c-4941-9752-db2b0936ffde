import 'package:equatable/equatable.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pb.dart';

/// 検品レスチェック
class VendorInspectionInfo extends Equatable {
  /// 標準コンストラクタ
  const VendorInspectionInfo({
    required this.vendorCode,
    required this.isInsufficientInspection,
  });

  /// grpc応答から初期化する
  factory VendorInspectionInfo.fromGrpc(
    GetVendorCDResponse_VendorInfo info,
  ) {
    return VendorInspectionInfo(
      vendorCode: info.vendorCode,
      isInsufficientInspection: info.isInsufficientInspection,
    );
  }

  /// ベンダーコード
  final int vendorCode;

  /// true：検品レス　false：通常検品
  final bool isInsufficientInspection;

  @override
  List<Object?> get props => [
        vendorCode,
        isInsufficientInspection,
      ];
}
