import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../../../../utils/async_value_ui.dart';
import '../../../../../../app/application/global_loading_service.dart';
import '../../../../config/strings.dart';
import '../../../../routing/acceptance_inspection_route.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../../../../widgets/scan_bottom_tab_bar.dart';
import '../warehouse_inspection_controller.dart';

/// スキャン登録 tab bar
class WarehouseInspectionBottomTabBar extends ConsumerWidget {
  /// constructor
  const WarehouseInspectionBottomTabBar({
    super.key,
    required this.cancelCallback,
  });

  /// cancel callback
  final VoidCallback cancelCallback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(insertDataProvider, (previous, next) {
      next.showSnackBarOnError(context);
      final result = next.asData?.value;
      if (result != null) {
        _pushListPage(ref);
      }
    });

    return ScanBottomTabBar(
      leading: const PacerBackButton(),
      actions: [
        OutlineRoundTextButton(
          kCancel,
          onPressed: cancelCallback,
        ),
        const SizedBox(width: 8),
        OutlineRoundTextButton(
          kEnd,
          onPressed: () => _onConfirmClick(context, ref),
        ),
      ],
    );
  }

  void _onConfirmClick(
    BuildContext context,
    WidgetRef ref,
  ) {
    final inputNumber = ref.read(inputNumberStateProvider);
    if (inputNumber.isEmpty) {
      _pushListPage(ref);
      return;
    }
    final goodsInfo = ref.read(inputJanStateProvider).value;
    if (goodsInfo == null) return;
    final isAllOne = ref.read(fixedInputValueStateProvider);
    if (goodsInfo.isNumberExceedPlannedLimit(
      inputNumber,
      isAllOne: isAllOne,
    )) {
      showAlertDialog(
        context: context,
        title: kInputValueGreaterThanPlanedQuantity,
      );
      return;
    }

    ref.read(globalLoadingServiceProvider.notifier).wrap(
          ref.read(insertDataProvider.notifier).insertData(),
        );
  }

  void _pushListPage(WidgetRef ref) {
    ref.read(inputJanStateProvider.notifier).clear();
    ref.read(productCodeProvider.notifier).clear();
    ref.read(fixedInputValueStateProvider.notifier).clear();

    const WarehouseConfirmRoute().go(ref.context);
  }
}
