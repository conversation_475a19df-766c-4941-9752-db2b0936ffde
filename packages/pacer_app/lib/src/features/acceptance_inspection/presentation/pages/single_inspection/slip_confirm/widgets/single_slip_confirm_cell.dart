import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../../../../themes/app_color_scheme.dart';
import '../../../../../domain/single/correct_details.dart';
import '../../../../widgets/number_text_field.dart';
import '../single_slip_confirm_controller.dart';

/// 伝票検品　確認登録②（誤算あり）cell
class SingleSlipConfirmCell extends ConsumerWidget {
  /// 標準コンストラクタ
  const SingleSlipConfirmCell({
    super.key,
    required this.slipInfo,
    required this.isSelected,
    required this.onPressed,
    required this.onChanged,
  });

  /// 商品
  final CorrectDetails slipInfo;

  /// 選択されているかどうか
  final bool isSelected;

  /// クリックイベントのコールバック
  final VoidCallback onPressed;

  /// チェックボックスの変更イベント
  final void Function({bool? isCheck}) onChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textColor = isSelected ? colors.onPrimaryContainer : colors.onSurface;

    final textTheme = texts.bodyMedium?.copyWith(
      color: textColor,
      height: 2,
    );

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: colors.surface,
          border: Border.fromBorderSide(
            BorderSide(
              color: colors.outlineVariant,
            ),
          ),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              InkWell(
                onTap: () => onChanged.call(isCheck: !slipInfo.isError),
                child: SizedBox(
                  width: 48,
                  height: 72,
                  child: Checkbox(
                    value: slipInfo.isError,
                    tristate: true,
                    onChanged: (_) => onChanged.call(isCheck: !slipInfo.isError),
                  ),
                ),
              ),
              VerticalDivider(
                width: 1,
                thickness: 1,
                color: colors.outlineVariant,
              ),
              Expanded(
                child: Column(
                  children: [
                    IntrinsicHeight(
                      child: Row(
                        children: [
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                slipInfo.lineNo.toString(),
                                textAlign: TextAlign.center,
                                style: textTheme,
                              ),
                            ),
                          ),
                          VerticalDivider(
                            width: 1,
                            thickness: 1,
                            color: colors.outlineVariant,
                          ),
                          Expanded(
                            flex: 4,
                            child: Text(
                              slipInfo.productName,
                              textAlign: TextAlign.center,
                              style: textTheme,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: colors.outlineVariant,
                    ),
                    IntrinsicHeight(
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              slipInfo.orderNum.toInt().toString(),
                              textAlign: TextAlign.center,
                              style: textTheme,
                            ),
                          ),
                          VerticalDivider(
                            width: 1,
                            thickness: 1,
                            color: colors.outlineVariant,
                          ),
                          Expanded(
                            child: Text(
                              slipInfo.asnNum.toInt().toString(),
                              textAlign: TextAlign.center,
                              style: textTheme,
                            ),
                          ),
                          VerticalDivider(
                            width: 1,
                            thickness: 1,
                            color: colors.outlineVariant,
                          ),
                          Expanded(
                            flex: 2,
                            child: Container(
                              width: double.infinity,
                              height: double.infinity,
                              decoration: BoxDecoration(
                                color: colors.button,
                                borderRadius: const BorderRadius.only(
                                  bottomRight: Radius.circular(20),
                                ),
                              ),
                              child: NumberTextField(
                                inputText: slipInfo.changedDeliverNum ?? '',
                                textAlign: TextAlign.center,
                                onFieldSubmitted: (value) {
                                  value = value.isEmpty ? '0' : value;

                                  final tempValue = slipInfo.asnNum > 0 ? slipInfo.asnNum : slipInfo.orderNum;
                                  final inputValue = double.tryParse(value) ?? 0.0;
                                  final isError = inputValue != tempValue;
                                  final newGoods = slipInfo.copyWith(
                                    changedDeliveryNumber: value,
                                    isError: isError,
                                  );
                                  ref.read(confirmGoodsListProvider.notifier).replaceObjectAtIndexWithObject(
                                        slipInfo,
                                        newGoods,
                                      );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
