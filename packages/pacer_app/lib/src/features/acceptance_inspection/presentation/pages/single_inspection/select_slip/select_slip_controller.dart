import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/single_inspection_service.dart';
import '../../../../domain/single/slip_info_tree.dart';
import '../form/single_inspection_form_controller.dart';

part 'select_slip_controller.g.dart';

/// goods list
@riverpod
class SlipInfoListState extends _$SlipInfoListState {
  @override
  FutureOr<List<SlipInfoTree>> build() async {
    final productCode = ref.read(getTempInfoStateProvider)?.goods.productCode;
    if (productCode == null) return [];
    final slipList =
        await ref.watch(singleInspectionServiceProvider).getSameProductCodeSlipList(productCode: productCode);
    final dataArray = <SlipInfoTree>[];
    for (var i = 0; i < slipList.length; i++) {
      final slipInfo = slipList[i];
      final slipItem = _getCacheDataBySlipNo(slipInfo.slipNo, dataArray);
      if (slipItem == null) {
        final info = SlipInfoTree(
          time: slipInfo.deliveryDate,
          slipNo: slipInfo.slipNo,
          goodsList: [slipInfo],
        );
        dataArray.add(info);
      } else {
        final info = slipItem.copyWith(
          list: [
            ...slipItem.goodsList,
            ...[slipInfo],
          ],
        );
        final index = dataArray.indexOf(slipItem);
        dataArray
          ..removeAt(index)
          ..insert(index, info);
      }
    }

    return dataArray;
  }

  SlipInfoTree? _getCacheDataBySlipNo(String slipNo, List<SlipInfoTree> list) {
    for (var i = 0; i < list.length; i++) {
      final info = list[i];
      if (info.slipNo == slipNo) {
        return info;
      }
    }
    return null;
  }
}

/// 选中传票
@riverpod
class SelectSlipNoState extends _$SelectSlipNoState {
  /// init
  @override
  String? build() => null;

  /// update selected goods
  Future<void> update(String? slipNo) async {
    log('select slip: $slipNo');
    state = slipNo;
  }

  /// clean selected goods
  void clear() {
    state = null;
  }
}
