import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/single/slip_confirm_info.dart';
import '../../../config/strings.dart';
import 'select_slip_code_state.dart';
import 'single_confirm_controller.dart';
import 'widgets/single_confirm_bottom_tab_bar.dart';
import 'widgets/single_confirm_cell.dart';

/// 単品検品確定ページ
class SingleConfirmPage extends HookConsumerWidget {
  /// init
  const SingleConfirmPage({super.key});

  static const double _lineHeight = 8;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final slipsArray = ref.watch(confirmSlipListProvider);
    final selectSlipList = ref.watch(selectSlipListProvider);

    ref
      ..listen(setSuspendStatusStateProvider, (previous, next) {
        final slipInfo = next.asData?.value;
        if (slipInfo == null) return;
        ref.read(confirmSlipListProvider.notifier).changeSelectState(slipInfo);
      })
      ..listen(confirmSlipListProvider, (previous, next) async {
        final array = next.asData?.value;
        if (array != null && array.isEmpty) {
          await showAlertDialog(context: context, title: kNoData);
          if (context.mounted) {
            context.pop();
          }
          return;
        }
        _checkData(ref);
      })
      ..listen(selectSlipCodeStateProvider, (_, state) {});

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text('$kConfirmRegister①'),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: _lineHeight,
          horizontal: _edge,
        ),
        child: Column(
          children: [
            _Header(),
            switch (slipsArray) {
              AsyncData(:final value) => Expanded(
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      final slipInfo = value[index];
                      final checkState = selectSlipList.contains(slipInfo);

                      return Consumer(
                        builder: (ctx, ref, _) {
                          final selectedGoods = ref.watch(selectSlipStateProvider);

                          return SingleConfirmCell(
                            slipInfo: slipInfo,
                            isSelected: selectedGoods == slipInfo,
                            onPressed: () => ref.read(selectSlipStateProvider.notifier).update(slipInfo),
                            isCheck: checkState,
                            onChanged: ({bool? isCheck}) => onCheckChanged(
                              ref: ref,
                              isCheck: isCheck,
                              slipInfo: slipInfo,
                            ),
                          );
                        },
                      );
                    },
                    separatorBuilder: (context, _) => const SizedBox(
                      height: 6,
                    ),
                    itemCount: value.length,
                  ),
                ),
              AsyncError(:final error) => Text(
                  switch (error) {
                    UnknownException() => error.message,

                    /// 取得に失敗しました
                    _ => kRequestError,
                  },
                ),
              AsyncLoading() => const Center(
                  child: CircularProgressIndicator(),
                ),
            },
          ],
        ),
      ),
      bottomNavigationBar: const SingleConfirmBottomTabBar(),
    );
  }

  /// チェックボックスの変更イベント
  void onCheckChanged({
    required WidgetRef ref,
    required bool? isCheck,
    required SlipConfirmInfo slipInfo,
  }) {
    isCheck ?? false
        ? ref.read(selectSlipListProvider.notifier).add(slipInfo)
        : ref.read(selectSlipListProvider.notifier).remove(slipInfo);

    ref.read(setSuspendStatusStateProvider.notifier).setSuspendStatus(slipInfo);
  }

  void _checkData(WidgetRef ref) {
    final dataArray = ref.read(confirmSlipListProvider).value;
    if (dataArray == null) return;

    ref.read(selectSlipListProvider.notifier).clear();
    var isShowTip = false;
    for (final element in dataArray) {
      if (element.statusName == kStatusIncomplete && !element.isSuspended) {
        isShowTip = true;
      }
      if (element.isSuspended) {
        ref.read(selectSlipListProvider.notifier).add(element);
      }
    }
    ref.read(isShowTipStateProvider.notifier).changeStatus(isShow: isShowTip);
  }
}

class _Header extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyMedium?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          color: colors.button,
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(bottom: 8),
          child: Text(
            '$kOperationTip\n$kOperationTip1',
            style: texts.titleMedium,
          ),
        ),
        Consumer(
          builder: (ctx, ref, _) {
            final isShowTip = ref.watch(isShowTipStateProvider);

            return isShowTip
                ? Text(
                    kThereAreIncompleteSlip,
                    style: texts.bodyLarge?.copyWith(color: colors.error),
                  )
                : const Text('');
          },
        ),
        Container(
          decoration: BoxDecoration(
            color: colors.surfaceContainerHighest,
            border: Border.fromBorderSide(
              BorderSide(
                color: colors.outlineVariant,
              ),
            ),
          ),
          margin: const EdgeInsets.only(bottom: 12),
          child: IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    kOnHold,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    kReceiptsNo,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                const Expanded(
                  flex: 2,
                  child: Text(
                    '',
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
