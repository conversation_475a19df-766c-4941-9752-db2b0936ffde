import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../check_expiry/presentation/common_widgets/outline_round_text_button.dart';
import '../../../../config/strings.dart';
import '../../../../widgets/scan_bottom_tab_bar.dart';

/// 伝票検品設定画面 tab bar
class SlipSettingBottomTabBar extends ConsumerWidget {
  /// constructor
  const SlipSettingBottomTabBar({
    super.key,
    required this.startCallback,
  });

  /// begin call back
  final VoidCallback startCallback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ScanBottomTabBar(
      leading: const PacerBackButton(),
      actions: [
        OutlineRoundTextButton(
          kStartInspection,
          onPressed: startCallback,
        ),
      ],
    );
  }
}
