import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/warehouse_inspection_service.dart';
import '../../../../domain/warehouse/login_slip.dart';
import '../form/warehouse_inspection_controller.dart';

part 'warehouse_confirm_controller.g.dart';

/// goods list
@riverpod
class ConfirmSlipList extends _$ConfirmSlipList {
  @override
  FutureOr<List<LoginSlip>> build() async {
    ref.showGlobalLoading();
    final slipCode = ref.watch(slipCodeStateProvider);
    return ref.watch(warehouseInspectionServiceProvider).getConfirmLoginSlipList(slipNumber: slipCode);
  }

  /// upload
  void reload() {
    state = const AsyncData([]);
    ref.read(selectSlipListProvider.notifier).clear();
    ref.invalidateSelf();
  }
}

/// 选中传票
@riverpod
class SelectSlipState extends _$SelectSlipState {
  /// init
  @override
  LoginSlip? build() {
    final goodsArray = ref.watch(confirmSlipListProvider).value ?? [];

    return goodsArray.isEmpty ? null : goodsArray.first;
  }

  /// update selected goods
  Future<void> update(LoginSlip? slipInfo) async {
    log('scrap reason select: $slipInfo');
    state = slipInfo;
  }

  /// clean selected goods
  void clear() {
    state = null;
  }
}

/// 選択された传票リスト
@riverpod
class SelectSlipList extends _$SelectSlipList {
  @override
  List<LoginSlip> build() => [];

  /// 追加
  void add(LoginSlip slipInfo) {
    state = [...state, slipInfo];
  }

  /// 追加 all
  void addAll(List<LoginSlip> slips) {
    state = [...slips];
  }

  /// 削除
  void remove(LoginSlip slipInfo) {
    state = [...state]..remove(slipInfo);
  }

  /// クリア
  void clear() {
    state = [];
  }
}

/// select all and cancel select
@riverpod
class CheckAllState extends _$CheckAllState {
  /// init
  @override
  bool build() => false;

  /// change state
  void changeState() {
    final goodsArray = ref.read(confirmSlipListProvider).value ?? [];
    if (!state) {
      final selectedArray = <LoginSlip>[];
      for (var i = 0; i < goodsArray.length; i++) {
        final slipInfo = goodsArray[i];
        if (slipInfo.isCanSelect) {
          selectedArray.add(slipInfo);
        }
      }
      ref.read(selectSlipListProvider.notifier).addAll(selectedArray);
    } else {
      ref.read(selectSlipListProvider.notifier).clear();
    }
  }

  /// 選択した項目が変わります。全選択ボタンの状態を確認してください
  void singleSlipChangeState() {
    final selectSlips = ref.read(selectSlipListProvider);
    if (selectSlips.isEmpty) {
      state = false;
      return;
    }
    final goodsArray = ref.read(confirmSlipListProvider).value ?? [];
    var canSelectSlipNumber = 0;
    for (var i = 0; i < goodsArray.length; i++) {
      if (goodsArray[i].isCanSelect) {
        canSelectSlipNumber++;
      }
    }
    state = canSelectSlipNumber == selectSlips.length;
  }
}

/// 確定
@riverpod
class ConfirmSelectSlipState extends _$ConfirmSelectSlipState {
  /// init
  @override
  FutureOr<bool> build() {
    ref.showGlobalLoading();
    return true;
  }

  /// confirm all goods
  FutureOr<void> confirmSlips(List<String> slipNumbers) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(warehouseInspectionServiceProvider).confirmReceiveSlip(slipNumbers: slipNumbers);
      },
    );
  }
}
