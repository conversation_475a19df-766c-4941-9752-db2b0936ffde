import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../themes/app_color_scheme.dart';
import '../../../../../domain/slip_inspection/correct_details.dart';
import '../../../../../domain/slip_inspection/correct_details_tree.dart';
import '../../../../config/strings.dart';
import '../../../../widgets/number_text_field.dart';
import '../slip_inspection_list_controller.dart';

/// 一覧表示
class SlipInspectionSection extends ConsumerWidget {
  /// 標準コンストラクタ
  const SlipInspectionSection({
    super.key,
    required this.sectionInfo,
    required this.onChanged,
  });

  /// 商品
  final CorrectDetailsTree sectionInfo;

  /// チェックボックスの変更イベント
  final void Function({
    bool? isCheck,
    CorrectDetails? slipInfo,
  }) onChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textColor = colors.onSurface;

    final textTheme = texts.bodyLarge?.copyWith(
      color: textColor,
      height: 2,
    );

    return Container(
      decoration: BoxDecoration(
        border: Border.fromBorderSide(
          BorderSide(
            color: colors.outlineVariant,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            color: colors.primaryContainer,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '$kReceiptsNo：${sectionInfo.slipNo}',
                  style: textTheme,
                ),
                Text(
                  '$kTotalNum：${sectionInfo.sum.toInt()}',
                  style: textTheme,
                ),
              ],
            ),
          ),
          ...sectionInfo.slipInspectionList.map(
            (slipInfo) => _SlipInspectionCell(
              slipInfo: slipInfo,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }
}

class _SlipInspectionCell extends ConsumerWidget {
  /// 標準コンストラクタ
  const _SlipInspectionCell({
    required this.slipInfo,
    required this.onChanged,
  });

  /// 商品
  final CorrectDetails slipInfo;

  /// チェックボックスの変更イベント
  final void Function({
    bool? isCheck,
    CorrectDetails? slipInfo,
  }) onChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final textTheme = texts.bodyLarge?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Container(
      decoration: BoxDecoration(
        color: colors.surface,
        border: Border(
          top: BorderSide(
            color: colors.outlineVariant,
          ),
        ),
      ),
      child: IntrinsicHeight(
        child: Row(
          children: [
            InkWell(
              onTap: () => onChanged.call(
                isCheck: !slipInfo.isSelect,
                slipInfo: slipInfo,
              ),
              child: SizedBox(
                width: 48,
                height: 72,
                child: Checkbox(
                  value: slipInfo.isSelect,
                  tristate: true,
                  onChanged: (_) => onChanged.call(
                    isCheck: !slipInfo.isSelect,
                    slipInfo: slipInfo,
                  ),
                ),
              ),
            ),
            VerticalDivider(
              width: 1,
              thickness: 1,
              color: colors.outlineVariant,
            ),
            Expanded(
              child: Column(
                children: [
                  IntrinsicHeight(
                    child: Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Text(
                              slipInfo.lineNo.toString(),
                              textAlign: TextAlign.center,
                              style: textTheme,
                            ),
                          ),
                        ),
                        VerticalDivider(
                          width: 1,
                          thickness: 1,
                          color: colors.outlineVariant,
                        ),
                        Expanded(
                          flex: 5,
                          child: Text(
                            slipInfo.productName,
                            textAlign: TextAlign.center,
                            style: textTheme,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  IntrinsicHeight(
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            slipInfo.orderNum.toInt().toString(),
                            textAlign: TextAlign.center,
                            style: textTheme,
                          ),
                        ),
                        VerticalDivider(
                          width: 1,
                          thickness: 1,
                          color: colors.outlineVariant,
                        ),
                        Expanded(
                          child: Text(
                            (double.tryParse(slipInfo.asnNum) ?? 0.0).toInt().toString(),
                            textAlign: TextAlign.center,
                            style: textTheme,
                          ),
                        ),
                        VerticalDivider(
                          width: 1,
                          thickness: 1,
                          color: colors.outlineVariant,
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            width: double.infinity,
                            height: double.infinity,
                            decoration: BoxDecoration(
                              color: colors.button,
                              borderRadius: const BorderRadius.only(
                                bottomRight: Radius.circular(20),
                              ),
                            ),
                            child: NumberTextField(
                              inputText: slipInfo.changedDeliverNum ?? '',
                              textAlign: TextAlign.center,
                              onFieldSubmitted: (value) {
                                value = value.isEmpty ? '0' : value;
                                _dealWithInputValue(ref, value);
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _dealWithInputValue(WidgetRef ref, String value) {
    var inputValueString = value;
    final inputValue = double.tryParse(inputValueString) ?? 0.0;
    if (inputValue > 99999) {
      showAlertDialog(
        context: ref.context,
        title: kTotalNumMoreThanMax,
      );
      inputValueString = slipInfo.changedDeliverNum ?? '';
    }
    if (!slipInfo.isDivMeat && inputValue > slipInfo.orderNum) {
      showAlertDialog(
        context: ref.context,
        title: kInputValueGreaterThanPlanedQuantity,
      );
      inputValueString = slipInfo.changedDeliverNum ?? '';
    }

    final newGoods = slipInfo.copyWith(
      changedDeliveryNumber: inputValueString,
    );
    ref.read(slipInfoListStateProvider.notifier).replaceObjectAtIndexWithObject(
          slipInfo,
          newGoods,
        );
  }
}
