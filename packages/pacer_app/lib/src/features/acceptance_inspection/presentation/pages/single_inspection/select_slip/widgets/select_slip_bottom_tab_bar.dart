import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../domain/single/select_slip_info.dart';
import '../../../../config/strings.dart';
import '../../../../widgets/bottom_tab_bar.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../../form/single_inspection_form_controller.dart';
import '../select_slip_controller.dart';

/// 伝票選択 tab bar
class SelectSlipBottomTabBar extends ConsumerWidget {
  /// constructor
  const SelectSlipBottomTabBar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BottomTabBar(
      leading: PacerBackButton(
        onPressed: () => context.pop(),
      ),
      actions: [
        OutlineRoundTextButton(
          kSure,
          onPressed: () => _onConfirmClick(ref),
        ),
      ],
    );
  }

  void _onConfirmClick(
    WidgetRef ref,
  ) {
    final selectSlipNo = ref.read(selectSlipNoStateProvider);
    if (selectSlipNo == null) {
      showAlertDialog(
        context: ref.context,
        title: kPleaseSelectOneSlip,
      );
      return;
    }

    final info = _getSelectSlip(ref);
    if (info == null) return;

    ref.read(tempVariateStateProvider.notifier).updateEntrance(2);
    final tempInfo = ref.read(getTempInfoStateProvider);
    final copyGoodsInfo = tempInfo?.goods.copyWith(info.vendorCode);
    final copyTempInfo = tempInfo?.copyWith(copyGoodsInfo);
    ref.read(getTempInfoStateProvider.notifier).updateInfo(copyTempInfo);

    ref.read(getOrderDataForSlipStateProvider.notifier).getData(info.slipNo, info.productCode, isBread: false);
    ref.context.pop();
  }

  SelectSlipInfo? _getSelectSlip(WidgetRef ref) {
    final list = ref.read(slipInfoListStateProvider).value;
    final selectSlipNo = ref.read(selectSlipNoStateProvider);
    if (list == null) return null;
    for (final item in list) {
      if (selectSlipNo == item.slipNo) {
        for (final info in item.goodsList) {
          if (info.productCode == ref.read(getTempInfoStateProvider)?.goods.productCode) {
            return info;
          }
        }
      }
    }
    return null;
  }
}
