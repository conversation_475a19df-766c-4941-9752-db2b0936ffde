import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../config/strings.dart';
import '../confirm/select_slip_code_state.dart';
import 'single_edit_number_controller.dart';
import 'widgets/single_edit_bottom_tab_bar.dart';
import 'widgets/single_edit_number_cell.dart';

/// 単品検品確定ページ
class SingleEditNumberPage extends HookConsumerWidget {
  /// init
  const SingleEditNumberPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goodsArray = ref.watch(confirmGoodsListProvider);
    final slipCode = ref.read(selectSlipCodeStateProvider);

    ref.listen(confirmGoodsListProvider, (previous, next) {
      final list = next.asData?.value;
      if (list == null) return;
      if (list.isEmpty) {
        showAlertDialog(context: context, title: kNoData);
      }
    });

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text('$kConfirmRegister②'),
      ),
      body: switch (goodsArray) {
        AsyncData(:final value) => Padding(
            padding: const EdgeInsets.symmetric(
              vertical: _lineHeight,
              horizontal: _edge,
            ),
            child: Column(
              children: [
                _Header(
                  slipCode: slipCode,
                ),
                Expanded(
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      final goodsInfo = value[index];

                      return SingleEditNumberCell(
                        lineIndex: index,
                        goods: goodsInfo,
                        isSelected: false,
                        onPressed: () {},
                      );
                    },
                    separatorBuilder: (context, _) => const SizedBox(
                      height: 6,
                    ),
                    itemCount: value.length,
                  ),
                ),
              ],
            ),
          ),
        AsyncError(:final error) => Text(
            switch (error) {
              UnknownException() => error.message,

              /// 取得に失敗しました
              _ => kRequestError,
            },
          ),
        AsyncLoading() => const Center(
            child: CircularProgressIndicator(),
          ),
      },
      bottomNavigationBar: const SingleEditBottomTabBar(),
    );
  }
}

class _Header extends StatelessWidget {
  const _Header({
    required this.slipCode,
  });

  /// 伝票NO
  final String slipCode;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyLarge?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(kLoginNumber, style: texts.titleMedium),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 14,
          ),
          child: Text(
            '$kReceiptsNo：$slipCode',
            style: texts.titleMedium?.copyWith(color: colors.primary),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: colors.surfaceContainerHighest,
            border: Border.fromBorderSide(
              BorderSide(
                color: colors.outlineVariant,
              ),
            ),
          ),
          margin: const EdgeInsets.only(bottom: 12),
          child: IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      '$kLine/$kNumberOfOrders',
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  child: Text(
                    kPlanNumber,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  child: Text(
                    kInspectionNumber,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
