import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/single_inspection_service.dart';
import '../../../../domain/warehouse/history_info.dart';

part 'history_controller.g.dart';

/// history list
@riverpod
class HistoryList extends _$HistoryList {
  @override
  FutureOr<List<HistoryInfo>> build() async {
    return ref.read(singleInspectionServiceProvider).getHistoryData();
  }

  /// upload
  void reload() {
    ref.read(selectGoodsStateProvider.notifier).clear();
    ref.invalidateSelf();
  }

  /// sort list
  void sort({bool isAscending = true}) {
    final list = state.value;
    if (list == null || list.isEmpty) return;
    final status = ref.read(listSortStatusProvider);
    if (status != isAscending) {
      state = AsyncData(list.reversed.toList());
      ref.read(listSortStatusProvider.notifier).update(isAscending: isAscending);
    }
  }
}

/// sort status
@riverpod
class ListSortStatus extends _$ListSortStatus {
  /// init
  @override
  bool build() => false;

  /// upload
  void update({bool isAscending = true}) {
    log('sort isAscending: $isAscending');
    state = isAscending;
  }
}

/// 选中商品
@riverpod
class SelectGoodsState extends _$SelectGoodsState {
  /// init
  @override
  HistoryInfo? build() {
    final goodsArray = ref.watch(historyListProvider).asData?.value ?? [];

    return goodsArray.isEmpty ? null : goodsArray.first;
  }

  /// update selected goods
  Future<void> update(HistoryInfo? goods) async {
    log('scrap reason select: $goods');
    state = goods;
  }

  /// clean selected goods
  void clear() {
    state = null;
  }
}
