import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/jan_code_text_form_field.dart';
import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../../../../routing/app_router.dart';
import '../../../../../../themes/app_color_scheme.dart';

import '../../../../../../utils/async_util.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../../domain/product_code.dart';
import '../../../../domain/single/enum/ip_status.dart';
import '../../../../domain/single/enum/order_type.dart';
import '../../../../domain/single/goods_and_vendor_info.dart';
import '../../../../domain/single/order.dart';
import '../../../config/strings.dart';
import '../../../routing/acceptance_inspection_route.dart';
import '../../single_inspection/form/single_inspection_form_controller.dart';
import 'widgets/single_inspection_form_bottom_tab_bar.dart';
import 'widgets/single_inspection_form_table.dart';

/// alert dialog key
final alertGlobalKey = GlobalKey<State>();

/// 単品検品 登録ページ
class SingleInspectionFormPage extends HookConsumerWidget {
  /// init
  const SingleInspectionFormPage({super.key});

  static const double _lineHeight = 8;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    /// 納品数量の入力値を固定するか？初期値はfalse。
    final janTextController = useTextEditingController();
    final inputJanState = ref.watch(getGoodsAndVendorInfoStateProvider);

    /// janCodeの入力のFocusNode
    final janFocusNode = useFocusNode();

    /// is need check goods and vendor info
    final isNeedCheckInfo = useState(false);

    /// First call
    useEffect(
      () {
        delayed(() {
          ref.read(globalLoadingServiceProvider.notifier).wrap(
                ref.read(initDataStateProvider.notifier).initConfigData(),
              );
        });
        return;
      },
      [],
    );

    ref
      ..listen(productCodeProvider, (previous, next) {
        janTextController.text = next;
      })
      ..listen(initDataStateProvider, (previous, next) async {
        /// init
        next.showSnackBarOnError(context);
        final ipStatus = next.asData?.value;
        if (ipStatus == null) return;
        switch (ipStatus) {
          case IpStatus.unspecified:
            await showAlertDialog(
              key: alertGlobalKey,
              context: context,
              title: kPORActivationFlagIsNotSet,
            );
          case IpStatus.abnormalWithData:
            final isOk = await showAlertDialog(
              key: alertGlobalKey,
              context: ref.context,
              title: kRestorePreviousData,
              cancelActionText: kNo,
              defaultActionText: kYes,
            );
            if (isOk != null && isOk) {
              await ref.read(getProgressStateProvider.notifier).getProgress();
            } else {
              await ref.read(deleteCacheDataByCurrentIpStateProvider.notifier).delete();
            }
          case IpStatus.normal:
            await ref.read(globalLoadingServiceProvider.notifier).wrap(
                  ref.read(getInitialSingleDataStateProvider.notifier).getData(),
                );
        }
      })
      ..listen(deleteCacheDataByCurrentIpStateProvider, (previous, next) async {
        await ref.read(getInitialSingleDataStateProvider.notifier).getData();
      })
      ..listen(getInitialSingleDataStateProvider, (previous, next) {
        log('get initial single data');
      })
      ..listen(getGoodsAndVendorInfoStateProvider, (previous, next) {
        final info = next.asData?.value;
        if (info == null) return;
        if (info.slipList.isEmpty && info.goods.productCode.isEmpty) {
          showAlertDialog(
            context: context,
            title: kInputCorrectGoodsCode,
          );
          return;
        }

        /// 每次扫描需要做check
        if (isNeedCheckInfo.value) {
          isNeedCheckInfo.value = false;
          _checkInfo(ref, info);
        }
        ref.read(getTempInfoStateProvider.notifier).updateInfo(info);
      })
      ..listen(getScanCountDataStateProvider, (previous, next) {
        next.showSnackBarOnError(context);
        final info = next.asData?.value;
        if (info == null) return;
        ref.read(tempVariateStateProvider.notifier).updateScanId(info.idCount.toString());
        ref.read(progressStateProvider.notifier).updateValue(
              info.scanCount.toString(),
              info.slipCount.toString(),
              info.idCount.toString(),
            );
      })
      ..listen(updateScanDataFlgStateProvider, (previous, next) {
        if (next.asData?.value ?? true) {
          if (ref.read(tempVariateStateProvider).isOverlapSlipFlg) {
            _pushSelectSlipPage(ref);
          } else {
            final tempQuantity = ref.read(tempVariateStateProvider).tempQuantity;
            ref.read(tempVariateStateProvider.notifier)
              ..updateQuantity(tempQuantity)
              ..updateTempQuantity(0);
          }
        }
      })
      ..listen(updateDeliveriesNumberStateProvider, (previous, next) {
        final tempQuantity = ref.read(tempVariateStateProvider).tempQuantity;
        ref.read(tempVariateStateProvider.notifier)
          ..updateQuantity(tempQuantity)
          ..updateTempQuantity(0);
      })
      ..listen(deleteAllDataStateProvider, (previous, next) {
        next.showSnackBarOnError(context);
        final isSuccess = next.value;
        if (isSuccess ?? false) {
          _cleanData(ref);

          /// close page
          const InspectionMenuRoute().go(context);
        }
      })
      ..listen(deleteDataStateProvider, (previous, next) {
        final isSuccess = next.value;
        if (isSuccess ?? false) {
          /// close alert
          context.pop();
          _cleanAllData(ref);
        }
      })
      ..listen(tempVariateStateProvider, (previous, next) {
        log('temp variate change');
      })
      ..listen(updateOrderStateProvider, (previous, next) {
        log('update order status: $next');
      })
      ..listen(getTempInfoStateProvider, (previous, next) {
        log('temp info change: $next');
      })
      ..listen(getOrderDataForSlipStateProvider, (previous, next) {
        next.showSnackBarOnError(context);
        final info = next.asData?.value;
        if (info == null) return;
        _dealWithOrderInfo(ref, info);
      })
      ..listen(inputNumberStateProvider, (previous, next) {
        log('input number: $next');
      })
      ..listen(progressStateProvider, (previous, next) {
        log('progress info: $next');
      })
      ..listen(getProgressStateProvider, (previous, next) {
        final progressInfo = next.asData?.value;
        if (progressInfo == null) return;
        ref.read(tempVariateStateProvider.notifier).updateScanId('1');
        ref.read(progressStateProvider.notifier).updateValue(
              '0',
              '0',
              '1',
            );
      });

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop || ref.watch(globalLoadingServiceProvider)) return;
        await _backClick(ref);
      },
      child: Scaffold(
        appBar: PacerAppBar(
          context: context,
          title: const Text(kScanRegistration),
        ),
        body: ListView(
          padding: const EdgeInsets.symmetric(horizontal: _edge),
          children: [
            const SizedBox(
              height: _lineHeight,
            ),
            _TopProgressWidget(),
            const SizedBox(
              height: _lineHeight,
            ),
            JanCodeTextFormField(
              kCode,
              focusNode: janFocusNode,
              controller: janTextController,
              onFieldSubmitted: (value) => _onEditingComplete(
                ref,
                value,
                janTextController,
                isNeedCheckInfo,
              ),
              routeFullPath: '/inspection/single',
            ),
            const SizedBox(
              height: _lineHeight,
            ),
            switch (inputJanState) {
              AsyncData(:final value) => value == null || value.slipList.isEmpty
                  ? Center(
                      /// バーコードをスキャンまたは入力してください。
                      child: Text(
                        kPleaseInputCode,
                        style: texts.bodyLarge,
                      ),
                    )
                  : SingleInspectionTable(
                      info: value,
                      onFieldSubmitted: (value) {
                        _checkInputNumber(ref, value);
                      },
                    ),
              AsyncError(:final error) => Text(
                  switch (error) {
                    UnknownException() => error.message,
                    _ => kGetGoodsError,
                  },
                ),
              AsyncLoading() => const SizedBox.shrink(),
            },
            const SizedBox(
              height: _lineHeight,
            ),
            SwitchListTile(
              key: GlobalKey(),
              value: ref.watch(fixedInputValueStateProvider),
              onChanged: (newValue) => ref.read(fixedInputValueStateProvider.notifier).updateValue(isFixed: newValue),
              title: const Text(kFixedInputValue),
              tileColor: colors.button,
              shape: OutlineInputBorder(
                borderSide: BorderSide(
                  color: colors.primary.withOpacity(0.2),
                ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: SingleInspectionFormBottomTabBar(
          backCallback: () => _backClick(ref),
          cancelCallback: () => _cancelClick(ref),
          cleanCallback: () => _cleanData(ref),
        ),
        resizeToAvoidBottomInset: false,
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: ScanFloatingIconButton(
          onScan: (String value) => _onEditingComplete(
            ref,
            value,
            janTextController,
            isNeedCheckInfo,
          ),
        ),
      ),
    );
  }

  /// 新しいJANコードが入力された際には、入力前のチェックとJANのチェックを行います。
  void _onEditingComplete(
    WidgetRef ref,
    String value,
    TextEditingController controller,
    ValueNotifier<bool> isNeedCheckInfo,
  ) {
    log('input scan code value:[$value]');
    if (alertGlobalKey.currentContext != null) {
      controller.text = ref.read(productCodeProvider);
      return;
    }
    final inputValue = value.trim().replaceAll('\r', '');
    isNeedCheckInfo.value = true;
    if (_checkJanIsCorrect(ref.context, inputValue)) {
      ref.read(getTempInfoStateProvider.notifier).clear();
      ref.read(productCodeProvider.notifier)
        ..clear()
        ..update(inputValue);
    } else {
      _cleanData(ref);
    }
  }

  // clean input data
  void _cleanData(WidgetRef ref) {
    ref.read(productCodeProvider.notifier).clear();
    ref.read(progressStateProvider.notifier).clear();
    ref.read(getGoodsAndVendorInfoStateProvider.notifier).clear();
  }

  void _cleanAllData(WidgetRef ref) {
    ref.read(tempVariateStateProvider.notifier).clear();
    ref.read(getTempInfoStateProvider.notifier).clear();
    ref.read(fixedInputValueStateProvider.notifier).clear();
    _cleanData(ref);
  }

  Future<void> _checkInfo(WidgetRef ref, GoodsAndVendorInfo info) async {
    final slipIsEmpty = info.slipList.isEmpty;
    final tempStateProvider = ref.read(tempVariateStateProvider.notifier)
      ..updateUnInspectedNum(0)
      ..updateVendorCdFlg(info.goods.isBread ? '1' : '0')
      ..updateOrderPlanedNum(slipIsEmpty ? 0.0 : info.slipList.first.asnNum)
      ..updateIsOverlapSlipFlg()
      ..updateOrderNum(slipIsEmpty ? 0.0 : info.slipList.first.orderNum);
    if (info.goods.isBread) {
      switch (info.checkOrderData) {
        case OrderType.unspecified:
          await showAlertDialog(
            key: alertGlobalKey,
            context: ref.context,
            title: kScannedProductsHaveNotBeenOrdered,
          );
          _cleanData(ref);
        case OrderType.add:
          tempStateProvider.updateUnInspectedNum(info.unInspectedCount);
          final isOk = await showAlertDialog(
            key: alertGlobalKey,
            context: ref.context,
            title: kIsInspection,
            cancelActionText: kNo,
            defaultActionText: kYes,
          );
          if (isOk != null && isOk) {
            _dealWithDataPushAdditionScanPage(
              ref,
              info.slipList.first.deliveryNum,
            );
          } else {
            _cleanData(ref);
          }
        case OrderType.today:
          tempStateProvider.updateUnInspectedNum(info.unInspectedCount);
          if (info.slipList.length > 1) {
            _cleanData(ref);
            _pushSelectSlipPage(ref);
          } else {
            final number = info.slipList.first.deliveryNum;
            _updateQuantity(ref, number);
            _updateInputNumber(ref, number.toInt().toString());
            await ref.read(getScanCountDataStateProvider.notifier).getData();
            await ref.read(updateOrderStateProvider.notifier).updateOrder(number);
          }
        case OrderType.nextDay:
          tempStateProvider.updateUnInspectedNum(info.unInspectedCount);

          final checkScannedSlipMsg = int.tryParse(info.checkScannedSlipMessage);
          if (checkScannedSlipMsg == 0) {
            final isOk = await showAlertDialog(
              context: ref.context,
              title: '納品予定日が${''}の商品です。検品を行ないますか？',
              cancelActionText: kNo,
              defaultActionText: kYes,
            );
            if (isOk != null && isOk) {
              if (info.slipList.length > 1) {
                _cleanData(ref);
                _pushSelectSlipPage(ref);
              } else {
                final number = info.slipList.first.deliveryNum;
                _updateQuantity(ref, number);
                await ref.read(getScanCountDataStateProvider.notifier).getData();
                await ref.read(updateOrderStateProvider.notifier).updateOrder(number);
              }
            } else {
              _cleanData(ref);
            }
          } else {
            if (info.slipList.length > 1) {
              _cleanData(ref);
              _pushSelectSlipPage(ref);
            } else {
              final number = info.slipList.first.deliveryNum;
              _updateQuantity(ref, number);
              await ref.read(getScanCountDataStateProvider.notifier).getData();
              await ref.read(updateOrderStateProvider.notifier).updateOrder(number);
            }
          }
        case OrderType.todayAndNextDay:

        /// 誤差報告を移動する (废弃)
        case OrderType.scanned:
          await showAlertDialog(
            key: alertGlobalKey,
            context: ref.context,
            title: kAlreadyScanned,
          );
          _cleanData(ref);
      }
    } else {
      switch (info.checkOrderData) {
        case OrderType.unspecified:
          await showAlertDialog(
            key: alertGlobalKey,
            context: ref.context,
            title: kScannedProductsHaveNotBeenOrdered,
          );
          _cleanData(ref);
        case OrderType.today:
          _cleanData(ref);
          _pushSelectSlipPage(ref);
        case OrderType.nextDay:
          final isFixedOne = ref.read(fixedInputValueStateProvider);
          if (isFixedOne) {
            final slipInfo = info.slipList.first;
            _updateQuantity(ref, slipInfo.deliveryNum);
            await ref.read(getScanCountDataStateProvider.notifier).getData();

            /// 检品数（1）+ 已经检品的数量（DELIV_NUM）> 发注数（PO_NUM）
            if (1 + slipInfo.deliveryNum > slipInfo.orderNum) {
              final ctx = ref.context;
              if (!ctx.mounted) return;
              await showAlertDialog(
                key: alertGlobalKey,
                context: ctx,
                title: kInputValueGreaterThanPlanedQuantity,
              );
            } else {
              final number = 1 + slipInfo.deliveryNum;
              await ref.read(updateDeliveriesNumberStateProvider.notifier).updateNumber(number);
            }
          } else {
            final isOk = await showAlertDialog(
              key: alertGlobalKey,
              context: ref.context,
              title: kIsInspection,
              cancelActionText: kNo,
              defaultActionText: kYes,
            );
            if (isOk ?? false) {
              _dealWithDataPushAdditionScanPage(
                ref,
                info.slipList.first.deliveryNum,
              );
            } else {
              _cleanData(ref);
            }
          }
        case OrderType.todayAndNextDay:
          await showAlertDialog(
            key: alertGlobalKey,
            context: ref.context,
            title: kAlreadyScanned,
          );
          _cleanData(ref);
        case _:
          final isFixedOne = ref.read(fixedInputValueStateProvider);
          final number = isFixedOne ? 1.0 : info.slipList.first.deliveryNum;
          _updateQuantity(ref, number);
          _updateInputNumber(ref, number.toInt().toString());
          await ref.read(getScanCountDataStateProvider.notifier).getData();
          await ref.read(updateOrderStateProvider.notifier).updateOrder(number);
      }
    }
  }

  void _dealWithDataPushAdditionScanPage(
    WidgetRef ref,
    double deliveryNum,
  ) {
    _updateQuantity(ref, deliveryNum);
    ref.read(getScanCountDataStateProvider.notifier).getData();
    ref.read(updateOrderStateProvider.notifier).updateOrder(deliveryNum);
    _pushAdditionScanPage(ref);
    _cleanData(ref);
  }

  void _updateInputNumber(WidgetRef ref, String number) {
    ref.read(inputNumberStateProvider.notifier).updateNumber(number);
  }

  Future<void> _checkInputNumber(WidgetRef ref, String input) async {
    final oldInputNumber = ref.read(inputNumberStateProvider);
    _updateInputNumber(ref, input);
    if (input.isEmpty) return;
    final inputValue = double.tryParse(input) ?? 0.0;
    final info = ref.read(getGoodsAndVendorInfoStateProvider).value;
    if (info == null) return;

    final tempStateProvider = ref.read(tempVariateStateProvider.notifier);
    final tempVariate = ref.read(tempVariateStateProvider);
    final isFromMenu = tempVariate.isFromMenu;

    /// InputCheckVar.isOverlapSlipFlg 单品检品进入是false, 传票选择、追加扫描可能是true
    final vendorCdFlg = tempVariate.vendorCdFlg;
    final unInspectedNum = tempVariate.unInspectedNum;
    final orderPlanedNum = tempVariate.orderPlanedNum;
    final orderNum = tempVariate.orderNum;
    var tempOrderCount = orderPlanedNum > 0 ? orderPlanedNum : orderNum;
    final quantity = tempVariate.quantity;
    var errorNum = 0;
    if (quantity != orderPlanedNum && !isFromMenu) {
      errorNum = (inputValue - tempOrderCount + quantity).toInt();
    } else {
      errorNum = (inputValue - tempOrderCount).toInt();
    }
    tempStateProvider.updateErrorNum(
      errorNum,
    );
    if (isFromMenu) {
      if (vendorCdFlg == '1') {
        if (inputValue > tempOrderCount) {
          if (tempOrderCount == 0) {
            tempStateProvider.updateIsChecked(isChecked: true);
            await ref.read(updateScanDataFlgStateProvider.notifier).updateScanData(inputValue);
          } else {
            if (unInspectedNum > 1) {
              final isOk = await showAlertDialog(
                key: alertGlobalKey,
                context: ref.context,
                title: kTipCanReplace.replaceAll('***', errorNum.toString()),
                cancelActionText: kNo,
                defaultActionText: kYes,
              );
              if (isOk != null && isOk) {
                tempStateProvider
                  ..updateIsOverlapSlipFlg(isOverlapSlipFlg: true)
                  ..updateIsFromMenu()
                  ..updateUnInspectedNum(unInspectedNum - 1)
                  ..updateIsChecked();
                await ref.read(updateScanDataFlgStateProvider.notifier).updateScanData(tempOrderCount);
              } else {
                tempStateProvider
                  ..updateIsOverlapSlipFlg()
                  ..updateIsFromMenu()
                  ..updateUnInspectedNum(unInspectedNum - 1)
                  ..updateIsChecked(isChecked: true);
                await ref.read(updateScanDataFlgStateProvider.notifier).updateScanData(inputValue);
              }
            } else {
              final isOk = await showAlertDialog(
                key: alertGlobalKey,
                context: ref.context,
                title: kExcessQuantity,
                cancelActionText: kNo,
                defaultActionText: kYes,
              );
              if (isOk != null && isOk) {
                tempStateProvider
                  ..updateIsOverlapSlipFlg()
                  ..updateIsChecked(isChecked: true);
                await ref.read(updateScanDataFlgStateProvider.notifier).updateScanData(tempOrderCount);
              } else {
                _cleanData(ref);
              }
            }
          }
        } else {
          tempStateProvider.updateIsChecked();
          await ref.read(updateScanDataFlgStateProvider.notifier).updateScanData(inputValue);
        }
      } else {
        tempOrderCount = orderPlanedNum > 0 ? orderPlanedNum : orderNum;
        if (inputValue > tempOrderCount) {
          await showAlertDialog(
            key: alertGlobalKey,
            context: ref.context,
            title: kInputValueGreaterThanPlanedQuantity,
          );
          _updateInputNumber(ref, oldInputNumber);
        } else {
          final tempQuantity = inputValue;
          tempStateProvider.updateTempQuantity(tempQuantity);
          await ref.read(updateDeliveriesNumberStateProvider.notifier).updateNumber(tempQuantity);
        }
      }
    } else {
      if (vendorCdFlg == '1') {
        if (inputValue + quantity > orderNum) {
          final tempQuantity = inputValue + quantity;
          tempStateProvider.updateTempQuantity(tempQuantity);
          if (orderNum == 0) {
            tempStateProvider.updateIsChecked(isChecked: true);
            await ref.read(updateDeliveriesNumberStateProvider.notifier).updateNumber(tempQuantity);
          } else {
            if (unInspectedNum <= 1) {
              final isOk = await showAlertDialog(
                key: alertGlobalKey,
                context: ref.context,
                title: kExcessQuantity,
                cancelActionText: kNo,
                defaultActionText: kYes,
              );
              if (isOk != null && isOk) {
                tempStateProvider.updateIsChecked(isChecked: true);
                await ref.read(updateScanDataFlgStateProvider.notifier).updateScanData(tempQuantity);
              }
            } else {
              final isOk = await showAlertDialog(
                key: alertGlobalKey,
                context: ref.context,
                title: kTipCanReplace.replaceAll('***', errorNum.toString()),
                cancelActionText: kNo,
                defaultActionText: kYes,
              );
              if (isOk != null && isOk) {
                /// 誤差登録機能上で伝票選択を行なう
                tempStateProvider
                  ..updateIsOverlapSlipFlg(isOverlapSlipFlg: true)
                  ..updateIsFromMenu()
                  ..updateUnInspectedNum(unInspectedNum - 1)
                  ..updateIsChecked();
                await ref.read(updateScanDataFlgStateProvider.notifier).updateScanData(tempOrderCount);
              } else {
                tempStateProvider
                  ..updateIsFromMenu()
                  ..updateUnInspectedNum(unInspectedNum - 1);
                final tempSum = errorNum + tempOrderCount;
                await _inputSurplusQuantity(ref, tempSum);
              }
            }
          }
        } else {
          final tempQuantity = inputValue + quantity;
          tempStateProvider
            ..updateTempQuantity(tempQuantity)
            ..updateIsChecked();
          await ref.read(updateDeliveriesNumberStateProvider.notifier).updateNumber(tempQuantity);
        }
      } else {
        if (inputValue + quantity > orderNum) {
          await showAlertDialog(
            key: alertGlobalKey,
            context: ref.context,
            title: kInputValueGreaterThanPlanedQuantity,
          );
          _updateInputNumber(ref, oldInputNumber);
        } else {
          final tempQuantity = inputValue + quantity;
          tempStateProvider.updateTempQuantity(tempQuantity);
          await ref.read(updateDeliveriesNumberStateProvider.notifier).updateNumber(tempQuantity);
        }
      }
    }
  }

  Future<void> _inputSurplusQuantity(WidgetRef ref, double num) async {
    final isOk = await showAlertDialog(
      key: alertGlobalKey,
      context: ref.context,
      title: kExcessQuantity,
      cancelActionText: kNo,
      defaultActionText: kYes,
    );
    if (isOk != null && isOk) {
      ref.read(tempVariateStateProvider.notifier)
        ..updateIsChecked(isChecked: true)
        ..updateIsOverlapSlipFlg();
      await ref.read(updateScanDataFlgStateProvider.notifier).updateScanData(num);
    } else {
      _cleanData(ref);
    }
  }

  void _dealWithOrderInfo(WidgetRef ref, Order info) {
    ref.read(progressStateProvider.notifier).updateValue(
          info.scanCountInfo.scanCount.toString(),
          info.scanCountInfo.slipCount.toString(),
          info.scanCountInfo.idCount.toString(),
        );

    if (info.slipList.isNotEmpty) {
      final slipInfo = info.slipList.first;
      final isFixedOne = ref.read(fixedInputValueStateProvider);
      final tempVariateState = ref.read(tempVariateStateProvider.notifier);
      ref.read(getTempInfoStateProvider.notifier).updateSlip(slipInfo);
      tempVariateState
        ..updateOrderNum(slipInfo.orderNum)
        ..updateOrderPlanedNum(slipInfo.asnNum)
        ..updateQuantity(isFixedOne ? 1 : slipInfo.deliveryNum)
        ..updateScanId(info.scanCountInfo.idCount.toString());

      var tempVariate = ref.read(tempVariateStateProvider);
      if (tempVariate.isOverlapSlipFlg) {
        ref.read(tempVariateStateProvider.notifier).updateIsOverlapSlipFlg();
        if (tempVariate.errorNum > tempVariate.quantity) {
          showAlertDialog(
            key: alertGlobalKey,
            context: ref.context,
            title: kMoreThanOrderQuantityWarning,
          );
          _cleanData(ref);
          return;
        } else {
          tempVariateState
            ..updateIsFromMenu(isFromMenu: false)
            ..updateQuantity(tempVariate.errorNum.toDouble());
        }
      }
      tempVariate = ref.read(tempVariateStateProvider);
      final tempGoodsAndVendorInfo = ref.read(getTempInfoStateProvider);
      ref.read(getGoodsAndVendorInfoStateProvider.notifier).updateInfo(tempGoodsAndVendorInfo);

      ref.read(updateOrderStateProvider.notifier).updateOrder(
            tempVariate.quantity,
          );

      ref.read(productCodeProvider.notifier).update(tempGoodsAndVendorInfo?.goods.productCode ?? '');

      _updateInputNumber(ref, tempVariate.quantity.toInt().toString());
    }
  }

  Future<void> _backClick(WidgetRef ref) async {
    final isOk = await showAlertDialog(
      key: alertGlobalKey,
      context: ref.context,
      title: kCancelScanRegistrationDeleteData,
      cancelActionText: kNo,
      defaultActionText: kYes,
    );
    if (isOk != null && isOk) {
      await ref.read(globalLoadingServiceProvider.notifier).wrap(
            ref.read(deleteAllDataStateProvider.notifier).delete(),
          );
    }
  }

  void _updateQuantity(WidgetRef ref, double number) {
    ref.read(tempVariateStateProvider.notifier).updateQuantity(number);
  }

  /// ProductCodeの入力をチェック
  bool _checkJanIsCorrect(BuildContext context, String input) {
    if (input.isEmpty) return false;

    final isProductCode = input.isValidProductCode();
    if (!isProductCode) {
      /// 正しい商品を入力してください。
      showSnackBar(context, kInputCorrectGoodsCode, isErrorStyle: true);
    }

    return isProductCode;
  }

  Future<void> _cancelClick(WidgetRef ref) async {
    final goodsCode = ref.read(productCodeProvider);
    final slipInfo = ref.read(getGoodsAndVendorInfoStateProvider).value?.slipList.first;
    if (slipInfo == null) {
      _cleanAllData(ref);
      return;
    }
    if (goodsCode.isNotEmpty && slipInfo.slipNo.isNotEmpty) {
      final isOk = await showAlertDialog(
        key: alertGlobalKey,
        context: ref.context,
        title: kDoYouWantDeleteIt,
        cancelActionText: kNo,
        defaultActionText: kYes,
      );
      if (isOk != null && isOk) {
        _deleteData(ref);
      }
    }
  }

  void _deleteData(WidgetRef ref) {
    ref.read(deleteDataStateProvider.notifier).delete();
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final deleteDataState = ref.watch(deleteDataStateProvider);

            return AlertDialog(
              key: alertGlobalKey,
              title: const Text(kDeleting),
              content: switch (deleteDataState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncData() || AsyncLoading() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (deleteDataState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _pushAdditionScanPage(WidgetRef ref) {
    const AdditionScanRoute().go(ref.context);
  }

  void _pushSelectSlipPage(WidgetRef ref) {
    const SelectSlipRoute().go(ref.context);
  }
}

class _TopProgressWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;
    final progress = ref.watch(progressStateProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          kScanAndInputNum,
          style: texts.bodyLarge,
        ),
        const SizedBox(
          height: 6,
        ),
        RichText(
          text: TextSpan(
            text: kStep,
            style: texts.bodyLarge,
            children: <TextSpan>[
              TextSpan(
                text: progress,
                style: texts.bodyLarge?.copyWith(
                  color: colors.error,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
