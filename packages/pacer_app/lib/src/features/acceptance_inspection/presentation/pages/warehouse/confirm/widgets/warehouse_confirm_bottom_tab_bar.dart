import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../exceptions/app_exception.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../../domain/warehouse/login_slip.dart';
import '../../../../config/strings.dart';
import '../../../../routing/acceptance_inspection_route.dart';
import '../../../../widgets/bottom_tab_bar.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../warehouse_confirm_controller.dart';

/// 確定登録 tab bar
class WarehouseConfirmBottomTabBar extends ConsumerWidget {
  /// constructor
  const WarehouseConfirmBottomTabBar({
    super.key,
    required this.selectSlipList,
  });

  /// 選択した传票リスト
  final List<LoginSlip> selectSlipList;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(confirmSelectSlipStateProvider, (previous, next) {
      final isSuccess = next.asData?.value;
      if (isSuccess != null && isSuccess) {
        context.pop();
        _showConfirmAlert(context);
      }
    });

    return BottomTabBar(
      leading: PacerBackButton(
        onPressed: () => _backMenuList(context),
      ),
      actions: [
        OutlineRoundTextButton(
          kFix,
          onPressed: () => _onEditClick(context, ref),
        ),
        const SizedBox(width: 8),
        OutlineRoundTextButton(
          kSure,
          onPressed: selectSlipList.isNotEmpty ? () => _onConfirmClick(context, ref) : null,
        ),
      ],
    );
  }

  Future<void> _onEditClick(
    BuildContext context,
    WidgetRef ref,
  ) async {
    const WarehouseEditNumberRoute().go(context);
  }

  Future<void> _showConfirmAlert(BuildContext context) async {
    final isSuccess = await showAlertDialog(
      context: context,
      title: kConfirmed,
    );
    if ((isSuccess ?? false) && context.mounted) {
      context
        ..pop()
        ..pop();
    }
  }

  void _backMenuList(BuildContext context) {
    const WarehouseInspectionScanRegistrationRoute().go(context);
  }

  Future<void> _onConfirmClick(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final isOk = await showAlertDialog(
      context: context,
      title: kAreYouSureToConfirm,
      cancelActionText: kNo,
      defaultActionText: kYes,
    );
    if (isOk == null || !isOk) return;
    _insertData(ref);
  }

  void _insertData(WidgetRef ref) {
    ref.read(confirmSelectSlipStateProvider.notifier).confirmSlips(
          ref
              .read(selectSlipListProvider)
              .map(
                (e) => e.slipNumber,
              )
              .toList(),
        );
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final confirmSlipState = ref.watch(confirmSelectSlipStateProvider);

            return AlertDialog(
              /// データ登録中、少々お待ちください。
              title: const Text(kInsertingGoods),
              content: switch (confirmSlipState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncLoading() || AsyncData() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (confirmSlipState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}
