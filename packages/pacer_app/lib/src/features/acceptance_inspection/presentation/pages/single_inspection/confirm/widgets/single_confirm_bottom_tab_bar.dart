import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../exceptions/app_exception.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../config/strings.dart';
import '../../../../routing/acceptance_inspection_route.dart';
import '../../../../widgets/bottom_tab_bar.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../../form/single_inspection_form_controller.dart' as form;
import '../select_slip_code_state.dart';
import '../single_confirm_controller.dart';

/// 確定登録 tab bar
class SingleConfirmBottomTabBar extends ConsumerWidget {
  /// constructor
  const SingleConfirmBottomTabBar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref
      ..listen(confirmSelectSlipStateProvider, (previous, next) {
        final isSuccess = next.asData?.value;
        if (isSuccess != null && isSuccess) {
          context.pop();
          _showConfirmAlert(ref.context);
        }
      })
      ..listen(getProgressStateProvider, (previous, next) {
        final value = next.asData?.value;
        if (value == null) return;
        context.pop();
        ref.read(form.tempVariateStateProvider.notifier).updateEntrance(1);
        _backMenuList(context);
      });

    return BottomTabBar(
      leading: PacerBackButton(
        onPressed: () => _back(ref),
      ),
      actions: [
        OutlineRoundTextButton(
          kFix,
          onPressed: () => _onEditClick(context, ref),
        ),
        const SizedBox(width: 8),
        OutlineRoundTextButton(
          kSure,
          onPressed: () => _onConfirmClick(context, ref),
        ),
      ],
    );
  }

  Future<void> _onEditClick(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final selectCellInfo = ref.read(selectSlipStateProvider);
    if (selectCellInfo == null) return;
    ref.read(selectSlipCodeStateProvider.notifier).update(selectCellInfo.slipNo);
    if (selectCellInfo.isHavePorVendor) {
      /// 「伝票検品　確認登録②」へ遷移
      const SingleSlipConfirmRoute().go(context);
    } else {
      /// 「確認登録②」へ遷移
      const SingleEditNumberRoute().go(context);
    }
  }

  Future<void> _showConfirmAlert(BuildContext context) async {
    final isOk = await showAlertDialog(
      context: context,
      title: kConfirmed,
    );
    if (isOk != null && isOk && context.mounted) {
      const InspectionMenuRoute().go(context);
    }
  }

  void _back(WidgetRef ref) {
    ref.read(getProgressStateProvider.notifier).getProgress();
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final progressState = ref.watch(getProgressStateProvider);

            return AlertDialog(
              title: const Text(''),
              content: switch (progressState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncLoading() || AsyncData() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (progressState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _backMenuList(BuildContext context) {
    context.pop();
  }

  Future<void> _onConfirmClick(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final dataArray = ref.read(confirmSlipListProvider).value;
    if (dataArray == null) return;

    final checkList = [
      kStatusConfirmed,
      kStatusCompletedDiff,
      kStatusCompletedAlternative,
      kStatusCompleted,
    ];
    var isCanConfirm = false;
    for (var i = 0; i < dataArray.length; i++) {
      final info = dataArray[i];
      if (info.statusName == kStatusIncomplete && !info.isSuspended) {
        break;
      }

      if ((info.statusName == kStatusIncomplete && info.isSuspended) || checkList.contains(info.statusName)) {
        isCanConfirm = true;
      }
    }

    if (isCanConfirm) {
      final isOk = await showAlertDialog(
        context: context,
        title: kConfirmDataIsOk,
        cancelActionText: kNo,
        defaultActionText: kYes,
      );
      if (isOk == null || !isOk) return;
      _insertData(ref);
    } else {
      await showAlertDialog(context: context, title: kHasIncompleteItem);
    }
  }

  void _insertData(WidgetRef ref) {
    ref.read(confirmSelectSlipStateProvider.notifier).confirmSlips();
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final confirmSlipState = ref.watch(confirmSelectSlipStateProvider);

            return AlertDialog(
              /// データ登録中、少々お待ちください。
              title: const Text(kInsertingGoods),
              content: switch (confirmSlipState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncLoading() || AsyncData() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (confirmSlipState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}
