// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'single_slip_confirm_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$confirmGoodsListHash() => r'1ca19d8cc27ae1c8e753a086370cd6b42d7465d8';

/// goods list
///
/// Copied from [ConfirmGoodsList].
@ProviderFor(ConfirmGoodsList)
final confirmGoodsListProvider = AutoDisposeAsyncNotifierProvider<ConfirmGoodsList, List<CorrectDetails>>.internal(
  ConfirmGoodsList.new,
  name: r'confirmGoodsListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmGoodsListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmGoodsList = AutoDisposeAsyncNotifier<List<CorrectDetails>>;
String _$confirmSelectGoodsStateHash() => r'f589f0ba261a34c02639338901abe3e723e1b48d';

/// 確定
///
/// Copied from [ConfirmSelectGoodsState].
@ProviderFor(ConfirmSelectGoodsState)
final confirmSelectGoodsStateProvider = AutoDisposeAsyncNotifierProvider<ConfirmSelectGoodsState, int?>.internal(
  ConfirmSelectGoodsState.new,
  name: r'confirmSelectGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmSelectGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmSelectGoodsState = AutoDisposeAsyncNotifier<int?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
