part of 'edit_page.dart';

/// 一覧表示
class _WarehouseEditNumberCell extends ConsumerWidget {
  /// 標準コンストラクタ
  const _WarehouseEditNumberCell({
    required this.lineIndex,
    required this.goods,
    required this.isSelected,
    required this.onPressed,
  });

  /// line number
  final int lineIndex;

  /// 商品
  final LoginGoods goods;

  /// 選択されているかどうか
  final bool isSelected;

  /// クリックイベントのコールバック
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final containerColor = isSelected ? colors.primaryContainer : colors.surface;

    final textColor = isSelected ? colors.onPrimaryContainer : colors.onSurface;

    final textTheme = texts.bodyLarge?.copyWith(
      color: textColor,
      height: 2,
    );

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: containerColor,
          border: Border.fromBorderSide(
            BorderSide(
              color: colors.outlineVariant,
            ),
          ),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        child: Column(
          children: [
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        (lineIndex + 1).toString(),
                        textAlign: TextAlign.center,
                        style: textTheme,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    flex: 3,
                    child: Tooltip(
                      message: goods.productNameRead,
                      child: Text(
                        goods.productNameRead,
                        textAlign: TextAlign.center,
                        style: textTheme,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    flex: 2,
                    child: Tooltip(
                      message: goods.subName,
                      child: Text(
                        goods.subName,
                        textAlign: TextAlign.center,
                        style: textTheme,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: colors.outlineVariant,
            ),
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        checkDoubleToString(goods.quantity),
                        textAlign: TextAlign.center,
                        style: textTheme,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: colors.button,
                        borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(20),
                        ),
                      ),
                      child: NumberTextField(
                        inputText: goods.changedReceiveNum ?? '',
                        textAlign: TextAlign.center,
                        isCanInputDouble: is28PrefixJan(goods.productCode),
                        onFieldSubmitted: (value) {
                          value = value.isEmpty ? '0' : value;
                          if (goods.isNumberExceedPlannedLimit(value)) {
                            showAlertDialog(
                              context: ref.context,
                              title: kInputValueGreaterThanPlanedQuantity,
                            );
                            value = goods.changedReceiveNum ?? '';
                          }

                          final newGoods = goods.copyWith(changedReceiveNum: value);
                          ref.read(confirmGoodsListProvider.notifier).replaceObjectAtIndexWithObject(goods, newGoods);
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
