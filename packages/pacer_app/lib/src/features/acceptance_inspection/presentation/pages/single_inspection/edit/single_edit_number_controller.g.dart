// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'single_edit_number_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$confirmGoodsListHash() => r'82979cb9e97fefb70f4aaa428be47519696f8d5d';

/// goods list
///
/// Copied from [ConfirmGoodsList].
@ProviderFor(ConfirmGoodsList)
final confirmGoodsListProvider = AutoDisposeAsyncNotifierProvider<ConfirmGoodsList, List<CorrectDetails>>.internal(
  ConfirmGoodsList.new,
  name: r'confirmGoodsListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmGoodsListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmGoodsList = AutoDisposeAsyncNotifier<List<CorrectDetails>>;
String _$confirmSelectGoodsStateHash() => r'5a48d199ff4d1875bc10f3c80c4646003fd1b690';

/// 確定
///
/// Copied from [ConfirmSelectGoodsState].
@ProviderFor(ConfirmSelectGoodsState)
final confirmSelectGoodsStateProvider = AutoDisposeAsyncNotifierProvider<ConfirmSelectGoodsState, int?>.internal(
  ConfirmSelectGoodsState.new,
  name: r'confirmSelectGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmSelectGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmSelectGoodsState = AutoDisposeAsyncNotifier<int?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
