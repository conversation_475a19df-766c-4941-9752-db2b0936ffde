// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'addition_scan_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getSlipInfoStateHash() => r'0b3530463705e88c4cdef2d14f789d0cdd8bdf08';

/// slip info
///
/// Copied from [GetSlipInfoState].
@ProviderFor(GetSlipInfoState)
final getSlipInfoStateProvider = AutoDisposeAsyncNotifierProvider<GetSlipInfoState, ScanData?>.internal(
  GetSlipInfoState.new,
  name: r'getSlipInfoStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getSlipInfoStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetSlipInfoState = AutoDisposeAsyncNotifier<ScanData?>;
String _$getOrderProgressStateHash() => r'73c2c851b4c9b07875fb0a303fb0bb4ad7e6adf0';

/// get progress
///
/// Copied from [GetOrderProgressState].
@ProviderFor(GetOrderProgressState)
final getOrderProgressStateProvider = AutoDisposeAsyncNotifierProvider<GetOrderProgressState, OrderProgress?>.internal(
  GetOrderProgressState.new,
  name: r'getOrderProgressStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getOrderProgressStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetOrderProgressState = AutoDisposeAsyncNotifier<OrderProgress?>;
String _$goodsInputNumberNotifierHash() => r'356e883afbfd998f2700994c9e81828d0820a75d';

/// all input number
///
/// Copied from [GoodsInputNumberNotifier].
@ProviderFor(GoodsInputNumberNotifier)
final goodsInputNumberNotifierProvider =
    AutoDisposeNotifierProvider<GoodsInputNumberNotifier, GoodsInputNumberState>.internal(
  GoodsInputNumberNotifier.new,
  name: r'goodsInputNumberNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$goodsInputNumberNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GoodsInputNumberNotifier = AutoDisposeNotifier<GoodsInputNumberState>;
String _$updateDeliveryNumberStateHash() => r'0e3660ea897a6c6b694a397e9580d922f499d333';

/// 納品数更新
///
/// Copied from [UpdateDeliveryNumberState].
@ProviderFor(UpdateDeliveryNumberState)
final updateDeliveryNumberStateProvider = AutoDisposeAsyncNotifierProvider<UpdateDeliveryNumberState, bool?>.internal(
  UpdateDeliveryNumberState.new,
  name: r'updateDeliveryNumberStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$updateDeliveryNumberStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateDeliveryNumberState = AutoDisposeAsyncNotifier<bool?>;
String _$updateScanDataFlgHash() => r'd670e86debb98f2acec55c3f8d5275f85c1ef74d';

/// 確定登録②　納品数・誤差登録
///
/// Copied from [UpdateScanDataFlg].
@ProviderFor(UpdateScanDataFlg)
final updateScanDataFlgProvider = AutoDisposeAsyncNotifierProvider<UpdateScanDataFlg, bool?>.internal(
  UpdateScanDataFlg.new,
  name: r'updateScanDataFlgProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$updateScanDataFlgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateScanDataFlg = AutoDisposeAsyncNotifier<bool?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
