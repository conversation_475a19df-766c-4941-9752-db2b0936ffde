import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/jan_code_text_form_field.dart';
import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/async_util.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../../domain/product_code.dart';
import '../../../../domain/warehouse/enum/inspection_status.dart';
import '../../../../domain/warehouse/enum/scan_type.dart';
import '../../../../domain/warehouse/goods.dart';
import '../../../../domain/warehouse/scan_info.dart';
import '../../../config/strings.dart';
import '../scan_registration/warehouse_inspection_scan_registration_controller.dart';
import 'warehouse_inspection_controller.dart';
import 'widgets/warehouse_inspection_bottom_tab_bar.dart';
import 'widgets/warehouse_inspection_table.dart';

/// 入庫検品 登録ページ
/// 商品をスキャンしたら、検品確定待ちリストに追加
/// 納品数を1に固定しているなら、納品数1で登録される
/// 固定していないなら、納品予定数で登録される
class WarehouseInspectionPage extends HookConsumerWidget {
  /// init
  const WarehouseInspectionPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    /// 納品数量の入力値を固定するか？初期値はfalse。
    final janTextController = useTextEditingController();
    final inputJanState = ref.watch(inputJanStateProvider);

    /// janCodeの入力のFocusNode
    final janFocusNode = useFocusNode();

    ref
      ..listen(productCodeProvider, (previous, next) {
        janTextController.text = next;
      })
      ..listen(getProgressStateProvider, (previous, next) {
        if (next.hasError) {
          next.showSnackBarOnError(context);
          return;
        }
      })
      ..listen(inputJanStateProvider, (previous, next) async {
        final goodsInfo = next.asData?.value;
        if (goodsInfo == null) return;
        janTextController.text = goodsInfo.productCode;
        ref.read(progressStateProvider.notifier).updateValue(
              '${goodsInfo.unInspectedSlipCount}/${goodsInfo.totalSlipCount}',
            );
        ref.read(slipCodeStateProvider.notifier).updateValue(goodsInfo.slipCode);

        if (goodsInfo.inspectionStatus == InspectionStatus.wasInspected) {
          final isOk = await showAlertDialog(
            context: ref.context,
            title: kIsInspection,
            cancelActionText: kNo,
            defaultActionText: kYes,
          );
          if (isOk != null && isOk) {
            _updateInputValue(ref, goodsInfo);
          } else {
            /// clean
            _cleanData(ref);
          }
        } else {
          _updateInputValue(ref, goodsInfo);
        }
      })
      ..listen(scanResultStateProvider, (previous, next) {
        if (next.hasError) {
          next.showSnackBarOnError(context);
          return;
        }
        final scanInfo = next.asData?.value;
        if (scanInfo == null) return;
        _checkScanResult(ref, scanInfo);
      })
      ..listen(inputNumberStateProvider, (previous, next) {
        log('input number: $next');
      })
      ..listen(fixedInputValueStateProvider, (previous, next) {
        log('fixed button state: $next');
      });

    /// First call
    useEffect(
      () {
        delayed(() {
          final codeInfo = ref.read(selectSlipAndGoodsProvider);
          ref.read(slipCodeStateProvider.notifier).updateValue(codeInfo.slipCode);

          /// jan
          if (codeInfo.goodsCode.isNotEmpty) {
            ref.read(productCodeProvider.notifier).update(codeInfo.goodsCode);
            ref.read(globalLoadingServiceProvider.notifier).wrap(
                  ref.read(inputJanStateProvider.notifier).dealWithInsertDataAndGetNewGoodsInfo(isCheck: false),
                );
          } else {
            ref.read(getProgressStateProvider.notifier).getProgress();
          }
        });
        return;
      },
      [],
    );

    return PopScope<int>(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop || ref.watch(globalLoadingServiceProvider)) return;
        context.pop();
      },
      child: Scaffold(
        appBar: PacerAppBar(
          context: context,
          title: const Text(kScanRegistration),
        ),
        body: ListView(
          padding: const EdgeInsets.symmetric(horizontal: _edge),
          children: [
            const SizedBox(
              height: _lineHeight,
            ),
            _TopProgressWidget(),
            const SizedBox(
              height: _lineHeight,
            ),
            JanCodeTextFormField(
              kJAN,
              focusNode: janFocusNode,
              controller: janTextController,
              onFieldSubmitted: (value) => _onEditingComplete(ref, value, janTextController),
              routeFullPath: '/inspection/warehouse_scan_registration/warehouse',
            ),
            const SizedBox(
              height: _lineHeight,
            ),
            switch (inputJanState) {
              AsyncData(:final value) => value == null
                  ? Center(
                      /// バーコードをスキャンまたは入力してください。
                      child: Text(
                        kPleaseInputCode,
                        style: texts.bodyLarge,
                      ),
                    )
                  : Column(
                      children: [
                        WarehouseInspectionTable(
                          goods: value,
                        ),
                        const SizedBox(
                          height: _lineHeight,
                        ),
                        SwitchListTile(
                          value: ref.watch(fixedInputValueStateProvider),
                          onChanged: (newValue) =>
                              ref.read(fixedInputValueStateProvider.notifier).updateValue(isFixed: newValue),
                          title: const Text(kFixedInputValue),
                          tileColor: colors.button,
                          shape: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: colors.primary.withOpacity(0.2),
                            ),
                          ),
                        ),
                      ],
                    ),
              AsyncError(:final error) => Text(
                  switch (error) {
                    UnknownException() => error.message,
                    _ => kGetGoodsError,
                  },
                ),
              AsyncLoading() => const SizedBox.shrink(),
            },
          ],
        ),
        resizeToAvoidBottomInset: false,
        bottomNavigationBar: WarehouseInspectionBottomTabBar(
          cancelCallback: () => _cleanData(ref),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: ScanFloatingIconButton(
          onScan: (String value) => _onEditingComplete(ref, value, janTextController),
        ),
      ),
    );
  }

  /// 新しいJANコードが入力された際には、入力前のチェックとJANのチェックを行います。
  void _onEditingComplete(
    WidgetRef ref,
    String value,
    TextEditingController controller,
  ) {
    log('warehouse input scan code value:[$value]');
    // currentProductが空の場合、廃棄された入力をチェックする必要はありません。
    final currentProduct = ref.read(inputJanStateProvider).asData?.value;

    if (currentProduct != null) {
      final inputNumberString = ref.read(inputNumberStateProvider);
      if (inputNumberString.isEmpty) {
        log('invalid input number');
        _cleanData(ref);
      }
      final isAllOne = ref.read(fixedInputValueStateProvider);
      if (currentProduct.isNumberExceedPlannedLimit(
        inputNumberString,
        isAllOne: isAllOne,
      )) {
        showAlertDialog(
          context: ref.context,
          title: kInputValueGreaterThanPlanedQuantity,
        );
        return;
      }
    }

    if (_checkJanIsCorrect(ref.context, value)) {
      ref.read(productCodeProvider.notifier).update(value);
      ref.read(globalLoadingServiceProvider.notifier).wrap(
            ref.read(inputJanStateProvider.notifier).dealWithInsertDataAndGetNewGoodsInfo(),
          );
    }
  }

  void _updateInputValue(WidgetRef ref, Goods goods) {
    final number = ref.read(fixedInputValueStateProvider) ? '1' : goods.prefillValue();
    ref.read(inputNumberStateProvider.notifier).updateNumber(number);
  }

  // clean input data
  void _cleanData(WidgetRef ref) {
    ref.read(inputJanStateProvider.notifier).clear();
    ref.read(productCodeProvider.notifier).clear();
    ref.read(inputNumberStateProvider.notifier).clear();
  }

  /// check scan result
  void _checkScanResult(WidgetRef ref, ScanInfo scanInfo) {
    switch (scanInfo.scanFlag) {
      case ScanType.subjectToReceipt:
        {
          final checkShips = ref.read(checkShipsStateProvider.notifier).checkShips(
                scanInfo.slipInfo.shipsTypeCode,
                scanInfo.slipInfo.receiveFlag,
              );
          if (checkShips != kOk) {
            showAlertDialog(context: ref.context, title: checkShips);
          }
          break;
        }
      case ScanType.subjectUnknown:
        {
          showAlertDialog(context: ref.context, title: kPleaseCheckOtherTable);
          break;
        }
      case ScanType.notSubjectToReceipt:
        {
          showAlertDialog(context: ref.context, title: kNoStockInformation);
          break;
        }
    }
  }

  /// ProductCodeの入力をチェック
  bool _checkJanIsCorrect(BuildContext context, String input) {
    if (input.isEmpty) return false;

    final isProductCode = input.isValidProductCode();
    if (!isProductCode) {
      /// 正しい商品を入力してください。
      showSnackBar(context, kInputCorrectGoodsCode, isErrorStyle: true);
    }

    return isProductCode;
  }
}

class _TopProgressWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;
    final slipCode = ref.watch(slipCodeStateProvider);
    final progress = ref.watch(progressStateProvider);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        RichText(
          text: TextSpan(
            text: kStep,
            style: texts.bodyLarge,
            children: <TextSpan>[
              TextSpan(
                text: progress,
                style: texts.bodyLarge?.copyWith(
                  color: colors.error,
                ),
              ),
            ],
          ),
        ),
        Text(
          '$kReceiptsNo：$slipCode',
          style: texts.bodyLarge,
        ),
      ],
    );
  }
}
