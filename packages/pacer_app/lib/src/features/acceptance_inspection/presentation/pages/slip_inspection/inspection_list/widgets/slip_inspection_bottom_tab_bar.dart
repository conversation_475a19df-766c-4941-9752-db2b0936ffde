import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../exceptions/app_exception.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../../../check_expiry/presentation/common_widgets/outline_round_text_button.dart';
import '../../../../../domain/slip_inspection/correct_param.dart';
import '../../../../config/strings.dart';
import '../../../../routing/acceptance_inspection_route.dart';
import '../../../../widgets/bottom_tab_bar.dart';
import '../slip_inspection_list_controller.dart';

/// 伝票検品 画面 tab bar
class SlipInspectionBottomTabBar extends ConsumerWidget {
  /// constructor
  const SlipInspectionBottomTabBar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref
      ..listen(deleteAllDataStateProvider, (_, state) {
        final isSuccess = state.asData?.value;
        if (isSuccess ?? false) {
          context
            ..pop()
            ..pop();
        }
      })
      ..listen(confirmDataStateProvider, (_, state) {
        final num = state.asData?.value;
        if (num == null) return;
        context.pop();
        const SlipInspectionConfirmRoute().go(context);
      });

    return BottomTabBar(
      leading: PacerBackButton(
        onPressed: () => _backClick(ref),
      ),
      actions: [
        OutlineRoundTextButton(
          kLogin,
          onPressed: () => _confirmClick(ref),
        ),
      ],
    );
  }

  void _backClick(WidgetRef ref) {
    ref.read(deleteAllDataStateProvider.notifier).delete();
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final deleteAllDataState = ref.watch(deleteAllDataStateProvider);

            return AlertDialog(
              title: const Text(kRequestData),
              content: switch (deleteAllDataState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncData() || AsyncLoading() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (deleteAllDataState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _confirmClick(WidgetRef ref) {
    final slipModuleList = ref.read(slipInfoListStateProvider).value;
    if (slipModuleList == null) return;

    final params = <CorrectionParam>[];
    final slipNoList = <String>[];
    for (var i = 0; i < slipModuleList.length; i++) {
      final info = slipModuleList[i];
      var selectItemCount = 0;
      for (var j = 0; j < info.slipInspectionList.length; j++) {
        final slipInfo = info.slipInspectionList[j];
        if (slipInfo.isSelect) {
          selectItemCount++;
          params.add(
            CorrectionParam(
              sequenceNumber: slipNoList.length.toString(),
              lineNo: slipInfo.lineNo.toString(),
              deliveryNum: double.tryParse(slipInfo.changedDeliverNum ?? '0.0') ?? 0.0,
              isSuspended: slipInfo.isSuspended == '1',
            ),
          );
        }
      }
      if (selectItemCount == info.slipInspectionList.length) {
        slipNoList.add(info.slipNo);
      }

      /// The current group has unchecked
      if (selectItemCount != 0 && selectItemCount != info.slipInspectionList.length) {
        showAlertDialog(context: ref.context, title: kPleaseCheckGroup);
        return;
      }
    }

    if (params.isEmpty) {
      showAlertDialog(context: ref.context, title: kPleaseSelectItem);
      return;
    }

    _insertData(ref, params, slipNoList);
  }

  void _insertData(
    WidgetRef ref,
    List<CorrectionParam> params,
    List<String> slipNoList,
  ) {
    ref.read(confirmDataStateProvider.notifier).confirm(
          params,
          slipNoList,
        );
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final confirmDataState = ref.watch(confirmDataStateProvider);

            return AlertDialog(
              title: const Text(kLogin),
              content: switch (confirmDataState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncData() || AsyncLoading() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (confirmDataState.hasError && !confirmDataState.isLoading)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}
