import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../config/strings.dart';
import 'single_correction_controller.dart';
import 'widgets/single_correction_bottom_tab_bar.dart';
import 'widgets/single_correction_table.dart';

/// 単品検品ー訂正登録
class SingleCorrectionPage extends HookConsumerWidget {
  /// init
  const SingleCorrectionPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final slipInfo = ref.watch(getSlipInfoStateProvider);

    ref
      ..listen(inputNumberStateProvider, (previous, next) {
        log('input number: $next');
      })
      ..listen(getSlipInfoStateProvider, (previous, next) {
        final info = next.asData?.value;
        if (info == null) return;
        ref.read(inputNumberStateProvider.notifier).updateNumber(info.deliveryNum.toInt().toString());
      });

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kCorrectionLoginTitle),
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: _edge),
        children: [
          const SizedBox(
            height: _lineHeight,
          ),
          switch (slipInfo) {
            AsyncData(:final value) => value == null
                ? const Center(
                    child: Text(kRequestError),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _TopProgressWidget(
                        id: value.id,
                      ),
                      const SizedBox(
                        height: _lineHeight,
                      ),
                      SingleCorrectionTable(
                        info: value,
                      ),
                    ],
                  ),
            AsyncError(:final error) => Text(
                switch (error) {
                  UnknownException() => error.message,
                  _ => kGetGoodsError,
                },
              ),
            AsyncLoading() => const Center(
                child: CircularProgressIndicator(),
              )
          },
        ],
      ),
      bottomNavigationBar: const SingleCorrectionBottomTabBar(),
    );
  }
}

class _TopProgressWidget extends StatelessWidget {
  const _TopProgressWidget({
    required this.id,
  });

  /// ID
  final String id;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          kCorrectionPageTip,
          style: texts.bodyLarge,
        ),
        const SizedBox(
          height: 6,
        ),
        Text(
          'ID：$id',
          style: texts.bodyLarge?.copyWith(
            color: colors.error,
          ),
        ),
      ],
    );
  }
}
