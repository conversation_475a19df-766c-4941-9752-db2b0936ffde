import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/warehouse/login_goods.dart';
import '../../../config/strings.dart';
import '../../../utils/utils.dart';
import '../../../widgets/number_text_field.dart';
import '../confirm/warehouse_confirm_controller.dart';
import 'warehouse_edit_number_controller.dart';
import 'widgets/warehouse_edit_number_bottom_tab_bar.dart';

part 'number_cell.dart';

/// 入庫検品確定ページ
class WarehouseEditNumberPage extends HookConsumerWidget {
  /// init
  const WarehouseEditNumberPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goodsArray = ref.watch(confirmGoodsListProvider);
    final slipCode = ref.read(selectSlipStateProvider)?.slipNumber;

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kConfirmRegister),
      ),
      body: switch (goodsArray) {
        AsyncData(:final value) => Padding(
            padding: const EdgeInsets.symmetric(
              vertical: _lineHeight,
              horizontal: _edge,
            ),
            child: Column(
              children: [
                _Header(
                  slipCode: slipCode ?? '',
                ),
                Expanded(
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      final goodsInfo = value[index];

                      return _WarehouseEditNumberCell(
                        lineIndex: index,
                        goods: goodsInfo,
                        isSelected: false,
                        onPressed: () {},
                      );
                    },
                    separatorBuilder: (context, _) => const SizedBox(
                      height: _lineHeight,
                    ),
                    itemCount: value.length,
                  ),
                ),
              ],
            ),
          ),
        AsyncError(:final error) => Text(
            switch (error) {
              UnknownException() => error.message,

              /// 取得に失敗しました
              _ => kRequestError,
            },
          ),
        AsyncLoading() => const Center(
            child: CircularProgressIndicator(),
          ),
      },
      bottomNavigationBar: const WarehouseEditNumberBottomTabBar(),
    );
  }
}

class _Header extends StatelessWidget {
  const _Header({
    required this.slipCode,
  });

  /// 伝票NO
  final String slipCode;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyLarge?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(kLoginNumber, style: texts.bodyLarge),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 14,
          ),
          child: Text(
            '$kReceiptsNo：$slipCode',
            style: texts.titleMedium?.copyWith(color: colors.primary),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: colors.surfaceContainerHighest,
            border: Border.fromBorderSide(
              BorderSide(
                color: colors.outlineVariant,
              ),
            ),
          ),
          margin: const EdgeInsets.only(bottom: 12),
          child: IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      '$kLine/$kPlanNumber',
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    kInspectionNumber,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    kSpec,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
