import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../../../../themes/app_color_scheme.dart';
import '../../../../../domain/single/slip_info_tree.dart';
import '../../../../config/strings.dart';
import '../select_slip_controller.dart';

/// 伝票選択 Cell
class SelectSlipCell extends ConsumerWidget {
  /// 標準コンストラクタ
  const SelectSlipCell({
    super.key,
    required this.slipInfo,
    required this.onChanged,
    required this.productCode,
  });

  /// 商品
  final SlipInfoTree slipInfo;

  /// product code
  final String productCode;

  /// チェックボックスの変更イベント
  final void Function() onChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textColor = colors.onSurface;

    final textTheme = texts.bodyMedium?.copyWith(
      color: textColor,
      height: 2,
    );

    return Column(
      children: [
        ExpansionTile(
          title: Row(
            children: [
              Consumer(
                builder: (ctx, ref, _) {
                  final selectSlipNo = ref.watch(selectSlipNoStateProvider);

                  return InkWell(
                    onTap: onChanged,
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: Checkbox(
                          value: selectSlipNo == slipInfo.slipNo,
                          tristate: true,
                          onChanged: (value) => onChanged(),
                        ),
                      ),
                    ),
                  );
                },
              ),
              Expanded(
                child: Text(
                  '$kReceiptsNo:${slipInfo.slipNo}(${slipInfo.time})',
                  style: texts.titleMedium,
                ),
              ),
            ],
          ),
          children: slipInfo.goodsList
              .map(
                (e) => FractionallySizedBox(
                  widthFactor: 1,
                  child: Container(
                    decoration: BoxDecoration(
                      color: switch (e.isInspected) {
                        true => colors.surfaceContainerHighest,
                        false when e.productCode == productCode => colors.primaryContainer,
                        _ => colors.surface,
                      },
                      border: Border(
                        bottom: BorderSide(
                          color: colors.outlineVariant,
                        ),
                      ),
                    ),
                    child: IntrinsicHeight(
                      child: Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: Text(
                              e.productCode,
                              textAlign: TextAlign.center,
                              style: textTheme,
                            ),
                          ),
                          VerticalDivider(
                            width: 1,
                            thickness: 1,
                            color: colors.outlineVariant,
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              e.productName,
                              textAlign: TextAlign.center,
                              style: textTheme,
                            ),
                          ),
                          VerticalDivider(
                            width: 1,
                            thickness: 1,
                            color: colors.outlineVariant,
                          ),
                          Expanded(
                            child: Text(
                              e.asnNum.toInt().toString(),
                              textAlign: TextAlign.center,
                              style: textTheme,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              )
              .toList(),
        ),
        Divider(
          height: 1,
          color: colors.line,
        ),
      ],
    );
  }
}
