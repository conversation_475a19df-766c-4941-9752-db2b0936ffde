import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../common_widgets/pacer_app_bar.dart';
import '../../../../routing/app_router.dart';
import '../../../input_check/presentation/routing/input_check_route.dart';
import '../../../miss_center/presentation/routing/miss_center_route.dart';
import '../../../slip-confirmation/presentation/routing/slip_confirm_routing.dart';
import '../config/strings.dart';
import '../routing/acceptance_inspection_route.dart';
import '../widgets/bottom_tab_bar.dart';
import '../widgets/out_line_round_text_button.dart';

/// 検品アプリ一覧メニュー
class InspectionMenuPage extends StatelessWidget {
  /// init
  const InspectionMenuPage({super.key});

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kMenuPageTitle),
      ),
      bottomNavigationBar: BottomTabBar(
        leading: TextButton(
          onPressed: () => context.pop(),
          child: Text(
            kEnd,
            style: texts.titleMedium,
          ),
        ),
      ),
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          children: [
            const SizedBox(height: 40),

            /// 単品検品
            OutlineRoundTextButton(
              kSingleCheck,
              width: double.infinity,
              height: 60,
              onPressed: () => const SingleInspectionFormRoute().go(context),
            ),

            /// 伝票検品
            const SizedBox(height: 20),
            OutlineRoundTextButton(
              kReceiptsCheck,
              width: double.infinity,
              height: 60,
              onPressed: () => const SlipSettingRoute().go(context),
            ),

            /// 伝票確定
            const SizedBox(height: 20),
            OutlineRoundTextButton(
              kReceiptsConfirm,
              width: double.infinity,
              height: 60,
              onPressed: () => const SlipConfirmSettingsRoute().go(context),
            ),

            /// 入庫検品
            const SizedBox(height: 20),
            OutlineRoundTextButton(
              kWarehouseInspection,
              width: double.infinity,
              height: 60,
              onPressed: () => const WarehouseInspectionScanRegistrationRoute().go(context),
            ),

            /// 本日入荷一覧
            const SizedBox(height: 20),
            OutlineRoundTextButton(
              kTodayArrivalList,
              width: double.infinity,
              height: 60,
              onPressed: () => const InputCheckRoute().go(context),
            ),

            /// 誤配登録
            const SizedBox(height: 20),
            OutlineRoundTextButton(
              kMisdeliveryRegistration,
              width: double.infinity,
              height: 60,
              onPressed: () => const MissCenterRegistrationRoute().go(context),
            ),
          ],
        ),
      ),
    );
  }
}
