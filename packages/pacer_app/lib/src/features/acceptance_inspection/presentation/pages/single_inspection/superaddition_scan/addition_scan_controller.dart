import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/single_inspection_service.dart';
import '../../../../domain/single/order_progress.dart';
import '../../../../domain/single/scan_data.dart';
import '../form/single_inspection_form_controller.dart';

part 'addition_scan_controller.g.dart';

/// slip info
@riverpod
class GetSlipInfoState extends _$GetSlipInfoState {
  @override
  FutureOr<ScanData?> build() async {
    final info = ref.read(getTempInfoStateProvider)?.slipList.first;
    if (info == null) return null;
    return ref.watch(singleInspectionServiceProvider).getScanData(
          slipNo: info.slipNo,
          lineNo: info.lineNo,
        );
  }

  /// clear
  void clear() {
    state = const AsyncData(null);
  }
}

/// get progress
@riverpod
class GetOrderProgressState extends _$GetOrderProgressState {
  /// build
  @override
  FutureOr<OrderProgress?> build() async => null;

  /// get progress
  Future<void> getProgress() async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).getOrderProgress(),
    );
  }
}

/// all input number
@riverpod
class GoodsInputNumberNotifier extends _$GoodsInputNumberNotifier {
  /// init
  @override
  GoodsInputNumberState build() => GoodsInputNumberState();

  /// update box number
  void updateBoxNumber(String boxNumber) {
    final num = _getSingleGoodsNum(boxNumber, state.packageNumber);
    state = state.copyWith(
      boxNumber: boxNumber,
      singleGoodsNumber: num,
      sum: _getSum(num),
    );
  }

  /// update package number
  void updatePackageNumber(String packageNumber) {
    final num = _getSingleGoodsNum(state.boxNumber, packageNumber);
    state = state.copyWith(
      packageNumber: packageNumber,
      singleGoodsNumber: num,
      sum: _getSum(num),
    );
  }

  /// update single goods number
  void updateSingleGoodsNumber(String singleGoodsNumber) {
    state = state.copyWith(
      singleGoodsNumber: singleGoodsNumber,
      sum: _getSum(singleGoodsNumber),
    );
  }

  /// clear
  void clear() {
    state = GoodsInputNumberState();
  }

  String _getSingleGoodsNum(String boxNumber, String packNumber) {
    if (boxNumber.isEmpty) return '';
    final sum = (int.tryParse(boxNumber) ?? 0) * (int.tryParse(packNumber) ?? 0);

    return sum.toString();
  }

  String _getSum(
    String singleGoodsNumber,
  ) {
    final info = ref.read(getSlipInfoStateProvider).value;
    final deliverNum = info?.deliveryNum ?? 0.0;
    final sum = deliverNum + (int.tryParse(singleGoodsNumber) ?? 0);

    return sum.toInt().toString();
  }
}

/// 納品数更新
@riverpod
class UpdateDeliveryNumberState extends _$UpdateDeliveryNumberState {
  /// init
  @override
  FutureOr<bool?> build() => null;

  /// update delivery number
  Future<bool> updateNumber(double number) async {
    final info = ref.read(getSlipInfoStateProvider).value;
    if (info == null) return false;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(singleInspectionServiceProvider).updateDeliveriesNumber(
              deliveryNum: number,
              slipNo: info.slipNo,
              lineNo: info.lineNo,
            );
      },
    );
    if (state.hasError) return false;
    return true;
  }
}

/// 確定登録②　納品数・誤差登録
@riverpod
class UpdateScanDataFlg extends _$UpdateScanDataFlg {
  /// init
  @override
  FutureOr<bool?> build() => null;

  /// update delivery number
  Future<bool> updateScanData(double number, {required bool isError}) async {
    final info = ref.read(getSlipInfoStateProvider).value;
    if (info == null) return false;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(singleInspectionServiceProvider).updateScanDataFlg(
              deliveryNum: number,
              slipNo: info.slipNo,
              lineNo: info.lineNo,
              isError: isError,
            );
      },
    );
    if (state.hasError) return false;
    return true;
  }
}

/// all input number
class GoodsInputNumberState {
  /// init
  GoodsInputNumberState({
    this.boxNumber = '',
    this.packageNumber = '',
    this.singleGoodsNumber = '',
    this.sum = '',
  });

  /// input box number
  final String boxNumber;

  /// input package number
  final String packageNumber;

  /// 単品商品コード
  final String singleGoodsNumber;

  /// sum
  final String sum;

  /// copy
  GoodsInputNumberState copyWith({
    String? boxNumber,
    String? packageNumber,
    String? singleGoodsNumber,
    String? sum,
  }) {
    return GoodsInputNumberState(
      boxNumber: boxNumber ?? this.boxNumber,
      packageNumber: packageNumber ?? this.packageNumber,
      singleGoodsNumber: singleGoodsNumber ?? this.singleGoodsNumber,
      sum: sum ?? this.sum,
    );
  }
}
