import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/slip_inspection_service.dart';
import '../../../../domain/slip_inspection/correct_details.dart';
import '../../../../domain/slip_inspection/correct_details_tree.dart';
import '../../../../domain/slip_inspection/correct_param.dart';

part 'slip_inspection_list_controller.g.dart';

/// slip list
@riverpod
class SlipInfoListState extends _$SlipInfoListState {
  @override
  FutureOr<List<CorrectDetailsTree>> build() async {
    final slipList = await ref.watch(slipInspectionServiceProvider).getSlipInspectionList();
    final dataArray = <CorrectDetailsTree>[];
    for (var i = 0; i < slipList.length; i++) {
      final slipInfo = slipList[i];
      final slipItem = _getCacheDataBySlipNo(slipInfo.slipNo, dataArray);
      if (slipItem == null) {
        final info = CorrectDetailsTree(
          sum: slipInfo.deliveryNum,
          slipNo: slipInfo.slipNo,
          slipInspectionList: [slipInfo],
        );
        dataArray.add(info);
      } else {
        final info = slipItem.copyWith(
          sum: slipItem.sum + slipInfo.deliveryNum,
          list: [
            ...slipItem.slipInspectionList,
            ...[slipInfo],
          ],
        );
        final index = dataArray.indexOf(slipItem);
        dataArray
          ..removeAt(index)
          ..insert(index, info);
      }
    }

    return dataArray;
  }

  /// replace oldGoods with newGoods
  void replaceObjectAtIndexWithObject(
    CorrectDetails oldGoods,
    CorrectDetails newGoods,
  ) {
    final array = state.value;
    if (array == null) return;

    CorrectDetailsTree? selectInfo;
    for (var i = 0; i < array.length; i++) {
      final tempInfo = array[i];
      if (newGoods.slipNo == tempInfo.slipNo) {
        selectInfo = tempInfo;
        break;
      }
    }
    if (selectInfo == null) return;

    final index = selectInfo.slipInspectionList.indexOf(oldGoods);
    selectInfo.slipInspectionList
      ..removeAt(index)
      ..insert(index, newGoods);

    /// reset sum
    var sum = 0.0;
    for (final info in selectInfo.slipInspectionList) {
      sum += int.tryParse(info.changedDeliverNum ?? '0.0') ?? 0.0;
    }
    final newInfo = selectInfo.copyWith(sum: sum);
    final treeIndex = array.indexOf(selectInfo);
    array
      ..removeAt(treeIndex)
      ..insert(treeIndex, newInfo);

    state = AsyncData(array);
  }

  CorrectDetailsTree? _getCacheDataBySlipNo(
    String slipNo,
    List<CorrectDetailsTree> list,
  ) {
    for (var i = 0; i < list.length; i++) {
      final info = list[i];
      if (info.slipNo == slipNo) {
        return info;
      }
    }
    return null;
  }
}

/// delete all data
@riverpod
class DeleteAllDataState extends _$DeleteAllDataState {
  /// build
  @override
  FutureOr<bool?> build() => null;

  ///
  Future<void> delete() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(slipInspectionServiceProvider).deleteAllInspectionData(),
    );
  }
}

/// confirm data
@riverpod
class ConfirmDataState extends _$ConfirmDataState {
  /// build
  @override
  FutureOr<int?> build() => null;

  ///
  Future<void> confirm(
    List<CorrectionParam> params,
    List<String> slipNoList,
  ) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(slipInspectionServiceProvider).updateCorrectedData(
            params: params,
            slipNoList: slipNoList,
          ),
    );
  }
}
