import 'package:flutter/material.dart';

import '../../../../domain/warehouse/login_slip.dart';

/// 一覧表示
class WarehouseConfirmCell extends StatelessWidget {
  /// 標準コンストラクタ
  const WarehouseConfirmCell({
    super.key,
    required this.slipInfo,
    required this.isSelected,
    required this.onPressed,
    required this.onChanged,
    required this.isCheck,
  });

  /// 商品
  final LoginSlip slipInfo;

  /// 選択されているかどうか
  final bool isSelected;

  /// クリックイベントのコールバック
  final VoidCallback onPressed;

  /// チェックボックスの変更イベント
  final void Function({bool? isCheck}) onChanged;

  /// チェック状態
  final bool isCheck;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final containerColor = isSelected ? colors.primaryContainer : colors.surface;

    final textColor = isSelected ? colors.onPrimaryContainer : colors.onSurface;

    final textTheme = texts.bodyLarge?.copyWith(
      color: textColor,
      height: 2,
    );

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: containerColor,
          border: Border.fromBorderSide(
            BorderSide(
              color: colors.outlineVariant,
            ),
          ),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: slipInfo.isCanSelect ? () => onChanged.call(isCheck: !isCheck) : null,
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: Checkbox(
                        value: slipInfo.isCanSelect ? isCheck : null,
                        tristate: true,
                        onChanged: (_) => onChanged.call(isCheck: !isCheck),
                      ),
                    ),
                  ),
                ),
              ),
              VerticalDivider(
                width: 1,
                thickness: 1,
                color: colors.outlineVariant,
              ),
              Expanded(
                flex: 2,
                child: Text(
                  slipInfo.slipNumber,
                  textAlign: TextAlign.center,
                  style: textTheme,
                ),
              ),
              VerticalDivider(
                width: 1,
                thickness: 1,
                color: colors.outlineVariant,
              ),
              Expanded(
                flex: 2,
                child: Text(
                  slipInfo.confirmFlagString,
                  textAlign: TextAlign.center,
                  style: textTheme,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
