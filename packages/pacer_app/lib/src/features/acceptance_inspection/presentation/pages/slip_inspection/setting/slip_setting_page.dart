import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../common_widgets/snack_bars.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../../../../routing/app_router.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../../../utils/adaptive_number_input_type.dart';
import '../../../../../../utils/async_util.dart';
import '../../../../../device/application/pacer_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../../../fixed_assets/presentation/common_widgets/dialog_helpers.dart';
import '../../../../../slip-confirmation/domain/slip_confirmation.dart';
import '../../../../domain/single/store_supplier_authority.dart';
import '../../../config/strings.dart';
import '../../../routing/acceptance_inspection_route.dart';
import 'slip_setting_controller.dart';
import 'widgets/slip_setting_bottom_tab_bar.dart';

/// 伝票検品 設定 画面
class SlipSettingPage extends HookConsumerWidget {
  /// init
  const SlipSettingPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final vendorCodeController = useTextEditingController();

    useEffect(
      () {
        delayed(() {
          ref.read(systemDateStateProvider.notifier).getSystemDate();
          ref.read(storeAndSupplierAuthorityStateProvider.notifier).getAuthority();
        });
        return null;
      },
      [],
    );

    ref
      ..listen(systemDateStateProvider, (_, state) {
        final date = state.asData?.value;
        if (date == null) return;
        ref.read(dateTimeStateProvider.notifier).updateSystemTime(
              date,
            );
      })
      ..listen(storeAndSupplierAuthorityStateProvider, (_, state) {
        state.showAlertDialogOnError(context);
        final info = state.asData?.value;
        if (info == null) return;
        _checkAuthority(ref, info);
      })
      ..listen(unCheckVendorListStateProvider, (_, state) {
        state.showAlertDialogOnError(context);
        final list = state.asData?.value;
        if (list == null) return;
        if (list.isEmpty) {
          showAlertDialog(context: context, title: kNoSupplierTargets);
        }
      })
      ..listen(vendorNameControllerProvider, (_, state) {
        state.showAlertDialogOnError(context);
        final vendorName = state.asData?.value;
        if (vendorName == null) return;
        if (vendorCodeController.text.isNotEmpty && vendorName.isEmpty) {
          showSnackBar(
            context,
            kEnterValidSupplier,
            isErrorStyle: true,
          );
          vendorCodeController.text = '';
        }
      })
      ..listen(scanCodeProvider, (_, state) {
        final location = GoRouterState.of(context).fullPath;
        if (location != '/inspection/slip_setting') return;
        final jan = state.value;
        if (jan == null || jan.isEmpty) return;
        ref.read(getVendorByJanStateProvider.notifier).getVendor(jan);
      })
      ..listen(getVendorByJanStateProvider, (_, state) {
        final info = state.asData?.value;
        if (info == null) return;
        if (info.vendorCode == 0 && info.vendorName.isEmpty) {
          showSnackBar(
            context,
            kTheScannedProductUnInspected,
            isErrorStyle: true,
          );
          return;
        }
        vendorCodeController.text = info.vendorCode.toString();
        ref.read(vendorNameControllerProvider.notifier).setVendorName(info.vendorName);
      })
      ..listen(confirmDataStateProvider, (_, state) {
        final info = state.asData?.value;
        if (info == null) return;
        context.pop();
        _cleanData(ref, vendorCodeController);
        const SlipInspectionListRoute().go(ref.context);
      })
      ..listen(isInsufficientInspectionStateProvider, (previous, next) {});

    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: PacerAppBar(
        context: context,
        title: const Text(
          kSlipInspectionSettingTitle,
        ),
      ),
      body: SafeArea(child: _SlipConfirmSettingsBody(vendorCodeController)),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: SlipSettingBottomTabBar(
        startCallback: () => _startClick(ref, vendorCodeController),
      ),
      floatingActionButton: ScanFloatingIconButton(
        onScan: (String value) => ref.read(getVendorByJanStateProvider.notifier).getVendor(value),
      ),
    );
  }

  void _startClick(
    WidgetRef ref,
    TextEditingController vendorCodeController,
  ) {
    final vendorCode = vendorCodeController.text;
    if (vendorCode.isEmpty) {
      showAlertDialog(
        context: ref.context,
        title: kInputSupplier,
      );
      return;
    }
    FocusScope.of(ref.context).requestFocus(FocusNode());
    ref.read(confirmDataStateProvider.notifier).confirm(vendorCode);
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final progressState = ref.watch(confirmDataStateProvider);

            return AlertDialog(
              title: const Text(kStartInspection),
              content: switch (progressState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncLoading() || AsyncData() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (progressState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _checkAuthority(
    WidgetRef ref,
    StoreAndSupplierAuthority info,
  ) {
    if (ref.read(dateTimeStateProvider.notifier).timeInterval() > 40) {
      showAlertDialog(context: ref.context, title: kDeliveryDateMoreThan40Days);
    } else {
      ref.read(unCheckVendorListStateProvider.notifier).getList();
    }
  }

  void _cleanData(
    WidgetRef ref,
    TextEditingController vendorCodeController,
  ) {
    vendorCodeController.text = '';
    ref.read(vendorNameControllerProvider.notifier).setVendorName('');
  }
}

class _SlipConfirmSettingsBody extends HookConsumerWidget {
  const _SlipConfirmSettingsBody(this.vendorCodeController);

  final TextEditingController vendorCodeController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textStyle = Theme.of(context).textTheme.bodyLarge;
    final state = ref.watch(unCheckVendorListStateProvider);
    final vendorName = ref.watch(vendorNameControllerProvider);

    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            kEnterSupplierNote,
            style: textStyle,
          ),
          Text(
            kEnterJANNote,
            style: textStyle?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          Row(
            children: [
              SizedBox(
                width: 100,
                child: Center(
                  child: Text(
                    kSupplier,
                    style: textStyle,
                  ),
                ),
              ),
              Expanded(
                child: Center(
                  child: Text(
                    kPurchaseName,
                    style: textStyle,
                  ),
                ),
              ),
            ],
          ),
          Row(
            children: [
              SizedBox(
                width: 96,
                child: _VendorCodeField(
                  controller: vendorCodeController,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  vendorName.value ?? '',
                  style: textStyle,
                ),
              ),
            ],
          ),
          const Divider(
            thickness: 3,
          ),
          Expanded(
            child: switch (state) {
              AsyncData(:final value) => ListView.separated(
                  itemBuilder: (_, index) => _ItemVendor(
                    value[index],
                    vendorCodeController,
                  ),
                  separatorBuilder: (_, __) => const Divider(),
                  itemCount: value.length,
                ),
              AsyncLoading() || AsyncData(isLoading: true) => const Center(
                  child: CircularProgressIndicator(),
                ),
              AsyncError(:final error) => Text(
                  switch (error) {
                    UnknownException() => error.message,
                    _ => kRequestError,
                  },
                ),
            },
          ),
          _TimeSelectField(),
        ],
      ),
    );
  }
}

class _VendorCodeField extends ConsumerWidget {
  const _VendorCodeField({required this.controller});

  final TextEditingController controller;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextField(
      controller: controller,
      textAlign: TextAlign.center,
      decoration: const InputDecoration(
        filled: true,
        border: InputBorder.none,
      ),
      onTapOutside: (_) => FocusScope.of(context).unfocus(),
      textInputAction: TextInputAction.done,
      keyboardType: TextInputType.number.withEnter(),
      onSubmitted: (vendorCode) {
        ref.read(vendorNameControllerProvider.notifier).getVendorName(vendorCode);
      },
      style: Theme.of(context).textTheme.titleLarge,
      inputFormatters: [
        LengthLimitingTextInputFormatter(8),
        FilteringTextInputFormatter.digitsOnly,
      ],
    );
  }
}

class _ItemVendor extends ConsumerWidget {
  const _ItemVendor(this.vendor, this.vendorCodeController);

  final Vendor vendor;
  final TextEditingController vendorCodeController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textStyle = Theme.of(context).textTheme.bodyLarge;
    return InkWell(
      onTap: () {
        vendorCodeController.text = vendor.vendorCode.toString();
        ref.read(vendorNameControllerProvider.notifier).setVendorName(vendor.vendorName);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            SizedBox(
              width: 100,
              child: Center(
                child: Text(
                  vendor.vendorCode.toString(),
                  style: textStyle,
                ),
              ),
            ),
            Expanded(
              child: Text(
                vendor.vendorName,
                style: textStyle,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TimeSelectField extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dateInfo = ref.watch(dateTimeStateProvider);
    final textStyle = Theme.of(context).textTheme.bodyLarge;

    return Column(
      children: [
        const Divider(
          thickness: 3,
        ),
        _ItemTimeSelected(
          title: kPast,
          timeSelected: Text(
            DateFormat('yyyy/MM/dd').format(dateInfo.startTime),
            style: textStyle,
          ),
          onPressed: () async {
            final result = await showDatePicker(
              context: context,
              firstDate: dateInfo.systemTime.subtract(const Duration(days: 40)),
              lastDate: dateInfo.endTime,
              initialDate: dateInfo.startTime,
              initialEntryMode: DatePickerEntryMode.calendarOnly,
            );
            if (result != null) {
              ref.read(dateTimeStateProvider.notifier).updateStartTime(result);
              await ref.read(unCheckVendorListStateProvider.notifier).getList();
            }
          },
        ),
        const SizedBox(height: 4),
        _ItemTimeSelected(
          title: kFuture,
          timeSelected: Text(
            DateFormat('yyyy/MM/dd').format(dateInfo.endTime),
            style: textStyle,
          ),
          onPressed: () async {
            final result = await showDatePicker(
              context: context,
              firstDate: dateInfo.startTime,
              lastDate: dateInfo.systemTime.add(const Duration(days: 99)),
              initialDate: dateInfo.endTime,
              initialEntryMode: DatePickerEntryMode.calendarOnly,
            );
            if (result != null) {
              ref.read(dateTimeStateProvider.notifier).updateEndTime(result);
              await ref.read(unCheckVendorListStateProvider.notifier).getList();
            }
          },
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}

class _ItemTimeSelected extends StatelessWidget {
  const _ItemTimeSelected({
    required this.title,
    required this.timeSelected,
    this.onPressed,
  });

  final String title;
  final Widget timeSelected;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    final textStyle = Theme.of(context).textTheme.titleLarge;
    return Row(
      children: [
        SizedBox(
          width: 100,
          child: Center(
            child: Text(
              title,
              style: textStyle,
            ),
          ),
        ),
        Expanded(
          child: InkWell(
            onTap: onPressed,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.button,
              ),
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    timeSelected,
                    const Icon(Icons.calendar_today),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
