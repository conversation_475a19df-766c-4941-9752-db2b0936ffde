import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/single_inspection_service.dart';
import '../../../../domain/single/order_progress.dart';
import '../../../../domain/single/slip_confirm_info.dart';

part 'single_confirm_controller.g.dart';

/// slip list
@riverpod
class ConfirmSlipList extends _$ConfirmSlipList {
  /// init
  @override
  FutureOr<List<SlipConfirmInfo>> build() async {
    return ref.read(singleInspectionServiceProvider).getSlipInfoList();
  }

  ///
  void changeSelectState(SlipConfirmInfo info) {
    final newInfo = info.copyWith();
    final array = state.value;
    if (array == null) return;
    final index = array.indexOf(info);
    array
      ..removeAt(index)
      ..insert(index, newInfo);
    state = AsyncData(array);
  }

  /// upload
  void reload() {
    ref.read(selectSlipListProvider.notifier).clear();
    ref.invalidateSelf();
  }
}

/// get progress
@riverpod
class GetProgressState extends _$GetProgressState {
  /// build
  @override
  FutureOr<OrderProgress?> build() async => null;

  /// get progress
  Future<void> getProgress() async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).getOrderProgress(),
    );
  }
}

/// 选中传票
@riverpod
class SelectSlipState extends _$SelectSlipState {
  /// init
  @override
  SlipConfirmInfo? build() {
    final goodsArray = ref.watch(confirmSlipListProvider).value ?? [];
    return goodsArray.isEmpty ? null : goodsArray.first;
  }

  /// update selected goods
  Future<void> update(SlipConfirmInfo? slipInfo) async {
    log('scrap reason select: $slipInfo');
    state = slipInfo;
  }

  /// clean selected goods
  void clear() {
    state = null;
  }
}

/// 選択された传票リスト
@riverpod
class SelectSlipList extends _$SelectSlipList {
  @override
  List<SlipConfirmInfo> build() => [];

  /// 追加
  void add(SlipConfirmInfo slipInfo) {
    state = [...state, slipInfo];
  }

  /// 追加 all
  void addAll(List<SlipConfirmInfo> slips) {
    state = [...slips];
  }

  /// 削除
  void remove(SlipConfirmInfo slipInfo) {
    state = [...state]..remove(slipInfo);
  }

  /// クリア
  void clear() {
    state = [];
  }
}

/// 確定
@riverpod
class ConfirmSelectSlipState extends _$ConfirmSelectSlipState {
  /// init
  @override
  FutureOr<bool> build() {
    return true;
  }

  /// confirm all goods
  FutureOr<void> confirmSlips() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(singleInspectionServiceProvider).confirmInspectionData();
      },
    );
  }
}

/// is show tip
@riverpod
class IsShowTipState extends _$IsShowTipState {
  /// init
  @override
  bool build() => false;

  /// change value
  void changeStatus({bool isShow = false}) {
    log('change status: $isShow');
    state = isShow;
  }

  /// clear
  void clear() {
    state = false;
  }
}

/// 保留フラグ設定
@riverpod
class SetSuspendStatusState extends _$SetSuspendStatusState {
  /// init
  @override
  FutureOr<SlipConfirmInfo?> build() => null;

  /// change status
  FutureOr<void> setSuspendStatus(
    SlipConfirmInfo info,
  ) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        final isSuccess = await ref.read(singleInspectionServiceProvider).setSuspendFlg(
              slipNo: info.slipNo,
              isSuspended: !info.isSuspended,
            );
        return isSuccess ? info : null;
      },
    );
  }
}
