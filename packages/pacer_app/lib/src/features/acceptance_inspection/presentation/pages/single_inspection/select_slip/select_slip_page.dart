import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../config/strings.dart';
import '../form/single_inspection_form_controller.dart';
import 'select_slip_controller.dart';
import 'widgets/select_slip_bottom_tab_bar.dart';
import 'widgets/select_slip_cell.dart';

/// 単品検品ー伝票選択
class SelectSlipPage extends HookConsumerWidget {
  /// init
  const SelectSlipPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dataArray = ref.watch(slipInfoListStateProvider);

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kSlipSelectTitle),
      ),
      body: Column(
        children: [
          _Header(),
          switch (dataArray) {
            AsyncData(:final value) => Expanded(
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    final slipInfoTree = value[index];

                    return SelectSlipCell(
                      slipInfo: slipInfoTree,
                      productCode: ref.read(getTempInfoStateProvider)?.goods.productCode ?? '',
                      onChanged: () {
                        final selectSlipNoState = ref.read(selectSlipNoStateProvider.notifier);
                        final selectSlipNo = ref.read(selectSlipNoStateProvider);
                        if (selectSlipNo == slipInfoTree.slipNo) {
                          selectSlipNoState.clear();
                        } else {
                          selectSlipNoState.update(slipInfoTree.slipNo);
                        }
                      },
                    );
                  },
                  itemCount: value.length,
                ),
              ),
            AsyncError(:final error) => Text(
                switch (error) {
                  UnknownException() => error.message,

                  /// 取得に失敗しました
                  _ => kRequestError,
                },
              ),
            AsyncLoading() => const Center(
                child: CircularProgressIndicator(),
              ),
          },
        ],
      ),
      bottomNavigationBar: const SelectSlipBottomTabBar(),
    );
  }
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyLarge?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(12),
          child: Text(kPleaseSelectSlip, style: texts.titleMedium),
        ),
        Container(
          decoration: BoxDecoration(
            color: colors.surfaceContainerHighest,
            border: Border.fromBorderSide(
              BorderSide(
                color: colors.outlineVariant,
              ),
            ),
          ),
          child: IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    kJAN,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    kGoodsName,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  child: Text(
                    kNumOfShipments,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
