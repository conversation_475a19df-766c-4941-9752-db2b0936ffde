import 'dart:developer';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/single_inspection_service.dart';
import '../../../../domain/product_code.dart';
import '../../../../domain/single/enum/ip_status.dart';
import '../../../../domain/single/goods_and_vendor_info.dart';
import '../../../../domain/single/initial_single_data.dart';
import '../../../../domain/single/order.dart';
import '../../../../domain/single/order_progress.dart';
import '../../../../domain/single/scan_count.dart';
import '../../../../domain/single/slip_info.dart';
import '../../../../domain/single/store_supplier_authority.dart';
import '../../../../domain/single/sysytem_date.dart';
import '../../../../domain/single/update_order_param.dart';

part 'single_inspection_form_controller.g.dart';

/// input number
@riverpod
class InputNumberState extends _$InputNumberState {
  /// init
  @override
  String build() => '';

  /// update number
  void updateNumber(String number) {
    log('input number: $number');
    state = number;
  }

  /// clear
  void clear() {
    state = '';
  }
}

/// temp quantity
@riverpod
class TempVariateState extends _$TempVariateState {
  /// init
  @override
  TempVariate build() => TempVariate();

  /// update quantity
  void updateQuantity(double number) {
    state = state.copyWith(quantity: number);
  }

  /// update temp quantity
  void updateTempQuantity(double number) {
    state = state.copyWith(tempQuantity: number);
  }

  /// update unInspectedNum
  void updateUnInspectedNum(int number) {
    state = state.copyWith(unInspectedNum: number);
  }

  /// update vendor flg
  void updateVendorCdFlg(String flg) {
    state = state.copyWith(vendorCdFlg: flg);
  }

  /// update isCheckedFlg
  void updateIsChecked({bool isChecked = false}) {
    state = state.copyWith(isChecked: isChecked);
  }

  /// update isFromMenu
  void updateIsFromMenu({bool isFromMenu = true}) {
    state = state.copyWith(isFromMenu: isFromMenu);
  }

  /// update errorNum
  void updateErrorNum(int number) {
    state = state.copyWith(errorNum: number);
  }

  /// update orderPlanedNum
  void updateOrderPlanedNum(double number) {
    state = state.copyWith(orderPlanedNum: number);
  }

  /// update isOverlapSlipFlg
  void updateIsOverlapSlipFlg({bool isOverlapSlipFlg = false}) {
    state = state.copyWith(isOverlapSlipFlg: isOverlapSlipFlg);
  }

  /// update orderNum
  void updateOrderNum(double number) {
    state = state.copyWith(orderNum: number);
  }

  /// update scanId
  void updateScanId(String scanId) {
    state = state.copyWith(scanId: scanId);
  }

  /// update entrance
  void updateEntrance(int entrance) {
    state = state.copyWith(entrance: entrance);
  }

  /// clear
  void clear() {
    state = TempVariate();
  }
}

/// fixed input value
@riverpod
class FixedInputValueState extends _$FixedInputValueState {
  /// init
  @override
  bool build() => false;

  /// update value
  void updateValue({bool isFixed = false}) {
    log('fixed input value: $isFixed');
    ref.read(inputNumberStateProvider.notifier).updateNumber(isFixed ? '1' : '');
    state = isFixed;
  }

  /// clear
  void clear() {
    state = false;
  }
}

/// jan
@riverpod
class ProductCode extends _$ProductCode {
  /// build
  @override
  String build() {
    return '';
  }

  ///
  Future<void> update(String value) async {
    state = value.productCodeParse();
  }

  /// clear value
  void clear() {
    state = '';
  }
}

/// slip code
@riverpod
class SlipCodeState extends _$SlipCodeState {
  /// init
  @override
  String build() => '';

  /// update progress
  void updateValue(String value) {
    log('slip code value: $value');
    state = value;
  }

  /// clear
  void clear() {
    state = '';
  }
}

/// progress (1/2)
@riverpod
class ProgressState extends _$ProgressState {
  /// init
  @override
  String build() => '0/0(ID:1)';

  /// update progress
  void updateValue(String completedCount, String totality, String id) {
    state = '$completedCount/$totality(ID:$id)';
  }

  /// clear
  void clear() {
    state = '0/0(ID:1)';
  }
}

/// get progress
@riverpod
class GetProgressState extends _$GetProgressState {
  /// build
  @override
  FutureOr<OrderProgress?> build() async => null;

  /// get progress
  Future<void> getProgress() async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).getOrderProgress(),
    );
  }
}

/// init data
@riverpod
class InitDataState extends _$InitDataState {
  /// build
  @override
  FutureOr<IpStatus?> build() => null;

  /// init config data
  Future<void> initConfigData() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final systemDate = await ref.read(singleInspectionServiceProvider).getSystemDate();
      if (systemDate.date.isEmpty) {
        return null;
      }
      await ref.read(singleInspectionServiceProvider).getStoreAndSupplierAuthority();
      await ref.read(singleInspectionServiceProvider).getStoreAuthority();
      return ref.read(singleInspectionServiceProvider).getIPStatus();
    });
  }
}

/// 当日日付、時間、週取得
@riverpod
class GetSystemDateState extends _$GetSystemDateState {
  /// build
  @override
  FutureOr<SystemDate> build() async {
    return ref.read(singleInspectionServiceProvider).getSystemDate();
  }
}

/// 店舗制御とベンダー制御取得
@riverpod
Future<StoreAndSupplierAuthority> getStoreAndSupplierAuthority(
  GetStoreAndSupplierAuthorityRef ref,
) async {
  return ref.read(singleInspectionServiceProvider).getStoreAndSupplierAuthority();
}

/// 店舗制御フラグ
@riverpod
Future<bool> getStoreAuthority(
  GetStoreAuthorityRef ref,
) async {
  return ref.read(singleInspectionServiceProvider).getStoreAuthority();
}

/// 途中検品したデータチェック
@riverpod
Future<IpStatus> getIPStatus(
  GetIPStatusRef ref,
) async {
  return ref.read(singleInspectionServiceProvider).getIPStatus();
}

/// delete cache data by current ip
@riverpod
class DeleteCacheDataByCurrentIpState extends _$DeleteCacheDataByCurrentIpState {
  /// build
  @override
  FutureOr<bool?> build() => null;

  ///
  Future<void> delete() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).deleteCacheDataByCurrentIp(),
    );
  }
}

/// delete all data
@riverpod
class DeleteAllDataState extends _$DeleteAllDataState {
  /// build
  @override
  FutureOr<bool?> build() => null;

  ///
  Future<void> delete() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).deleteAllData(),
    );
  }
}

/// delete data
@riverpod
class DeleteDataState extends _$DeleteDataState {
  /// build
  @override
  FutureOr<bool?> build() => null;

  ///
  Future<void> delete() async {
    final info = ref.read(getGoodsAndVendorInfoStateProvider).value;
    if (info == null) return;
    final slipInfo = info.slipList.first;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).deleteData(
            slipNo: slipInfo.slipNo,
            lineNo: slipInfo.lineNo,
          ),
    );
  }
}

///
@riverpod
class GetInitialSingleDataState extends _$GetInitialSingleDataState {
  /// build
  @override
  FutureOr<InitialSingleData?> build() => null;

  ///
  Future<void> getData() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).getInitialSingleData(),
    );
  }
}

/// jan input
@riverpod
class GetGoodsAndVendorInfoState extends _$GetGoodsAndVendorInfoState {
  /// build
  @override
  FutureOr<GoodsAndVendorInfo?> build() async {
    ref.showGlobalLoading();
    final scanProductCode = ref.watch(productCodeProvider);
    if (scanProductCode.isEmpty) return null;
    final tempInfo = ref.read(getTempInfoStateProvider);
    if (tempInfo != null) {
      return tempInfo;
    }
    final service = ref.read(singleInspectionServiceProvider);
    if (scanProductCode.isEmpty) return null;

    return service.getGoodsAndVendorInfo(productCode: scanProductCode);
  }

  /// update
  void updateInfo(GoodsAndVendorInfo? info) {
    log('update goods and vendor info');
    state = AsyncData(info);
  }

  /// clear
  void clear() {
    state = const AsyncData(null);
  }
}

/// temp info
@riverpod
class GetTempInfoState extends _$GetTempInfoState {
  /// build
  @override
  GoodsAndVendorInfo? build() => null;

  /// update
  void updateInfo(GoodsAndVendorInfo? info) {
    log('update temp info');
    state = info;
  }

  /// update Slip
  void updateSlip(SlipInfo slipInfo) {
    final info = state;
    if (info != null) {
      info.slipList
        ..removeAt(0)
        ..insert(0, slipInfo);
      state = info;
    }
  }

  /// clear
  void clear() {
    state = null;
  }
}

/// 納品数更新
@riverpod
class UpdateDeliveriesNumberState extends _$UpdateDeliveriesNumberState {
  /// init
  @override
  FutureOr<bool?> build() => null;

  /// update delivery number
  Future<bool> updateNumber(double number) async {
    final info = ref.read(getGoodsAndVendorInfoStateProvider).value;
    if (info == null) return false;
    final slipInfo = info.slipList.first;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(singleInspectionServiceProvider).updateDeliveriesNumber(
              deliveryNum: number,
              slipNo: slipInfo.slipNo,
              packNum: info.goods.packNum,
              caseNum: 0,
              lineNo: slipInfo.lineNo,
            );
      },
    );
    if (state.hasError) return false;
    return true;
  }
}

/// 単品検品の該当伝票の検品進捗取得
@riverpod
class GetScanCountDataState extends _$GetScanCountDataState {
  /// build
  @override
  FutureOr<ScanCount?> build() => null;

  ///
  Future<void> getData() async {
    final info = ref.read(getGoodsAndVendorInfoStateProvider).value;
    if (info == null) return;
    final slipInfo = info.slipList.first;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).getScanCountData(
            slipNo: slipInfo.slipNo,
            productCode: info.goods.productCode,
          ),
    );
  }
}

/// ベンダーチェック
@riverpod
class CheckVendorState extends _$CheckVendorState {
  /// build
  @override
  FutureOr<bool?> build() => null;

  ///
  Future<void> checkVendor() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).checkVendor(),
    );
  }
}

/// 納品数更新
@riverpod
class UpdateOrderState extends _$UpdateOrderState {
  /// init
  @override
  FutureOr<bool?> build() => null;

  /// update delivery number
  Future<bool> updateOrder(double number, {bool isInspected = true}) async {
    final info = ref.read(getGoodsAndVendorInfoStateProvider).value;
    if (info == null) return false;
    final scanId = ref.read(tempVariateStateProvider).scanId;
    final slipInfo = info.slipList.first;

    final params = UpdateOrderParam(
      specName: info.goods.specName,
      brandName: info.goods.brandName,
      colorAndSize: info.goods.colorAndSize,
      packNum: info.goods.packNum,
      productName: info.goods.productName,
      orderNum: slipInfo.orderNum.toInt(),
      deliveryNum: slipInfo.asnNum.toInt(),
    );

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).updateOrderData(
            deliveryNum: number,
            slipNo: slipInfo.slipNo,
            lineNo: slipInfo.lineNo,
            isInspected: isInspected,
            id: scanId,
            productCode: info.goods.productCode,
            vendorCode: info.goods.vendorCode,
            info: params,
          ),
    );
    if (state.hasError) return false;
    return true;
  }
}

/// 確定登録②　納品数・誤差登録
@riverpod
class UpdateScanDataFlgState extends _$UpdateScanDataFlgState {
  /// init
  @override
  FutureOr<bool?> build() => null;

  /// update delivery number
  Future<bool> updateScanData(double number) async {
    final info = ref.read(getGoodsAndVendorInfoStateProvider).value;
    if (info == null) return false;
    final slipInfo = info.slipList.first;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(singleInspectionServiceProvider).updateScanDataFlg(
              deliveryNum: number,
              slipNo: slipInfo.slipNo,
              lineNo: slipInfo.lineNo,
              isError: ref.read(tempVariateStateProvider).isChecked,
              packNum: info.goods.packNum,
              caseNum: 0,
            );
      },
    );
    if (state.hasError) return false;
    return true;
  }
}

/// 同じ商品、複数伝票から一つ伝票情報取得して、スキャン登録画面へ表示
@riverpod
class GetOrderDataForSlipState extends _$GetOrderDataForSlipState {
  /// init
  @override
  FutureOr<Order?> build() => null;

  /// get data
  Future<void> getData(
    String slipNo,
    String productCode, {
    required bool isBread,
  }) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(singleInspectionServiceProvider).getOrderDataForSlip(
              productCode: productCode,
              slipNo: slipNo,
              isBread: isBread,
            );
      },
    );
  }
}

/// temporary variate
class TempVariate {
  /// init
  TempVariate({
    this.quantity = 0.0,
    this.tempQuantity = 0.0,
    this.unInspectedNum = 0,
    this.vendorCdFlg = '',
    this.isChecked = false,
    this.isFromMenu = true,
    this.errorNum = 0,
    this.orderPlanedNum = 0.0,
    this.isOverlapSlipFlg = false,
    this.orderNum = 0.0,
    this.scanId = '',
    this.entrance = 0,
  });

  /// quantity
  final double quantity;

  /// temp quantity
  final double tempQuantity;

  /// Number of planed
  final double orderPlanedNum;

  /// Number of unInspected
  final int unInspectedNum;

  ///
  final String vendorCdFlg;

  /// is checked flg
  final bool isChecked;

  /// is from menu
  final bool isFromMenu;

  /// Number of error
  final int errorNum;

  /// is checked flg
  final bool isOverlapSlipFlg;

  /// 発注数
  final double orderNum;

  ///
  final String scanId;

  /// entrance
  final int entrance;

  /// copy
  TempVariate copyWith({
    double? quantity,
    double? tempQuantity,
    int? unInspectedNum,
    String? vendorCdFlg,
    bool? isChecked,
    bool? isFromMenu,
    int? errorNum,
    double? orderPlanedNum,
    bool? isOverlapSlipFlg,
    double? orderNum,
    String? scanId,
    int? entrance,
  }) {
    return TempVariate(
      quantity: quantity ?? this.quantity,
      tempQuantity: tempQuantity ?? this.tempQuantity,
      unInspectedNum: unInspectedNum ?? this.unInspectedNum,
      vendorCdFlg: vendorCdFlg ?? this.vendorCdFlg,
      isChecked: isChecked ?? this.isChecked,
      isFromMenu: isFromMenu ?? this.isFromMenu,
      errorNum: errorNum ?? this.errorNum,
      orderPlanedNum: orderPlanedNum ?? this.orderPlanedNum,
      isOverlapSlipFlg: isOverlapSlipFlg ?? this.isOverlapSlipFlg,
      orderNum: orderNum ?? this.orderNum,
      scanId: scanId ?? this.scanId,
      entrance: entrance ?? this.entrance,
    );
  }
}
