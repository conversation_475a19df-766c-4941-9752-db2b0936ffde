// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse_edit_number_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$confirmGoodsListHash() => r'eba5de85fb06760bda5c95cd27e9b9ad617dcfe3';

/// goods list
///
/// Copied from [ConfirmGoodsList].
@ProviderFor(ConfirmGoodsList)
final confirmGoodsListProvider = AutoDisposeAsyncNotifierProvider<ConfirmGoodsList, List<LoginGoods>>.internal(
  ConfirmGoodsList.new,
  name: r'confirmGoodsListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmGoodsListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmGoodsList = AutoDisposeAsyncNotifier<List<LoginGoods>>;
String _$selectGoodsStateHash() => r'1c51f32cf6f8f5c8dd04963f1fa60784a9943d3a';

/// 选中商品
///
/// Copied from [SelectGoodsState].
@ProviderFor(SelectGoodsState)
final selectGoodsStateProvider = AutoDisposeNotifierProvider<SelectGoodsState, LoginGoods?>.internal(
  SelectGoodsState.new,
  name: r'selectGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectGoodsState = AutoDisposeNotifier<LoginGoods?>;
String _$confirmSelectGoodsStateHash() => r'd08de1bb568a4bc5f7b7ad2cb561d69dd04ced3a';

/// 確定
///
/// Copied from [ConfirmSelectGoodsState].
@ProviderFor(ConfirmSelectGoodsState)
final confirmSelectGoodsStateProvider = AutoDisposeAsyncNotifierProvider<ConfirmSelectGoodsState, String>.internal(
  ConfirmSelectGoodsState.new,
  name: r'confirmSelectGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmSelectGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmSelectGoodsState = AutoDisposeAsyncNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
