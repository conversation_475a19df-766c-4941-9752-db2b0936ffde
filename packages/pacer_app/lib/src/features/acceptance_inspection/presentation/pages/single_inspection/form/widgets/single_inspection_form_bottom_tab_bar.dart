import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../../../../utils/async_value_ui.dart';
import '../../../../../../app/application/global_loading_service.dart';
import '../../../../../../fixed_assets/presentation/common_widgets/dialog_helpers.dart';
import '../../../../config/strings.dart';
import '../../../../routing/acceptance_inspection_route.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../../../../widgets/scan_bottom_tab_bar.dart';
import '../single_inspection_form_controller.dart';

/// スキャン登録 tab bar
class SingleInspectionFormBottomTabBar extends ConsumerWidget {
  /// constructor
  const SingleInspectionFormBottomTabBar({
    super.key,
    required this.cancelCallback,
    required this.backCallback,
    required this.cleanCallback,
  });

  /// cancel callback
  final VoidCallback cancelCallback;

  /// back callback
  final VoidCallback backCallback;

  /// clean callback
  final VoidCallback cleanCallback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const buttonPadding = EdgeInsets.symmetric(horizontal: 15);
    ref.listen(checkVendorStateProvider, (previous, next) {
      next.showSnackBarOnError(context);
      final isSuccess = next.asData?.value;
      if (isSuccess == null) return;
      if (isSuccess) {
        cleanCallback();
        const SingleConfirmRoute().go(context);
      } else {
        showAlertDialog(context: context, title: kPleaseScan);
      }
    });

    return ScanBottomTabBar(
      leading: PacerBackButton(
        onPressed: backCallback,
      ),
      actions: [
        OutlineRoundTextButton(
          kCancel,
          padding: buttonPadding,
          onPressed: cancelCallback,
        ),
        const SizedBox(width: 2),
        OutlineRoundTextButton(
          kLogs,
          padding: buttonPadding,
          onPressed: () {
            const HistoryRoute().go(context);
            cleanCallback();
          },
        ),
        const SizedBox(width: 2),
        OutlineRoundTextButton(
          kEnd,
          padding: buttonPadding,
          onPressed: () => _onConfirmClick(context, ref),
        ),
      ],
    );
  }

  void _onConfirmClick(
    BuildContext context,
    WidgetRef ref,
  ) {
    ref.read(globalLoadingServiceProvider.notifier).wrap(
          ref.read(checkVendorStateProvider.notifier).checkVendor(),
        );
  }
}
