import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../themes/app_color_scheme.dart';
import '../../../../../domain/single/correct_details.dart';
import '../../../../config/strings.dart';
import '../../../../utils/utils.dart';
import '../../../../widgets/number_text_field.dart';
import '../single_edit_number_controller.dart';

/// 一覧表示
class SingleEditNumberCell extends ConsumerWidget {
  /// 標準コンストラクタ
  const SingleEditNumberCell({
    super.key,
    required this.lineIndex,
    required this.goods,
    required this.isSelected,
    required this.onPressed,
  });

  /// line number
  final int lineIndex;

  /// 商品
  final CorrectDetails goods;

  /// 選択されているかどうか
  final bool isSelected;

  /// クリックイベントのコールバック
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final containerColor = isSelected ? colors.primaryContainer : colors.surface;

    final textColor = isSelected ? colors.onPrimaryContainer : colors.onSurface;

    final textTheme = texts.bodyMedium?.copyWith(
      color: textColor,
      height: 2,
    );

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: containerColor,
          border: Border.fromBorderSide(
            BorderSide(
              color: colors.outlineVariant,
            ),
          ),
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        child: Column(
          children: [
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        (lineIndex + 1).toString(),
                        textAlign: TextAlign.center,
                        style: textTheme,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    flex: 4,
                    child: Text(
                      goods.productName,
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: colors.outlineVariant,
            ),
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        checkDoubleToString(goods.orderNum),
                        textAlign: TextAlign.center,
                        style: textTheme,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        checkDoubleToString(goods.asnNum),
                        textAlign: TextAlign.center,
                        style: textTheme,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    width: 1,
                    thickness: 1,
                    color: colors.outlineVariant,
                  ),
                  Expanded(
                    flex: 2,
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: colors.button,
                        borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(20),
                        ),
                      ),
                      child: NumberTextField(
                        inputText: goods.changedDeliverNum ?? '',
                        textAlign: TextAlign.center,
                        onFieldSubmitted: (value) {
                          value = value.isEmpty ? '0' : value;
                          if (goods.isNumberExceedPlannedLimit(value)) {
                            showAlertDialog(
                              context: ref.context,
                              title: kInputValueGreaterThanPlanedQuantity,
                            );
                            value = goods.changedDeliverNum ?? '';
                          }

                          final newGoods = goods.copyWith(changedDeliveryNumber: value);
                          ref.read(confirmGoodsListProvider.notifier).replaceObjectAtIndexWithObject(goods, newGoods);
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
