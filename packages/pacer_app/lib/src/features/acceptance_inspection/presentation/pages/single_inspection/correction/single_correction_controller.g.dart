// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'single_correction_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getSlipInfoStateHash() => r'd0e99f23d04869a294a0be9323a48071d8ff5d75';

/// slip info
///
/// Copied from [GetSlipInfoState].
@ProviderFor(GetSlipInfoState)
final getSlipInfoStateProvider = AutoDisposeAsyncNotifierProvider<GetSlipInfoState, ScanData?>.internal(
  GetSlipInfoState.new,
  name: r'getSlipInfoStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getSlipInfoStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetSlipInfoState = AutoDisposeAsyncNotifier<ScanData?>;
String _$deleteDataStateHash() => r'978d7e3512e2ea7bfced16bf9f1dcd25b022f990';

/// delete data
///
/// Copied from [DeleteDataState].
@ProviderFor(DeleteDataState)
final deleteDataStateProvider = AutoDisposeAsyncNotifierProvider<DeleteDataState, bool?>.internal(
  DeleteDataState.new,
  name: r'deleteDataStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deleteDataStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteDataState = AutoDisposeAsyncNotifier<bool?>;
String _$updateDeliveriesNumberStateHash() => r'8e7d54b6f0e21d7db1c95cbb76f54d82312ec959';

/// 納品数更新
///
/// Copied from [UpdateDeliveriesNumberState].
@ProviderFor(UpdateDeliveriesNumberState)
final updateDeliveriesNumberStateProvider =
    AutoDisposeAsyncNotifierProvider<UpdateDeliveriesNumberState, bool?>.internal(
  UpdateDeliveriesNumberState.new,
  name: r'updateDeliveriesNumberStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$updateDeliveriesNumberStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateDeliveriesNumberState = AutoDisposeAsyncNotifier<bool?>;
String _$updateScanDataFlgStateHash() => r'8a68161f6726c0174502779eaa01bec9f9e19616';

/// 納品数・誤差登録
///
/// Copied from [UpdateScanDataFlgState].
@ProviderFor(UpdateScanDataFlgState)
final updateScanDataFlgStateProvider = AutoDisposeAsyncNotifierProvider<UpdateScanDataFlgState, bool?>.internal(
  UpdateScanDataFlgState.new,
  name: r'updateScanDataFlgStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$updateScanDataFlgStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateScanDataFlgState = AutoDisposeAsyncNotifier<bool?>;
String _$inputNumberStateHash() => r'f1d04959b15d197012792c90b1ed8f0ba9ce9718';

/// input number
///
/// Copied from [InputNumberState].
@ProviderFor(InputNumberState)
final inputNumberStateProvider = AutoDisposeNotifierProvider<InputNumberState, String>.internal(
  InputNumberState.new,
  name: r'inputNumberStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputNumberStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputNumberState = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
