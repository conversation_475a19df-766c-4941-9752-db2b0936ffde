import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/single_inspection_service.dart';
import '../../../../domain/single/correct_details.dart';
import '../../../../domain/single/update_receive_number_param.dart';
import '../confirm/select_slip_code_state.dart';

part 'single_edit_number_controller.g.dart';

/// goods list
@riverpod
class ConfirmGoodsList extends _$ConfirmGoodsList {
  @override
  FutureOr<List<CorrectDetails>> build() async {
    final slipCode = ref.read(selectSlipCodeStateProvider);
    if (slipCode.isEmpty) return [];
    return ref.watch(singleInspectionServiceProvider).getCorrectDetailsList(slipNo: slipCode);
  }

  /// replace oldGoods with newGoods
  void replaceObjectAtIndexWithObject(
    CorrectDetails oldGoods,
    CorrectDetails newGoods,
  ) {
    final array = state.value;
    if (array == null) return;
    final index = array.indexOf(oldGoods);
    array
      ..removeAt(index)
      ..insert(index, newGoods);
    state = AsyncData(array);
  }

  /// upload
  void reload() {
    state = const AsyncValue.loading();

    ref.invalidateSelf();
  }
}

/// 確定
@riverpod
class ConfirmSelectGoodsState extends _$ConfirmSelectGoodsState {
  /// init
  @override
  FutureOr<int?> build() => null;

  /// confirm all goods
  FutureOr<void> confirmGoods() async {
    final slipCode = ref.read(selectSlipCodeStateProvider);
    if (slipCode.isEmpty) return;

    final lists = ref
        .read(confirmGoodsListProvider)
        .value
        ?.map(
          (e) => UpdateReceiveNumberParam(
            lineNo: e.lineNo.toString(),
            deliveryNum: double.parse(e.changedDeliverNum ?? e.deliveryNum.toString()),
            isSuspended: e.isSuspended,
          ),
        )
        .toList();
    if (lists == null) return;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(singleInspectionServiceProvider).updateCorrectedData(
              slipNo: slipCode,
              slipInfoList: lists,
            );
      },
    );
  }
}
