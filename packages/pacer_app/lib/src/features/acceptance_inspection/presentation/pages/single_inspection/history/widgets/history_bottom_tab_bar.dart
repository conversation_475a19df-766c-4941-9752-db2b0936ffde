import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../../../consumable_adjust/presentation/widgets/bottom_tab_bar.dart';
import '../../../../config/strings.dart';
import '../../../../routing/acceptance_inspection_route.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../history_controller.dart';

/// 履歴 tab bar
class HistoryBottomTabBar extends ConsumerWidget {
  /// constructor
  const HistoryBottomTabBar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const buttonPadding = EdgeInsets.symmetric(horizontal: 8);

    ref.listen(listSortStatusProvider, (previous, next) {
      log('current sort isAscending: $next');
    });

    return BottomTabBar(
      leading: PacerBackButton(
        onPressed: () => context.pop(),
      ),
      actions: [
        OutlineRoundTextButton(
          kDescending,
          padding: buttonPadding,
          onPressed: () => _listSort(ref, false),
        ),
        const SizedBox(width: 2),
        OutlineRoundTextButton(
          kAscending,
          padding: buttonPadding,
          onPressed: () => _listSort(ref, true),
        ),
        const SizedBox(width: 2),
        OutlineRoundTextButton(
          kCorrection,
          padding: buttonPadding,
          onPressed: ref.watch(historyListProvider).value?.isNotEmpty ?? false ? () => _pushFixPage(ref) : null,
        ),
      ],
    );
  }

  void _listSort(WidgetRef ref, bool isAscending) {
    ref.read(historyListProvider.notifier).sort(isAscending: isAscending);
  }

  void _pushFixPage(WidgetRef ref) {
    const SingleCorrectionRoute().go(ref.context);
  }
}
