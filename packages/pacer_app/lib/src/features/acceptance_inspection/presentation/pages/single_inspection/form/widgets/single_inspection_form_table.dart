import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../themes/app_color_scheme.dart';
import '../../../../../domain/single/goods_and_vendor_info.dart';
import '../../../../config/strings.dart';
import '../../../../utils/utils.dart';
import '../../../../widgets/number_text_field.dart';
import '../single_inspection_form_controller.dart';

/// table
class SingleInspectionTable extends ConsumerWidget {
  /// constructor
  const SingleInspectionTable({
    super.key,
    required this.info,
    required this.onFieldSubmitted,
  });

  /// goods info
  final GoodsAndVendorInfo info;

  /// submit call back
  final void Function(String)? onFieldSubmitted;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;
    final goods = info.goods;
    final slipInfo = info.slipList.first;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3), // 1列目は全体の50%
        1: FractionColumnWidth(0.7), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(4),
        ),
      ),
      children: [
        TableRow(
          children: [
            /// 商品名
            const _ItemNameText(kGoodsName),
            _ValueText(goods.productName),
          ],
        ),
        TableRow(
          children: [
            /// 規格
            const _ItemNameText(kSpec),
            _ValueText(goods.specName),
          ],
        ),
        TableRow(
          children: [
            /// ブランド
            const _ItemNameText(kBrand),
            _ValueText(goods.brandName),
          ],
        ),
        TableRow(
          children: [
            /// カラー 、サイズ
            _ItemNameText(kColorAndSize, textStyle: texts.bodySmall),
            _ValueText(goods.colorAndSize),
          ],
        ),
        TableRow(
          children: [
            /// 発注数
            const _ItemNameText(kNumberOfOrders),
            _ModuleValueView(
              leftValue: slipInfo.orderNum.toInt().toString(),
              rightTitle: kNumberOfCases,
              rightValue: '0',
            ),
          ],
        ),
        TableRow(
          children: [
            /// 入数
            const _ItemNameText(kQuantity),
            _ModuleValueView(
              leftValue: goods.packNum.toString(),
              rightTitle: kPlanNumber,
              rightValue: slipInfo.asnNum.toInt().toString(),
            ),
          ],
        ),
        TableRow(
          children: [
            /// 数量
            const _ItemNameText(kNumber),
            Consumer(
              builder: (ctx, ref, _) {
                final isFixed = ref.watch(fixedInputValueStateProvider);

                return NumberTextField(
                  inputText: ref.watch(inputNumberStateProvider),
                  readOnly: isFixed,
                  isCanInputDouble: is28PrefixJan(info.goods.productCode),
                  onFieldSubmitted: onFieldSubmitted,
                );
              },
            ),
          ],
        ),
      ],
    );
  }
}

class _ModuleValueView extends ConsumerWidget {
  const _ModuleValueView({
    required this.leftValue,
    required this.rightTitle,
    required this.rightValue,
  });

  /// left value
  final String leftValue;

  /// right title
  final String rightTitle;

  /// right value
  final String rightValue;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.1),
        1: FractionColumnWidth(0.3),
        2: FractionColumnWidth(0.1),
      },
      border: TableBorder.all(
        color: colors.line,
      ),
      children: [
        TableRow(
          children: [
            _ValueText(leftValue),
            _ItemNameText(rightTitle),
            _ValueText(rightValue),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text, {this.textStyle});

  final String text;

  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
        child: Text(
          text,
          style: textStyle ?? texts.bodyLarge,
          textAlign: TextAlign.right,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.bodyLarge,
        ),
      ),
    );
  }
}
