import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../../../../themes/app_color_scheme.dart';
import '../../../../domain/warehouse/login_slip.dart';
import '../../../config/strings.dart';
import 'warehouse_confirm_cell.dart';
import 'warehouse_confirm_controller.dart';
import 'widgets/warehouse_confirm_bottom_tab_bar.dart';

/// 入庫検品確定ページ
/// 検品した入庫データを振替依頼伝票番号ごとにまとめて表示
/// 検品データを選択することで納品数を変更することができる
/// 納品数が正しいなら、伝票を選択して確定ボタンを押すことで検品確定する
class WarehouseConfirmPage extends HookConsumerWidget {
  /// init
  const WarehouseConfirmPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final slipsArray = ref.watch(confirmSlipListProvider);
    final selectSlipList = ref.watch(selectSlipListProvider);

    ref
      ..listen(selectSlipListProvider, (previous, next) {
        ref.read(checkAllStateProvider.notifier).singleSlipChangeState();
      })
      ..listen(confirmSlipListProvider, (previous, next) async {
        final array = next.asData?.value;
        if (array != null && array.isEmpty) {
          await showAlertDialog(context: context, title: kNoData);
          if (context.mounted) {
            context.pop();
          }
        }
      });

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kConfirmRegister),
      ),
      body: switch (slipsArray) {
        AsyncData(:final value) => Padding(
            padding: const EdgeInsets.symmetric(
              vertical: _lineHeight,
              horizontal: _edge,
            ),
            child: Column(
              children: [
                _Header(),
                Expanded(
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      final slipInfo = value[index];
                      final checkState = selectSlipList.contains(slipInfo);

                      return Consumer(
                        builder: (ctx, ref, _) {
                          final selectedGoods = ref.watch(selectSlipStateProvider);

                          return WarehouseConfirmCell(
                            slipInfo: slipInfo,
                            isSelected: selectedGoods == slipInfo,
                            onPressed: () => ref.read(selectSlipStateProvider.notifier).update(slipInfo),
                            isCheck: checkState,
                            onChanged: ({bool? isCheck}) => onCheckChanged(
                              ref: ref,
                              isCheck: isCheck,
                              slipInfo: slipInfo,
                            ),
                          );
                        },
                      );
                    },
                    separatorBuilder: (context, _) => const SizedBox(
                      height: _lineHeight,
                    ),
                    itemCount: value.length,
                  ),
                ),
              ],
            ),
          ),
        AsyncError(:final error) => Text(
            switch (error) {
              UnknownException() => error.message,

              /// 取得に失敗しました
              _ => kRequestError,
            },
          ),
        AsyncLoading() => const Center(
            child: CircularProgressIndicator(),
          ),
      },
      bottomNavigationBar: WarehouseConfirmBottomTabBar(
        selectSlipList: selectSlipList,
      ),
    );
  }

  /// チェックボックスの変更イベント
  void onCheckChanged({
    required WidgetRef ref,
    required bool? isCheck,
    required LoginSlip slipInfo,
  }) {
    if (isCheck == null) {
      return;
    }
    isCheck
        ? ref.read(selectSlipListProvider.notifier).add(slipInfo)
        : ref.read(selectSlipListProvider.notifier).remove(slipInfo);
  }
}

class _Header extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyLarge?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Column(
      children: [
        Container(
          color: colors.button,
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(bottom: 8),
          child: Text(
            '$kOperationTip\n$kOperationTip1',
            style: texts.titleMedium,
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: colors.surfaceContainerHighest,
            border: Border.fromBorderSide(
              BorderSide(
                color: colors.outlineVariant,
              ),
            ),
          ),
          margin: const EdgeInsets.only(bottom: 12),
          child: IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  child: Consumer(
                    builder: (context, ref, _) {
                      final isCheckAll = ref.watch(checkAllStateProvider);

                      return InkWell(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            isCheckAll ? kUnSelectAll : kSelectAll,
                            textAlign: TextAlign.center,
                            style: textTheme?.copyWith(color: colors.primary),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        onTap: () => ref.read(checkAllStateProvider.notifier).changeState(),
                      );
                    },
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    kReceiptsNo,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                const Expanded(
                  flex: 2,
                  child: Text(
                    '',
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
