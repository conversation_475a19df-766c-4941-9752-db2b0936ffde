import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../themes/app_color_scheme.dart';
import '../../../../../domain/single/scan_data.dart';
import '../../../../config/strings.dart';
import '../../../../widgets/number_text_field.dart';
import '../single_correction_controller.dart';

/// table
class SingleCorrectionTable extends ConsumerWidget {
  /// constructor
  const SingleCorrectionTable({
    super.key,
    required this.info,
  });

  /// slip info
  final ScanData info;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3), // 1列目は全体の50%
        1: FractionColumnWidth(0.7), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(6),
        ),
      ),
      children: [
        TableRow(
          children: [
            /// 商品CD
            const _ItemNameText(kGoodsCode),
            _ValueText(info.productCode),
          ],
        ),
        TableRow(
          children: [
            /// 商品名
            const _ItemNameText(kGoodsName),
            _ValueText(info.productName),
          ],
        ),
        TableRow(
          children: [
            /// 規格
            const _ItemNameText(kSpec),
            _ValueText(
              info.specName,
              isRed: info.productName == kNoLogin,
            ),
          ],
        ),
        TableRow(
          children: [
            /// ブランド
            const _ItemNameText(kBrand),
            _ValueText(info.brandName),
          ],
        ),
        TableRow(
          children: [
            /// カラー 、サイズ
            const _ItemNameText(kColorAndSize),
            _ValueText(info.colorAndSize),
          ],
        ),
        TableRow(
          children: [
            /// 発注数
            const _ItemNameText(kNumberOfOrders),
            _ValueText(info.orderNum.toInt().toString()),
          ],
        ),
        TableRow(
          children: [
            /// 入数
            const _ItemNameText(kQuantity),
            _ValueText(info.packNum.toString()),
          ],
        ),
        TableRow(
          children: [
            /// ケース数
            const _ItemNameText(kNumberOfCases),
            _ValueText(info.caseNum.toString()),
          ],
        ),
        TableRow(
          children: [
            /// 納品予定数
            const _ItemNameText(kPlanNumber),
            _ValueText(
              info.asnNum == 0 ? info.orderNum.toInt().toString() : info.asnNum.toInt().toString(),
            ),
          ],
        ),
        TableRow(
          children: [
            /// 数量
            const _ItemNameText(kNumber),
            NumberTextField(
              inputText: ref.watch(inputNumberStateProvider),
              onFieldSubmitted: (value) {
                value = value.isEmpty ? info.deliveryNum.toString() : value;
                final tempValue = info.asnNum > 0 ? info.asnNum : info.orderNum;
                if ((int.tryParse(value) ?? 0) > tempValue) {
                  if (!info.isBread) {
                    showAlertDialog(
                      context: context,
                      title: kInputValueGreaterThanPlanedQuantity,
                    );
                    ref.read(inputNumberStateProvider.notifier).updateNumber(ref.read(inputNumberStateProvider));
                    return;
                  }
                }
                ref.read(inputNumberStateProvider.notifier).updateNumber(value);
              },
            ),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text, {this.isRed = false});

  final String text;

  final bool isRed;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: isRed ? texts.titleMedium?.copyWith(color: colors.error) : texts.titleMedium,
        ),
      ),
    );
  }
}
