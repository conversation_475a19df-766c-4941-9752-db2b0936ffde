// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse_confirm_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$confirmSlipListHash() => r'bfcaa210dbbbe5cce8b4e9f90427e3cac4d9d341';

/// goods list
///
/// Copied from [ConfirmSlipList].
@ProviderFor(ConfirmSlipList)
final confirmSlipListProvider = AutoDisposeAsyncNotifierProvider<ConfirmSlipList, List<LoginSlip>>.internal(
  ConfirmSlipList.new,
  name: r'confirmSlipListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmSlipListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmSlipList = AutoDisposeAsyncNotifier<List<LoginSlip>>;
String _$selectSlipStateHash() => r'ec8cc8bb5970cd6f700512335b546a79d0a3eae9';

/// 选中传票
///
/// Copied from [SelectSlipState].
@ProviderFor(SelectSlipState)
final selectSlipStateProvider = AutoDisposeNotifierProvider<SelectSlipState, LoginSlip?>.internal(
  SelectSlipState.new,
  name: r'selectSlipStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectSlipStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectSlipState = AutoDisposeNotifier<LoginSlip?>;
String _$selectSlipListHash() => r'6c0b05bcd4e71ff80200e2b2212f8deca28600fb';

/// 選択された传票リスト
///
/// Copied from [SelectSlipList].
@ProviderFor(SelectSlipList)
final selectSlipListProvider = AutoDisposeNotifierProvider<SelectSlipList, List<LoginSlip>>.internal(
  SelectSlipList.new,
  name: r'selectSlipListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectSlipListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectSlipList = AutoDisposeNotifier<List<LoginSlip>>;
String _$checkAllStateHash() => r'7e1ef18ecc41a805792c0e85d7baece44cd409f3';

/// select all and cancel select
///
/// Copied from [CheckAllState].
@ProviderFor(CheckAllState)
final checkAllStateProvider = AutoDisposeNotifierProvider<CheckAllState, bool>.internal(
  CheckAllState.new,
  name: r'checkAllStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkAllStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckAllState = AutoDisposeNotifier<bool>;
String _$confirmSelectSlipStateHash() => r'87c79b2f7451258d9d591a49e7ca92cc360bf88c';

/// 確定
///
/// Copied from [ConfirmSelectSlipState].
@ProviderFor(ConfirmSelectSlipState)
final confirmSelectSlipStateProvider = AutoDisposeAsyncNotifierProvider<ConfirmSelectSlipState, bool>.internal(
  ConfirmSelectSlipState.new,
  name: r'confirmSelectSlipStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmSelectSlipStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmSelectSlipState = AutoDisposeAsyncNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
