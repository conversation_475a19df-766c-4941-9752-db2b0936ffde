// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'select_slip_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$slipInfoListStateHash() => r'31708633af38f4dbc71fdf48965c453e424624af';

/// goods list
///
/// Copied from [SlipInfoListState].
@ProviderFor(SlipInfoListState)
final slipInfoListStateProvider = AutoDisposeAsyncNotifierProvider<SlipInfoListState, List<SlipInfoTree>>.internal(
  SlipInfoListState.new,
  name: r'slipInfoListStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$slipInfoListStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SlipInfoListState = AutoDisposeAsyncNotifier<List<SlipInfoTree>>;
String _$selectSlipNoStateHash() => r'2f4066d1b8001e9b022485011c89dbd7ea6ca12d';

/// 选中传票
///
/// Copied from [SelectSlipNoState].
@ProviderFor(SelectSlipNoState)
final selectSlipNoStateProvider = AutoDisposeNotifierProvider<SelectSlipNoState, String?>.internal(
  SelectSlipNoState.new,
  name: r'selectSlipNoStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectSlipNoStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectSlipNoState = AutoDisposeNotifier<String?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
