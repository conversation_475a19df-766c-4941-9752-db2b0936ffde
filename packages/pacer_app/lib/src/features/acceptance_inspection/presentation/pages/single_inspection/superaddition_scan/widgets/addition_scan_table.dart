import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../themes/app_color_scheme.dart';
import '../../../../../domain/single/scan_data.dart';
import '../../../../config/strings.dart';
import '../../../../widgets/number_text_field.dart';
import '../../form/single_inspection_form_controller.dart';
import '../addition_scan_controller.dart';

/// table
class AdditionScanTable extends ConsumerWidget {
  /// constructor
  const AdditionScanTable({
    super.key,
    required this.info,
  });

  /// goods info
  final ScanData info;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3), // 1列目は全体の50%
        1: FractionColumnWidth(0.7), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(6),
        ),
      ),
      children: [
        TableRow(
          children: [
            /// 商品CD
            const _ItemNameText(kGoodsCode),
            _ValueText(info.productCode),
          ],
        ),
        TableRow(
          children: [
            /// 商品名
            const _ItemNameText(kGoodsName),
            _ValueText(info.productName),
          ],
        ),
        TableRow(
          children: [
            /// 規格
            const _ItemNameText(kSpec),
            _ValueText(info.specName),
          ],
        ),
        TableRow(
          children: [
            /// 発注数
            const _ItemNameText(kNumberOfOrders),
            _ValueText(info.orderNum.toInt().toString()),
          ],
        ),
        TableRow(
          children: [
            /// 納品予定数
            const _ItemNameText(kPlanNumber),
            _ValueText(info.asnNum.toInt().toString()),
          ],
        ),
        TableRow(
          children: [
            /// 既検品数
            const _ItemNameText(kNumOfInspectedItem),
            _ValueText(info.deliveryNum.toInt().toString()),
          ],
        ),
        TableRow(
          children: [
            /// 追加検品数
            const _ItemNameText(kNumOfAdditionalInspections),
            _InputNumberView(
              onFieldSubmitted: (_) {},
            ),
          ],
        ),
        TableRow(
          children: [
            /// 合計検品数
            const _ItemNameText(kTotalNumOfInspection),
            Consumer(
              builder: (ctx, ref, _) {
                final goodsInputNumberState = ref.watch(goodsInputNumberNotifierProvider);

                return _ValueText(goodsInputNumberState.sum);
              },
            ),
          ],
        ),
      ],
    );
  }
}

class _InputNumberView extends ConsumerWidget {
  const _InputNumberView({
    required this.onFieldSubmitted,
  });

  /// submitted
  final void Function(String)? onFieldSubmitted;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;
    final goodsInputNumberState = ref.watch(goodsInputNumberNotifierProvider);

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.2),
        1: FractionColumnWidth(0.1),
        2: FractionColumnWidth(0.2),
        3: FractionColumnWidth(0.1),
        4: FractionColumnWidth(0.3),
      },
      border: TableBorder.all(
        color: colors.line,
      ),
      children: [
        TableRow(
          children: [
            ColoredBox(
              color: colors.button,
              child: NumberTextField(
                inputText: goodsInputNumberState.boxNumber,
                onFieldSubmitted: (value) {
                  value = value.isEmpty ? '0' : value;
                  final inputNumberState = ref.read(goodsInputNumberNotifierProvider);
                  final boxNum = int.tryParse(value) ?? 0;
                  final packNum = int.tryParse(inputNumberState.packageNumber) ?? 0;
                  if (boxNum * packNum > 9999) {
                    showAlertDialog(context: context, title: kEnterNumIsTooBig);
                  } else {
                    ref.read(goodsInputNumberNotifierProvider.notifier).updateBoxNumber(value);
                  }
                },
              ),
            ),
            const _ValueText('*'),
            ColoredBox(
              color: colors.button,
              child: NumberTextField(
                inputText: goodsInputNumberState.packageNumber,
                onFieldSubmitted: (value) {
                  value = value.isEmpty ? '0' : value;
                  final inputNumberState = ref.read(goodsInputNumberNotifierProvider);
                  final boxNum = int.tryParse(inputNumberState.boxNumber) ?? 0;
                  final packNum = int.tryParse(value) ?? 0;
                  if (boxNum * packNum > 9999) {
                    showAlertDialog(context: context, title: kEnterNumIsTooBig);
                  } else {
                    ref.read(goodsInputNumberNotifierProvider.notifier).updatePackageNumber(value);
                  }
                },
              ),
            ),
            const _ValueText('='),
            ColoredBox(
              color: colors.button,
              child: NumberTextField(
                inputText: goodsInputNumberState.singleGoodsNumber,
                onFieldSubmitted: (value) {
                  value = value.isEmpty ? '0' : value;
                  final info = ref.read(getSlipInfoStateProvider).value;
                  final singleNum = int.tryParse(value) ?? 0;
                  final deliverNum = info?.deliveryNum ?? 0.0;
                  final orderNum = info?.orderNum ?? 0.0;
                  if (ref.read(tempVariateStateProvider).vendorCdFlg == '0') {
                    if (singleNum + deliverNum > orderNum) {
                      showAlertDialog(
                        context: context,
                        title: kInputValueGreaterThanPlanedQuantity,
                      );
                      final num = ref.read(goodsInputNumberNotifierProvider).singleGoodsNumber;
                      ref.read(goodsInputNumberNotifierProvider.notifier).updateSingleGoodsNumber(num);
                      return;
                    }
                  }
                  if (deliverNum + singleNum > 9999) {
                    showAlertDialog(context: context, title: kEnterNumIsTooBig);
                  } else {
                    ref.read(goodsInputNumberNotifierProvider.notifier).updateSingleGoodsNumber(value);
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.titleMedium,
        ),
      ),
    );
  }
}
