import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../exceptions/app_exception.dart';
import '../../../../config/strings.dart';
import '../../../../widgets/bottom_tab_bar.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../../history/history_controller.dart';
import '../single_correction_controller.dart';

/// 訂正登録 tab bar
class SingleCorrectionBottomTabBar extends ConsumerWidget {
  /// constructor
  const SingleCorrectionBottomTabBar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref
      ..listen(deleteDataStateProvider, (previous, next) {
        final isSuccess = next.value;
        if (isSuccess ?? false) {
          /// close alert
          context.pop();
          _back(ref);
        }
      })
      ..listen(updateDeliveriesNumberStateProvider, (previous, next) {
        final isSuccess = next.value;
        if (isSuccess ?? false) {
          /// close alert
          context.pop();
          _back(ref);
        }
      })
      ..listen(updateScanDataFlgStateProvider, (previous, next) {
        final isSuccess = next.value;
        if (isSuccess ?? false) {
          /// close alert
          context.pop();
          _back(ref);
        }
      });

    return BottomTabBar(
      leading: PacerBackButton(
        onPressed: () => context.pop(),
      ),
      actions: [
        OutlineRoundTextButton(
          kDelete,
          onPressed: () => _delete(ref),
        ),
        OutlineRoundTextButton(
          kQuantityCorrection,
          onPressed: () => _onConfirmClick(ref),
        ),
      ],
    );
  }

  void _back(WidgetRef ref) {
    ref.read(historyListProvider.notifier).reload();
    ref.context.pop();
  }

  Future<void> _delete(WidgetRef ref) async {
    final isOk = await showAlertDialog(
      context: ref.context,
      title: kDoYouWantDeleteIt,
      cancelActionText: kNo,
      defaultActionText: kYes,
    );
    if (isOk ?? false) {
      _deleteData(ref);
    }
  }

  Future<void> _onConfirmClick(
    WidgetRef ref,
  ) async {
    final info = ref.read(getSlipInfoStateProvider).value;
    if (info == null) return;

    final inputValue = double.tryParse(ref.read(inputNumberStateProvider)) ?? 0.0;
    if (info.isBread) {
      final tempValue = info.asnNum > 0 ? info.asnNum : info.orderNum;
      if (inputValue > tempValue) {
        if (tempValue == 0) {
          _updateScanDataFlg(ref, inputValue, true);
        } else {
          final isOk = await showAlertDialog(
            context: ref.context,
            title: kExcessQuantity,
            cancelActionText: kNo,
            defaultActionText: kYes,
          );
          if (isOk ?? false) {
            _updateScanDataFlg(ref, inputValue, true);
          }
        }
      } else {
        _updateScanDataFlg(ref, inputValue, false);
      }
    } else {
      if (inputValue > info.orderNum) {
        await showAlertDialog(
          context: ref.context,
          title: kInputValueGreaterThanPlanedQuantity,
        );
      } else {
        _updateScanData(ref, inputValue);
      }
    }
  }

  void _updateScanData(WidgetRef ref, double inputValue) {
    ref.read(updateDeliveriesNumberStateProvider.notifier).updateNumber(inputValue);
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final updateDataState = ref.watch(updateDeliveriesNumberStateProvider);

            return AlertDialog(
              title: const Text(''),
              content: switch (updateDataState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncData() || AsyncLoading() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (updateDataState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _deleteData(WidgetRef ref) {
    ref.read(deleteDataStateProvider.notifier).delete();
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final deleteDataState = ref.watch(deleteDataStateProvider);

            return AlertDialog(
              title: const Text(kDeleting),
              content: switch (deleteDataState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncData() || AsyncLoading() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (deleteDataState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _updateScanDataFlg(WidgetRef ref, double inputValue, bool isError) {
    ref.read(updateScanDataFlgStateProvider.notifier).updateScanData(inputValue, isError: isError);
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final updateScanFlgState = ref.watch(updateScanDataFlgStateProvider);

            return AlertDialog(
              /// データ登録中、少々お待ちください。
              title: const Text(''),
              content: switch (updateScanFlgState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncData() || AsyncLoading() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (updateScanFlgState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}
