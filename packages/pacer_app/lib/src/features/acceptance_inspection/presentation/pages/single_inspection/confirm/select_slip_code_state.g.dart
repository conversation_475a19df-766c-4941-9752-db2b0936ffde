// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'select_slip_code_state.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectSlipCodeStateHash() => r'6c4cd75d0d0d7bd0684fff5d51d8756597a9c138';

/// selected slip code
///
/// Copied from [SelectSlipCodeState].
@ProviderFor(SelectSlipCodeState)
final selectSlipCodeStateProvider = AutoDisposeNotifierProvider<SelectSlipCodeState, String>.internal(
  SelectSlipCodeState.new,
  name: r'selectSlipCodeStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectSlipCodeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectSlipCodeState = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
