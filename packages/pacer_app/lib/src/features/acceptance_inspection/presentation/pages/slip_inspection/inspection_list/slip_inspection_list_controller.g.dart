// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slip_inspection_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$slipInfoListStateHash() => r'4c86fa28fd792165b19ddafa46ad320c96339f56';

/// slip list
///
/// Copied from [SlipInfoListState].
@ProviderFor(SlipInfoListState)
final slipInfoListStateProvider =
    AutoDisposeAsyncNotifierProvider<SlipInfoListState, List<CorrectDetailsTree>>.internal(
  SlipInfoListState.new,
  name: r'slipInfoListStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$slipInfoListStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SlipInfoListState = AutoDisposeAsyncNotifier<List<CorrectDetailsTree>>;
String _$deleteAllDataStateHash() => r'a28b24b38972ac8b72f85d191d71abbe83a5c9e5';

/// delete all data
///
/// Copied from [DeleteAllDataState].
@ProviderFor(DeleteAllDataState)
final deleteAllDataStateProvider = AutoDisposeAsyncNotifierProvider<DeleteAllDataState, bool?>.internal(
  DeleteAllDataState.new,
  name: r'deleteAllDataStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deleteAllDataStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteAllDataState = AutoDisposeAsyncNotifier<bool?>;
String _$confirmDataStateHash() => r'1a4b34dcb6a2613318de09144d2550d51dd707a2';

/// confirm data
///
/// Copied from [ConfirmDataState].
@ProviderFor(ConfirmDataState)
final confirmDataStateProvider = AutoDisposeAsyncNotifierProvider<ConfirmDataState, int?>.internal(
  ConfirmDataState.new,
  name: r'confirmDataStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmDataStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmDataState = AutoDisposeAsyncNotifier<int?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
