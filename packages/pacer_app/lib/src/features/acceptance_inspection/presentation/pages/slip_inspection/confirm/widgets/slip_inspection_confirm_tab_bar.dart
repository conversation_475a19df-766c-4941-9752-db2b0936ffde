import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../exceptions/app_exception.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../../../../utils/async_value_ui.dart';
import '../../../../../domain/slip_inspection/support_info.dart';
import '../../../../config/strings.dart';
import '../../../../routing/acceptance_inspection_route.dart';
import '../../../../widgets/bottom_tab_bar.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../../../single_inspection/confirm/select_slip_code_state.dart';
import '../../inspection_list/slip_inspection_list_controller.dart';
import '../../setting/slip_setting_controller.dart';
import '../slip_inspection_confirm_controller.dart';

/// 確定登録 tab bar
class SlipInspectionConfirmTabBar extends ConsumerWidget {
  /// constructor
  const SlipInspectionConfirmTabBar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final supportListState = ref.watch(getSupportListStateProvider);

    ref
      ..listen(confirmSelectSlipStateProvider, (previous, next) {
        final isSuccess = next.asData?.value;
        if (isSuccess != null && isSuccess) {
          context.pop();
          _showConfirmAlert(ref.context);
        }
      })
      ..listen(deleteAllDataStateProvider, (previous, next) {
        final isSuccess = next.asData?.value;
        if (isSuccess != null && isSuccess) {
          context.pop();
          _backMenuList(ref);
        }
      })
      ..listen(getSupportListStateProvider, (_, state) {
        state.showSnackBarOnError(context);
        final supportList = state.asData?.value;
        if (supportList == null) return;
        _checkSupportList(ref, supportList);
      });

    return BottomTabBar(
      leading: PacerBackButton(
        onPressed: () => _back(ref),
      ),
      actions: [
        OutlineRoundTextButton(
          kFix,
          onPressed: () => _onEditClick(context, ref),
        ),
        const SizedBox(width: 8),
        switch (supportListState) {
          AsyncValue(isLoading: true) => const Center(
              child: CircularProgressIndicator(),
            ),
          _ => OutlineRoundTextButton(
              kSure,
              onPressed: () => _onConfirmClick(context, ref),
            ),
        },
      ],
    );
  }

  Future<void> _onEditClick(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final selectCellInfo = ref.read(selectSlipStateProvider);
    if (selectCellInfo == null) return;
    ref.read(selectSlipCodeStateProvider.notifier).update(selectCellInfo.slipNo);
    final isInspection = ref.read(isInsufficientInspectionStateProvider);
    if (isInspection) {
      /// 「伝票検品　確認登録②」へ遷移
      const SlipConfirmRoute().go(context);
    } else {
      /// 「確認登録②」へ遷移
      const SlipEditNumberRoute().go(context);
    }
  }

  Future<void> _showConfirmAlert(BuildContext context) async {
    final isOk = await showAlertDialog(
      context: context,
      title: kConfirmed,
    );
    if (isOk != null && isOk && context.mounted) {
      const InspectionMenuRoute().go(context);
    }
  }

  void _back(WidgetRef ref) {
    ref.read(deleteAllDataStateProvider.notifier).delete();
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final deleteAllDataState = ref.watch(deleteAllDataStateProvider);

            return AlertDialog(
              title: const Text(kRequestData),
              content: switch (deleteAllDataState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncData() || AsyncLoading() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (deleteAllDataState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _backMenuList(WidgetRef ref) {
    ref.read(unCheckVendorListStateProvider.notifier).getList();
    const SlipSettingRoute().go(ref.context);
  }

  Future<void> _checkSupportList(
    WidgetRef ref,
    List<SupportInfo> supportList,
  ) async {
    if (supportList.isNotEmpty) {
      final info = supportList.first;
      if (info.registeredCount > 0 || info.childCount > 0) {
        final isOk = await showAlertDialog(
          context: ref.context,
          title: kConfirmDataIsOk,
          cancelActionText: kNo,
          defaultActionText: kYes,
        );
        if (isOk == null || !isOk) return;
      }
    }
    _insertData(ref);
  }

  Future<void> _onConfirmClick(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final dataArray = ref.read(confirmSlipListProvider).value;
    if (dataArray == null) return;

    var isCanConfirm = false;
    for (var i = 0; i < dataArray.length; i++) {
      final info = dataArray[i];
      if (info.statusName == kStatusIncomplete && !info.isSuspended) {
        break;
      }

      if (info.statusName.startsWith(kStatusCompleted) || info.statusName == kStatusConfirmed) {
        isCanConfirm = true;
      }
    }

    if (isCanConfirm) {
      final isOk = await showAlertDialog(
        context: context,
        title: kConfirmDataIsOk,
        cancelActionText: kNo,
        defaultActionText: kYes,
      );
      if (isOk == null || !isOk) return;
      ref.read(getSupportListStateProvider.notifier).getData();
    } else {
      await showAlertDialog(context: context, title: kHasIncompleteItem);
    }
  }

  void _insertData(WidgetRef ref) {
    ref.read(confirmSelectSlipStateProvider.notifier).confirmSlips();
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final confirmSlipState = ref.watch(confirmSelectSlipStateProvider);

            return AlertDialog(
              /// データ登録中、少々お待ちください。
              title: const Text(kInsertingGoods),
              content: switch (confirmSlipState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncLoading() || AsyncData() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (confirmSlipState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}
