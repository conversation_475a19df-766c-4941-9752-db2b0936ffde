import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../themes/app_color_scheme.dart';
import '../../../../../domain/warehouse/goods.dart';
import '../../../../config/strings.dart';
import '../../../../utils/utils.dart';
import '../../../../widgets/number_text_field.dart';
import '../warehouse_inspection_controller.dart';

/// table
class WarehouseInspectionTable extends ConsumerWidget {
  /// constructor
  const WarehouseInspectionTable({
    super.key,
    required this.goods,
  });

  /// goods info
  final Goods goods;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = Theme.of(context).colorScheme;

    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.3), // 1列目は全体の50%
        1: FractionColumnWidth(0.7), // 2列目は全体の25%
      },
      border: TableBorder.all(
        color: colors.line,
        borderRadius: const BorderRadius.all(
          Radius.circular(6),
        ),
      ),
      children: [
        TableRow(
          children: [
            /// 商品名
            const _ItemNameText(kGoodsName),
            _ValueText(goods.productName),
          ],
        ),
        TableRow(
          children: [
            /// 規格
            const _ItemNameText(kSpec),
            _ValueText(goods.specName),
          ],
        ),
        TableRow(
          children: [
            /// カラー
            const _ItemNameText(kColor),
            _ValueText(goods.productColor),
          ],
        ),
        TableRow(
          children: [
            /// サイズ
            const _ItemNameText(kSize),
            _ValueText(goods.productSize),
          ],
        ),
        TableRow(
          children: [
            /// 納品予定数
            const _ItemNameText(kPlanNumber),
            _ValueText(checkDoubleToString(goods.quantity)),
          ],
        ),
        TableRow(
          children: [
            /// 数量
            const _ItemNameText(kNumber),
            Consumer(
              builder: (ctx, ref, _) {
                final isFixed = ref.watch(fixedInputValueStateProvider);

                return NumberTextField(
                  inputText: ref.watch(inputNumberStateProvider),
                  readOnly: isFixed,
                  isCanInputDouble: is28PrefixJan(goods.productCode),
                  onFieldSubmitted: (value) => ref.read(inputNumberStateProvider.notifier).updateNumber(value),
                );
              },
            ),
          ],
        ),
      ],
    );
  }
}

class _ItemNameText extends StatelessWidget {
  const _ItemNameText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.bodyLarge,
          textAlign: TextAlign.right,
        ),
      ),
    );
  }
}

class _ValueText extends StatelessWidget {
  const _ValueText(this.text);

  final String text;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;

    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Text(
          text,
          style: texts.bodyLarge,
        ),
      ),
    );
  }
}
