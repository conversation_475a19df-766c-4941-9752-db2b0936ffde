import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../exceptions/app_exception.dart';
import '../../../../../../slip-confirmation/presentation/slip_confirm_registration/slip_confirm_registration_controller.dart'
    as slip_confirm;
import '../../../../config/strings.dart';
import '../../../../widgets/bottom_tab_bar.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../../../slip_inspection/confirm/slip_inspection_confirm_controller.dart' as slip;
import '../../confirm/single_confirm_controller.dart';
import '../single_edit_number_controller.dart';

/// 確定登録 tab bar
class SingleEditBottomTabBar extends ConsumerWidget {
  /// constructor
  const SingleEditBottomTabBar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(confirmSelectGoodsStateProvider, (previous, next) {
      final updateReceiveNum = next.asData?.value;
      if (updateReceiveNum != null) {
        context.pop();
        _showConfirmAlert(ref, ref.context);
      }
    });

    return BottomTabBar(
      leading: PacerBackButton(
        onPressed: () => context.pop(),
      ),
      actions: [
        OutlineRoundTextButton(
          kLogin,
          onPressed: () => _onConfirmClick(ref),
        ),
      ],
    );
  }

  Future<void> _showConfirmAlert(WidgetRef ref, BuildContext context) async {
    final isOk = await showAlertDialog(
      context: context,
      title: kHadLogin,
    );
    if (isOk != null && isOk && context.mounted) {
      final location = GoRouterState.of(context).uri.toString();
      if (location.contains('/single/confirm')) {
        ref.read(confirmSlipListProvider.notifier).reload();
      } else if (location.contains('/slip_setting/inspection_list/confirm')) {
        ref.read(slip.confirmSlipListProvider.notifier).reload();
      } else if (location.contains('/slip_confirm_registration')) {
        ref.read(slip_confirm.confirmSlipListProvider.notifier).reload();
      }
      context.pop();
    }
  }

  Future<void> _onConfirmClick(
    WidgetRef ref,
  ) async {
    final isOk = await showAlertDialog(
      context: ref.context,
      title: kAreYouSureToLogin,
      cancelActionText: kNo,
      defaultActionText: kYes,
    );
    if (isOk == null || !isOk) return;
    _insertData(ref);
  }

  void _insertData(WidgetRef ref) {
    ref.read(confirmSelectGoodsStateProvider.notifier).confirmGoods();
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final confirmSlipState = ref.watch(confirmSelectGoodsStateProvider);

            return AlertDialog(
              /// データ登録中、少々お待ちください。
              title: const Text(kInsertingGoods),
              content: switch (confirmSlipState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncData() || AsyncLoading() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (confirmSlipState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}
