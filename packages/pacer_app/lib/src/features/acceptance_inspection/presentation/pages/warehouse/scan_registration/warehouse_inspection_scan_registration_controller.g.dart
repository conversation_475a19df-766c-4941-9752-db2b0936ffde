// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse_inspection_scan_registration_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$scanRegistrationCodeHash() => r'0d40c69ef494a8bf1ff8fada89e05f496648e139';

/// code state
///
/// Copied from [ScanRegistrationCode].
@ProviderFor(ScanRegistrationCode)
final scanRegistrationCodeProvider = AutoDisposeNotifierProvider<ScanRegistrationCode, String>.internal(
  ScanRegistrationCode.new,
  name: r'scanRegistrationCodeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$scanRegistrationCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ScanRegistrationCode = AutoDisposeNotifier<String>;
String _$selectSlipAndGoodsHash() => r'f541a64adc5ec68dd2e0f992804e070ecf203ee2';

/// selecting SelectSlipAndGoodsInfo state
///
/// Copied from [SelectSlipAndGoods].
@ProviderFor(SelectSlipAndGoods)
final selectSlipAndGoodsProvider = AutoDisposeNotifierProvider<SelectSlipAndGoods, SelectSlipAndGoodsInfo>.internal(
  SelectSlipAndGoods.new,
  name: r'selectSlipAndGoodsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectSlipAndGoodsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectSlipAndGoods = AutoDisposeNotifier<SelectSlipAndGoodsInfo>;
String _$checkCodeTypeGetInfoHash() => r'984c447ced5c60968e18c1c81dc6b552ea111f64';

/// check input code, get slip info
///
/// Copied from [CheckCodeTypeGetInfo].
@ProviderFor(CheckCodeTypeGetInfo)
final checkCodeTypeGetInfoProvider = AutoDisposeAsyncNotifierProvider<CheckCodeTypeGetInfo, bool?>.internal(
  CheckCodeTypeGetInfo.new,
  name: r'checkCodeTypeGetInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkCodeTypeGetInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckCodeTypeGetInfo = AutoDisposeAsyncNotifier<bool?>;
String _$checkSlipAndGoodsInfoHash() => r'31d714509b1cbc6c41048aaf4d32c7bdff9059e4';

/// JANより 伝票番号情報を取る
///
/// Copied from [CheckSlipAndGoodsInfo].
@ProviderFor(CheckSlipAndGoodsInfo)
final checkSlipAndGoodsInfoProvider =
    AutoDisposeAsyncNotifierProvider<CheckSlipAndGoodsInfo, List<SlipCodeInfo>?>.internal(
  CheckSlipAndGoodsInfo.new,
  name: r'checkSlipAndGoodsInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkSlipAndGoodsInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckSlipAndGoodsInfo = AutoDisposeAsyncNotifier<List<SlipCodeInfo>?>;
String _$checkSlipNoHash() => r'f86d4b1d868b23e817bb6887485eb1e79c39aab4';

/// 伝票番号が検品済みかどうかを判断する
///
/// Copied from [CheckSlipNo].
@ProviderFor(CheckSlipNo)
final checkSlipNoProvider = AutoDisposeAsyncNotifierProvider<CheckSlipNo, SlipCodeJudge?>.internal(
  CheckSlipNo.new,
  name: r'checkSlipNoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkSlipNoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckSlipNo = AutoDisposeAsyncNotifier<SlipCodeJudge?>;
String _$checkShipsStateHash() => r'a47a4db51f2adeb45680b767e100084e5a433783';

/// check status
///
/// Copied from [CheckShipsState].
@ProviderFor(CheckShipsState)
final checkShipsStateProvider = AutoDisposeNotifierProvider<CheckShipsState, void>.internal(
  CheckShipsState.new,
  name: r'checkShipsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkShipsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckShipsState = AutoDisposeNotifier<void>;
String _$isShowAlertStateHash() => r'58217291445760825b30779d93ff6d2c5659301d';

/// alert status
///
/// Copied from [IsShowAlertState].
@ProviderFor(IsShowAlertState)
final isShowAlertStateProvider = AutoDisposeNotifierProvider<IsShowAlertState, bool>.internal(
  IsShowAlertState.new,
  name: r'isShowAlertStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$isShowAlertStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$IsShowAlertState = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
