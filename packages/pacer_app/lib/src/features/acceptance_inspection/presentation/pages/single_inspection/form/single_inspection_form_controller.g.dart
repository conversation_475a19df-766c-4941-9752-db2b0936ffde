// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'single_inspection_form_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getStoreAndSupplierAuthorityHash() => r'e1226d4f4dc64d00e3530de4ec505a1f0a617e17';

/// 店舗制御とベンダー制御取得
///
/// Copied from [getStoreAndSupplierAuthority].
@ProviderFor(getStoreAndSupplierAuthority)
final getStoreAndSupplierAuthorityProvider = AutoDisposeFutureProvider<StoreAndSupplierAuthority>.internal(
  getStoreAndSupplierAuthority,
  name: r'getStoreAndSupplierAuthorityProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getStoreAndSupplierAuthorityHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef GetStoreAndSupplierAuthorityRef = AutoDisposeFutureProviderRef<StoreAndSupplierAuthority>;
String _$getStoreAuthorityHash() => r'84e441e20a337ca12c6016f4428282604ae75bde';

/// 店舗制御フラグ
///
/// Copied from [getStoreAuthority].
@ProviderFor(getStoreAuthority)
final getStoreAuthorityProvider = AutoDisposeFutureProvider<bool>.internal(
  getStoreAuthority,
  name: r'getStoreAuthorityProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getStoreAuthorityHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef GetStoreAuthorityRef = AutoDisposeFutureProviderRef<bool>;
String _$getIPStatusHash() => r'ff00680e1aaa703771861780fd190949985e19d3';

/// 途中検品したデータチェック
///
/// Copied from [getIPStatus].
@ProviderFor(getIPStatus)
final getIPStatusProvider = AutoDisposeFutureProvider<IpStatus>.internal(
  getIPStatus,
  name: r'getIPStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getIPStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef GetIPStatusRef = AutoDisposeFutureProviderRef<IpStatus>;
String _$inputNumberStateHash() => r'f1d04959b15d197012792c90b1ed8f0ba9ce9718';

/// input number
///
/// Copied from [InputNumberState].
@ProviderFor(InputNumberState)
final inputNumberStateProvider = AutoDisposeNotifierProvider<InputNumberState, String>.internal(
  InputNumberState.new,
  name: r'inputNumberStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputNumberStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputNumberState = AutoDisposeNotifier<String>;
String _$tempVariateStateHash() => r'a164b2c2b22d0cb7808b31a2a2247ff35124fd26';

/// temp quantity
///
/// Copied from [TempVariateState].
@ProviderFor(TempVariateState)
final tempVariateStateProvider = AutoDisposeNotifierProvider<TempVariateState, TempVariate>.internal(
  TempVariateState.new,
  name: r'tempVariateStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$tempVariateStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TempVariateState = AutoDisposeNotifier<TempVariate>;
String _$fixedInputValueStateHash() => r'd12df63906cff76568e8cde18ce8465985ad3a62';

/// fixed input value
///
/// Copied from [FixedInputValueState].
@ProviderFor(FixedInputValueState)
final fixedInputValueStateProvider = AutoDisposeNotifierProvider<FixedInputValueState, bool>.internal(
  FixedInputValueState.new,
  name: r'fixedInputValueStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fixedInputValueStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FixedInputValueState = AutoDisposeNotifier<bool>;
String _$productCodeHash() => r'99178bcbe15623778dcd2979469001fe523d9729';

/// jan
///
/// Copied from [ProductCode].
@ProviderFor(ProductCode)
final productCodeProvider = AutoDisposeNotifierProvider<ProductCode, String>.internal(
  ProductCode.new,
  name: r'productCodeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$productCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProductCode = AutoDisposeNotifier<String>;
String _$slipCodeStateHash() => r'0cafbc7cbe303138af3198095a28843681721c90';

/// slip code
///
/// Copied from [SlipCodeState].
@ProviderFor(SlipCodeState)
final slipCodeStateProvider = AutoDisposeNotifierProvider<SlipCodeState, String>.internal(
  SlipCodeState.new,
  name: r'slipCodeStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$slipCodeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SlipCodeState = AutoDisposeNotifier<String>;
String _$progressStateHash() => r'26d30efa920beea13d2240474087dd02fcf94c91';

/// progress (1/2)
///
/// Copied from [ProgressState].
@ProviderFor(ProgressState)
final progressStateProvider = AutoDisposeNotifierProvider<ProgressState, String>.internal(
  ProgressState.new,
  name: r'progressStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$progressStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProgressState = AutoDisposeNotifier<String>;
String _$getProgressStateHash() => r'b2128026a8f096110db5dd23da9d486f786e4272';

/// get progress
///
/// Copied from [GetProgressState].
@ProviderFor(GetProgressState)
final getProgressStateProvider = AutoDisposeAsyncNotifierProvider<GetProgressState, OrderProgress?>.internal(
  GetProgressState.new,
  name: r'getProgressStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getProgressStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetProgressState = AutoDisposeAsyncNotifier<OrderProgress?>;
String _$initDataStateHash() => r'431ae47292691f377a1a57127f7580f6dafeb9d6';

/// init data
///
/// Copied from [InitDataState].
@ProviderFor(InitDataState)
final initDataStateProvider = AutoDisposeAsyncNotifierProvider<InitDataState, IpStatus?>.internal(
  InitDataState.new,
  name: r'initDataStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$initDataStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InitDataState = AutoDisposeAsyncNotifier<IpStatus?>;
String _$getSystemDateStateHash() => r'3b26b1851f6053b0656548f6789e219bfea52a90';

/// 当日日付、時間、週取得
///
/// Copied from [GetSystemDateState].
@ProviderFor(GetSystemDateState)
final getSystemDateStateProvider = AutoDisposeAsyncNotifierProvider<GetSystemDateState, SystemDate>.internal(
  GetSystemDateState.new,
  name: r'getSystemDateStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getSystemDateStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetSystemDateState = AutoDisposeAsyncNotifier<SystemDate>;
String _$deleteCacheDataByCurrentIpStateHash() => r'31b4989bb47bef88d36877dc134a6cd0e296b718';

/// delete cache data by current ip
///
/// Copied from [DeleteCacheDataByCurrentIpState].
@ProviderFor(DeleteCacheDataByCurrentIpState)
final deleteCacheDataByCurrentIpStateProvider =
    AutoDisposeAsyncNotifierProvider<DeleteCacheDataByCurrentIpState, bool?>.internal(
  DeleteCacheDataByCurrentIpState.new,
  name: r'deleteCacheDataByCurrentIpStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$deleteCacheDataByCurrentIpStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteCacheDataByCurrentIpState = AutoDisposeAsyncNotifier<bool?>;
String _$deleteAllDataStateHash() => r'347d0ce7dc2f445106795450061f7a1a8c3bb42c';

/// delete all data
///
/// Copied from [DeleteAllDataState].
@ProviderFor(DeleteAllDataState)
final deleteAllDataStateProvider = AutoDisposeAsyncNotifierProvider<DeleteAllDataState, bool?>.internal(
  DeleteAllDataState.new,
  name: r'deleteAllDataStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deleteAllDataStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteAllDataState = AutoDisposeAsyncNotifier<bool?>;
String _$deleteDataStateHash() => r'cda3357f54250a84102004ebc4c1193a1965544a';

/// delete data
///
/// Copied from [DeleteDataState].
@ProviderFor(DeleteDataState)
final deleteDataStateProvider = AutoDisposeAsyncNotifierProvider<DeleteDataState, bool?>.internal(
  DeleteDataState.new,
  name: r'deleteDataStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$deleteDataStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteDataState = AutoDisposeAsyncNotifier<bool?>;
String _$getInitialSingleDataStateHash() => r'87c8188de2e3b0e9cd15cce508e6077357526707';

///
///
/// Copied from [GetInitialSingleDataState].
@ProviderFor(GetInitialSingleDataState)
final getInitialSingleDataStateProvider =
    AutoDisposeAsyncNotifierProvider<GetInitialSingleDataState, InitialSingleData?>.internal(
  GetInitialSingleDataState.new,
  name: r'getInitialSingleDataStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getInitialSingleDataStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetInitialSingleDataState = AutoDisposeAsyncNotifier<InitialSingleData?>;
String _$getGoodsAndVendorInfoStateHash() => r'e717378d04f63496c151539d3757c2ace0f981af';

/// jan input
///
/// Copied from [GetGoodsAndVendorInfoState].
@ProviderFor(GetGoodsAndVendorInfoState)
final getGoodsAndVendorInfoStateProvider =
    AutoDisposeAsyncNotifierProvider<GetGoodsAndVendorInfoState, GoodsAndVendorInfo?>.internal(
  GetGoodsAndVendorInfoState.new,
  name: r'getGoodsAndVendorInfoStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getGoodsAndVendorInfoStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetGoodsAndVendorInfoState = AutoDisposeAsyncNotifier<GoodsAndVendorInfo?>;
String _$getTempInfoStateHash() => r'37255db87767d216ccdcb89ab6183b1178dec856';

/// temp info
///
/// Copied from [GetTempInfoState].
@ProviderFor(GetTempInfoState)
final getTempInfoStateProvider = AutoDisposeNotifierProvider<GetTempInfoState, GoodsAndVendorInfo?>.internal(
  GetTempInfoState.new,
  name: r'getTempInfoStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getTempInfoStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetTempInfoState = AutoDisposeNotifier<GoodsAndVendorInfo?>;
String _$updateDeliveriesNumberStateHash() => r'11b0b5ea69974b03b4b22b7e59ee13eb46ab9a2d';

/// 納品数更新
///
/// Copied from [UpdateDeliveriesNumberState].
@ProviderFor(UpdateDeliveriesNumberState)
final updateDeliveriesNumberStateProvider =
    AutoDisposeAsyncNotifierProvider<UpdateDeliveriesNumberState, bool?>.internal(
  UpdateDeliveriesNumberState.new,
  name: r'updateDeliveriesNumberStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$updateDeliveriesNumberStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateDeliveriesNumberState = AutoDisposeAsyncNotifier<bool?>;
String _$getScanCountDataStateHash() => r'6eb4f346e5d611d677a6b2f9c749aacb7db7cb6c';

/// 単品検品の該当伝票の検品進捗取得
///
/// Copied from [GetScanCountDataState].
@ProviderFor(GetScanCountDataState)
final getScanCountDataStateProvider = AutoDisposeAsyncNotifierProvider<GetScanCountDataState, ScanCount?>.internal(
  GetScanCountDataState.new,
  name: r'getScanCountDataStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getScanCountDataStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetScanCountDataState = AutoDisposeAsyncNotifier<ScanCount?>;
String _$checkVendorStateHash() => r'ff8df5b69582e36ee51396a31b5ce7bce6e44c41';

/// ベンダーチェック
///
/// Copied from [CheckVendorState].
@ProviderFor(CheckVendorState)
final checkVendorStateProvider = AutoDisposeAsyncNotifierProvider<CheckVendorState, bool?>.internal(
  CheckVendorState.new,
  name: r'checkVendorStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$checkVendorStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckVendorState = AutoDisposeAsyncNotifier<bool?>;
String _$updateOrderStateHash() => r'0f4b65e181ffcec0fae0da5ed5d39f9bd41311a6';

/// 納品数更新
///
/// Copied from [UpdateOrderState].
@ProviderFor(UpdateOrderState)
final updateOrderStateProvider = AutoDisposeAsyncNotifierProvider<UpdateOrderState, bool?>.internal(
  UpdateOrderState.new,
  name: r'updateOrderStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$updateOrderStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateOrderState = AutoDisposeAsyncNotifier<bool?>;
String _$updateScanDataFlgStateHash() => r'8c57d836690c9f6fa271a19b7a9e71cfaf9da4ae';

/// 確定登録②　納品数・誤差登録
///
/// Copied from [UpdateScanDataFlgState].
@ProviderFor(UpdateScanDataFlgState)
final updateScanDataFlgStateProvider = AutoDisposeAsyncNotifierProvider<UpdateScanDataFlgState, bool?>.internal(
  UpdateScanDataFlgState.new,
  name: r'updateScanDataFlgStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$updateScanDataFlgStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateScanDataFlgState = AutoDisposeAsyncNotifier<bool?>;
String _$getOrderDataForSlipStateHash() => r'44dedac868a58633e76ecf2af95de109d24d9b88';

/// 同じ商品、複数伝票から一つ伝票情報取得して、スキャン登録画面へ表示
///
/// Copied from [GetOrderDataForSlipState].
@ProviderFor(GetOrderDataForSlipState)
final getOrderDataForSlipStateProvider = AutoDisposeAsyncNotifierProvider<GetOrderDataForSlipState, Order?>.internal(
  GetOrderDataForSlipState.new,
  name: r'getOrderDataForSlipStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getOrderDataForSlipStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetOrderDataForSlipState = AutoDisposeAsyncNotifier<Order?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
