import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../config/strings.dart';
import 'history_controller.dart';
import 'widgets/history_bottom_tab_bar.dart';
import 'widgets/history_cell.dart';

/// 単品検品ー履歴
class HistoryPage extends HookConsumerWidget {
  /// init
  const HistoryPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = useScrollController();
    final goodsArray = ref.watch(historyListProvider);

    ref.listen(historyListProvider, (previous, next) async {
      final result = next.asData?.value;
      if (result != null && result.isEmpty) {
        await showAlertDialog(context: context, title: kNoGoods);
        if (context.mounted) {
          context.pop();
        }
      }
    });

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kHistoryTitle),
      ),
      body: switch (goodsArray) {
        AsyncData(:final value) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            child: Column(
              children: [
                _Header(),
                Expanded(
                  child: ListView.separated(
                    controller: scrollController,
                    itemBuilder: (context, index) {
                      final cellInfo = value[index];

                      return Consumer(
                        builder: (ctx, ref, _) {
                          final selectedGoods = ref.watch(selectGoodsStateProvider);

                          return HistoryCell(
                            goods: cellInfo,
                            isSelected: selectedGoods == cellInfo,
                            onPressed: () => ref.read(selectGoodsStateProvider.notifier).update(cellInfo),
                          );
                        },
                      );
                    },
                    separatorBuilder: (context, _) => const SizedBox(
                      height: 6,
                    ),
                    itemCount: value.length,
                  ),
                ),
              ],
            ),
          ),
        AsyncError(:final error) => Text(
            switch (error) {
              UnknownException() => error.message,

              /// 取得に失敗しました
              _ => kRequestError,
            },
          ),
        _ => const Center(
            child: CircularProgressIndicator(),
          ),
      },
      bottomNavigationBar: const HistoryBottomTabBar(),
    );
  }
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyLarge?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(kHistoryTip, style: texts.titleMedium),
        const SizedBox(
          height: 4,
        ),
        Text(kPushCorrectionTip, style: texts.titleMedium),
        Container(
          decoration: BoxDecoration(
            color: colors.surfaceContainerHighest,
            border: Border.fromBorderSide(
              BorderSide(color: colors.outlineVariant),
            ),
          ),
          margin: const EdgeInsets.only(bottom: 12, top: 10),
          child: IntrinsicHeight(
            child: Row(
              children: [
                /// 商品情報
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      kGoodsName,
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  child: Text(
                    kSpec,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),

                /// 検品数
                Expanded(
                  child: Text(
                    kInspectionNumber,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
