import 'dart:developer';

import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../app/application/global_loading_service.dart';
import '../../../../../slip-confirmation/application/slip_confirm_service.dart';
import '../../../../../slip-confirmation/domain/slip_confirmation.dart';
import '../../../../application/single_inspection_service.dart';
import '../../../../application/slip_inspection_service.dart';
import '../../../../domain/product_code.dart';
import '../../../../domain/single/store_supplier_authority.dart';
import '../../../../domain/slip_inspection/input_check_info.dart';
import '../../../../domain/slip_inspection/vendor_info.dart';

part 'slip_setting_controller.g.dart';

/// init data
@riverpod
class StoreAndSupplierAuthorityState extends _$StoreAndSupplierAuthorityState {
  @override
  AsyncValue<StoreAndSupplierAuthority?> build() {
    ref.showGlobalLoading();
    return const AsyncData(null);
  }

  /// get
  Future<void> getAuthority() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() {
      return ref.read(singleInspectionServiceProvider).getStoreAndSupplierAuthority();
    });
  }
}

/// get system date
@riverpod
class SystemDateState extends _$SystemDateState {
  /// init
  @override
  AsyncValue<DateTime?> build() {
    ref.showGlobalLoading();
    return const AsyncData(null);
  }

  /// get
  Future<void> getSystemDate() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final systemDate = await ref.read(singleInspectionServiceProvider).getSystemDate();
      return DateFormat('yyyy/MM/dd').parse(systemDate.date);
    });
  }
}

/// 伝票検品設定画面の仕入先一覧（ベンダー一覧）
@riverpod
class UnCheckVendorListState extends _$UnCheckVendorListState {
  @override
  AsyncValue<List<Vendor>> build() => const AsyncData([]);

  /// リストを更新する
  Future<void> getList() async {
    final timeInfo = ref.read(dateTimeStateProvider);
    state = const AsyncLoading();
    state = await AsyncValue.guard(() {
      return ref.read(slipConfirmServiceProvider).listVendors(
            startDate: timeInfo.startTime,
            endDate: timeInfo.endTime,
          );
    });
  }
}

/// 商品コードによりベンダー名取得
@riverpod
class GetVendorByJanState extends _$GetVendorByJanState {
  @override
  AsyncValue<VendorInfo?> build() {
    ref.showGlobalLoading();
    return const AsyncData(null);
  }

  /// リストを更新する
  Future<void> getVendor(String jan) async {
    final code = jan.productCodeParse();
    if (code.isEmpty) {
      return;
    }
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(slipInspectionServiceProvider).getVendorByJan(
            productCode: jan,
          ),
    );
  }
}

/// time
@riverpod
class DateTimeState extends _$DateTimeState {
  @override
  DateTimeInfo build() => DateTimeInfo(
        startTime: DateTime.now(),
        systemTime: DateTime.now(),
        endTime: DateTime.now(),
      );

  /// update start time
  void updateStartTime(DateTime startTime) {
    state = state.copyWith(
      startTime: startTime,
      systemTime: state.systemTime,
      endTime: state.endTime,
    );
  }

  /// update system time
  void updateSystemTime(DateTime systemTime) {
    state = state.copyWith(
      startTime: state.startTime,
      systemTime: systemTime,
      endTime: state.endTime,
    );
  }

  /// update end time
  void updateEndTime(DateTime endTime) {
    state = state.copyWith(
      startTime: state.startTime,
      systemTime: state.systemTime,
      endTime: endTime,
    );
  }

  /// time interval
  int timeInterval() {
    return state.systemTime.difference(state.startTime).inDays;
  }

  /// clear
  void clear() {
    state = DateTimeInfo(
      startTime: DateTime.now(),
      systemTime: DateTime.now(),
      endTime: DateTime.now(),
    );
  }
}

/// confirm state
@riverpod
class ConfirmDataState extends _$ConfirmDataState {
  /// init
  @override
  AsyncValue<InputCheckInfo?> build() => const AsyncData(null);

  /// confirm data
  Future<void> confirm(String vendorCode) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => _all(vendorCode));
  }

  Future<InputCheckInfo> _all(String vendorCode) async {
    final vendorInfo = await ref.read(slipInspectionServiceProvider).getVendorCd(vendorCdData: int.parse(vendorCode));
// ignore: avoid_manual_providers_as_generated_provider_dependency
    ref
        .read(isInsufficientInspectionStateProvider.notifier)
        .update(isInsufficientInspection: vendorInfo.isInsufficientInspection);
    final timeState = ref.read(dateTimeStateProvider);
    return ref.read(slipInspectionServiceProvider).inputCheckBegin(
          startDate: timeState.startTime,
          endDate: timeState.endTime,
          vendorCode: vendorCode,
        );
  }
}

///
@riverpod
class IsInsufficientInspectionState extends _$IsInsufficientInspectionState {
  /// init
  @override
  bool build() => false;

  /// upload
  void update({required bool isInsufficientInspection}) {
    log('is insufficient inspection: $isInsufficientInspection');
    state = isInsufficientInspection;
  }
}

///
class DateTimeInfo {
  /// init
  DateTimeInfo({
    required this.startTime,
    required this.systemTime,
    required this.endTime,
  });

  /// start time
  final DateTime startTime;

  /// system time
  final DateTime systemTime;

  /// end time
  final DateTime endTime;

  /// copy
  DateTimeInfo copyWith({
    DateTime? startTime,
    DateTime? systemTime,
    DateTime? endTime,
  }) {
    return DateTimeInfo(
      startTime: startTime ?? this.startTime,
      systemTime: systemTime ?? this.systemTime,
      endTime: endTime ?? this.endTime,
    );
  }
}

/// ベンダー名コントローラー
@riverpod
class VendorNameController extends _$VendorNameController {
  @override
  AsyncValue<String> build() {
    ref.showGlobalLoading();
    return const AsyncData('');
  }

  /// ベンダー名を取得
  Future<void> getVendorName(String scanCode) async {
    final code = int.tryParse(scanCode);
    if (code == null) return;
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(slipConfirmServiceProvider).getVendorName(code),
    );
  }

  /// ベンダー名を設定
  void setVendorName(String name) {
    state = AsyncData(name);
  }
}
