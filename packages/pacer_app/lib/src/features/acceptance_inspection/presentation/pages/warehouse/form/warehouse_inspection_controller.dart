import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/warehouse_inspection_service.dart';
import '../../../../domain/product_code.dart';
import '../../../../domain/warehouse/enum/receive_status.dart';
import '../../../../domain/warehouse/enum/scan_type.dart';
import '../../../../domain/warehouse/enum/ships_type.dart';
import '../../../../domain/warehouse/goods.dart';
import '../../../../domain/warehouse/scan_info.dart';

import '../scan_registration/warehouse_inspection_scan_registration_controller.dart';

part 'warehouse_inspection_controller.g.dart';

/// input number
@riverpod
class InputNumberState extends _$InputNumberState {
  /// init
  @override
  String build() => '';

  /// update number
  void updateNumber(String number) {
    log('input number: $number');
    state = number;
  }

  /// clear
  void clear() {
    state = '';
  }
}

/// fixed input value
@riverpod
class FixedInputValueState extends _$FixedInputValueState {
  /// init
  @override
  bool build() => false;

  /// update value
  void updateValue({bool isFixed = false}) {
    log('fixed input value: $isFixed');
    ref.read(inputNumberStateProvider.notifier).updateNumber(isFixed ? '1' : '');
    state = isFixed;
  }

  /// clear
  void clear() {
    state = false;
  }
}

/// jan
@riverpod
class ProductCode extends _$ProductCode {
  /// build
  @override
  String build() {
    return '';
  }

  /// update product code
  Future<void> update(String value) async {
    // 外税対応以降、入庫検品でNONPLUコードをスキャンする場合は価格情報を含める必要があります。
    state = value.productCodeParse(processNONPLU: false);
  }

  /// clear value
  void clear() {
    state = '';
  }
}

/// slip code
@riverpod
class SlipCodeState extends _$SlipCodeState {
  /// init
  @override
  String build() => '';

  /// update progress
  void updateValue(String value) {
    log('slip code value: $value');
    state = value;
  }

  /// clear
  void clear() {
    state = '';
  }
}

/// progress (1/2)
@riverpod
class ProgressState extends _$ProgressState {
  /// init
  @override
  String build() => '';

  /// update progress
  void updateValue(String progress) {
    log('progress value: $progress');
    state = progress;
  }

  /// clear
  void clear() {
    state = '';
  }
}

/// get progress
@riverpod
class GetProgressState extends _$GetProgressState {
  /// build
  @override
  FutureOr<String?> build() async => null;

  /// get progress
  Future<void> getProgress() async {
    state = const AsyncLoading();

    final slipNumber = ref.read(selectSlipAndGoodsProvider).slipCode;
    state = await AsyncValue.guard(
      () => ref.read(warehouseInspectionServiceProvider).getProgress(slipNumber: slipNumber),
    );
    final value = state.value;
    if (!state.hasError && value != null) {
      ref.read(progressStateProvider.notifier).updateValue(value);
    }
  }
}

/// insert data
@riverpod
class InsertData extends _$InsertData {
  /// init
  @override
  FutureOr<int?> build() => null;

  /// insert goods
  Future<bool> insertData() async {
    final productCode = ref.read(productCodeProvider);
    final slipCode = ref.read(slipCodeStateProvider);
    final isAllOne = ref.read(fixedInputValueStateProvider);
    final inputNumberState = ref.read(inputNumberStateProvider);
    final numberValue = double.tryParse(inputNumberState);
    if (numberValue == null) return false;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(warehouseInspectionServiceProvider).updateShipsReceiveNum(
              slipNumber: slipCode,
              productCode: productCode,
              receiveNum: numberValue,
              isAllOne: isAllOne,
            );
      },
    );
    if (state.hasError) return false;
    return true;
  }
}

/// async state
/// insert data
@riverpod
class ScanResultState extends _$ScanResultState {
  /// init
  @override
  FutureOr<ScanInfo?> build() {
    ref.showGlobalLoading();
    return null;
  }

  /// insert goods
  Future<bool> scan() async {
    final productCode = ref.read(productCodeProvider);
    final slipCode = ref.read(slipCodeStateProvider);

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () {
        return ref.read(warehouseInspectionServiceProvider).getShipsBySlipNo(
              slipNumber: slipCode,
              productCode: productCode,
            );
      },
    );
    if (state.hasError) return false;
    final scanInfo = state.value;
    if (scanInfo == null) return false;
    if (scanInfo.scanFlag == ScanType.subjectToReceipt) {
      final info = scanInfo.slipInfo;
      if ((info.shipsTypeCode == ShipsType.tls && info.receiveFlag == ReceiveStatus.inspectionCompleted) ||
          (info.shipsTypeCode == ShipsType.member && info.receiveFlag == ReceiveStatus.notInspected)) {
        ref.read(slipCodeStateProvider.notifier).updateValue(info.slipNumber);
        return true;
      }
    }
    return false;
  }
}

/// jan input
@riverpod
class InputJanState extends _$InputJanState {
  @override
  FutureOr<Goods?> build() => null;

  /// insert current data and get new goods info
  Future<bool> dealWithInsertDataAndGetNewGoodsInfo({
    bool isCheck = true,
  }) async {
    final productCode = ref.read(productCodeProvider);
    final service = ref.read(warehouseInspectionServiceProvider);

    if (productCode.isEmpty) return false;

    /// oldProductが空の場合、製品情報の初回読み込みを表します。
    /// もしproductがnullの場合、getProductInfoインターフェースを直接使用します。
    final oldProduct = state.value;
    if (oldProduct == null) {
      state = const AsyncLoading();
      state = await AsyncValue.guard(
        () async {
          if (isCheck) {
            final isSuccess = await ref.read(scanResultStateProvider.notifier).scan();
            if (!isSuccess) return null;
          }
          return service.getGoods(
            productCode: productCode,
            slipNumber: ref.read(slipCodeStateProvider),
          );
        },
      );
      if (state.hasError) return false;
      return true;
    }
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => insertDataAndFetchNextProductInfo(oldProduct: oldProduct),
    );
    if (state.hasError) return false;
    return true;
  }

  /// もしnextProductCodeに空が渡された場合、nullを返します。
  Future<Goods?> insertDataAndFetchNextProductInfo({
    required Goods oldProduct,
  }) async {
    final service = ref.read(warehouseInspectionServiceProvider);
    final newProductCode = ref.read(productCodeProvider);
    final isAllOne = ref.read(fixedInputValueStateProvider);

    final inputNumberState = ref.read(inputNumberStateProvider);
    final numberValue = double.tryParse(inputNumberState);
    if (numberValue == null) return null;

    /// upload previous goods
    await service.updateShipsReceiveNum(
      slipNumber: oldProduct.slipCode,
      productCode: oldProduct.productCode,
      receiveNum: numberValue,
      isAllOne: isAllOne,
    );

    /// clean history input data
    ref.read(inputNumberStateProvider.notifier).clear();

    if (newProductCode.isEmpty) return null;

    final isSuccess = await ref.read(scanResultStateProvider.notifier).scan();
    if (!isSuccess) return null;

    return service.getGoods(
      productCode: newProductCode,
      slipNumber: ref.read(slipCodeStateProvider),
    );
  }

  /// insert data
  Future<bool> insertCurrentGoods() async {
    state = const AsyncLoading();

    final oldProduct = state.value;
    if (oldProduct == null) return true;

    final result = await AsyncValue.guard(
      () => insertDataAndFetchNextProductInfo(oldProduct: oldProduct),
    );
    state = result;
    if (result.hasError) {
      return false;
    }

    return true;
  }

  /// clear
  void clear() {
    state = const AsyncData(null);
  }
}
