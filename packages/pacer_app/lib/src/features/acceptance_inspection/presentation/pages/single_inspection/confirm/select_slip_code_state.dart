import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'select_slip_code_state.g.dart';

/// selected slip code
@riverpod
class SelectSlipCodeState extends _$SelectSlipCodeState {
  /// init
  @override
  String build() => '';

  /// update selected slip
  void update(String slipCode) {
    log('select slip code: $slipCode');
    state = slipCode;
  }

  /// clean selected slip
  void clear() {
    state = '';
  }
}
