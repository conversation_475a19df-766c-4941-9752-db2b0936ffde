import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../../domain/single/correct_details.dart';
import '../../../config/strings.dart';
import '../confirm/select_slip_code_state.dart';
import 'single_slip_confirm_controller.dart';
import 'widgets/single_slip_confirm_bottom_tab_bar.dart';
import 'widgets/single_slip_confirm_cell.dart';

/// 伝票検品　確認登録②（誤算あり）
class SingleSlipConfirmPage extends HookConsumerWidget {
  /// init
  const SingleSlipConfirmPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goodsArray = ref.watch(confirmGoodsListProvider);
    final slipCode = ref.read(selectSlipCodeStateProvider);

    ref.listen(confirmGoodsListProvider, (previous, next) async {
      final array = next.asData?.value;
      if (array != null && array.isEmpty) {
        await showAlertDialog(context: context, title: kNoData);
        if (context.mounted) {
          context.pop();
        }
        return;
      }
    });

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text('$kConfirmRegister②'),
      ),
      body: switch (goodsArray) {
        AsyncData(:final value) => Padding(
            padding: const EdgeInsets.symmetric(
              vertical: _lineHeight,
              horizontal: _edge,
            ),
            child: Column(
              children: [
                _Header(
                  slipCode: slipCode,
                ),
                Expanded(
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      final slipInfo = value[index];

                      return SingleSlipConfirmCell(
                        slipInfo: slipInfo,
                        isSelected: false,
                        onPressed: () {},
                        onChanged: ({bool? isCheck}) => onCheckChanged(
                          ref: ref,
                          isCheck: isCheck,
                          slipInfo: slipInfo,
                        ),
                      );
                    },
                    separatorBuilder: (context, _) => const SizedBox(
                      height: 6,
                    ),
                    itemCount: value.length,
                  ),
                ),
              ],
            ),
          ),
        AsyncError(:final error) => Text(
            switch (error) {
              UnknownException() => error.message,

              /// 取得に失敗しました
              _ => kRequestError,
            },
          ),
        AsyncLoading() => const Center(
            child: CircularProgressIndicator(),
          ),
      },
      bottomNavigationBar: const SingleSlipConfirmBottomTabBar(),
    );
  }

  /// チェックボックスの変更イベント
  Future<void> onCheckChanged({
    required WidgetRef ref,
    required bool? isCheck,
    required CorrectDetails slipInfo,
  }) async {
    if (isCheck == null) {
      return;
    }
    final tempValue = slipInfo.asnNum > 0 ? slipInfo.asnNum : slipInfo.orderNum;
    final deliverNum = double.tryParse(slipInfo.changedDeliverNum ?? '') ?? 0.0;
    if (tempValue != deliverNum) {
      final isOk = await showAlertDialog(
        context: ref.context,
        title: kReportAnError,
        defaultActionText: kYes,
        cancelActionText: kNo,
      );
      final newGoods = slipInfo.copyWith(
        isError: isOk ?? false,
      );
      ref.read(confirmGoodsListProvider.notifier).replaceObjectAtIndexWithObject(
            slipInfo,
            newGoods,
          );
    }
  }
}

class _Header extends StatelessWidget {
  const _Header({
    required this.slipCode,
  });

  /// 伝票NO
  final String slipCode;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyLarge?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(kLoginNumber, style: texts.titleMedium),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 14,
          ),
          child: Text(
            '$kReceiptsNo：$slipCode',
            style: texts.titleMedium?.copyWith(color: colors.primary),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: colors.surfaceContainerHighest,
            border: Border.fromBorderSide(
              BorderSide(
                color: colors.outlineVariant,
              ),
            ),
          ),
          margin: const EdgeInsets.only(bottom: 12),
          child: IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    kMiscalculation,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      '$kLine/$kNumberOfOrders',
                      textAlign: TextAlign.center,
                      style: textTheme,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  child: Text(
                    kPlanNumber,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
                VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: colors.outlineVariant,
                ),
                Expanded(
                  child: Text(
                    kInspectionNumber,
                    textAlign: TextAlign.center,
                    style: textTheme,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
