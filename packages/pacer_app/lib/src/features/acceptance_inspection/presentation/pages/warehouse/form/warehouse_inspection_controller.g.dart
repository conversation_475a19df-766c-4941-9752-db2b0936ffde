// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse_inspection_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$inputNumberStateHash() => r'f1d04959b15d197012792c90b1ed8f0ba9ce9718';

/// input number
///
/// Copied from [InputNumberState].
@ProviderFor(InputNumberState)
final inputNumberStateProvider = AutoDisposeNotifierProvider<InputNumberState, String>.internal(
  InputNumberState.new,
  name: r'inputNumberStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputNumberStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputNumberState = AutoDisposeNotifier<String>;
String _$fixedInputValueStateHash() => r'd12df63906cff76568e8cde18ce8465985ad3a62';

/// fixed input value
///
/// Copied from [FixedInputValueState].
@ProviderFor(FixedInputValueState)
final fixedInputValueStateProvider = AutoDisposeNotifierProvider<FixedInputValueState, bool>.internal(
  FixedInputValueState.new,
  name: r'fixedInputValueStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$fixedInputValueStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FixedInputValueState = AutoDisposeNotifier<bool>;
String _$productCodeHash() => r'87bd22cafbe4925f21db9e6e167304bfece66736';

/// jan
///
/// Copied from [ProductCode].
@ProviderFor(ProductCode)
final productCodeProvider = AutoDisposeNotifierProvider<ProductCode, String>.internal(
  ProductCode.new,
  name: r'productCodeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$productCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProductCode = AutoDisposeNotifier<String>;
String _$slipCodeStateHash() => r'0cafbc7cbe303138af3198095a28843681721c90';

/// slip code
///
/// Copied from [SlipCodeState].
@ProviderFor(SlipCodeState)
final slipCodeStateProvider = AutoDisposeNotifierProvider<SlipCodeState, String>.internal(
  SlipCodeState.new,
  name: r'slipCodeStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$slipCodeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SlipCodeState = AutoDisposeNotifier<String>;
String _$progressStateHash() => r'd2dfaeb7ce938ccf443c2808228a1cd7841e52a3';

/// progress (1/2)
///
/// Copied from [ProgressState].
@ProviderFor(ProgressState)
final progressStateProvider = AutoDisposeNotifierProvider<ProgressState, String>.internal(
  ProgressState.new,
  name: r'progressStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$progressStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProgressState = AutoDisposeNotifier<String>;
String _$getProgressStateHash() => r'2f4ac30dbda5eb3a241a1bdcad9e85a6f55c0389';

/// get progress
///
/// Copied from [GetProgressState].
@ProviderFor(GetProgressState)
final getProgressStateProvider = AutoDisposeAsyncNotifierProvider<GetProgressState, String?>.internal(
  GetProgressState.new,
  name: r'getProgressStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getProgressStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetProgressState = AutoDisposeAsyncNotifier<String?>;
String _$insertDataHash() => r'f4a387bfcd050673a18b8f0940bd4253a461dcbc';

/// insert data
///
/// Copied from [InsertData].
@ProviderFor(InsertData)
final insertDataProvider = AutoDisposeAsyncNotifierProvider<InsertData, int?>.internal(
  InsertData.new,
  name: r'insertDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$insertDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InsertData = AutoDisposeAsyncNotifier<int?>;
String _$scanResultStateHash() => r'068130a25fbe6372a226a02280ccc52414680cb7';

/// async state
/// insert data
///
/// Copied from [ScanResultState].
@ProviderFor(ScanResultState)
final scanResultStateProvider = AutoDisposeAsyncNotifierProvider<ScanResultState, ScanInfo?>.internal(
  ScanResultState.new,
  name: r'scanResultStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$scanResultStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ScanResultState = AutoDisposeAsyncNotifier<ScanInfo?>;
String _$inputJanStateHash() => r'385fb4e802cce37782f01ed00ad4e7d225788c45';

/// jan input
///
/// Copied from [InputJanState].
@ProviderFor(InputJanState)
final inputJanStateProvider = AutoDisposeAsyncNotifierProvider<InputJanState, Goods?>.internal(
  InputJanState.new,
  name: r'inputJanStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$inputJanStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InputJanState = AutoDisposeAsyncNotifier<Goods?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
