// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'single_confirm_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$confirmSlipListHash() => r'a14d66a2dfdcc78d9f903b7ecceba8e6eb0802e3';

/// slip list
///
/// Copied from [ConfirmSlipList].
@ProviderFor(ConfirmSlipList)
final confirmSlipListProvider = AutoDisposeAsyncNotifierProvider<ConfirmSlipList, List<SlipConfirmInfo>>.internal(
  ConfirmSlipList.new,
  name: r'confirmSlipListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmSlipListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmSlipList = AutoDisposeAsyncNotifier<List<SlipConfirmInfo>>;
String _$getProgressStateHash() => r'b2128026a8f096110db5dd23da9d486f786e4272';

/// get progress
///
/// Copied from [GetProgressState].
@ProviderFor(GetProgressState)
final getProgressStateProvider = AutoDisposeAsyncNotifierProvider<GetProgressState, OrderProgress?>.internal(
  GetProgressState.new,
  name: r'getProgressStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getProgressStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetProgressState = AutoDisposeAsyncNotifier<OrderProgress?>;
String _$selectSlipStateHash() => r'bdb9390025ec392c1efbc3233a0bd172c099d3cc';

/// 选中传票
///
/// Copied from [SelectSlipState].
@ProviderFor(SelectSlipState)
final selectSlipStateProvider = AutoDisposeNotifierProvider<SelectSlipState, SlipConfirmInfo?>.internal(
  SelectSlipState.new,
  name: r'selectSlipStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectSlipStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectSlipState = AutoDisposeNotifier<SlipConfirmInfo?>;
String _$selectSlipListHash() => r'e7a7bb057dae9f8e90805ad89a0932366d62b2f2';

/// 選択された传票リスト
///
/// Copied from [SelectSlipList].
@ProviderFor(SelectSlipList)
final selectSlipListProvider = AutoDisposeNotifierProvider<SelectSlipList, List<SlipConfirmInfo>>.internal(
  SelectSlipList.new,
  name: r'selectSlipListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectSlipListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectSlipList = AutoDisposeNotifier<List<SlipConfirmInfo>>;
String _$confirmSelectSlipStateHash() => r'39258eb2a92fb678523ce2cf5e040a8e85e9f0de';

/// 確定
///
/// Copied from [ConfirmSelectSlipState].
@ProviderFor(ConfirmSelectSlipState)
final confirmSelectSlipStateProvider = AutoDisposeAsyncNotifierProvider<ConfirmSelectSlipState, bool>.internal(
  ConfirmSelectSlipState.new,
  name: r'confirmSelectSlipStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmSelectSlipStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmSelectSlipState = AutoDisposeAsyncNotifier<bool>;
String _$isShowTipStateHash() => r'ce9d2c5abe6332126d7a6adcbe62e96302d4e569';

/// is show tip
///
/// Copied from [IsShowTipState].
@ProviderFor(IsShowTipState)
final isShowTipStateProvider = AutoDisposeNotifierProvider<IsShowTipState, bool>.internal(
  IsShowTipState.new,
  name: r'isShowTipStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$isShowTipStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$IsShowTipState = AutoDisposeNotifier<bool>;
String _$setSuspendStatusStateHash() => r'9de57108f47c53578ba5e28f34e16f4947193cb4';

/// 保留フラグ設定
///
/// Copied from [SetSuspendStatusState].
@ProviderFor(SetSuspendStatusState)
final setSuspendStatusStateProvider =
    AutoDisposeAsyncNotifierProvider<SetSuspendStatusState, SlipConfirmInfo?>.internal(
  SetSuspendStatusState.new,
  name: r'setSuspendStatusStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$setSuspendStatusStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SetSuspendStatusState = AutoDisposeAsyncNotifier<SlipConfirmInfo?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
