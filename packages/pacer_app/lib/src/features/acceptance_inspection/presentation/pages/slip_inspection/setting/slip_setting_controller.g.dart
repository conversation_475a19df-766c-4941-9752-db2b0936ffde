// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slip_setting_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$storeAndSupplierAuthorityStateHash() => r'378439fd53fc19f5da5b909ab1cc76ae49143f03';

/// init data
///
/// Copied from [StoreAndSupplierAuthorityState].
@ProviderFor(StoreAndSupplierAuthorityState)
final storeAndSupplierAuthorityStateProvider =
    AutoDisposeNotifierProvider<StoreAndSupplierAuthorityState, AsyncValue<StoreAndSupplierAuthority?>>.internal(
  StoreAndSupplierAuthorityState.new,
  name: r'storeAndSupplierAuthorityStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$storeAndSupplierAuthorityStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$StoreAndSupplierAuthorityState = AutoDisposeNotifier<AsyncValue<StoreAndSupplierAuthority?>>;
String _$systemDateStateHash() => r'3b7116704b9b2b23330d9c46f0b5f398c46b8b19';

/// get system date
///
/// Copied from [SystemDateState].
@ProviderFor(SystemDateState)
final systemDateStateProvider = AutoDisposeNotifierProvider<SystemDateState, AsyncValue<DateTime?>>.internal(
  SystemDateState.new,
  name: r'systemDateStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$systemDateStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SystemDateState = AutoDisposeNotifier<AsyncValue<DateTime?>>;
String _$unCheckVendorListStateHash() => r'0db47d7b377f3121d572d61c787d492d65d159e1';

/// 伝票検品設定画面の仕入先一覧（ベンダー一覧）
///
/// Copied from [UnCheckVendorListState].
@ProviderFor(UnCheckVendorListState)
final unCheckVendorListStateProvider =
    AutoDisposeNotifierProvider<UnCheckVendorListState, AsyncValue<List<Vendor>>>.internal(
  UnCheckVendorListState.new,
  name: r'unCheckVendorListStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$unCheckVendorListStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UnCheckVendorListState = AutoDisposeNotifier<AsyncValue<List<Vendor>>>;
String _$getVendorByJanStateHash() => r'fc9db963b0096247602c8df4bb329cc4730b134f';

/// 商品コードによりベンダー名取得
///
/// Copied from [GetVendorByJanState].
@ProviderFor(GetVendorByJanState)
final getVendorByJanStateProvider = AutoDisposeNotifierProvider<GetVendorByJanState, AsyncValue<VendorInfo?>>.internal(
  GetVendorByJanState.new,
  name: r'getVendorByJanStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$getVendorByJanStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GetVendorByJanState = AutoDisposeNotifier<AsyncValue<VendorInfo?>>;
String _$dateTimeStateHash() => r'aa8e464f0b0698e223634f8813a391276481f3ee';

/// time
///
/// Copied from [DateTimeState].
@ProviderFor(DateTimeState)
final dateTimeStateProvider = AutoDisposeNotifierProvider<DateTimeState, DateTimeInfo>.internal(
  DateTimeState.new,
  name: r'dateTimeStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$dateTimeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DateTimeState = AutoDisposeNotifier<DateTimeInfo>;
String _$confirmDataStateHash() => r'bf4846e268655ed43c85ea56ac7d3a8800fbefca';

/// confirm state
///
/// Copied from [ConfirmDataState].
@ProviderFor(ConfirmDataState)
final confirmDataStateProvider = AutoDisposeNotifierProvider<ConfirmDataState, AsyncValue<InputCheckInfo?>>.internal(
  ConfirmDataState.new,
  name: r'confirmDataStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$confirmDataStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfirmDataState = AutoDisposeNotifier<AsyncValue<InputCheckInfo?>>;
String _$isInsufficientInspectionStateHash() => r'1b1b6b2ae7e054de4541c445f89a6d91a583bdf3';

///
///
/// Copied from [IsInsufficientInspectionState].
@ProviderFor(IsInsufficientInspectionState)
final isInsufficientInspectionStateProvider = AutoDisposeNotifierProvider<IsInsufficientInspectionState, bool>.internal(
  IsInsufficientInspectionState.new,
  name: r'isInsufficientInspectionStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$isInsufficientInspectionStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$IsInsufficientInspectionState = AutoDisposeNotifier<bool>;
String _$vendorNameControllerHash() => r'be1b11bccbcdad49081f4c81fd7bc507ea47fcea';

/// ベンダー名コントローラー
///
/// Copied from [VendorNameController].
@ProviderFor(VendorNameController)
final vendorNameControllerProvider = AutoDisposeNotifierProvider<VendorNameController, AsyncValue<String>>.internal(
  VendorNameController.new,
  name: r'vendorNameControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$vendorNameControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VendorNameController = AutoDisposeNotifier<AsyncValue<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
