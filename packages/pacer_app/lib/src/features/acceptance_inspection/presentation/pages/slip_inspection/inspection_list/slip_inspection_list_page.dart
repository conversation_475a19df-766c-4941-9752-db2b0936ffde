import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../../domain/slip_inspection/correct_details.dart';
import '../../../config/strings.dart';
import 'slip_inspection_list_controller.dart';
import 'widgets/slip_inspection_bottom_tab_bar.dart';
import 'widgets/slip_inspection_section.dart';

/// 伝票検品 画面
class SlipInspectionListPage extends HookConsumerWidget {
  /// init
  const SlipInspectionListPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final list = ref.watch(slipInfoListStateProvider);

    ref.listen(slipInfoListStateProvider, (previous, next) {});

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kSlipInspectionTitle),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: _lineHeight,
          horizontal: _edge,
        ),
        child: Column(
          children: [
            _Header(),
            Expanded(
              child: switch (list) {
                AsyncData(:final value) => ListView.separated(
                    itemBuilder: (ctx, index) {
                      return SlipInspectionSection(
                        sectionInfo: value[index],
                        onChanged: ({
                          bool? isCheck,
                          CorrectDetails? slipInfo,
                        }) =>
                            onCheckChanged(
                          ref: ref,
                          isCheck: isCheck,
                          slipInfo: slipInfo,
                        ),
                      );
                    },
                    separatorBuilder: (context, _) => const SizedBox(
                      height: 6,
                    ),
                    itemCount: value.length,
                  ),
                AsyncLoading() => const Center(
                    child: CircularProgressIndicator(),
                  ),
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,
                      _ => kRequestError,
                    },
                  ),
              },
            ),
          ],
        ),
      ),
      bottomNavigationBar: const SlipInspectionBottomTabBar(),
    );
  }

  /// チェックボックスの変更イベント
  Future<void> onCheckChanged({
    required WidgetRef ref,
    required bool? isCheck,
    required CorrectDetails? slipInfo,
  }) async {
    if (isCheck == null || slipInfo == null) {
      return;
    }
    final newGoods = slipInfo.copyWith(
      isSelect: isCheck,
    );
    ref.read(slipInfoListStateProvider.notifier).replaceObjectAtIndexWithObject(
          slipInfo,
          newGoods,
        );
  }
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    final textTheme = texts.bodyLarge?.copyWith(
      color: colors.onSurface,
      height: 2,
    );

    return Container(
      decoration: BoxDecoration(
        color: colors.surfaceContainerHighest,
        border: Border.fromBorderSide(BorderSide(color: colors.outlineVariant)),
      ),
      margin: const EdgeInsets.only(bottom: 12),
      child: IntrinsicHeight(
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Text(
                  '$kLine/$kNumberOfOrders',
                  textAlign: TextAlign.center,
                  style: textTheme,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            VerticalDivider(
              width: 1,
              thickness: 1,
              color: colors.outlineVariant,
            ),
            Expanded(
              flex: 3,
              child: Text(
                kPlanNumber,
                textAlign: TextAlign.center,
                style: textTheme,
              ),
            ),
            VerticalDivider(
              width: 1,
              thickness: 1,
              color: colors.outlineVariant,
            ),
            Expanded(
              flex: 2,
              child: Text(
                kInspectionNumber,
                textAlign: TextAlign.center,
                style: textTheme,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
