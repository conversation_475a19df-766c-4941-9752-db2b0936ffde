import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../exceptions/app_exception.dart';
import '../../../config/strings.dart';
import 'addition_scan_controller.dart';
import 'widgets/addition_scan_bottom_tab_bar.dart';
import 'widgets/addition_scan_table.dart';

/// 単品検品ー追加スキャン
class AdditionScanPage extends HookConsumerWidget {
  /// init
  const AdditionScanPage({super.key});

  static const double _lineHeight = 12;
  static const double _edge = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;
    final info = ref.watch(getSlipInfoStateProvider);

    ref
      ..listen(getSlipInfoStateProvider, (previous, next) {
        final info = next.asData?.value;
        if (info == null) return;
        ref.read(goodsInputNumberNotifierProvider.notifier)
          ..updateBoxNumber(info.packNum.toString())
          ..updatePackageNumber(info.caseNum.toString());
      })
      ..listen(goodsInputNumberNotifierProvider, (previous, next) {
        log('input number: $next');
      });

    return Scaffold(
      appBar: PacerAppBar(
        context: context,
        title: const Text(kAdditionalScanTitle),
      ),
      body: switch (info) {
        AsyncData(:final value) => value == null
            ? const Text(kNoData)
            : ListView(
                padding: const EdgeInsets.symmetric(horizontal: _edge),
                children: [
                  const SizedBox(
                    height: _lineHeight,
                  ),
                  _Header(
                    id: value.id,
                    slipCodeAndLine: '${value.slipNo}-${value.lineNo}',
                  ),
                  AdditionScanTable(
                    info: value,
                  ),
                  const SizedBox(
                    height: _lineHeight,
                  ),
                  Text(
                    '※ $kNumOfInspectedItem '
                    '+ $kNumOfAdditionalInspections '
                    '= $kTotalNumOfInspection',
                    style: texts.titleMedium?.copyWith(
                      color: colors.primary,
                    ),
                  ),
                ],
              ),
        AsyncError(:final error) => Text(
            switch (error) {
              UnknownException() => error.message,

              /// 取得に失敗しました
              _ => kRequestError,
            },
          ),
        AsyncLoading() => const Center(
            child: CircularProgressIndicator(),
          ),
      },
      bottomNavigationBar: const AdditionScanBottomTabBar(),
    );
  }
}

class _Header extends StatelessWidget {
  const _Header({
    required this.id,
    required this.slipCodeAndLine,
  });

  /// ID
  final String id;

  /// 伝票-行
  final String slipCodeAndLine;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(kLoginNumber, style: texts.bodyLarge),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 14,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'ID:$id',
                style: texts.bodyLarge?.copyWith(color: colors.primary),
              ),
              Text(
                '$kReceipts-$kLine：$slipCodeAndLine',
                style: texts.bodyLarge?.copyWith(color: colors.primary),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
