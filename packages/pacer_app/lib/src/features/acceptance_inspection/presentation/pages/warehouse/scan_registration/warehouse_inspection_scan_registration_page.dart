import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../common_widgets/jan_code_text_form_field.dart';
import '../../../../../../common_widgets/pacer_app_bar.dart';
import '../../../../../../routing/app_router.dart';
import '../../../../../../utils/async_value_ui.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../../device/presentation/scan_window.dart';
import '../../../../domain/warehouse/slip_code_info.dart';
import '../../../../domain/warehouse/slip_code_judge.dart';
import '../../../config/strings.dart';
import '../../../routing/acceptance_inspection_route.dart';
import '../../../widgets/scan_bottom_tab_bar.dart';
import '../../../widgets/show_menu.dart';
import 'warehouse_inspection_scan_registration_controller.dart';

/// call back
typedef ValueCallback = void Function(SlipCodeInfo selectInfo);

/// スキャン登録
class WarehouseInspectionScanRegistrationPage extends HookConsumerWidget {
  /// init
  const WarehouseInspectionScanRegistrationPage({super.key});
  static const double _lineHeight = 12;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final texts = Theme.of(context).textTheme;
    final codeTextController = useTextEditingController();

    /// janCodeの入力のFocusNode
    final janFocusNode = useFocusNode();

    ref
      ..listen(scanRegistrationCodeProvider, (previous, next) {
        codeTextController.text = next;
        if (next.isNotEmpty) {
          ref.read(checkCodeTypeGetInfoProvider.notifier).getSlipAndGoodsInfo();
        }
      })
      ..listen(checkCodeTypeGetInfoProvider, (previous, next) {
        if (next.hasError) {
          next.showSnackBarOnError(context);
          return;
        }
      })
      ..listen(checkSlipAndGoodsInfoProvider, (previous, next) {
        if (next.hasError) {
          next.showSnackBarOnError(context);
          return;
        }
        final list = next.asData?.value;
        if (list == null) return;
        _checkSlipAndGoodsInfo(ref, codeTextController, list);
      })
      ..listen(checkSlipNoProvider, (previous, next) {
        if (next.hasError) {
          next.showSnackBarOnError(context);
          return;
        }
        final slipCodeJudge = next.asData?.value;
        if (slipCodeJudge == null) return;
        _checkSlipNoJudge(ref, slipCodeJudge);
      })
      ..listen(selectSlipAndGoodsProvider, (previous, next) {})
      ..listen(isShowAlertStateProvider, (previous, next) {});

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop || ref.watch(globalLoadingServiceProvider)) return;
        context.pop();
      },
      child: Scaffold(
        appBar: PacerAppBar(
          title: const Text(kScanRegistration),
          context: context,
        ),
        body: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          children: [
            const SizedBox(
              height: _lineHeight,
            ),
            JanCodeTextFormField(
              kCode,
              focusNode: janFocusNode,
              controller: codeTextController,
              onFieldSubmitted: (value) => _onFieldSubmitted(ref, value),
              routeFullPath: '/inspection/warehouse_scan_registration',
            ),
            const SizedBox(
              height: _lineHeight,
            ),
            Text(
              kScanTip,
              style: texts.titleMedium,
            ),
            const SizedBox(
              height: _lineHeight,
            ),
          ],
        ),
        bottomNavigationBar: ScanBottomTabBar(
          leading: TextButton(
            onPressed: () => context.pop(),
            child: Text(
              kEnd,
              style: texts.titleMedium,
            ),
          ),
        ),
        resizeToAvoidBottomInset: false,
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: ScanFloatingIconButton(
          onScan: (String value) => _onFieldSubmitted(ref, value),
        ),
      ),
    );
  }

  void _pushWarehouseInspectionPage(
    WidgetRef ref,
    String slipCode, {
    String goodsCode = '',
  }) {
    ref.read(selectSlipAndGoodsProvider.notifier).update(
          slipCode,
          goodsCode: goodsCode,
        );
    _cleanInputValue(ref);
    const WarehouseInspectionRoute().go(ref.context);
  }

  Future<void> _checkSlipAndGoodsInfo(
    WidgetRef ref,
    TextEditingController controller,
    List<SlipCodeInfo> list,
  ) async {
    if (list.isEmpty) {
      await _showAlert(ref, kNoStockInformation);
      return;
    }

    final goodsCode = ref.read(scanRegistrationCodeProvider);
    if (list.length == 1) {
      final slipCodeInfo = list.first;
      final checkShips = ref.read(checkShipsStateProvider.notifier).checkShips(
            slipCodeInfo.shipsTypeCode,
            slipCodeInfo.receiveFlag,
          );
      if (checkShips != kOk) {
        await _showAlert(ref, checkShips);
        return;
      }
      _pushWarehouseInspectionPage(
        ref,
        slipCodeInfo.slipNumber,
        goodsCode: goodsCode,
      );
      return;
    }

    final passArray = <SlipCodeInfo>[];

    /// no pass check
    var hasCheckedIndex = 0;
    var unableToCheckIndex = 0;
    for (var i = 0; i < list.length; i++) {
      final slipCodeInfo = list[i];
      final checkShips = ref.read(checkShipsStateProvider.notifier).checkShips(
            slipCodeInfo.shipsTypeCode,
            slipCodeInfo.receiveFlag,
          );
      if (checkShips == kOk) {
        passArray.add(slipCodeInfo);
      } else if (checkShips == kHasChecked) {
        hasCheckedIndex += 1;
      } else if (checkShips == kUnableToCheck) {
        unableToCheckIndex += 1;
      }
    }
    if (passArray.length == 1) {
      _pushWarehouseInspectionPage(
        ref,
        passArray.first.slipNumber,
        goodsCode: goodsCode,
      );
    } else if (passArray.length > 1) {
      ref.read(isShowAlertStateProvider.notifier).changeStatus(isShow: true);
      await showCustomMenu(ref, passArray, (selectInfo) {
        _pushWarehouseInspectionPage(
          ref,
          selectInfo.slipNumber,
          goodsCode: goodsCode,
        );
      });
      ref.read(isShowAlertStateProvider.notifier).changeStatus();
    } else if (hasCheckedIndex > 0) {
      await _showAlert(ref, kHasChecked);
    } else if (unableToCheckIndex > 0) {
      await _showAlert(ref, kUnableToCheck);
    } else {
      await _showAlert(ref, kNoStockInformation);
    }
  }

  void _onFieldSubmitted(WidgetRef ref, String inputValue) {
    if (ref.read(isShowAlertStateProvider)) {
      ref.context.pop();
    }
    ref.read(scanRegistrationCodeProvider.notifier).clear();
    ref.read(scanRegistrationCodeProvider.notifier).update(inputValue);
  }

  void _checkSlipNoJudge(WidgetRef ref, SlipCodeJudge slipInfo) {
    final checkShips = ref.read(checkShipsStateProvider.notifier).checkShips(
          slipInfo.shipsType,
          slipInfo.receiveStatus,
        );
    if (checkShips != kOk) {
      _showAlert(ref, checkShips);
      return;
    }
    _pushWarehouseInspectionPage(ref, ref.read(scanRegistrationCodeProvider));
  }

  Future<void> _showAlert(WidgetRef ref, String tipString) async {
    _cleanInputValue(ref);
    ref.read(isShowAlertStateProvider.notifier).changeStatus(isShow: true);
    await showAlertDialog(context: ref.context, title: tipString);
    ref.read(isShowAlertStateProvider.notifier).changeStatus();
  }

  void _cleanInputValue(WidgetRef ref) {
    ref.read(scanRegistrationCodeProvider.notifier).clear();
  }
}
