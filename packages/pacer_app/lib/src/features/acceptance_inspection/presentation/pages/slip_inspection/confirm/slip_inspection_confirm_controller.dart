import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/single_inspection_service.dart';
import '../../../../application/slip_inspection_service.dart';
import '../../../../domain/slip_inspection/slip_confirm_info.dart';
import '../../../../domain/slip_inspection/support_info.dart';
import '../setting/slip_setting_controller.dart';

part 'slip_inspection_confirm_controller.g.dart';

/// slip list
@riverpod
class ConfirmSlipList extends _$ConfirmSlipList {
  /// init
  @override
  FutureOr<List<SlipConfirmInfo>> build() async {
    final isInspection = ref.read(isInsufficientInspectionStateProvider);
    return isInspection
        ? ref.read(slipInspectionServiceProvider).getSlipInfoNewList()
        : ref.read(slipInspectionServiceProvider).getSlipInfoList();
  }

  ///
  void changeSelectState(SlipConfirmInfo info) {
    final newInfo = info.copyWith();
    final array = state.value;
    if (array == null) return;
    final index = array.indexOf(info);
    array
      ..removeAt(index)
      ..insert(index, newInfo);
    state = AsyncData(array);
  }

  /// upload
  void reload() {
    ref.read(selectSlipListProvider.notifier).clear();
    ref.invalidateSelf();
  }
}

/// 选中传票
@riverpod
class SelectSlipState extends _$SelectSlipState {
  /// init
  @override
  SlipConfirmInfo? build() {
    final goodsArray = ref.watch(confirmSlipListProvider).value ?? [];

    return goodsArray.isEmpty ? null : goodsArray.first;
  }

  /// update selected goods
  Future<void> update(SlipConfirmInfo? slipInfo) async {
    log('scrap reason select: $slipInfo');
    state = slipInfo;
  }

  /// clean selected goods
  void clear() {
    state = null;
  }
}

/// 選択された传票リスト
@riverpod
class SelectSlipList extends _$SelectSlipList {
  @override
  List<SlipConfirmInfo> build() => [];

  /// 追加
  void add(SlipConfirmInfo slipInfo) {
    state = [...state, slipInfo];
  }

  /// 追加 all
  void addAll(List<SlipConfirmInfo> slips) {
    state = [...slips];
  }

  /// 削除
  void remove(SlipConfirmInfo slipInfo) {
    state = [...state]..remove(slipInfo);
  }

  /// クリア
  void clear() {
    state = [];
  }
}

/// 確定
@riverpod
class ConfirmSelectSlipState extends _$ConfirmSelectSlipState {
  /// init
  @override
  FutureOr<bool> build() {
    return true;
  }

  /// confirm all goods
  FutureOr<void> confirmSlips() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        final isInspection = ref.read(isInsufficientInspectionStateProvider);
        return isInspection
            ? ref.read(slipInspectionServiceProvider).putBreadVendorPOData()
            : ref.read(slipInspectionServiceProvider).putPOData();
      },
    );
  }
}

/// is show tip
@riverpod
class IsShowTipState extends _$IsShowTipState {
  /// init
  @override
  bool build() => false;

  /// change value
  void changeStatus({bool isShow = false}) {
    log('change status: $isShow');
    state = isShow;
  }

  /// clear
  void clear() {
    state = false;
  }
}

/// 保留フラグ設定
@riverpod
class SetSuspendStatusState extends _$SetSuspendStatusState {
  /// init
  @override
  FutureOr<SlipConfirmInfo?> build() => null;

  /// change status
  FutureOr<void> setSuspendStatus(
    SlipConfirmInfo info,
  ) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        final isSuccess = await ref.read(singleInspectionServiceProvider).setSuspendFlg(
              slipNo: info.slipNo,
              isSuspended: !info.isSuspended,
            );
        return isSuccess ? info : null;
      },
    );
  }
}

/// 伝票検品の一時テーブルのベンダー一覧取得
@riverpod
class GetSupportListState extends _$GetSupportListState {
  /// init
  @override
  FutureOr<List<SupportInfo>?> build() => null;

  /// confirm all goods
  FutureOr<void> getData() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(slipInspectionServiceProvider).getSupportList();
      },
    );
  }
}
