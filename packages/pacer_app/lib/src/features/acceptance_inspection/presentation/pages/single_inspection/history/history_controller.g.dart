// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$historyListHash() => r'9fb13edf20ccb20d7287c3eb66f35e1e3899b1fa';

/// history list
///
/// Copied from [HistoryList].
@ProviderFor(HistoryList)
final historyListProvider = AutoDisposeAsyncNotifierProvider<HistoryList, List<HistoryInfo>>.internal(
  HistoryList.new,
  name: r'historyListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$historyListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HistoryList = AutoDisposeAsyncNotifier<List<HistoryInfo>>;
String _$listSortStatusHash() => r'e114eee8bb58f011933192b71e7e2db59a017af3';

/// sort status
///
/// Copied from [ListSortStatus].
@ProviderFor(ListSortStatus)
final listSortStatusProvider = AutoDisposeNotifierProvider<ListSortStatus, bool>.internal(
  ListSortStatus.new,
  name: r'listSortStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$listSortStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ListSortStatus = AutoDisposeNotifier<bool>;
String _$selectGoodsStateHash() => r'70ff384fe2787f60e6e80632621763bfca2c926e';

/// 选中商品
///
/// Copied from [SelectGoodsState].
@ProviderFor(SelectGoodsState)
final selectGoodsStateProvider = AutoDisposeNotifierProvider<SelectGoodsState, HistoryInfo?>.internal(
  SelectGoodsState.new,
  name: r'selectGoodsStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$selectGoodsStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectGoodsState = AutoDisposeNotifier<HistoryInfo?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
