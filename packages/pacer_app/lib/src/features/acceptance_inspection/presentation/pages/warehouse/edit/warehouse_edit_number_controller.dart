import 'dart:developer';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/warehouse_inspection_service.dart';
import '../../../../domain/warehouse/login_goods.dart';
import '../../../../domain/warehouse/update_receive_number_param.dart';
import '../confirm/warehouse_confirm_controller.dart';

part 'warehouse_edit_number_controller.g.dart';

/// goods list
@riverpod
class ConfirmGoodsList extends _$ConfirmGoodsList {
  @override
  FutureOr<List<LoginGoods>> build() async {
    ref.showGlobalLoading();

    final slipCode = ref.watch(selectSlipStateProvider)?.slipNumber;
    if (slipCode == null) return [];
    return ref.watch(warehouseInspectionServiceProvider).getConfirmLoginProductList(slipNumber: slipCode);
  }

  /// replace oldGoods with newGoods
  void replaceObjectAtIndexWithObject(
    LoginGoods oldGoods,
    LoginGoods newGoods,
  ) {
    final array = state.value;
    if (array == null) return;
    final index = array.indexOf(oldGoods);
    array
      ..removeAt(index)
      ..insert(index, newGoods);
    state = AsyncData(array);
  }

  /// upload
  void reload() {
    state = const AsyncValue.loading();

    ref.invalidateSelf();
  }
}

/// 选中商品
@riverpod
class SelectGoodsState extends _$SelectGoodsState {
  /// init
  @override
  LoginGoods? build() {
    final goodsArray = ref.watch(confirmGoodsListProvider).value ?? [];

    return goodsArray.isEmpty ? null : goodsArray.first;
  }

  /// update selected goods
  Future<void> update(LoginGoods? goods) async {
    log('scrap reason select: $goods');
    state = goods;
  }

  /// clean selected goods
  void clear() {
    state = null;
  }
}

/// 確定
@riverpod
class ConfirmSelectGoodsState extends _$ConfirmSelectGoodsState {
  /// init
  @override
  FutureOr<String> build() {
    ref.showGlobalLoading();
    return '';
  }

  /// confirm all goods
  FutureOr<void> confirmGoods() async {
    final slipCode = ref.watch(selectSlipStateProvider)?.slipNumber;
    if (slipCode == null) return;

    final lists = ref
        .read(confirmGoodsListProvider)
        .value
        ?.map(
          (e) => UpdateReceiveNumberParam(
            id: e.id,
            receiveNum: double.parse(e.changedReceiveNum ?? e.receiveNum.toString()),
          ),
        )
        .toList();
    if (lists == null) return;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(warehouseInspectionServiceProvider).updateReceiveNumber(
              slipNumber: slipCode,
              receiveNumbers: lists,
            );
      },
    );
  }
}
