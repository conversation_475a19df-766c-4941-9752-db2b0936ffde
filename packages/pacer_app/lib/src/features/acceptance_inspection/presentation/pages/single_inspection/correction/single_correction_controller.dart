import 'dart:developer';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../application/single_inspection_service.dart';
import '../../../../domain/single/scan_data.dart';
import '../history/history_controller.dart';

part 'single_correction_controller.g.dart';

/// slip info
@riverpod
class GetSlipInfoState extends _$GetSlipInfoState {
  @override
  FutureOr<ScanData?> build() async {
    final goods = ref.read(selectGoodsStateProvider);
    if (goods == null) return null;
    return ref.watch(singleInspectionServiceProvider).getScanData(
          slipNo: goods.slipNo,
          lineNo: goods.lineNo,
        );
  }

  /// clear
  void clear() {
    state = const AsyncData(null);
  }
}

/// delete data
@riverpod
class DeleteDataState extends _$DeleteDataState {
  /// build
  @override
  FutureOr<bool?> build() => null;

  /// delete
  Future<void> delete() async {
    final info = ref.read(getSlipInfoStateProvider).value;
    if (info == null) return;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(singleInspectionServiceProvider).deleteData(
            slipNo: info.slipNo,
            lineNo: info.lineNo,
          ),
    );
  }
}

/// 納品数更新
@riverpod
class UpdateDeliveriesNumberState extends _$UpdateDeliveriesNumberState {
  /// init
  @override
  FutureOr<bool?> build() => null;

  /// update delivery number
  Future<bool> updateNumber(double number) async {
    final info = ref.read(getSlipInfoStateProvider).value;
    if (info == null) return false;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(singleInspectionServiceProvider).updateDeliveriesNumber(
              deliveryNum: number,
              slipNo: info.slipNo,
              // packNum: 0,
              // caseNum: 0,
              lineNo: info.lineNo,
            );
      },
    );
    if (state.hasError) return false;
    return true;
  }
}

/// 納品数・誤差登録
@riverpod
class UpdateScanDataFlgState extends _$UpdateScanDataFlgState {
  /// init
  @override
  FutureOr<bool?> build() => null;

  /// update delivery number
  Future<bool> updateScanData(double number, {required bool isError}) async {
    final info = ref.read(getSlipInfoStateProvider).value;
    if (info == null) return false;

    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () async {
        return ref.read(singleInspectionServiceProvider).updateScanDataFlg(
              deliveryNum: number,
              slipNo: info.slipNo,
              lineNo: info.lineNo,
              isError: isError,
              // packNum: info.packNum,
              // caseNum: 0,
            );
      },
    );
    if (state.hasError) return false;
    return true;
  }
}

/// input number
@riverpod
class InputNumberState extends _$InputNumberState {
  /// init
  @override
  String build() => '';

  /// update number
  void updateNumber(String number) {
    log('input number: $number');
    state = number;
  }

  /// clear
  void clear() {
    state = '';
  }
}
