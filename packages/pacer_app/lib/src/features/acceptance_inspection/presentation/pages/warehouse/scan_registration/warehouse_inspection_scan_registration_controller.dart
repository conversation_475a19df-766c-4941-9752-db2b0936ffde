import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../../app/application/global_loading_service.dart';
import '../../../../application/warehouse_inspection_service.dart';
import '../../../../domain/warehouse/enum/code_type.dart';
import '../../../../domain/warehouse/enum/receive_status.dart';
import '../../../../domain/warehouse/enum/ships_type.dart';
import '../../../../domain/warehouse/slip_code_info.dart';
import '../../../../domain/warehouse/slip_code_judge.dart';
import '../../../config/strings.dart';

part 'warehouse_inspection_scan_registration_controller.g.dart';

/// code state
@riverpod
class ScanRegistrationCode extends _$ScanRegistrationCode {
  /// jan valid length
  static final List<int> toProcessJanCodeLengths = [20, 26];

  /// build
  @override
  String build() => '';

  /// update code
  Future<void> update(String value) async {
    log('warehouse inspection scan registration code : $value');
    state = _janHandle(value).replaceFirst(RegExp('^0+'), '');
  }

  /// clear value
  void clear() {
    state = '';
  }

  String _janHandle(String value) {
    if (toProcessJanCodeLengths.contains(value.length)) {
      final result = value.substring(0, 13);
      log('taking the first 13 characters ; $result');

      return result;
    }

    return value;
  }
}

/// selecting SelectSlipAndGoodsInfo state
@riverpod
class SelectSlipAndGoods extends _$SelectSlipAndGoods {
  /// build
  @override
  SelectSlipAndGoodsInfo build() => const SelectSlipAndGoodsInfo();

  /// update code
  void update(String slipCode, {String goodsCode = ''}) {
    log('warehouse inspection slip code : $slipCode');
    state = state.copyWith(
      slipCode: slipCode,
      goodsCode: goodsCode,
    );
  }

  /// clear value
  void clear() {
    state = const SelectSlipAndGoodsInfo();
  }
}

/// check input code, get slip info
@riverpod
class CheckCodeTypeGetInfo extends _$CheckCodeTypeGetInfo {
  /// build
  @override
  FutureOr<bool?> build() {
    ref.showGlobalLoading();
    return null;
  }

  /// get slip info and goods info
  Future<void> getSlipAndGoodsInfo() async {
    state = const AsyncLoading();

    final inputCode = ref.read(scanRegistrationCodeProvider);
    state = await AsyncValue.guard(
      () async {
        final codeType = await ref.read(warehouseInspectionServiceProvider).checkCodeType(inputCode: inputCode);
        if (codeType == CodeType.slipCode) {
          await ref.read(checkSlipNoProvider.notifier).getSlipNoJudge();
        } else {
          await ref.read(checkSlipAndGoodsInfoProvider.notifier).getSlipAndGoodsInfo();
        }
        return true;
      },
    );
  }
}

/// JANより 伝票番号情報を取る
@riverpod
class CheckSlipAndGoodsInfo extends _$CheckSlipAndGoodsInfo {
  /// build
  @override
  FutureOr<List<SlipCodeInfo>?> build() {
    ref.showGlobalLoading();
    return null;
  }

  /// get slip info and goods info
  Future<void> getSlipAndGoodsInfo() async {
    state = const AsyncLoading();

    final inputCode = ref.read(scanRegistrationCodeProvider);
    state = await AsyncValue.guard(
      () => ref.read(warehouseInspectionServiceProvider).getSlipNoByGoodsCode(productCode: inputCode),
    );
  }
}

/// 伝票番号が検品済みかどうかを判断する
@riverpod
class CheckSlipNo extends _$CheckSlipNo {
  /// build
  @override
  FutureOr<SlipCodeJudge?> build() {
    ref.showGlobalLoading();
    return null;
  }

  /// get slip info
  Future<void> getSlipNoJudge() async {
    state = const AsyncLoading();

    final inputCode = ref.read(scanRegistrationCodeProvider);
    state = await AsyncValue.guard(
      () => ref.read(warehouseInspectionServiceProvider).judgeBySlipNo(slipNumber: inputCode),
    );
  }
}

/// check status
@riverpod
class CheckShipsState extends _$CheckShipsState {
  /// build
  @override
  void build() {}

  /// check status
  String checkShips(ShipsType shipsType, ReceiveStatus receiveFlag) {
    var resultStr = '';
    if ((shipsType == ShipsType.tls && receiveFlag == ReceiveStatus.inspectionCompleted) ||
        (shipsType == ShipsType.member && receiveFlag == ReceiveStatus.notInspected)) {
      resultStr = kOk;
    } else if ((shipsType == ShipsType.tls && receiveFlag == ReceiveStatus.confirmInspectionCompleted) ||
        (shipsType == ShipsType.member && receiveFlag == ReceiveStatus.confirmInspectionCompleted)) {
      resultStr = kHasChecked;
    } else if (shipsType == ShipsType.tls && receiveFlag == ReceiveStatus.notInspected) {
      resultStr = kUnableToCheck;
    } else {
      resultStr = kNoStockInformation;
    }

    return resultStr;
  }
}

/// alert status
@riverpod
class IsShowAlertState extends _$IsShowAlertState {
  /// init
  @override
  bool build() => false;

  /// update value
  void changeStatus({bool isShow = false}) {
    log('alert status: $isShow');
    state = isShow;
  }

  /// clear
  void clear() {
    state = false;
  }
}

/// select slip and goods info
class SelectSlipAndGoodsInfo extends Equatable {
  /// init
  const SelectSlipAndGoodsInfo({
    this.slipCode = '',
    this.goodsCode = '',
  });

  /// slip code
  final String slipCode;

  /// goods code
  final String goodsCode;

  /// copy
  SelectSlipAndGoodsInfo copyWith({
    String? slipCode,
    String? goodsCode,
  }) {
    return SelectSlipAndGoodsInfo(
      slipCode: slipCode ?? this.slipCode,
      goodsCode: goodsCode ?? this.goodsCode,
    );
  }

  @override
  List<Object?> get props => [slipCode, goodsCode];
}
