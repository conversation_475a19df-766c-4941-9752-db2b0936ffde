import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../common_widgets/alert_dialogs.dart';
import '../../../../../../../common_widgets/pacer_back_button.dart';
import '../../../../../../../exceptions/app_exception.dart';
import '../../../../../../../routing/app_router.dart';
import '../../../../../domain/single/order_progress.dart';
import '../../../../config/strings.dart';
import '../../../../routing/acceptance_inspection_route.dart';
import '../../../../widgets/bottom_tab_bar.dart';
import '../../../../widgets/out_line_round_text_button.dart';
import '../../form/single_inspection_form_controller.dart';
import '../addition_scan_controller.dart';

/// 追加スキャン tab bar
class AdditionScanBottomTabBar extends ConsumerWidget {
  /// constructor
  const AdditionScanBottomTabBar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref
      ..listen(getOrderProgressStateProvider, (previous, next) {
        log('get scanId:${next.value?.scanId}');
        final info = next.asData?.value;
        if (info == null) return;
        context.pop();
        _checkProgressInfo(ref, info);
      })
      ..listen(updateDeliveryNumberStateProvider, (previous, next) {})
      ..listen(updateScanDataFlgProvider, (previous, next) {});

    return BottomTabBar(
      leading: PacerBackButton(
        onPressed: () => _backClick(ref),
      ),
      actions: [
        OutlineRoundTextButton(
          kApply,
          onPressed: () => _onConfirmClick(context, ref),
        ),
      ],
    );
  }

  void _checkProgressInfo(WidgetRef ref, OrderProgress info) {
    final tempVariate = ref.read(tempVariateStateProvider);
    if (tempVariate.isOverlapSlipFlg) {
      ref.read(tempVariateStateProvider.notifier).updateEntrance(4);

      /// 伝票選択画面を移動する
      const SelectSlipRoute().go(ref.context);
      return;
    }
    ref.read(tempVariateStateProvider.notifier).updateEntrance(1);
    ref.read(tempVariateStateProvider.notifier).updateScanId(info.scanId.toString());

    ref.context.pop();
  }

  void _backClick(WidgetRef ref) {
    _getOrderScanCount(ref);
  }

  void _getOrderScanCount(WidgetRef ref) {
    ref.read(getOrderProgressStateProvider.notifier).getProgress();
    showDialog<bool>(
      barrierDismissible: false,
      context: ref.context,
      builder: (context) {
        final errorTextStyle = TextStyle(
          color: Theme.of(context).colorScheme.error,
        );

        return Consumer(
          builder: (context, ref, _) {
            final getOrderProgressState = ref.watch(getOrderProgressStateProvider);

            return AlertDialog(
              /// データ登録中、少々お待ちください。
              title: const Text(''),
              content: switch (getOrderProgressState) {
                AsyncError(:final error) => Text(
                    switch (error) {
                      UnknownException() => error.message,

                      /// 何らかのエラーが発生しました。
                      _ => kRequestError,
                    },
                    style: errorTextStyle,
                  ),
                AsyncData() || AsyncLoading() => const SizedBox(
                    height: 40,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
              },
              actions: [
                if (getOrderProgressState.hasError)
                  OutlinedButton(
                    child: const Text(kClose),
                    onPressed: () => context.pop(),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _onConfirmClick(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final info = ref.read(getSlipInfoStateProvider).value;
    if (info == null) return;
    final tempVariate = ref.read(tempVariateStateProvider);
    final inputNumState = ref.read(goodsInputNumberNotifierProvider);
    final inputNum = int.tryParse(inputNumState.singleGoodsNumber) ?? 0;
    if (tempVariate.vendorCdFlg == '1') {
      final tempValue = info.asnNum > 0 ? info.asnNum : info.orderNum;
      final errorNum = (info.deliveryNum + inputNum - tempValue).toInt();
      ref.read(tempVariateStateProvider.notifier).updateErrorNum(errorNum);
      if (info.deliveryNum + inputNum > tempValue) {
        if (tempValue == 0) {
          await ref.read(updateScanDataFlgProvider.notifier).updateScanData(
                double.tryParse(inputNumState.sum) ?? 0.0,
                isError: true,
              );
          _getOrderScanCount(ref);
        } else {
          /// 追加スキャン誤差報告
          if (tempVariate.unInspectedNum < 1) {
            final isOk = await showAlertDialog(
              context: context,
              title: kExcessQuantity,
              cancelActionText: kNo,
              defaultActionText: kYes,
            );
            if (isOk ?? false) {
              await ref.read(updateScanDataFlgProvider.notifier).updateScanData(
                    double.tryParse(inputNumState.sum) ?? 0.0,
                    isError: true,
                  );
              _getOrderScanCount(ref);
            }
          } else {
            final isOk = await showAlertDialog(
              context: context,
              title: kTipCanReplace.replaceAll('***', errorNum.toString()),
              cancelActionText: kNo,
              defaultActionText: kYes,
            );
            if (isOk ?? false) {
              /// 誤差登録機能上で伝票選択を行なう
              ref.read(tempVariateStateProvider.notifier).updateIsOverlapSlipFlg(isOverlapSlipFlg: true);
              await ref.read(updateScanDataFlgProvider.notifier).updateScanData(
                    tempValue,
                    isError: false,
                  );
            } else {
              if (!context.mounted) return;
              final isOk = await showAlertDialog(
                context: context,
                title: kExcessQuantity,
                cancelActionText: kNo,
                defaultActionText: kYes,
              );
              if (isOk ?? false) {
                ref.read(tempVariateStateProvider.notifier).updateIsOverlapSlipFlg();
                await ref.read(updateScanDataFlgProvider.notifier).updateScanData(
                      info.deliveryNum + inputNum,
                      isError: true,
                    );
              }
            }
          }
        }
      } else {
        final isOk = await showAlertDialog(
          context: context,
          title: kAreYouSureApplyTheAddition,
          cancelActionText: kNo,
          defaultActionText: kYes,
        );
        if (isOk ?? false) {
          await ref.read(updateScanDataFlgProvider.notifier).updateScanData(
                double.tryParse(inputNumState.sum) ?? 0.0,
                isError: false,
              );
          _getOrderScanCount(ref);
        }
      }
    } else {
      if (info.deliveryNum + inputNum > info.orderNum) {
        await showAlertDialog(
          context: context,
          title: kInputValueGreaterThanPlanedQuantity,
        );
      } else {
        final isOk = await showAlertDialog(
          context: context,
          title: kAreYouSureApplyTheAddition,
          cancelActionText: kNo,
          defaultActionText: kYes,
        );
        if (isOk ?? false) {
          await ref
              .read(updateDeliveryNumberStateProvider.notifier)
              .updateNumber(double.tryParse(inputNumState.sum) ?? 0.0);
          _getOrderScanCount(ref);
        }
      }
    }
  }
}
