// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../pages/inspection_menu_page.dart';
import '../pages/single_inspection/confirm/single_confirm_page.dart';
import '../pages/single_inspection/correction/single_correction_page.dart';
import '../pages/single_inspection/edit/single_edit_number_page.dart';
import '../pages/single_inspection/form/single_inspection_form_page.dart';
import '../pages/single_inspection/history/history_page.dart';
import '../pages/single_inspection/select_slip/select_slip_page.dart';
import '../pages/single_inspection/slip_confirm/single_slip_confirm_page.dart';
import '../pages/single_inspection/superaddition_scan/addition_scan_page.dart';
import '../pages/slip_inspection/confirm/slip_inspection_confirm_page.dart';
import '../pages/slip_inspection/inspection_list/slip_inspection_list_page.dart';
import '../pages/slip_inspection/setting/slip_setting_page.dart';
import '../pages/warehouse/confirm/warehouse_confirm_page.dart';
import '../pages/warehouse/edit/edit_page.dart';
import '../pages/warehouse/form/warehouse_inspection_page.dart';
import '../pages/warehouse/scan_registration/warehouse_inspection_scan_registration_page.dart';

/// 検品機能一覧ページ
class InspectionMenuRoute extends GoRouteData {
  const InspectionMenuRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const InspectionMenuPage();
}

/// スキャン登録
class WarehouseInspectionScanRegistrationRoute extends GoRouteData {
  const WarehouseInspectionScanRegistrationRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const WarehouseInspectionScanRegistrationPage();
}

/// 入庫検品ページ
class WarehouseInspectionRoute extends GoRouteData {
  const WarehouseInspectionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const WarehouseInspectionPage();
}

/// 確定登録
class WarehouseConfirmRoute extends GoRouteData {
  const WarehouseConfirmRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const WarehouseConfirmPage();
}

/// 確定登録
class WarehouseEditNumberRoute extends GoRouteData {
  const WarehouseEditNumberRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const WarehouseEditNumberPage();
}

/// 単品検品 スキャン登録
class SingleInspectionFormRoute extends GoRouteData {
  const SingleInspectionFormRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SingleInspectionFormPage();
}

/// 単品検品 確定登録
class SingleConfirmRoute extends GoRouteData {
  const SingleConfirmRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SingleConfirmPage();
}

/// 単品検品 编辑
class SingleEditNumberRoute extends GoRouteData {
  const SingleEditNumberRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SingleEditNumberPage();
}

/// 単品検品ー伝票選択
class SelectSlipRoute extends GoRouteData {
  const SelectSlipRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SelectSlipPage();
}

/// 単品検品ー追加スキャン
class AdditionScanRoute extends GoRouteData {
  const AdditionScanRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const AdditionScanPage();
}

/// 単品検品ー履歴
class HistoryRoute extends GoRouteData {
  const HistoryRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const HistoryPage();
}

/// 単品検品ー訂正登録
class SingleCorrectionRoute extends GoRouteData {
  const SingleCorrectionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SingleCorrectionPage();
}

/// 伝票検品　確認登録②（誤算あり）
class SingleSlipConfirmRoute extends GoRouteData {
  const SingleSlipConfirmRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SingleSlipConfirmPage();
}

/// 伝票検品 設定 画面
class SlipSettingRoute extends GoRouteData {
  const SlipSettingRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SlipSettingPage();
}

/// 伝票検品 画面
class SlipInspectionListRoute extends GoRouteData {
  const SlipInspectionListRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SlipInspectionListPage();
}

/// 伝票検品 確認登録 画面
class SlipInspectionConfirmRoute extends GoRouteData {
  const SlipInspectionConfirmRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SlipInspectionConfirmPage();
}

/// 検品 编辑
class SlipEditNumberRoute extends GoRouteData {
  const SlipEditNumberRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SingleEditNumberPage();
}

/// 伝票検品 確認登録②（誤算あり）
class SlipConfirmRoute extends GoRouteData {
  const SlipConfirmRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SingleSlipConfirmPage();
}
