import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/quantity_28jan_formatter.dart';
import '../../../../utils/remove_zero_formatter.dart';

/// 数値入力時の最大文字数
const int maxCheckWeekNumberLength = 5;

/// number text field
class NumberTextField extends HookConsumerWidget {
  /// constructor
  const NumberTextField({
    super.key,
    required this.inputText,
    required this.onFieldSubmitted,
    this.hintText = '',
    this.autofocus = false,
    this.focusNode,
    this.readOnly = false,
    this.textAlign = TextAlign.start,
    this.isCanInputDouble = false,
  });

  /// ヒントテキスト
  final String hintText;

  /// 入力テキスト
  final String inputText;

  /// submit call back
  final void Function(String)? onFieldSubmitted;

  /// オートフォーカス
  final bool autofocus;

  /// フォーカスノード
  final FocusNode? focusNode;

  /// read only
  final bool readOnly;

  /// text align
  final TextAlign textAlign;

  /// is can input double
  final bool? isCanInputDouble;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textEditingController = useTextEditingController();
    final colors = Theme.of(context).colorScheme;
    final texts = Theme.of(context).textTheme;

    useEffect(
      () {
        textEditingController.text = inputText;

        return;
      },
    );

    return TextFormField(
      focusNode: focusNode,
      controller: textEditingController,
      keyboardType: TextInputType.number,
      maxLength: maxCheckWeekNumberLength,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      readOnly: readOnly,
      textAlign: textAlign,
      style: texts.bodyLarge,
      inputFormatters: (isCanInputDouble ?? false) ? [Quantity28JanFormatter()] : [RemoveZeroTextInputFormatter()],
      decoration: InputDecoration(
        filled: !readOnly,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: colors.primary.withOpacity(0),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: colors.primary.withOpacity(0),
          ),
        ),
        fillColor: colors.button,
        focusColor: colors.button,
        hintText: hintText,
        hintStyle: texts.titleMedium?.copyWith(color: colors.subText),
        counterText: '',
      ),
      onChanged: (value) {
        onFieldSubmitted?.call(value);
      },
      onFieldSubmitted: (value) {
        onFieldSubmitted?.call(value);
      },
      onTap: () {
        textEditingController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: textEditingController.text.length,
        );
      },
      autofocus: autofocus,
    );
  }
}
