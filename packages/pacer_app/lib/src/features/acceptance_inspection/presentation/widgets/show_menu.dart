import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../themes/app_color_scheme.dart';
import '../../domain/warehouse/slip_code_info.dart';
import '../pages/warehouse/scan_registration/warehouse_inspection_scan_registration_page.dart';

/// show menu
Future<String?> showCustomMenu(
  WidgetRef ref,
  List<SlipCodeInfo> list,
  ValueCallback callback,
) {
  final texts = Theme.of(ref.context).textTheme;
  final colors = Theme.of(ref.context).colorScheme;

  return showMenu(
    context: ref.context,
    color: colors.button,
    position: const RelativeRect.fromLTRB(35, 110, 35, 110),
    items: list
        .map(
          (info) => PopupMenuItem(
            onTap: () => callback(info),
            value: info.slipNumber,
            child: Wrap(
              children: [
                Text(
                  '${info.slipNumber} | '
                  '${info.storeCode} | '
                  '${info.storeName} | '
                  '${info.shipsType} | '
                  '${info.shipsDate}',
                  style: texts.bodyLarge,
                ),
                if (info != list.last)
                  const Divider(
                    height: 1,
                  ),
              ],
            ),
          ),
        )
        .toList(),
  );
}
