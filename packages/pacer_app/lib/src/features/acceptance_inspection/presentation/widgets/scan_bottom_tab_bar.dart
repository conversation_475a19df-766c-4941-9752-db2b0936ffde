import 'package:flutter/material.dart';

import '../../../../common_widgets/pacer_back_button.dart';

/// BottomTabBar
class ScanBottomTabBar extends StatelessWidget {
  /// init
  const ScanBottomTabBar({
    super.key,
    this.leading,
    this.actions = const [],
  });

  /// leading widget
  final Widget? leading;

  /// buttons
  final List<Widget> actions;

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      height: actions.length > 1 ? 100 : 80,
      shape: const CircularNotchedRectangle(),
      notchMargin: 8,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          leading ?? const PacerBackButton(),
          const Spacer(),
          ...actions,
        ],
      ),
    );
  }
}
