import 'package:grpc/grpc.dart';
import '../../../../exceptions/app_exception.dart';

/// extension
extension GrpcErrorExtension on GrpcError {
  ///
  AppException handle() {
    return switch (this) {
      GrpcError(code: StatusCode.notFound) => ProductNotFoundException(),
      GrpcError(code: StatusCode.deadlineExceeded) => TimeOutException(),
      GrpcError(code: StatusCode.invalidArgument) => WrongProductCodeException(),
      GrpcError(code: _) => UnknownException(message ?? toString()),
    };
  }
}
