// ignore_for_file: public_member_api_docs

/// 検品メニュー
const String kMenuPageTitle = '検品メニュー';
const String kSingleCheck = '単品検品';
const String kReceiptsCheck = '伝票検品';
const String kReceiptsConfirm = '伝票確定';
const String kWarehouseInspection = '入庫検品';
const String kTodayArrivalList = '本日入荷一覧';
const String kMisdeliveryRegistration = '誤配登録';
const String kEnd = '終了';

/// 入庫検品
const String kScanRegistration = 'スキャン登録';
const String kScanTip = '振替依頼書に記載されているバーコードか、商品のJANコードをスキャンしてください';
const String kCode = 'コード';
const String kStep = '検品進捗：';
const String kReceiptsNo = '伝票NO';
const String kJAN = 'JAN';
const String kGoodsName = '商品名';
const String kSpec = '規格';
const String kColor = 'カラー';
const String kSize = 'サイズ';
const String kPlanNumber = '納品予定数';
const String kNumber = '納品数';
const String kCancel = '取消';
const String kFixedInputValue = '納品数量を 1 に固定';
const String kConfirmRegister = '確認登録';
const String kFix = '修正';
const String kSure = '確定';
const String kLogin = '登録';
const String kOperationTip = '行を選択し修正ボタン押下で明細修正します';
const String kOperationTip1 = '確定ボタン押下で、検品確定となります';
const String kSelectAll = '全選';
const String kUnSelectAll = '解除';
const String kLoginNumber = '納品数を登録してください';
const String kLine = '行';
const String kInspectionNumber = '検品数';
const String kHasChecked = '検品確定済なので、再検品不要です。';
const String kUnableToCheck = 'センター未検品なので、入庫店検品できません。';
const String kNoStockInformation = '入庫情報ありません。';
const String kOk = 'ok';
const String kPleaseInputCode = 'バーコードをスキャンまたは入力してください。';
const String kGetGoodsError = '商品情報を取得する際にエラーが発生しました。';
const String kPleaseCheckOtherTable = '対象商品は振替依頼書に含まれていません。他の振替依頼書をご確認ください。';
const String kIsInspection = '既にスキャンされていますが、追加スキャンしますか？';
const String kNo = 'いいえ';
const String kYes = 'はい';
const String kInsertingGoods = 'データ登録中、少々お待ちください。';
const String kRequestError = '取得に失敗しました';
const String kClose = '閉じる';
const String kInputCorrectGoodsCode = '正しい商品を入力してください。';
const String kUnfinished = '未完了';
const String kAreYouSureToConfirm = '確定します、よろしいですか？';
const String kAreYouSureToLogin = '登録します、よろしいですか？';
const String kConfirmed = '確定しました。';
const String kHadLogin = '登録しました。';
const String kInputValueGreaterThanPlanedQuantity = '予定数量よりも多い検品数は入力できません。';
const String kNoData = 'データがありません';

/// 単品検品
const String kScanAndInputNum = '商品をスキャンして、数量を入力してください';
const String kLogs = '履歴';
const String kNumberOfOrders = '発注数';
const String kQuantity = '入数';
const String kNumberOfCases = 'ケース数';
const String kPORActivationFlagIsNotSet = 'PC側POR作動フラグが設定されていない';
const String kRestorePreviousData = 'スキャンデータが残っています。前回のデータを復元しますか？';
const String kBrand = 'ブランド';
const String kColorAndSize = 'カラー・サイズ';
const String kScannedProductsHaveNotBeenOrdered = 'スキャン商品は、発注されていません。';
const String kAlreadyScanned = '既にスキャン済みです。';
const String kExcessQuantity = '数量過剰です。誤差の報告しますか？';
const String kCancelScanRegistrationDeleteData = 'スキャン登録を中止します。現在登録中のデータは全て無くなります。よろしいですか？';
const String kDeleting = '削除中...';
const String kDoYouWantDeleteIt = '削除しますか？';
const String kSlipSelectTitle = '伝票選択';
const String kPleaseSelectSlip = '対象の伝票を選択して下さい。';
const String kNumOfShipments = '出荷数';
const String kAdditionalScanTitle = '追加スキャン';
const String kApply = '適用';
const String kNumOfInspectedItem = '既検品数';
const String kNumOfAdditionalInspections = '追加検品数';
const String kTotalNumOfInspection = '合計検品数';
const String kReceipts = '伝票';
const String kGoodsCode = '商品CD';
const String kHistoryTitle = '履歴';
const String kCorrection = '訂正';
const String kAscending = '並替 ↑';
const String kDescending = '並替 ↓';
const String kHistoryTip = '行をクリックして「訂正」ボタンを押すと';
const String kPushCorrectionTip = '数量訂正画面に移動します';
const String kNoGoods = '対象商品がありません。';
const String kPleaseScan = '検品対象をスキャンしてください';
const String kOnHold = '保留';
const String kThereAreIncompleteSlip = '未完了の伝票が存在します';
const String kStatusUnInspected = '未検品';
const String kStatusIncomplete = '未完了';
const String kStatusConfirmed = '確認済';
const String kStatusCompletedDiff = '済(差異)';
const String kStatusCompletedAlternative = '済(代替)';
const String kStatusCompleted = '済';
const String kHasIncompleteItem = '未完了伝票があります、あるいは完了伝票がありません。確認してください。';
const String kConfirmDataIsOk = 'データを確定します。よろしいですか？';
const String kCorrectionPageTip = '数量訂正、代替変更、削除を行います。';
const String kDelete = '削除';
const String kQuantityCorrection = '数量訂正';
const String kNoLogin = '未登録';
const String kMiscalculation = '誤差あり';
const String kReportAnError = '誤差の報告しますか？';
const String kPleaseSelectOneSlip = '伝票を選択してください。';
const String kEnterNumIsTooBig = '入数かケース数が大きすぎます';
const String kAreYouSureApplyTheAddition = '追加を適用してよろしいてすか?';
const String kTipCanReplace = '過剰分の***個の商品をほかの伝票に登録しますか？いいえの場合は誤差登録を行ないます。';
const String kMoreThanOrderQuantityWarning = '発注数以上入力されたので，もう一度スキャンしてください。';
const String kCorrectionLoginTitle = '訂正登録';

/// 伝票検品
const String kSlipInspectionSettingTitle = '伝票検品 設定 画面';
const String kSlipInspectionTitle = '伝票検品 画面';
const String kStartInspection = '検品開始';
const String kEnterSupplierNote = '検品対象の仕入先を入力してください';
const String kEnterJANNote = 'JANをスキャンするとベンダー番号が入力されます。';
const String kSupplier = '仕入先';
const String kPurchaseName = '仕入先名';
const String kPast = '過去';
const String kFuture = '未来';
const String kDeliveryDateMoreThan40Days = 'この伝票は納品予定から40日以上が経過しているため、POR出来ません。POR未登録で伝票回付をしてください。';
const String kNoSupplierTargets = '仕入先対象がありません。';
const String kInputSupplier = '仕入先を入力してください。';
const String kTotalNum = '合計';
const String kTotalNumMoreThanMax = '合計検品数が最大値「99999」を超えています、ご確認お願いします。';
const String kPleaseSelectItem = '検品が行われていません。商品リストを確認してチェックを押してください。';
const String kPleaseCheckGroup = 'すべての商品がチェックされていない伝票があります。すべてチェックを行うかチェックを外してください。';

/// 传票确认
const String kSlipConfirmSettingTitle = '伝票確定 設定 画面';
const String kFirstRegister = '確認登録①';
const String kUnSure = '未確定';
const String kEnterValidSupplier = '正しい仕入先を入力してください。';
const String kRequestData = 'データ取得中';
const String kTheScannedProductUnInspected = 'スキャンした商品は、未検品商品ではありません。';
