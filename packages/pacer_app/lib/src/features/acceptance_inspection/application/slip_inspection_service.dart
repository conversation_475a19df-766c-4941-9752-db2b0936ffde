import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../authentication/data/auth_repository.dart';
import '../../authentication/domain/app_user.dart';
import '../data/slip_inspection_repository.dart';
import '../domain/slip_inspection/correct_details.dart';
import '../domain/slip_inspection/correct_param.dart';
import '../domain/slip_inspection/input_check_info.dart';
import '../domain/slip_inspection/slip_confirm_info.dart';
import '../domain/slip_inspection/support_info.dart';
import '../domain/slip_inspection/vendor_info.dart';
import '../domain/slip_inspection/vendor_inspection_info.dart';

part 'slip_inspection_service.g.dart';

/// 単品検品 service
@Riverpod(keepAlive: true)
SlipInspectionService slipInspectionService(
  SlipInspectionServiceRef ref,
) =>
    SlipInspectionService(ref);

/// 単品検品 service
class SlipInspectionService {
  /// init
  SlipInspectionService(this.ref);

  /// ref
  final Ref ref;

  /// 操作するユーザ
  AppUser? get _caller => ref.read(authRepositoryProvider).currentUser;

  /// 検品レスチェック
  Future<VendorInspectionInfo> getVendorCd({
    required int vendorCdData,
  }) async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).getVendorCd(
          vendorCdData: vendorCdData,
        );
  }

  /// ベンダーコードにより伝票取得して一時テーブルに追加
  Future<InputCheckInfo> inputCheckBegin({
    required DateTime startDate,
    required DateTime endDate,
    required String vendorCode,
  }) async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).inputCheckBegin(
          startDate: startDate,
          endDate: endDate,
          vendorCode: vendorCode,
        );
  }

  /// 商品コードによりベンダー名取得
  Future<VendorInfo> getVendorByJan({
    required String productCode,
  }) async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).getVendorByJan(
          productCode: productCode,
        );
  }

  /// 伝票検品データ取得
  Future<List<CorrectDetails>> getSlipInspectionList() async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).getSlipInspectionList();
  }

  /// 伝票検品_全履歴削除
  Future<bool> deleteAllInspectionData() async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).deleteAllInspectionData();
  }

  /// 伝票検品データ登録
  Future<int> updateCorrectedData({
    required List<CorrectionParam> params,
    required List<String> slipNoList,
  }) async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).updateCorrectedData(
          slipNoList: slipNoList,
          params: params,
        );
  }

  /// 確定登録①　伝票とステータス取得
  Future<List<SlipConfirmInfo>> getSlipInfoList() async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).getSlipInfoList();
  }

  /// 伝票検品_確定登録①　伝票とステータス取得
  Future<List<SlipConfirmInfo>> getSlipInfoNewList() async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).getSlipInfoNewList();
  }

  /// 伝票検品の一時テーブルのベンダー一覧取得
  Future<List<SupportInfo>> getSupportList() async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).getSupportList();
  }

  /// 伝票検品_確定登録①　検品データ最終登録（確定ボタン）
  Future<bool> putPOData() async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).putPOData();
  }

  /// 伝票検品_確定登録①　特定のベンダー（パン）検品データ最終登録（確定ボタン）
  Future<bool> putBreadVendorPOData() async {
    return ref.read(slipInspectionRepositoryProvider(caller: _caller)).putBreadVendorPOData();
  }
}
