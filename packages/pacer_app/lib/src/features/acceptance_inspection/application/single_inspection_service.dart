import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../authentication/data/auth_repository.dart';
import '../../authentication/domain/app_user.dart';
import '../data/single_inspection_repository.dart';
import '../domain/single/correct_details.dart';
import '../domain/single/correction_param.dart';
import '../domain/single/enum/ip_status.dart';
import '../domain/single/goods_and_vendor_info.dart';
import '../domain/single/initial_single_data.dart';
import '../domain/single/order.dart';
import '../domain/single/order_progress.dart';
import '../domain/single/scan_count.dart';
import '../domain/single/scan_data.dart';
import '../domain/single/select_slip_info.dart';
import '../domain/single/slip_confirm_info.dart';
import '../domain/single/store_supplier_authority.dart';
import '../domain/single/sysytem_date.dart';
import '../domain/single/update_order_param.dart';
import '../domain/single/update_receive_number_param.dart';
import '../domain/warehouse/history_info.dart';

part 'single_inspection_service.g.dart';

/// 単品検品 service
@Riverpod(keepAlive: true)
SingleInspectionService singleInspectionService(
  SingleInspectionServiceRef ref,
) =>
    SingleInspectionService(ref);

/// 単品検品 service
class SingleInspectionService {
  /// init
  SingleInspectionService(this.ref);

  /// ref
  final Ref ref;

  /// 操作するユーザ
  AppUser? get _caller => ref.read(authRepositoryProvider).currentUser;

  /// 当日日付、時間、週取得
  Future<SystemDate> getSystemDate() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getSystemDate();
  }

  /// 店舗制御とベンダー制御取得
  Future<StoreAndSupplierAuthority> getStoreAndSupplierAuthority() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getStoreAndSupplierAuthority();
  }

  /// 店舗制御フラグ
  Future<bool> getStoreAuthority() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getStoreAuthority();
  }

  /// 途中検品したデータチェック
  Future<IpStatus> getIPStatus() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getIPStatus();
  }

  /// 単品検品の初期データ件数取得
  Future<InitialSingleData> getInitialSingleData() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getInitialSingleData();
  }

  /// delete cache data by current ip
  Future<bool> deleteCacheDataByCurrentIp() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).deleteCacheDataByCurrentIp();
  }

  /// 単品検品の商品情報取得
  Future<GoodsAndVendorInfo> getGoodsAndVendorInfo({
    required String productCode,
  }) async {
    return ref
        .read(singleInspectionRepositoryProvider(caller: _caller))
        .getGoodsAndVendorInfo(productCode: productCode);
  }

  /// 納品数更新
  Future<bool> updateDeliveriesNumber({
    required double deliveryNum,
    required String slipNo,
    int? packNum,
    int? caseNum,
    required int lineNo,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).updateDeliveriesNumber(
          deliveryNum: deliveryNum,
          slipNo: slipNo,
          packNum: packNum,
          caseNum: caseNum,
          lineNo: lineNo,
        );
  }

  /// 検品中の進捗取得
  Future<OrderProgress> getOrderProgress() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getOrderProgress();
  }

  /// 単品検品の該当伝票の検品進捗取得
  Future<ScanCount> getScanCountData({
    required String slipNo,
    required String productCode,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getScanCountData(
          slipNo: slipNo,
          productCode: productCode,
        );
  }

  /// 単品検品の検品データ登録
  Future<bool> updateOrderData({
    required double deliveryNum,
    required String slipNo,
    required bool isInspected,
    required String id,
    required String productCode,
    required String vendorCode,
    required int lineNo,
    required UpdateOrderParam info,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).updateOrderData(
          slipNo: slipNo,
          productCode: productCode,
          deliveryNum: deliveryNum,
          isInspected: isInspected,
          id: id,
          vendorCode: vendorCode,
          lineNo: lineNo,
          info: info,
        );
  }

  /// 確定登録②　納品数・誤差登録
  Future<bool> updateScanDataFlg({
    required double deliveryNum,
    required String slipNo,
    required bool isError,
    int? packNum,
    int? caseNum,
    required int lineNo,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).updateScanDataFlg(
          slipNo: slipNo,
          packNum: packNum,
          deliveryNum: deliveryNum,
          isError: isError,
          caseNum: caseNum,
          lineNo: lineNo,
        );
  }

  /// delete all data
  Future<bool> deleteAllData() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).deleteAllData();
  }

  /// 単品検品のスキャン登録画面の取消
  Future<bool> deleteData({
    required String slipNo,
    required int lineNo,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).deleteData(
          slipNo: slipNo,
          lineNo: lineNo,
        );
  }

  /// ベンダーチェック
  Future<bool> checkVendor() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).checkVendor();
  }

  /// 履歴一覧取得
  Future<List<HistoryInfo>> getHistoryData() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getHistoryData();
  }

  /// 確定登録①　伝票とステータス取得
  Future<List<SlipConfirmInfo>> getSlipInfoList() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getSlipInfoList();
  }

  /// 確定登録②　修正データ取得
  Future<List<CorrectDetails>> getCorrectDetailsList({
    required String slipNo,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getCorrectDetailsList(slipNo: slipNo);
  }

  /// 確定登録②　修正データ登録（明細のみ）
  Future<int> updateCorrectedData({
    required String slipNo,
    required List<UpdateReceiveNumberParam> slipInfoList,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).updateCorrectedData(
          slipNo: slipNo,
          slipInfoList: slipInfoList,
        );
  }

  /// 確定登録①　保留フラグ設定
  Future<bool> setSuspendFlg({
    required bool isSuspended,
    required String slipNo,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).setSuspendFlg(
          slipNo: slipNo,
          isSuspended: isSuspended,
        );
  }

  /// 単品検品_確定登録①　検品データ最終登録（確定ボタン）
  Future<bool> confirmInspectionData() async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).confirmInspectionData();
  }

  /// 確定登録②　修正データ取得
  Future<ScanData> getScanData({
    required String slipNo,
    required int lineNo,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getScanData(
          slipNo: slipNo,
          lineNo: lineNo,
        );
  }

  /// 確定登録②　修正データ登録(誤算と明細)
  Future<int> updateCorrectedDataFlg({
    required String slipNo,
    required List<UpdateReceiveNumberParam> correctedList,
    required List<CorrectionParam> errorList,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).updateCorrectedDataFlg(
          slipNo: slipNo,
          correctedList: correctedList,
          errorList: errorList,
        );
  }

  /// 同じ商品で伝票番号データ取得
  Future<List<SelectSlipInfo>> getSameProductCodeSlipList({
    required String productCode,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getSameProductCodeSlipList(
          productCode: productCode,
        );
  }

  /// 同じ商品、複数伝票から一つ伝票情報取得して、スキャン登録画面へ表示
  Future<Order> getOrderDataForSlip({
    required String productCode,
    required String slipNo,
    required bool isBread,
  }) async {
    return ref.read(singleInspectionRepositoryProvider(caller: _caller)).getOrderDataForSlip(
          productCode: productCode,
          slipNo: slipNo,
          isBread: isBread,
        );
  }
}
