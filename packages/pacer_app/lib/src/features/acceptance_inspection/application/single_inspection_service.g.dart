// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'single_inspection_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$singleInspectionServiceHash() => r'5f5088d51ab8d1fbf6fced904cdede781444bc21';

/// 単品検品 service
///
/// Copied from [singleInspectionService].
@ProviderFor(singleInspectionService)
final singleInspectionServiceProvider = Provider<SingleInspectionService>.internal(
  singleInspectionService,
  name: r'singleInspectionServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$singleInspectionServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SingleInspectionServiceRef = ProviderRef<SingleInspectionService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
