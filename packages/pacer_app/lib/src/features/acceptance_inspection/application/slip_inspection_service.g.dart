// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slip_inspection_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$slipInspectionServiceHash() => r'67f0aa0c7ff4d5687fa3d15950a76c0870dc0496';

/// 単品検品 service
///
/// Copied from [slipInspectionService].
@ProviderFor(slipInspectionService)
final slipInspectionServiceProvider = Provider<SlipInspectionService>.internal(
  slipInspectionService,
  name: r'slipInspectionServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$slipInspectionServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SlipInspectionServiceRef = ProviderRef<SlipInspectionService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
