import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../authentication/data/auth_repository.dart';
import '../../authentication/domain/app_user.dart';
import '../data/warehouse_inspection_repository.dart';
import '../domain/warehouse/enum/code_type.dart';
import '../domain/warehouse/goods.dart';
import '../domain/warehouse/login_goods.dart';
import '../domain/warehouse/login_slip.dart';
import '../domain/warehouse/scan_info.dart';
import '../domain/warehouse/slip_code_info.dart';
import '../domain/warehouse/slip_code_judge.dart';
import '../domain/warehouse/update_receive_number_param.dart';

part 'warehouse_inspection_service.g.dart';

/// 入庫検品 service
@Riverpod(keepAlive: true)
WarehouseInspectionService warehouseInspectionService(
  WarehouseInspectionServiceRef ref,
) =>
    WarehouseInspectionService(ref);

/// 入庫検品 service
class WarehouseInspectionService {
  /// init
  WarehouseInspectionService(this.ref);

  /// ref
  final Ref ref;

  /// 操作するユーザ
  AppUser? get _caller => ref.read(authRepositoryProvider).currentUser;

  /// 入力されたコードが伝票番号なのか、商品コードなのかを判定する
  Future<CodeType> checkCodeType({
    required String inputCode,
  }) async {
    return ref.read(warehouseInspectionRepositoryProvider(caller: _caller)).checkCodeType(inputCode: inputCode);
  }

  /// 伝票番号が検品済みかどうかを判断する
  Future<SlipCodeJudge> judgeBySlipNo({
    required String slipNumber,
  }) async {
    return ref.read(warehouseInspectionRepositoryProvider(caller: _caller)).judgeBySlipNo(
          slipNumber: slipNumber,
        );
  }

  /// JANより 伝票番号情報を取る
  Future<List<SlipCodeInfo>> getSlipNoByGoodsCode({
    required String productCode,
  }) async {
    return ref
        .read(warehouseInspectionRepositoryProvider(caller: _caller))
        .getSlipNoByGoodsCode(productCode: productCode);
  }

  /// 伝票検品進捗取得
  Future<String> getProgress({
    required String slipNumber,
  }) async {
    return ref.read(warehouseInspectionRepositoryProvider(caller: _caller)).getProgress(slipNumber: slipNumber);
  }

  /// 商品基本情報取得
  Future<Goods> getGoods({
    required String productCode,
    required String slipNumber,
  }) async {
    return ref.read(warehouseInspectionRepositoryProvider(caller: _caller)).getGoods(
          productCode: productCode,
          slipNumber: slipNumber,
        );
  }

  /// スキャン登録画面の検品数更新
  Future<int> updateShipsReceiveNum({
    required String productCode,
    required String slipNumber,
    required double receiveNum,
    required bool isAllOne,
  }) async {
    return ref.read(warehouseInspectionRepositoryProvider(caller: _caller)).updateShipsReceiveNum(
          receiveNum: receiveNum,
          productCode: productCode,
          slipNumber: slipNumber,
          isAllOne: isAllOne,
        );
  }

  /// 伝票番号より 商品基本情報取得
  Future<ScanInfo> getShipsBySlipNo({
    required String productCode,
    required String slipNumber,
  }) async {
    return ref
        .read(warehouseInspectionRepositoryProvider(caller: _caller))
        .getShipsBySlipNo(productCode: productCode, slipNumber: slipNumber);
  }

  /// 確認登録の伝票番号リスト抽出
  Future<List<LoginSlip>> getConfirmLoginSlipList({
    required String slipNumber,
  }) async {
    return ref
        .read(warehouseInspectionRepositoryProvider(caller: _caller))
        .getConfirmLoginSlipList(slipNumber: slipNumber);
  }

  /// 確認登録の伝票明細リスト抽出
  Future<List<LoginGoods>> getConfirmLoginProductList({
    required String slipNumber,
  }) async {
    return ref
        .read(warehouseInspectionRepositoryProvider(caller: _caller))
        .getConfirmLoginProductList(slipNumber: slipNumber);
  }

  /// 確認登録画面の検品数更新
  Future<String> updateReceiveNumber({
    required String slipNumber,
    required List<UpdateReceiveNumberParam> receiveNumbers,
  }) async {
    return ref.read(warehouseInspectionRepositoryProvider(caller: _caller)).updateReceiveNumber(
          slipNumber: slipNumber,
          receiveNumbers: receiveNumbers,
        );
  }

  /// 伝票確定
  Future<bool> confirmReceiveSlip({
    required List<String> slipNumbers,
  }) async {
    return ref
        .read(warehouseInspectionRepositoryProvider(caller: _caller))
        .confirmReceiveSlip(slipNumbers: slipNumbers);
  }
}
