// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse_inspection_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$warehouseInspectionServiceHash() => r'ffb705b8b66fee07ad06cbdf9055fc4a6a63a6de';

/// 入庫検品 service
///
/// Copied from [warehouseInspectionService].
@ProviderFor(warehouseInspectionService)
final warehouseInspectionServiceProvider = Provider<WarehouseInspectionService>.internal(
  warehouseInspectionService,
  name: r'warehouseInspectionServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$warehouseInspectionServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef WarehouseInspectionServiceRef = ProviderRef<WarehouseInspectionService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
