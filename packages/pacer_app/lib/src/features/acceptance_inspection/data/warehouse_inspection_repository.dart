import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/transfer_bll/v1/v1.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/warehouse/enum/code_type.dart';
import '../domain/warehouse/goods.dart';
import '../domain/warehouse/login_goods.dart';
import '../domain/warehouse/login_slip.dart';
import '../domain/warehouse/scan_info.dart';
import '../domain/warehouse/slip_code_info.dart';
import '../domain/warehouse/slip_code_judge.dart';
import '../domain/warehouse/update_receive_number_param.dart';
import '../presentation/utils/grpc_error_extension.dart';

part 'warehouse_inspection_repository.g.dart';

/// provider
@Riverpod(keepAlive: true)
WarehouseInspectionRepository warehouseInspectionRepository(
  WarehouseInspectionRepositoryRef ref, {
  required AppUser? caller,
}) =>
    WarehouseInspectionRepository(caller: caller);

/// 入庫検品 repository
class WarehouseInspectionRepository {
  /// init
  WarehouseInspectionRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  final _uri = Env.getApiBaseUrl();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));

  /// 入力されたコードが伝票番号なのか、商品コードなのかを判定する
  Future<CodeType> checkCodeType({required String inputCode}) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    final request = ShipsCheckSlipNoRequest(storeCode: store, slipOrProduct: inputCode);

    try {
      final resp = await stub.shipsCheckSlipNo(request);

      return switch (resp) {
        ShipsCheckSlipNoResponse(code: == '000') => resp.isSlip ? CodeType.slipCode : CodeType.productCode,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票番号が検品済みかどうかを判断する
  Future<SlipCodeJudge> judgeBySlipNo({
    required String slipNumber,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.getSlipNoJudge(
        GetSlipNoJudgeRequest(
          slipNumber: slipNumber,
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetSlipNoJudgeResponse(code: == '000') => SlipCodeJudge.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// JANより 伝票番号情報を取る
  Future<List<SlipCodeInfo>> getSlipNoByGoodsCode({
    required String productCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.shipsGetSlipNo(
        ShipsGetSlipNoRequest(
          productCode: productCode,
          storeCode: store,
        ),
      );

      return switch (resp) {
        ShipsGetSlipNoResponse(code: == '000') => resp.slipNumberInfo.map(SlipCodeInfo.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票検品進捗取得
  Future<String> getProgress({
    required String slipNumber,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.getSum(
        GetSumRequest(
          slipNumber: slipNumber,
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetSumResponse(code: == '000') => '${resp.uninspectedSlipCount.toInt()}/${resp.totalSlipCount.toInt()}',
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 商品基本情報取得
  Future<Goods> getGoods({
    required String productCode,
    required String slipNumber,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.getProdInfo(
        GetProdInfoRequest(
          productCode: productCode,
          slipNumber: slipNumber,
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetProdInfoResponse(code: == '000') => Goods.fromGrpc(
            resp,
            productCode,
            slipNumber,
          ),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// スキャン登録画面の検品数更新
  Future<int> updateShipsReceiveNum({
    required double receiveNum,
    required String productCode,
    required String slipNumber,
    required bool isAllOne,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.updateShipsInfoReceiveNum(
        UpdateShipsInfoReceiveNumRequest(
          receiveNum: receiveNum,
          productCode: productCode,
          slipNumber: slipNumber,
          isAllOne: isAllOne,
          storeCode: store,
        ),
      );

      return switch (resp) {
        UpdateShipsInfoReceiveNumResponse(code: == '000') => resp.pacerReceiveUpdateReceiveNumJoin,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票番号より 商品基本情報取得
  Future<ScanInfo> getShipsBySlipNo({
    required String productCode,
    required String slipNumber,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.shipsGetSlipNoScan(
        ShipsGetSlipNoScanRequest(
          productCode: productCode,
          slipNumber: slipNumber,
          storeCode: store,
        ),
      );

      return switch (resp) {
        ShipsGetSlipNoScanResponse(code: == '000') => ScanInfo.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確認登録の伝票番号リスト抽出
  Future<List<LoginSlip>> getConfirmLoginSlipList({
    required String slipNumber,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.getConfirmLoginSlip(
        GetConfirmLoginSlipRequest(
          slipNumber: slipNumber,
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetConfirmLoginSlipResponse(code: == '000') => resp.confirmInfo.map(LoginSlip.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確認登録の伝票明細リスト抽出
  Future<List<LoginGoods>> getConfirmLoginProductList({
    required String slipNumber,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.getConfirmLoginProductCd(
        GetConfirmLoginProductCdRequest(
          slipNumber: slipNumber,
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetConfirmLoginProductCdResponse(code: == '000') => resp.confirmInfo.map(LoginGoods.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確認登録画面の検品数更新
  Future<String> updateReceiveNumber({
    required List<UpdateReceiveNumberParam> receiveNumbers,
    required String slipNumber,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.updateReceiveNumNew(
        UpdateReceiveNumNewRequest(
          receiveNumInfo: receiveNumbers.map((e) => e.transform()).toList(),
          slipNumber: slipNumber,
          storeCode: store,
        ),
      );

      return switch (resp) {
        UpdateReceiveNumNewResponse(code: == '000') => resp.pacerReceiveInsertReceiveNumNew,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票確定
  Future<bool> confirmReceiveSlip({
    required List<String> slipNumbers,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TransferBllServiceClient(
      channel,
      options: _callOptions,
      interceptors: [ShiniseInterceptor(caller: caller)],
    );

    try {
      final resp = await stub.updateReceiveFlag(
        UpdateReceiveFlagRequest(
          slipNumber: slipNumbers,
          storeCode: store,
        ),
      );

      return switch (resp) {
        UpdateReceiveFlagResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
