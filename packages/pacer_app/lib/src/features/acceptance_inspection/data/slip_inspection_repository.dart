import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/common/v1/v1.dart' show Date;
import 'package:shinise_core_client/time_limit/v1/time_limit.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../../utils/device_ip_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/slip_inspection/correct_details.dart';
import '../domain/slip_inspection/correct_param.dart';
import '../domain/slip_inspection/input_check_info.dart';
import '../domain/slip_inspection/slip_confirm_info.dart';
import '../domain/slip_inspection/support_info.dart';
import '../domain/slip_inspection/vendor_info.dart';
import '../domain/slip_inspection/vendor_inspection_info.dart';
import '../presentation/utils/grpc_error_extension.dart';

part 'slip_inspection_repository.g.dart';

/// provider
@Riverpod(keepAlive: true)
SlipInspectionRepository slipInspectionRepository(
  SlipInspectionRepositoryRef ref, {
  required AppUser? caller,
}) =>
    SlipInspectionRepository(caller: caller);

/// 伝票検品 repository
class SlipInspectionRepository {
  /// init
  SlipInspectionRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  final _uri = Env.getApiBaseUrl();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));

  /// 検品レスチェック
  Future<VendorInspectionInfo> getVendorCd({
    required int vendorCdData,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getVendorCD(
        GetVendorCDRequest(
          storeCode: store,
          vendorCdData: vendorCdData,
        ),
      );

      return switch (resp) {
        GetVendorCDResponse(code: == '000') => VendorInspectionInfo.fromGrpc(resp.vendorInfo),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ベンダーコードにより伝票取得して一時テーブルに追加
  Future<InputCheckInfo> inputCheckBegin({
    required DateTime startDate,
    required DateTime endDate,
    required String vendorCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.inputCheckBeginClick(
        InputCheckBeginClickRequest(
          storeCode: store,
          dateFrom: Date(
            year: startDate.year,
            month: startDate.month,
            day: startDate.day,
          ),
          dateTo: Date(
            year: endDate.year,
            month: endDate.month,
            day: endDate.day,
          ),
          userName: caller?.name,
          vendorCode: vendorCode,
        ),
      );

      return switch (resp) {
        InputCheckBeginClickResponse(code: == '000') => InputCheckInfo.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 商品コードによりベンダー名取得
  Future<VendorInfo> getVendorByJan({
    required String productCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getJanVendorSearch(
        GetJanVendorSearchRequest(
          storeCode: store,
          productCode: productCode,
        ),
      );

      return switch (resp) {
        GetJanVendorSearchResponse(code: == '000') => VendorInfo.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票検品データ取得
  Future<List<CorrectDetails>> getSlipInspectionList() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getCorrectDetailsAll(
        GetCorrectDetailsAllRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetCorrectDetailsAllResponse(code: == '000') => resp.correctDetail.map(CorrectDetails.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票検品_全履歴削除
  Future<bool> deleteAllInspectionData() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.deleteAllData(
        DeleteAllDataRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        DeleteAllDataResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票検品データ登録
  Future<int> updateCorrectedData({
    required List<CorrectionParam> params,
    required List<String> slipNoList,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.updateCorrectedDataToSee(
        UpdateCorrectedDataToSeeRequest(
          storeCode: store,
          orderDetails: params.map((e) => e.transform()).toList(),
          slipNo: slipNoList,
        ),
      );

      return switch (resp) {
        UpdateCorrectedDataToSeeResponse(code: == '000') => resp.id,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確定登録①　伝票とステータス取得
  Future<List<SlipConfirmInfo>> getSlipInfoList() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getInputCheckInfo(
        GetInputCheckInfoRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetInputCheckInfoResponse(code: == '000') => resp.checkInfo.map(SlipConfirmInfo.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票検品_確定登録①　伝票とステータス取得
  Future<List<SlipConfirmInfo>> getSlipInfoNewList() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getInputCheckInfoNew(
        GetInputCheckInfoNewRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetInputCheckInfoNewResponse(code: == '000') => resp.checkInfo.map(SlipConfirmInfo.fromNewGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票検品の一時テーブルのベンダー一覧取得
  Future<List<SupportInfo>> getSupportList() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getSupportList(
        GetSupportListRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetSupportListResponse(code: == '000') => resp.supportInfo.map(SupportInfo.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票検品_確定登録①　検品データ最終登録（確定ボタン）
  Future<bool> putPOData() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.putPOData(
        PutPODataRequest(
          storeCode: store,
          employeeName: caller?.name,
        ),
      );

      return switch (resp) {
        PutPODataResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 伝票検品_確定登録①　特定のベンダー（パン）検品データ最終登録（確定ボタン）
  Future<bool> putBreadVendorPOData() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.putBreadVendorPOData(
        PutBreadVendorPODataRequest(
          storeCode: store,
          employeeName: caller?.name,
        ),
      );

      return switch (resp) {
        PutBreadVendorPODataResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
