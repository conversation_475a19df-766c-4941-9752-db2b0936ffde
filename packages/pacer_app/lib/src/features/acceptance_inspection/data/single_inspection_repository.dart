import 'package:grpc/grpc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/time_limit/v1/time_limit.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/client_interceptor.dart';
import '../../../utils/device_ip_interceptor.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/single/correct_details.dart';
import '../domain/single/correction_param.dart';
import '../domain/single/enum/ip_status.dart';
import '../domain/single/goods_and_vendor_info.dart';
import '../domain/single/initial_single_data.dart';
import '../domain/single/order.dart';
import '../domain/single/order_progress.dart';
import '../domain/single/scan_count.dart';
import '../domain/single/scan_data.dart';
import '../domain/single/select_slip_info.dart';
import '../domain/single/slip_confirm_info.dart';
import '../domain/single/store_supplier_authority.dart';
import '../domain/single/sysytem_date.dart';
import '../domain/single/update_order_param.dart';
import '../domain/single/update_receive_number_param.dart';
import '../domain/warehouse/history_info.dart';
import '../presentation/utils/grpc_error_extension.dart';

part 'single_inspection_repository.g.dart';

/// provider
@Riverpod(keepAlive: true)
SingleInspectionRepository singleInspectionRepository(
  SingleInspectionRepositoryRef ref, {
  required AppUser? caller,
}) =>
    SingleInspectionRepository(caller: caller);

/// 単品検品 repository
class SingleInspectionRepository {
  /// init
  SingleInspectionRepository({required this.caller});

  /// 操作を行うユーザー
  final AppUser? caller;

  final _uri = Env.getApiBaseUrl();

  final _callOptions = CallOptions(timeout: const Duration(seconds: 20));

  /// 当日日付、時間、週取得
  Future<SystemDate> getSystemDate() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getSystemDate(
        GetSystemDateRequest(storeCode: store),
      );

      return switch (resp) {
        GetSystemDateResponse(code: == '000') => SystemDate.fromGrpc(
            resp.systemDate,
          ),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 店舗制御とベンダー制御取得
  Future<StoreAndSupplierAuthority> getStoreAndSupplierAuthority() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getStoreAndVenderCategory(
        GetStoreAndVenderCategoryRequest(storeCode: store),
      );

      return switch (resp) {
        GetStoreAndVenderCategoryResponse(code: == '000') => StoreAndSupplierAuthority.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 店舗制御フラグ
  Future<bool> getStoreAuthority() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getStoreCdFlg(
        GetStoreCdFlgRequest(storeCode: store),
      );

      return switch (resp) {
        GetStoreCdFlgResponse(code: == '000') => resp.isStoreControlValid,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 途中検品したデータチェック
  Future<IpStatus> getIPStatus() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getThisIPStatusNew(
        GetThisIPStatusNewRequest(storeCode: store),
      );

      return switch (resp) {
        GetThisIPStatusNewResponse(code: == '000') => IpStatus.fromValue(resp.ipStatus.value),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 単品検品の初期データ件数取得
  Future<InitialSingleData> getInitialSingleData() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.inputCheckSingleDataCreate(
        InputCheckSingleDataCreateRequest(
          storeCode: store,
          userName: caller?.name,
        ),
      );

      return switch (resp) {
        InputCheckSingleDataCreateResponse(code: == '000') => InitialSingleData.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// delete cache data by current ip
  Future<bool> deleteCacheDataByCurrentIp() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.deleteThisIPDataNew(
        DeleteThisIPDataNewRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        DeleteThisIPDataNewResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 単品検品の商品情報取得
  Future<GoodsAndVendorInfo> getGoodsAndVendorInfo({
    required String productCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.productAndVendorInfo(
        ProductAndVendorInfoRequest(
          productCode: productCode,
          storeCode: store,
        ),
      );

      return switch (resp) {
        ProductAndVendorInfoResponse(code: == '000') => GoodsAndVendorInfo.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 納品数最更新
  Future<bool> updateDeliveriesNumber({
    required double deliveryNum,
    required String slipNo,
    int? packNum,
    int? caseNum,
    required int lineNo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.updateScanData(
        UpdateScanDataRequest(
          storeCode: store,
          deliveryNum: deliveryNum,
          slipNo: slipNo,
          lineNo: lineNo,
        ),
      );

      return switch (resp) {
        UpdateScanDataResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 検品中の進捗取得
  Future<OrderProgress> getOrderProgress() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getOrderScanCount(
        GetOrderScanCountRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetOrderScanCountResponse(code: == '000') => OrderProgress.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 単品検品の該当伝票の検品進捗取得
  Future<ScanCount> getScanCountData({
    required String slipNo,
    required String productCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getScanCountData(
        GetScanCountDataRequest(
          slipNo: slipNo,
          productCode: productCode,
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetScanCountDataResponse(code: == '000') => ScanCount.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 単品検品の検品データ登録
  Future<bool> updateOrderData({
    required double deliveryNum,
    required String slipNo,
    required bool isInspected,
    required String id,
    required String productCode,
    required String vendorCode,
    required int lineNo,
    required UpdateOrderParam info,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.updateOrderData(
        UpdateOrderDataRequest(
          storeCode: store,
          deliveryNum: deliveryNum,
          slipNo: slipNo,
          isInspected: isInspected,
          id: id,
          lineNo: lineNo,
          productCode: productCode,
          vendorCode: vendorCode,
          info: info.transform(),
        ),
      );

      return switch (resp) {
        UpdateOrderDataResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確定登録②　納品数・誤差登録
  Future<bool> updateScanDataFlg({
    required double deliveryNum,
    required String slipNo,
    required bool isError,
    int? packNum,
    int? caseNum,
    required int lineNo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.updateScanDataFlg(
        UpdateScanDataFlgRequest(
          storeCode: store,
          deliveryNum: deliveryNum,
          slipNo: slipNo,
          isError: isError,
          lineNo: lineNo,
        ),
      );

      return switch (resp) {
        UpdateScanDataFlgResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 全履歴削除
  Future<bool> deleteAllData() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.deleteAllDataNew(
        DeleteAllDataNewRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        DeleteAllDataNewResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 単品検品のスキャン登録画面の取消
  Future<bool> deleteData({
    required String slipNo,
    required int lineNo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.deleteData(
        DeleteDataRequest(
          lineNo: lineNo,
          slipNo: slipNo,
          storeCode: store,
        ),
      );

      return switch (resp) {
        DeleteDataResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// ベンダーチェック
  Future<bool> checkVendor() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.checkVendor(
        CheckVendorRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        CheckVendorResponse(code: == '000') => resp.vendorData.isNotEmpty,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 履歴一覧取得
  Future<List<HistoryInfo>> getHistoryData() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getScan(
        GetScanRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetScanResponse(code: == '000') => resp.scanInfo.map(HistoryInfo.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確定登録①　伝票とステータス取得
  Future<List<SlipConfirmInfo>> getSlipInfoList() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getInputCheckInfoVendor(
        GetInputCheckInfoVendorRequest(
          storeCode: store,
        ),
      );

      return switch (resp) {
        GetInputCheckInfoVendorResponse(code: == '000') => resp.inputCheckInfo.map(SlipConfirmInfo.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確定登録②　修正データ取得
  Future<List<CorrectDetails>> getCorrectDetailsList({
    required String slipNo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getCorrectDetails(
        GetCorrectDetailsRequest(storeCode: store, slipNo: slipNo),
      );

      return switch (resp) {
        GetCorrectDetailsResponse(code: == '000') => resp.correctDetails.map(CorrectDetails.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確定登録②　修正データ登録（明細のみ）
  Future<int> updateCorrectedData({
    required String slipNo,
    required List<UpdateReceiveNumberParam> slipInfoList,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.updateCorrectedData(
        UpdateCorrectedDataRequest(
          slipNo: slipNo,
          correctedData: slipInfoList.map((e) => e.transform()).toList(),
          storeCode: store,
        ),
      );

      return switch (resp) {
        UpdateCorrectedDataResponse(code: == '000') => resp.id,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確定登録①　保留フラグ設定
  Future<bool> setSuspendFlg({
    required bool isSuspended,
    required String slipNo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.setSuspendFlg(
        SetSuspendFlgRequest(
          slipNo: slipNo,
          isSuspended: isSuspended,
          storeCode: store,
        ),
      );

      return switch (resp) {
        SetSuspendFlgResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 単品検品_確定登録①　検品データ最終登録（確定ボタン）
  Future<bool> confirmInspectionData() async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');
    final name = caller?.name;

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.putSingleItemPOData(
        PutSingleItemPODataRequest(
          employeeName: name,
          storeCode: store,
        ),
      );

      return switch (resp) {
        PutSingleItemPODataResponse(code: == '000') => true,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確定登録②　修正データ取得
  Future<ScanData> getScanData({
    required String slipNo,
    required int lineNo,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getScanData(
        GetScanDataRequest(
          storeCode: store,
          slipNo: slipNo,
          lineNo: lineNo,
        ),
      );

      return switch (resp) {
        GetScanDataResponse(code: == '000') => ScanData.fromGrpc(resp.scanData.first),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 確定登録②　修正データ登録(誤算と明細)
  Future<int> updateCorrectedDataFlg({
    required String slipNo,
    required List<UpdateReceiveNumberParam> correctedList,
    required List<CorrectionParam> errorList,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.updateCorrectedDataFlg(
        UpdateCorrectedDataFlgRequest(
          slipNo: slipNo,
          correctedData: correctedList.map((e) => e.transform()).toList(),
          errorData: errorList.map((e) => e.transform()).toList(),
          storeCode: store,
        ),
      );

      return switch (resp) {
        UpdateCorrectedDataFlgResponse(code: == '000') => resp.id,
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 同じ商品で伝票番号データ取得
  Future<List<SelectSlipInfo>> getSameProductCodeSlipList({
    required String productCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getSlipInfoData(
        GetSlipInfoDataRequest(
          storeCode: store,
          productCode: productCode,
        ),
      );

      return switch (resp) {
        GetSlipInfoDataResponse(code: == '000') => resp.slipInfo.map(SelectSlipInfo.fromGrpc).toList(),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 同じ商品、複数伝票から一つ伝票情報取得して、スキャン登録画面へ表示
  Future<Order> getOrderDataForSlip({
    required String productCode,
    required String slipNo,
    required bool isBread,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);
    final store = caller?.clockInStore.code;
    if (store == null) throw ParseAuthFailure('ユーザー情報が不正です');

    final stub = TimeLimitServiceClient(
      channel,
      options: _callOptions,
      interceptors: [
        ShiniseInterceptor(caller: caller),
        const DeviceIPInterceptor(),
      ],
    );

    try {
      final resp = await stub.getOrderDataForSlip(
        GetOrderDataForSlipRequest(
          storeCode: store,
          slipNo: slipNo,
          isBread: isBread,
          productCode: productCode,
        ),
      );

      return switch (resp) {
        GetOrderDataForSlipResponse(code: == '000') => Order.fromGrpc(resp),
        _ => throw UnknownException(resp.message),
      };
    } on Exception catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handle(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
