// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'single_inspection_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$singleInspectionRepositoryHash() => r'0e59cacb6ef2faed13beaa37eaffaf89382323e2';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// provider
///
/// Copied from [singleInspectionRepository].
@ProviderFor(singleInspectionRepository)
const singleInspectionRepositoryProvider = SingleInspectionRepositoryFamily();

/// provider
///
/// Copied from [singleInspectionRepository].
class SingleInspectionRepositoryFamily extends Family {
  /// provider
  ///
  /// Copied from [singleInspectionRepository].
  const SingleInspectionRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'singleInspectionRepositoryProvider';

  /// provider
  ///
  /// Copied from [singleInspectionRepository].
  SingleInspectionRepositoryProvider call({
    required AppUser? caller,
  }) {
    return SingleInspectionRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  SingleInspectionRepositoryProvider getProviderOverride(
    covariant SingleInspectionRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(SingleInspectionRepository Function(SingleInspectionRepositoryRef ref) create) {
    return _$SingleInspectionRepositoryFamilyOverride(this, create);
  }
}

class _$SingleInspectionRepositoryFamilyOverride implements FamilyOverride {
  _$SingleInspectionRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final SingleInspectionRepository Function(SingleInspectionRepositoryRef ref) create;

  @override
  final SingleInspectionRepositoryFamily overriddenFamily;

  @override
  SingleInspectionRepositoryProvider getProviderOverride(
    covariant SingleInspectionRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// provider
///
/// Copied from [singleInspectionRepository].
class SingleInspectionRepositoryProvider extends Provider<SingleInspectionRepository> {
  /// provider
  ///
  /// Copied from [singleInspectionRepository].
  SingleInspectionRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => singleInspectionRepository(
            ref as SingleInspectionRepositoryRef,
            caller: caller,
          ),
          from: singleInspectionRepositoryProvider,
          name: r'singleInspectionRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$singleInspectionRepositoryHash,
          dependencies: SingleInspectionRepositoryFamily._dependencies,
          allTransitiveDependencies: SingleInspectionRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  SingleInspectionRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    SingleInspectionRepository Function(SingleInspectionRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SingleInspectionRepositoryProvider._internal(
        (ref) => create(ref as SingleInspectionRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  ProviderElement<SingleInspectionRepository> createElement() {
    return _SingleInspectionRepositoryProviderElement(this);
  }

  SingleInspectionRepositoryProvider _copyWith(
    SingleInspectionRepository Function(SingleInspectionRepositoryRef ref) create,
  ) {
    return SingleInspectionRepositoryProvider._internal(
      (ref) => create(ref as SingleInspectionRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is SingleInspectionRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SingleInspectionRepositoryRef on ProviderRef<SingleInspectionRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _SingleInspectionRepositoryProviderElement extends ProviderElement<SingleInspectionRepository>
    with SingleInspectionRepositoryRef {
  _SingleInspectionRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as SingleInspectionRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
