// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slip_inspection_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$slipInspectionRepositoryHash() => r'9e8d4d6e1c41e8bec8c8d260f863c26d6f6493e5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// provider
///
/// Copied from [slipInspectionRepository].
@ProviderFor(slipInspectionRepository)
const slipInspectionRepositoryProvider = SlipInspectionRepositoryFamily();

/// provider
///
/// Copied from [slipInspectionRepository].
class SlipInspectionRepositoryFamily extends Family {
  /// provider
  ///
  /// Copied from [slipInspectionRepository].
  const SlipInspectionRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'slipInspectionRepositoryProvider';

  /// provider
  ///
  /// Copied from [slipInspectionRepository].
  SlipInspectionRepositoryProvider call({
    required AppUser? caller,
  }) {
    return SlipInspectionRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  SlipInspectionRepositoryProvider getProviderOverride(
    covariant SlipInspectionRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(SlipInspectionRepository Function(SlipInspectionRepositoryRef ref) create) {
    return _$SlipInspectionRepositoryFamilyOverride(this, create);
  }
}

class _$SlipInspectionRepositoryFamilyOverride implements FamilyOverride {
  _$SlipInspectionRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final SlipInspectionRepository Function(SlipInspectionRepositoryRef ref) create;

  @override
  final SlipInspectionRepositoryFamily overriddenFamily;

  @override
  SlipInspectionRepositoryProvider getProviderOverride(
    covariant SlipInspectionRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// provider
///
/// Copied from [slipInspectionRepository].
class SlipInspectionRepositoryProvider extends Provider<SlipInspectionRepository> {
  /// provider
  ///
  /// Copied from [slipInspectionRepository].
  SlipInspectionRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => slipInspectionRepository(
            ref as SlipInspectionRepositoryRef,
            caller: caller,
          ),
          from: slipInspectionRepositoryProvider,
          name: r'slipInspectionRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$slipInspectionRepositoryHash,
          dependencies: SlipInspectionRepositoryFamily._dependencies,
          allTransitiveDependencies: SlipInspectionRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  SlipInspectionRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    SlipInspectionRepository Function(SlipInspectionRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SlipInspectionRepositoryProvider._internal(
        (ref) => create(ref as SlipInspectionRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  ProviderElement<SlipInspectionRepository> createElement() {
    return _SlipInspectionRepositoryProviderElement(this);
  }

  SlipInspectionRepositoryProvider _copyWith(
    SlipInspectionRepository Function(SlipInspectionRepositoryRef ref) create,
  ) {
    return SlipInspectionRepositoryProvider._internal(
      (ref) => create(ref as SlipInspectionRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is SlipInspectionRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SlipInspectionRepositoryRef on ProviderRef<SlipInspectionRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _SlipInspectionRepositoryProviderElement extends ProviderElement<SlipInspectionRepository>
    with SlipInspectionRepositoryRef {
  _SlipInspectionRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as SlipInspectionRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
