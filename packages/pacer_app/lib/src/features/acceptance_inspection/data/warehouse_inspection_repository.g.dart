// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse_inspection_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$warehouseInspectionRepositoryHash() => r'30f2c6994aa0080bf5bb5910af0a8e53db2adafb';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// provider
///
/// Copied from [warehouseInspectionRepository].
@ProviderFor(warehouseInspectionRepository)
const warehouseInspectionRepositoryProvider = WarehouseInspectionRepositoryFamily();

/// provider
///
/// Copied from [warehouseInspectionRepository].
class WarehouseInspectionRepositoryFamily extends Family {
  /// provider
  ///
  /// Copied from [warehouseInspectionRepository].
  const WarehouseInspectionRepositoryFamily();

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies => _allTransitiveDependencies;

  @override
  String? get name => r'warehouseInspectionRepositoryProvider';

  /// provider
  ///
  /// Copied from [warehouseInspectionRepository].
  WarehouseInspectionRepositoryProvider call({
    required AppUser? caller,
  }) {
    return WarehouseInspectionRepositoryProvider(
      caller: caller,
    );
  }

  @visibleForOverriding
  @override
  WarehouseInspectionRepositoryProvider getProviderOverride(
    covariant WarehouseInspectionRepositoryProvider provider,
  ) {
    return call(
      caller: provider.caller,
    );
  }

  /// Enables overriding the behavior of this provider, no matter the parameters.
  Override overrideWith(WarehouseInspectionRepository Function(WarehouseInspectionRepositoryRef ref) create) {
    return _$WarehouseInspectionRepositoryFamilyOverride(this, create);
  }
}

class _$WarehouseInspectionRepositoryFamilyOverride implements FamilyOverride {
  _$WarehouseInspectionRepositoryFamilyOverride(this.overriddenFamily, this.create);

  final WarehouseInspectionRepository Function(WarehouseInspectionRepositoryRef ref) create;

  @override
  final WarehouseInspectionRepositoryFamily overriddenFamily;

  @override
  WarehouseInspectionRepositoryProvider getProviderOverride(
    covariant WarehouseInspectionRepositoryProvider provider,
  ) {
    return provider._copyWith(create);
  }
}

/// provider
///
/// Copied from [warehouseInspectionRepository].
class WarehouseInspectionRepositoryProvider extends Provider<WarehouseInspectionRepository> {
  /// provider
  ///
  /// Copied from [warehouseInspectionRepository].
  WarehouseInspectionRepositoryProvider({
    required AppUser? caller,
  }) : this._internal(
          (ref) => warehouseInspectionRepository(
            ref as WarehouseInspectionRepositoryRef,
            caller: caller,
          ),
          from: warehouseInspectionRepositoryProvider,
          name: r'warehouseInspectionRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$warehouseInspectionRepositoryHash,
          dependencies: WarehouseInspectionRepositoryFamily._dependencies,
          allTransitiveDependencies: WarehouseInspectionRepositoryFamily._allTransitiveDependencies,
          caller: caller,
        );

  WarehouseInspectionRepositoryProvider._internal(
    super.create, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caller,
  }) : super.internal();

  final AppUser? caller;

  @override
  Override overrideWith(
    WarehouseInspectionRepository Function(WarehouseInspectionRepositoryRef ref) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WarehouseInspectionRepositoryProvider._internal(
        (ref) => create(ref as WarehouseInspectionRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caller: caller,
      ),
    );
  }

  @override
  ({
    AppUser? caller,
  }) get argument {
    return (caller: caller,);
  }

  @override
  ProviderElement<WarehouseInspectionRepository> createElement() {
    return _WarehouseInspectionRepositoryProviderElement(this);
  }

  WarehouseInspectionRepositoryProvider _copyWith(
    WarehouseInspectionRepository Function(WarehouseInspectionRepositoryRef ref) create,
  ) {
    return WarehouseInspectionRepositoryProvider._internal(
      (ref) => create(ref as WarehouseInspectionRepositoryRef),
      name: name,
      dependencies: dependencies,
      allTransitiveDependencies: allTransitiveDependencies,
      debugGetCreateSourceHash: debugGetCreateSourceHash,
      from: from,
      caller: caller,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is WarehouseInspectionRepositoryProvider && other.caller == caller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin WarehouseInspectionRepositoryRef on ProviderRef<WarehouseInspectionRepository> {
  /// The parameter `caller` of this provider.
  AppUser? get caller;
}

class _WarehouseInspectionRepositoryProviderElement extends ProviderElement<WarehouseInspectionRepository>
    with WarehouseInspectionRepositoryRef {
  _WarehouseInspectionRepositoryProviderElement(super.provider);

  @override
  AppUser? get caller => (origin as WarehouseInspectionRepositoryProvider).caller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
