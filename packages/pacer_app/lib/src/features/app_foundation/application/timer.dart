import 'dart:async';

import 'package:clock/clock.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'timer.g.dart';

/// ユーザーのログアウト時間と現在時刻を比較。
/// TimerState
@Riverpod(keepAlive: true)
class EveryOneMinutesTimer extends _$EveryOneMinutesTimer {
  static const _periodicMin = 1;
  late Timer _timer;

  void _setNow() {
    state = clock.now();
  }

  @override
  DateTime build() {
    _timer = Timer.periodic(const Duration(minutes: _periodicMin), (_) async {
      _setNow();
    });

    ref.onDispose(() {
      _timer.cancel();
    });

    return clock.now();
  }
}
