// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'timer.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$everyOneMinutesTimerHash() => r'a80e16869a2e7861bf3dd0241c45fe7ad7d00dd0';

/// ユーザーのログアウト時間と現在時刻を比較。
/// TimerState
///
/// Copied from [EveryOneMinutesTimer].
@ProviderFor(EveryOneMinutesTimer)
final everyOneMinutesTimerProvider = NotifierProvider<EveryOneMinutesTimer, DateTime>.internal(
  EveryOneMinutesTimer.new,
  name: r'everyOneMinutesTimerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$everyOneMinutesTimerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EveryOneMinutesTimer = Notifier<DateTime>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
