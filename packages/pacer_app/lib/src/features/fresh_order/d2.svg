<?xml version="1.0" encoding="utf-8"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" d2Version="v0.6.3-HEAD" preserveAspectRatio="xMidYMid meet" viewBox="0 0 2311 810"><svg id="d2-svg" class="d2-3710014836" width="2311" height="810" viewBox="-101 -112 2311 810"><rect x="-101.000000" y="-112.000000" width="2311.000000" height="810.000000" rx="0.000000" class=" fill-N7" stroke-width="0" /><style type="text/css"><![CDATA[
.d2-3710014836 .text {
	font-family: "d2-3710014836-font-regular";
}
@font-face {
	font-family: d2-3710014836-font-regular;
	src: url("data:application/font-woff;base64,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");
}
.appendix-icon {
	filter: drop-shadow(0px 0px 32px rgba(31, 36, 58, 0.1));
}
.d2-3710014836 .text-bold {
	font-family: "d2-3710014836-font-bold";
}
@font-face {
	font-family: d2-3710014836-font-bold;
	src: url("data:application/font-woff;base64,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");
}
.d2-3710014836 .text-italic {
	font-family: "d2-3710014836-font-italic";
}
@font-face {
	font-family: d2-3710014836-font-italic;
	src: url("data:application/font-woff;base64,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");
}]]></style><style type="text/css"><![CDATA[.shape {
  shape-rendering: geometricPrecision;
  stroke-linejoin: round;
}
.connection {
  stroke-linecap: round;
  stroke-linejoin: round;
}
.blend {
  mix-blend-mode: multiply;
  opacity: 0.5;
}

		.d2-3710014836 .fill-N1{fill:#0A0F25;}
		.d2-3710014836 .fill-N2{fill:#676C7E;}
		.d2-3710014836 .fill-N3{fill:#9499AB;}
		.d2-3710014836 .fill-N4{fill:#CFD2DD;}
		.d2-3710014836 .fill-N5{fill:#DEE1EB;}
		.d2-3710014836 .fill-N6{fill:#EEF1F8;}
		.d2-3710014836 .fill-N7{fill:#FFFFFF;}
		.d2-3710014836 .fill-B1{fill:#0D32B2;}
		.d2-3710014836 .fill-B2{fill:#0D32B2;}
		.d2-3710014836 .fill-B3{fill:#E3E9FD;}
		.d2-3710014836 .fill-B4{fill:#E3E9FD;}
		.d2-3710014836 .fill-B5{fill:#EDF0FD;}
		.d2-3710014836 .fill-B6{fill:#F7F8FE;}
		.d2-3710014836 .fill-AA2{fill:#4A6FF3;}
		.d2-3710014836 .fill-AA4{fill:#EDF0FD;}
		.d2-3710014836 .fill-AA5{fill:#F7F8FE;}
		.d2-3710014836 .fill-AB4{fill:#EDF0FD;}
		.d2-3710014836 .fill-AB5{fill:#F7F8FE;}
		.d2-3710014836 .stroke-N1{stroke:#0A0F25;}
		.d2-3710014836 .stroke-N2{stroke:#676C7E;}
		.d2-3710014836 .stroke-N3{stroke:#9499AB;}
		.d2-3710014836 .stroke-N4{stroke:#CFD2DD;}
		.d2-3710014836 .stroke-N5{stroke:#DEE1EB;}
		.d2-3710014836 .stroke-N6{stroke:#EEF1F8;}
		.d2-3710014836 .stroke-N7{stroke:#FFFFFF;}
		.d2-3710014836 .stroke-B1{stroke:#0D32B2;}
		.d2-3710014836 .stroke-B2{stroke:#0D32B2;}
		.d2-3710014836 .stroke-B3{stroke:#E3E9FD;}
		.d2-3710014836 .stroke-B4{stroke:#E3E9FD;}
		.d2-3710014836 .stroke-B5{stroke:#EDF0FD;}
		.d2-3710014836 .stroke-B6{stroke:#F7F8FE;}
		.d2-3710014836 .stroke-AA2{stroke:#4A6FF3;}
		.d2-3710014836 .stroke-AA4{stroke:#EDF0FD;}
		.d2-3710014836 .stroke-AA5{stroke:#F7F8FE;}
		.d2-3710014836 .stroke-AB4{stroke:#EDF0FD;}
		.d2-3710014836 .stroke-AB5{stroke:#F7F8FE;}
		.d2-3710014836 .background-color-N1{background-color:#0A0F25;}
		.d2-3710014836 .background-color-N2{background-color:#676C7E;}
		.d2-3710014836 .background-color-N3{background-color:#9499AB;}
		.d2-3710014836 .background-color-N4{background-color:#CFD2DD;}
		.d2-3710014836 .background-color-N5{background-color:#DEE1EB;}
		.d2-3710014836 .background-color-N6{background-color:#EEF1F8;}
		.d2-3710014836 .background-color-N7{background-color:#FFFFFF;}
		.d2-3710014836 .background-color-B1{background-color:#0D32B2;}
		.d2-3710014836 .background-color-B2{background-color:#0D32B2;}
		.d2-3710014836 .background-color-B3{background-color:#E3E9FD;}
		.d2-3710014836 .background-color-B4{background-color:#E3E9FD;}
		.d2-3710014836 .background-color-B5{background-color:#EDF0FD;}
		.d2-3710014836 .background-color-B6{background-color:#F7F8FE;}
		.d2-3710014836 .background-color-AA2{background-color:#4A6FF3;}
		.d2-3710014836 .background-color-AA4{background-color:#EDF0FD;}
		.d2-3710014836 .background-color-AA5{background-color:#F7F8FE;}
		.d2-3710014836 .background-color-AB4{background-color:#EDF0FD;}
		.d2-3710014836 .background-color-AB5{background-color:#F7F8FE;}
		.d2-3710014836 .color-N1{color:#0A0F25;}
		.d2-3710014836 .color-N2{color:#676C7E;}
		.d2-3710014836 .color-N3{color:#9499AB;}
		.d2-3710014836 .color-N4{color:#CFD2DD;}
		.d2-3710014836 .color-N5{color:#DEE1EB;}
		.d2-3710014836 .color-N6{color:#EEF1F8;}
		.d2-3710014836 .color-N7{color:#FFFFFF;}
		.d2-3710014836 .color-B1{color:#0D32B2;}
		.d2-3710014836 .color-B2{color:#0D32B2;}
		.d2-3710014836 .color-B3{color:#E3E9FD;}
		.d2-3710014836 .color-B4{color:#E3E9FD;}
		.d2-3710014836 .color-B5{color:#EDF0FD;}
		.d2-3710014836 .color-B6{color:#F7F8FE;}
		.d2-3710014836 .color-AA2{color:#4A6FF3;}
		.d2-3710014836 .color-AA4{color:#EDF0FD;}
		.d2-3710014836 .color-AA5{color:#F7F8FE;}
		.d2-3710014836 .color-AB4{color:#EDF0FD;}
		.d2-3710014836 .color-AB5{color:#F7F8FE;}.appendix text.text{fill:#0A0F25}.md{--color-fg-default:#0A0F25;--color-fg-muted:#676C7E;--color-fg-subtle:#9499AB;--color-canvas-default:#FFFFFF;--color-canvas-subtle:#EEF1F8;--color-border-default:#0D32B2;--color-border-muted:#0D32B2;--color-neutral-muted:#EEF1F8;--color-accent-fg:#0D32B2;--color-accent-emphasis:#0D32B2;--color-attention-subtle:#676C7E;--color-danger-fg:red;}.sketch-overlay-B1{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-B2{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-B3{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-B6{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AA2{fill:url(#streaks-dark);mix-blend-mode:overlay}.sketch-overlay-AA4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AA5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AB4{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-AB5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N1{fill:url(#streaks-darker);mix-blend-mode:lighten}.sketch-overlay-N2{fill:url(#streaks-dark);mix-blend-mode:overlay}.sketch-overlay-N3{fill:url(#streaks-normal);mix-blend-mode:color-burn}.sketch-overlay-N4{fill:url(#streaks-normal);mix-blend-mode:color-burn}.sketch-overlay-N5{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N6{fill:url(#streaks-bright);mix-blend-mode:darken}.sketch-overlay-N7{fill:url(#streaks-bright);mix-blend-mode:darken}.light-code{display: block}.dark-code{display: none}]]></style><g id="Legend"><g class="shape" ><rect x="0.000000" y="29.000000" width="694.000000" height="557.000000" class=" stroke-B1 fill-B4" style="stroke-width:2;" /></g><text x="347.000000" y="16.000000" class="text fill-N1" style="text-anchor:middle;font-size:28px">Legend</text></g><g id="freshOrderControllerProvider"><g class="shape" ><rect x="1017.000000" y="100.000000" width="290.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1162.000000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">freshOrderControllerProvider</text><title>生鮮発注コントローラ</title></g><g id="scanCodeProvider"><g class="shape" ><rect x="1402.000000" y="100.000000" width="207.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1505.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">scanCodeProvider</text><title>カメラやバーコードスキャナでスキャンしたコードの状態</title></g><g id="fetchWeathersProvider"><g class="shape" ><rect x="1803.000000" y="100.000000" width="243.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1924.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">fetchWeathersProvider</text><title>店舗の天気予報を取得する 2時間キャッシュ</title></g><g id="FreshOrderPage"><g class="shape" ><ellipse rx="127.000000" ry="127.000000" cx="1317.000000" cy="467.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1317.000000" y="472.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">FreshOrderPage</text><title>生鮮発注画面</title></g><g id="WeatherTable"><g class="shape" ><ellipse rx="116.500000" ry="116.500000" cx="1975.500000" cy="467.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1975.500000" y="473.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">WeatherTable</text><title>天気予報を表示する</title></g><g id="InputOrderTable"><g class="shape" ><ellipse rx="130.000000" ry="130.000000" cx="1669.000000" cy="467.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1669.000000" y="472.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">InputOrderTable</text><title>発注数入力表示</title></g><g id="_ItemValue"><g class="shape" ><ellipse rx="80.500000" ry="80.500000" cx="1032.500000" cy="467.500000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="1032.500000" y="473.000000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">_ItemValue</text></g><g id="_ScanField"><g class="shape" ><ellipse rx="78.000000" ry="78.000000" cx="814.000000" cy="467.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="814.000000" y="472.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">_ScanField</text></g><g id="Legend.Type"><g class="shape" ><rect x="30.000000" y="378.000000" width="345.000000" height="178.000000" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="202.500000" y="366.000000" class="text fill-N1" style="text-anchor:middle;font-size:24px">Type</text></g><g id="Legend.Arrows"><g class="shape" ><rect x="417.000000" y="70.000000" width="249.000000" height="460.000000" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="541.500000" y="58.000000" class="text fill-N1" style="text-anchor:middle;font-size:24px">Arrows</text></g><g id="Legend.Type.Widget"><g class="shape" ><ellipse rx="59.000000" ry="59.000000" cx="119.000000" cy="467.000000" class="shape stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="119.000000" y="472.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Widget</text></g><g id="Legend.Type.Provider"><g class="shape" ><rect x="238.000000" y="434.000000" width="107.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="291.500000" y="472.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Provider</text></g><g id="Legend.Arrows.&#34;.&#34;"><g class="shape" ><rect x="494.000000" y="100.000000" width="49.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="518.500000" y="138.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">.</text></g><g id="Legend.Arrows.&#34;..&#34;"><g class="shape" ><rect x="516.000000" y="434.000000" width="54.000000" height="66.000000" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="543.000000" y="472.500000" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">..</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[0]"><marker id="mk-2177206569" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" class="connection fill-B2" stroke-width="2" /> </marker><path d="M 544.211471 156.865322 C 603.549988 212.300003 606.349976 316.399994 558.304469 430.314403" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-3710014836)" /><text x="605.500000" y="292.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">read:</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[1]"><marker id="mk-3488378134" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" class="connection fill-B1" stroke-width="2" /> </marker><path d="M 527.757425 167.434559 C 540.049988 214.300003 543.250000 316.399994 543.250000 430.000000" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-3710014836)" /><text x="543.000000" y="304.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">listen</text></g><g id="Legend.Arrows.(&#34;.&#34; -&gt; &#34;..&#34;)[2]"><marker id="mk-3519660172" markerWidth="16.000000" markerHeight="20.000000" refX="10.000000" refY="10.000000" viewBox="0.000000 0.000000 16.000000 20.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 16.000000,10.000000 0.000000,20.000000" class="connection fill-B1" stroke-width="4" /> </marker><path d="M 497.725240 168.083623 C 470.450012 214.300003 476.250000 316.399994 525.419159 427.597943" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-3710014836)" /><text x="475.000000" y="307.000000" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">watch:</text></g><g id="(freshOrderControllerProvider -&gt; FreshOrderPage)[0]"><path d="M 1231.937638 166.832893 C 1327.650024 214.300003 1350.199951 298.000000 1343.282457 335.118481" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-3710014836)" /></g><g id="(scanCodeProvider -&gt; FreshOrderPage)[0]"><path d="M 1505.500000 167.500000 C 1505.500000 214.300003 1486.199951 305.399994 1411.895123 376.239880" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-3710014836)" /></g><g id="(freshOrderControllerProvider -&gt; FreshOrderPage)[1]"><path d="M 1223.999064 166.469936 C 1310.250000 214.300003 1331.199951 297.600006 1327.394292 336.019481" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-3488378134)" mask="url(#d2-3710014836)" /></g><g id="(freshOrderControllerProvider -&gt; FreshOrderPage)[2]"><path d="M 1179.382639 167.294700 C 1202.500000 214.300003 1217.000000 301.200012 1248.945572 354.567898" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-3710014836)" /></g><g id="(freshOrderControllerProvider -&gt; FreshOrderPage)[3]"><path d="M 1166.573550 167.473655 C 1174.250000 214.300003 1188.599976 303.000000 1235.555899 363.833555" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-3710014836)" /></g><g id="(fetchWeathersProvider -&gt; WeatherTable)[0]"><path d="M 1943.927457 168.138630 C 1968.900024 214.300003 1975.599976 299.799988 1975.945311 344.000214" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-3710014836)" /></g><g id="(fetchWeathersProvider -&gt; InputOrderTable)[0]"><path d="M 1831.186137 166.540277 C 1702.000000 214.300003 1669.000000 297.000000 1669.000000 330.000000" fill="none" class="connection stroke-B1" style="stroke-width:4;" marker-end="url(#mk-3519660172)" mask="url(#d2-3710014836)" /></g><g id="(freshOrderControllerProvider -&gt; _ItemValue)[0]"><path d="M 1113.888425 166.684410 C 1049.099976 214.300003 1032.598999 307.000000 1032.979950 383.000050" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-3710014836)" /></g><g id="(freshOrderControllerProvider -&gt; _ScanField)[0]"><path d="M 1037.069683 166.023331 C 859.000000 214.300003 814.000000 307.399994 814.000000 385.000000" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:8.000000,7.892511;" marker-end="url(#mk-2177206569)" mask="url(#d2-3710014836)" /></g><g transform="translate(1291 84)" class="appendix-icon"><title>生鮮発注コントローラ</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1593 84)" class="appendix-icon"><title>カメラやバーコードスキャナでスキャンしたコードの状態</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2030 84)" class="appendix-icon"><title>店舗の天気予報を取得する 2時間キャッシュ</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1391 361)" class="appendix-icon"><title>生鮮発注画面</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(2042 369)" class="appendix-icon"><title>天気予報を表示する</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><g transform="translate(1745 359)" class="appendix-icon"><title>発注数入力表示</title><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3427_35082111)">
<path d="M16 31.1109C24.3456 31.1109 31.1111 24.3454 31.1111 15.9998C31.1111 7.65415 24.3456 0.888672 16 0.888672C7.65436 0.888672 0.888885 7.65415 0.888885 15.9998C0.888885 24.3454 7.65436 31.1109 16 31.1109Z" fill="white" stroke="#DEE1EB"/>
<path d="M16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26Z" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 19.998V15.998" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 12H16.0098" stroke="#2E3346" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_3427_35082111">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
</g><mask id="d2-3710014836" maskUnits="userSpaceOnUse" x="-101" y="-112" width="2311" height="810">
<rect x="-101" y="-112" width="2311" height="810" fill="white"></rect>
<rect x="305.500000" y="-12.000000" width="83" height="36" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1055.500000" y="122.500000" width="213" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1440.500000" y="122.500000" width="130" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1841.500000" y="122.500000" width="166" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1260.000000" y="456.500000" width="114" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1926.000000" y="457.000000" width="99" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1610.000000" y="456.500000" width="118" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="992.500000" y="457.000000" width="80" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="775.500000" y="456.500000" width="77" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="178.000000" y="342.000000" width="49" height="31" fill="rgba(0,0,0,0.75)"></rect>
<rect x="506.500000" y="34.000000" width="70" height="31" fill="rgba(0,0,0,0.75)"></rect>
<rect x="94.000000" y="456.500000" width="50" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="260.500000" y="456.500000" width="62" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="516.500000" y="122.500000" width="4" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="538.500000" y="456.500000" width="9" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="588.000000" y="276.000000" width="35" height="21" fill="black"></rect>
<rect x="525.000000" y="288.000000" width="36" height="21" fill="black"></rect>
<rect x="453.000000" y="291.000000" width="44" height="21" fill="black"></rect>
</mask></svg></svg>