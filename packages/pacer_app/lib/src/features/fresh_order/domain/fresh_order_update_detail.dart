import 'fresh_order.dart';

/// 数量更新model
class FreshOrderUpdateDetail {
  /// コンストラクタ
  const FreshOrderUpdateDetail({
    required this.productCode,
    required this.bin,
    required this.dayIndex,
    required this.orderUnit,
    this.quantity,
  });

  /// 商品コード
  final ProductCode productCode;

  /// 更新するときのbin
  final String bin;

  /// 日情報index
  final int dayIndex;

  /// 更新単位
  final int orderUnit;

  /// 数量
  final int? quantity;
}
