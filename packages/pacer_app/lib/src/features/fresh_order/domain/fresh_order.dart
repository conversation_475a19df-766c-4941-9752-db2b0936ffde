import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:shinise_core_client/fresh_order/v1/fresh_order.pb.dart';

/// 生鮮食品の注文
class FreshOrder {
  /// コンストラクタ関数
  const FreshOrder({
    required this.product,
    required this.transactions,
    required this.orderQuantities,
    required this.recommendOrders,
    required this.bin,
  });

  /// grpc 応答からのコンストラクター関数
  factory FreshOrder.fromGrpc(ProductCode productCode, CommonResponse grpc) {
    return FreshOrder(
      product: FreshProduct.fromGrpc(
        productCode,
        grpc.table0,
      ),
      transactions: grpc.table1.map(FreshProductTransaction.fromGrpc).toList(),
      orderQuantities: grpc.table2
          .map(
            (e) => [
              int.tryParse(e.oneQuantity),
              int.tryParse(e.twoQuantity),
              int.tryParse(e.threeQuantity),
              int.tryParse(e.fourQuantity),
              int.tryParse(e.fiveQuantity),
              int.tryParse(e.sixQuantity),
              int.tryParse(e.sevenQuantity),
            ],
          )
          .expand((e) => e)
          .toList(),
      bin: switch (grpc.table3.elementAtOrNull(0)?.bin) {
        null => '',
        defaultBin => '',
        final nonNullBin => nonNullBin,
      },
      recommendOrders: grpc.table3
          .mapIndexed(
            (i, e) => [
              Order(
                bin: grpc.table3.elementAtOrNull(i)?.bin ?? '',
                quantity: double.tryParse(e.firstRecOrderQuantity)?.round(),
                canUpdate: e.isOrderableFirst,
              ),
              Order(
                bin: grpc.table3.elementAtOrNull(i)?.bin ?? '',
                quantity: double.tryParse(e.secondRecOrderQuantity)?.round(),
                canUpdate: e.isOrderableSecond,
              ),
              Order(
                bin: grpc.table3.elementAtOrNull(i)?.bin ?? '',
                quantity: double.tryParse(e.thirdRecOrderQuantity)?.round(),
                canUpdate: e.isOrderableThird,
              ),
              Order(
                bin: grpc.table3.elementAtOrNull(i)?.bin ?? '',
                quantity: double.tryParse(e.fourthRecOrderQuantity)?.round(),
                canUpdate: e.isOrderableFourth,
              ),
              Order(
                bin: grpc.table3.elementAtOrNull(i)?.bin ?? '',
                quantity: double.tryParse(e.fifthRecOrderQuantity)?.round(),
                canUpdate: e.isOrderableFifth,
              ),
              Order(
                bin: grpc.table3.elementAtOrNull(i)?.bin ?? '',
                quantity: double.tryParse(e.sixthRecOrderQuantity)?.round(),
                canUpdate: e.isOrderableSixth,
              ),
              Order(
                bin: grpc.table3.elementAtOrNull(i)?.bin ?? '',
                quantity: double.tryParse(e.seventhRecOrderQuantity)?.round(),
                canUpdate: e.isOrderableSeventh,
              ),
            ],
          )
          .expand((e) => e)
          .toList(),
    );
  }

  /// binは9999とか""の場合、〇〇便を表示しません。
  static const defaultBin = '9999';

  /// 新鮮な商品情報
  final FreshProduct product;

  /// 2週間以内のお取引
  final List<FreshProductTransaction> transactions;

  /// 発注数履歴
  final List<int?> orderQuantities;

  /// 先週と来週の推奨注文数量
  final List<Order> recommendOrders;

  /// 推奨発注数の便、table3.binです。
  final String bin;
}

/// 生鮮食品
class FreshProduct {
  /// コンストラクタ関数
  const FreshProduct({
    required this.code,
    required this.name,
    required this.specification,
    required this.salePrice,
    required this.orderCloseTime,
    required this.orderUnit,
    required this.innerCaseQuantity,
    required this.price,
  });

  /// grpc 応答からのコンストラクター関数
  factory FreshProduct.fromGrpc(ProductCode code, CommonResponse_Table0 grpc) => FreshProduct(
        code: code,
        name: grpc.productName,
        specification: grpc.specName,
        salePrice: grpc.salePrice,
        orderCloseTime: grpc.orderItem,
        orderUnit: grpc.orderUnit,
        innerCaseQuantity: grpc.innerCaseQuantity,
        price: grpc.purchasePrice,
      );

  /// 商品コード
  final ProductCode code;

  /// 商品名称
  final String name;

  /// 規格
  final String specification;

  /// 売単価
  final double salePrice;

  /// 発注締
  final String orderCloseTime;

  ///「発単」列のデータ
  final int orderUnit;

  ///「入数」列のデータ
  final int innerCaseQuantity;

  /// 原単価
  final double price;
}

/// 生鮮食品取引
class FreshProductTransaction {
  /// コンストラクタ関数
  const FreshProductTransaction({
    required this.date,
    required this.saleQuantity,
    required this.discountQuantity,
    required this.discardQuantity,
  });

  /// grpc 応答からのコンストラクター関数
  factory FreshProductTransaction.fromGrpc(CommonResponse_Table1 grpc) => FreshProductTransaction(
        date: DateFormat.yMd().add_Hms().parse(grpc.date),
        saleQuantity: double.tryParse(grpc.saleQuantity)?.round(),
        discountQuantity: double.tryParse(grpc.posDiscountQuantity)?.round(),
        discardQuantity: double.tryParse(grpc.wasteQuantity)?.abs().round(),
      );

  /// 日付
  final DateTime date;

  /// 売上数
  final int? saleQuantity;

  /// 値下数
  final int? discountQuantity;

  /// 廃棄数
  final int? discardQuantity;
}

/// 注文数量と編集可能
class Order {
  /// コンストラクタ関数
  const Order({
    required this.quantity,
    required this.bin,
    this.canUpdate = false,
  });

  /// 便
  /// APIから取得して更新に使用します
  final String bin;

  /// 注文数量
  final int? quantity;

  /// 更新可能
  final bool canUpdate;

  /// copy with function
  Order copyWith({
    int? quantity,
    String? bin,
    bool? canUpdate,
  }) =>
      Order(
        quantity: quantity ?? this.quantity,
        bin: bin ?? this.bin,
        canUpdate: canUpdate ?? this.canUpdate,
      );
}

/// 製品コード
class ProductCode {
  /// 初期化
  ProductCode._({required this.code});

  /// 文字列から製品コードを取得する
  /// ①、20桁、26桁の場合、頭から13桁を取得する
  /// ②、①+13桁＋13桁以下の商品コードは頭が0があるの場合、頭の0を消します。
  /// ③、①＋② の処理後、13桁+25JAN(頭から2桁は25の商品)の場合、後から6桁は0に処理してください
  factory ProductCode.parsed(String value) {
    return ProductCode._(
      code: value.handle20And26JAN.trimZeroLeading.handle25JAN,
    );
  }

  /// JANCODE
  final String code;
}

/// 商品コードextension
extension ProductCodeString on String {
  /// 20桁、26桁の場合、頭から13桁を取得する
  String get handle20And26JAN => length == 20 || length == 26 ? substring(0, 13) : this;

  /// 13桁以下の商品コードは頭が0があるの場合、頭の0を消します。
  String get trimZeroLeading => length > 13 ? this : RegExp(r'^0*(.*)$').firstMatch(this)?.group(1) ?? this;

  /// 13桁+25JAN(頭から2桁は25の商品)の場合、後から6桁は0に処理してください
  String get handle25JAN => length == 13 && startsWith('25') ? substring(0, 7).padRight(13, '0') : this;
}
