// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fresh_order_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freshOrderServiceHash() => r'89d18ae2cf41210f3021694ce503ef35bbab892b';

/// provider生成コード
///
/// Copied from [freshOrderService].
@ProviderFor(freshOrderService)
final freshOrderServiceProvider = Provider<FreshOrderService>.internal(
  freshOrderService,
  name: r'freshOrderServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshOrderServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FreshOrderServiceRef = ProviderRef<FreshOrderService>;
String _$listHolidaysHash() => r'a580883869c8198aa6b328d6b604593f960c47a5';

/// 祝日flagリストを取得
///
/// Copied from [listHolidays].
@ProviderFor(listHolidays)
final listHolidaysProvider = AutoDisposeFutureProvider<List<bool>>.internal(
  listHolidays,
  name: r'listHolidaysProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$listHolidaysHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ListHolidaysRef = AutoDisposeFutureProviderRef<List<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
