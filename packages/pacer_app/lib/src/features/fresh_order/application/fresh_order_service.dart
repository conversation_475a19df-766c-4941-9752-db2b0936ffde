import 'dart:developer';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/fresh_order_repository.dart';
import '../domain/fresh_order.dart';

part 'fresh_order_service.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
FreshOrderService freshOrderService(FreshOrderServiceRef ref) {
  return FreshOrderService(ref);
}

/// 生鮮発注サービス
class FreshOrderService {
  /// コンストラクタ関数
  const FreshOrderService(this.ref);

  /// riverpod ref
  final Ref ref;

  FreshOrderRepository get _repository => ref.read(freshOrderRepositoryProvider);

  /// 製品および注文情報の取得
  Future<FreshOrder> getFreshOrder({required ProductCode productCode}) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.getFreshOrder(
      storeCode: store.code,
      caller: caller,
      productCode: productCode,
    );
  }

  /// 発注数登録（更新）
  Future<FreshOrder> updateOrderAmount({
    required ProductCode productCode,
    required Order order,
    required int dayIndex,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;

    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final store = caller.clockInStore;

    return _repository.updateOrderAmount(
      storeCode: store.code,
      caller: caller,
      productCode: productCode,
      order: order,
      dayIndex: dayIndex,
    );
  }
}

/// 祝日flagリストを取得
@Riverpod()
Future<List<bool>> listHolidays(
  ListHolidaysRef ref,
) {
  ref
    ..onCancel(() => log('cancel: listHolidays'))
    ..onResume(() => log('resume: listHolidays'))
    ..onDispose(() => log('dispose: listHolidays'));

  final caller = ref.read(authRepositoryProvider).currentUser;

  if (caller == null) {
    throw ParseAuthFailure(caller.toString());
  }

  final store = caller.clockInStore;

  final repository = ref.read(freshOrderRepositoryProvider);

  return repository.listHolidays(
    storeCode: store.code,
    caller: caller,
  );
}
