Legend: {
  Type: {
    Widget.shape: circle
    Provider.shape: rectangle
  }
  Arrows: {
    "." -> "..": read: {style.stroke-dash: 4}
    "." -> "..": listen
    "." -> "..": watch: {style.stroke-width: 4}
  }
}

freshOrderControllerProvider: "freshOrderControllerProvider"
freshOrderControllerProvider.shape: rectangle
freshOrderControllerProvider.tooltip: "生鮮発注コントローラ"
scanCodeProvider: "scanCodeProvider"
scanCodeProvider.shape: rectangle
scanCodeProvider.tooltip: "カメラやバーコードスキャナでスキャンしたコードの状態"
fetchWeathersProvider: "fetchWeathersProvider"
fetchWeathersProvider.shape: rectangle
fetchWeathersProvider.tooltip: "店舗の天気予報を取得する 2時間キャッシュ"
FreshOrderPage.shape: circle
FreshOrderPage.tooltip: "生鮮発注画面"
WeatherTable.shape: circle
WeatherTable.tooltip: "天気予報を表示する"
InputOrderTable.shape: circle
InputOrderTable.tooltip: "発注数入力表示"
_ItemValue.shape: circle
_ScanField.shape: circle

freshOrderControllerProvider -> FreshOrderPage: {style.stroke-width: 4}
scanCodeProvider -> FreshOrderPage
freshOrderControllerProvider -> FreshOrderPage
freshOrderControllerProvider -> FreshOrderPage: {style.stroke-dash: 4}
freshOrderControllerProvider -> FreshOrderPage: {style.stroke-dash: 4}
fetchWeathersProvider -> WeatherTable: {style.stroke-width: 4}
fetchWeathersProvider -> InputOrderTable: {style.stroke-width: 4}
freshOrderControllerProvider -> _ItemValue: {style.stroke-dash: 4}
freshOrderControllerProvider -> _ScanField: {style.stroke-dash: 4}
