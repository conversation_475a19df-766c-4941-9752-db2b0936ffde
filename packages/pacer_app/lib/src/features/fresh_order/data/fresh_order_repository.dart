import 'package:grpc/grpc.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shinise_core_client/fresh_order/v1/fresh_order.pbgrpc.dart';

import '../../../constants/environment.dart';
import '../../../exceptions/app_exception.dart';
import '../../authentication/domain/app_user.dart';
import '../domain/fresh_order.dart';
import 'handle_grpc_error.dart';

part 'fresh_order_repository.g.dart';

/// provider生成コード
@Riverpod(keepAlive: true)
FreshOrderRepository freshOrderRepository(FreshOrderRepositoryRef ref) => FreshOrderRepository();

/// 生鮮発注リホシトリ
class FreshOrderRepository {
  /// コンストラクタ関数
  FreshOrderRepository();

  static const _timeout = Duration(seconds: 20);
  final _uri = Env.getApiBaseUrl();
  final _callOptions = CallOptions(timeout: _timeout);

  /// 製品および注文情報の取得
  Future<FreshOrder> getFreshOrder({
    required String storeCode,
    required AppUser? caller,
    required ProductCode productCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetProductRequest(storeCode: storeCode, productCode: productCode.code);

    try {
      final stub = FreshOrderServiceClient(
        channel,
        options: await _buildCallOptions(caller),
      );
      final response = await stub.getProduct(request);

      if (response.code != '000') {
        throw UnknownException(response.message);
      }

      if (response.hasProductCode() == false) {
        throw FreshProductNotFoundException();
      }
      if (response.hasCommonResponse() == false) {
        throw FreshProductNotFoundException();
      }
      if (response.commonResponse.hasTable0() == false) {
        throw FreshProductNotFoundException();
      }

      return FreshOrder.fromGrpc(
        ProductCode.parsed(response.productCode),
        response.commonResponse,
      );
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  /// 発注数登録（更新）
  Future<FreshOrder> updateOrderAmount({
    required String storeCode,
    required AppUser? caller,
    required ProductCode productCode,
    required Order order,
    required int dayIndex,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final orderData = SetOrderNumRequest_OrderData(flight: order.bin);
    final orderQuantityString = order.quantity?.toString() ?? '';
    switch (dayIndex) {
      case 8:
        orderData.numberOfOrderEight = orderQuantityString;
      case 9:
        orderData.numberOfOrderNine = orderQuantityString;
      case 10:
        orderData.numberOfOrderTen = orderQuantityString;
      case 11:
        orderData.numberOfOrderEleven = orderQuantityString;
      case 12:
        orderData.numberOfOrderTwelve = orderQuantityString;
      case 13:
        orderData.numberOfOrderThirteen = orderQuantityString;
      case 14:
        orderData.numberOfOrderFourteen = orderQuantityString;
    }

    final request = SetOrderNumRequest(
      storeCode: storeCode,
      productCodeOld: productCode.code,
      productCodeNew: productCode.code,
      orderData: [orderData],
    );

    try {
      final stub = FreshOrderServiceClient(
        channel,
        options: await _buildCallOptions(caller),
      );
      final response = await stub.setOrderNum(request);

      return switch (response) {
        SetOrderNumResponse(code: == '000') => FreshOrder.fromGrpc(productCode, response.commonResponse),
        _ => throw UnknownException(response.message),
      };
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }

  Future<CallOptions> _buildCallOptions(AppUser? user) async {
    if (user == null) throw const GrpcError.unauthenticated('ユーザー情報が不正です');

    final certificate = user.certificate;
    if (certificate == null) {
      throw const GrpcError.unauthenticated('認証情報が設定されていません');
    }
    final ip = await NetworkInfo().getWifiIP();
    return _callOptions.mergedWith(
      CallOptions(
        metadata: <String, String>{
          'certificate': certificate.certificate,
          'certificate_key': certificate.certificateKey,
          'user_id': user.userID,
          'user_code': user.userCode,
          'ip': ip ?? '',
          'logmac_address': '172_17_10_227',
        },
      ),
    );
  }

  /// 祝日flagリストを取得
  Future<List<bool>> listHolidays({
    required AppUser? caller,
    required String storeCode,
  }) async {
    final channel = ClientChannel(_uri.host, port: _uri.port);

    final request = GetSystemDateRequest(storeCode: storeCode);

    try {
      final stub = FreshOrderServiceClient(
        channel,
        options: await _buildCallOptions(caller),
      );
      final response = await stub.getSystemDate(request);
      return switch (response) {
        GetSystemDateResponse(code: != '000') => throw UnknownException(response.message),
        _ => response.table2.map((e) => e.dayFlag).toList(),
      };
    } catch (e, stack) {
      Error.throwWithStackTrace(
        switch (e) {
          AppException() => e,
          GrpcError() => e.handleGrpcError(),
          _ => UnknownException(e.toString()),
        },
        stack,
      );
    } finally {
      await channel.shutdown();
    }
  }
}
