// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fresh_order_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freshOrderRepositoryHash() => r'57ac5c253519b732dbe778cea0305517ac633ef5';

/// provider生成コード
///
/// Copied from [freshOrderRepository].
@ProviderFor(freshOrderRepository)
final freshOrderRepositoryProvider = Provider<FreshOrderRepository>.internal(
  freshOrderRepository,
  name: r'freshOrderRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshOrderRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FreshOrderRepositoryRef = ProviderRef<FreshOrderRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
