import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../common_widgets/pacer_app_bar.dart';
import '../../../exceptions/app_exception.dart';
import '../../../localization/app_localizations_context.dart';
import '../../../themes/app_color_scheme.dart';
import '../../../utils/adaptive_number_input_type.dart';
import '../../app/application/global_loading_service.dart';
import '../../device/application/pacer_service.dart';
import '../../device/presentation/scan_window.dart';
import '../application/fresh_order_service.dart';
import '../domain/fresh_order.dart';
import 'common_widgets/fresh_product_table.dart';
import 'common_widgets/input_order_table.dart';
import 'common_widgets/two_week_table.dart';
import 'common_widgets/weather_table.dart';
import 'fresh_order_controller.dart';

/// 生鮮発注画面
class FreshOrderPage extends HookConsumerWidget {
  /// コンストラクタ関数
  const FreshOrderPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final editingDialogState = ref.watch(freshOrderDialogContentStateProvider);
    final freshOrder = ref.watch(freshOrderControllerProvider);

    final updateFreshOrder = ref.watch(updateFreshOrderControllerProvider);

    final listHolidaysState = ref.watch(listHolidaysProvider);
    final isShowingDialog = useState(false);

    void getFreshOrder(String value) {
      if (isShowingDialog.value || freshOrder.isLoading || listHolidaysState.isLoading || updateFreshOrder.isLoading) {
        return;
      }

      final janCode = ProductCode.parsed(value);
      ref.read(globalLoadingServiceProvider.notifier).wrap(
            ref.read(freshOrderControllerProvider.notifier).getFreshOrder(janCode),
          );
    }

    ref
      ..listen(freshOrderDialogContentStateProvider, (_, state) {
        if (state != null) {
          isShowingDialog.value = true;
        }
      })
      ..listen(
        scanCodeProvider,
        (_, state) {
          if (state case AsyncData(:final value)) {
            getFreshOrder(value);
          }
        },
      )
      ..listen(
        freshOrderControllerProvider,
        (_, state) {
          switch (state) {
            case AsyncData(:final value) when value?.recommendOrders.isEmpty ?? true:
              {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    backgroundColor: Theme.of(context).colorScheme.error,
                    content: Text(context.loc.productOutOfOrder),
                  ),
                );
              }
            case AsyncLoading():
              {}
            case AsyncError():
              {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    backgroundColor: Theme.of(context).colorScheme.error,
                    content: Text(context.loc.productOutOfOrder),
                  ),
                );
              }
            case AsyncData():
              {}
          }
        },
      )
      ..listen(updateFreshOrderControllerProvider, (previous, next) {
        switch (next) {
          case AsyncError(:final error):
            {
              final errorMessage = switch (error) {
                FreshOrderQuantityOverException => context.loc.quantityTooMany,
                FreshOrderQuantityNotMultiplesException => context.loc.quantityNotMultiples,
                AppException(:final message) => message,
                _ => error.toString(),
              };
              _showErrorMessage(ref, errorMessage);
            }
          case _:
            {}
        }
      });

    Future<void> updateNumber(int? quantity) async {
      if (quantity == null) {
        return;
      }
      final updateDetail = ref.read(freshOrderDialogContentStateProvider);
      if (updateDetail == null || quantity == updateDetail.quantity) {
        return;
      }
      await ref.read(globalLoadingServiceProvider.notifier).wrap(
            ref.read(updateFreshOrderControllerProvider.notifier).updateOrderAmount(
                  productCode: updateDetail.productCode,
                  quantity: quantity,
                  bin: updateDetail.bin,
                  dayIndex: updateDetail.dayIndex,
                  orderUnit: updateDetail.orderUnit,
                ),
          );
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop || ref.watch(globalLoadingServiceProvider) || updateFreshOrder.isLoading) return;
        ref.context.pop();
      },
      child: Stack(
        children: [
          GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: Scaffold(
              appBar: PacerAppBar(
                context: context,
                title: Text(context.loc.freshOrder),
              ),
              body: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: switch (listHolidaysState) {
                    AsyncLoading() => const Center(child: CircularProgressIndicator()),
                    AsyncData() => _FreshOrderBody(freshOrder: freshOrder.value),
                    AsyncError(:final error) => Text(
                        (error is AppException) ? error.message : error.toString(),
                      ),
                  },
                ),
              ),
              bottomNavigationBar: BottomAppBar(
                child: Row(
                  children: [
                    TextButton(
                      onPressed: () => context.pop(),
                      child: Text(
                        context.loc.end,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                      ),
                    ),
                    const Spacer(),
                  ],
                ),
              ),
              floatingActionButton: switch (listHolidaysState) {
                AsyncError() || AsyncLoading() => null,
                AsyncData() => ScanFloatingIconButton(
                    onScan: (value) => {
                      getFreshOrder(value),
                    },
                  ),
              },
              floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
              resizeToAvoidBottomInset: false,
            ),
          ),
          if (editingDialogState != null)
            _FullScreenEditingDialog(
              onDismissRequest: () async {
                ref.read(freshOrderDialogContentStateProvider.notifier).clear();
                isShowingDialog.value = false;
              },
              onSubmitted: (quantity) async {
                primaryFocus?.unfocus();
                await updateNumber(int.tryParse(quantity));
                ref.read(freshOrderDialogContentStateProvider.notifier).clear();
                isShowingDialog.value = false;
              },
            ),
        ],
      ),
    );
  }

  /// エラーdialog
  void _showErrorMessage(WidgetRef ref, String? message) {
    showDialog<void>(
      context: ref.context,
      builder: (context) => AlertDialog(
        title: Text(
          message ?? '失敗しました',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Theme.of(context).colorScheme.error),
        ),
        actions: [
          OutlinedButton(
            child: const Text('OK'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );
  }
}

class _FreshOrderBody extends ConsumerWidget {
  const _FreshOrderBody({this.freshOrder});
  final FreshOrder? freshOrder;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FreshProductTable(product: freshOrder?.product),
        const SizedBox(height: 8),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.only(bottom: 32), // for floating button
            child: Column(
              children: [
                if (freshOrder case final FreshOrder nonNullValue)
                  InputOrderTable(
                    productCode: nonNullValue.product.code,
                    orders: nonNullValue.recommendOrders,
                    startDate: DateTime.now(),
                    orderUnit: nonNullValue.product.orderUnit,
                    bin: nonNullValue.bin,
                  ),
                if (freshOrder case final FreshOrder nonNullValue)
                  TwoWeekTable(
                    title: (nonNullValue.bin.isEmpty
                        ? '・${context.loc.orderHistory}'
                        : '・${nonNullValue.bin}便${context.loc.orderHistory}'),
                    values: nonNullValue.orderQuantities.map((e) => e).toList(),
                  ),
                TwoWeekTable(
                  title: context.loc.salesHistory,
                  values: freshOrder?.transactions.map((e) => e.saleQuantity).toList(),
                  startDate: DateTime.now(),
                ),
                const WeatherTable(),
                TwoWeekTable(
                  title: context.loc.discountHistory,
                  values: freshOrder?.transactions.map((e) => e.discountQuantity).toList(),
                ),
                TwoWeekTable(
                  title: context.loc.discardedHistory,
                  values: freshOrder?.transactions.map((e) => e.discardQuantity).toList(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _FullScreenEditingDialog extends HookConsumerWidget {
  const _FullScreenEditingDialog({
    required this.onDismissRequest,
    required this.onSubmitted,
  });

  final VoidCallback onDismissRequest;

  final void Function(String) onSubmitted;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final alertTextEditingController = useTextEditingController();
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textScheme = theme.textTheme;

    return GestureDetector(
      onTap: () async => onDismissRequest(),
      child: Dialog.fullscreen(
        backgroundColor: Colors.transparent.withOpacity(0.5),
        child: AlertDialog(
          title: Text(
            '数量修正',
            style: textScheme.titleLarge,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                autofocus: true,
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.all(10),
                  prefixIcon: const Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 2,
                    ),
                  ),
                  prefixIconConstraints: const BoxConstraints(),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: colorScheme.line,
                    ),
                  ),
                ),
                maxLength: 4,
                style: textScheme.titleLarge,
                controller: alertTextEditingController,
                keyboardType: TextInputType.number.withEnter(),
                autovalidateMode: AutovalidateMode.onUserInteraction,
                onFieldSubmitted: onSubmitted,
              ),
              const Gap(16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton(
                    onPressed: () async => onDismissRequest(),
                    child: Text(
                      '取消',
                      style: textScheme.titleLarge?.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () async => onSubmitted(alertTextEditingController.text),
                    child: Text(
                      '確定',
                      style: textScheme.titleLarge?.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
