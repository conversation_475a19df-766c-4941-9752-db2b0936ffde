import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../localization/app_localizations_context.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../../../utils/adaptive_number_input_type.dart';
import '../../../app/application/global_loading_service.dart';
import '../../domain/fresh_order.dart';
import '../fresh_order_controller.dart';

/// 生鮮食品データの表示
class FreshProductTable extends StatelessWidget {
  /// コンストラクタ関数
  const FreshProductTable({super.key, this.product});

  /// Fresh product
  final FreshProduct? product;

  @override
  Widget build(BuildContext context) {
    return Table(
      columnWidths: const {
        0: FractionColumnWidth(0.25),
        1: FractionColumnWidth(0.75),
      },
      border: TableBorder.all(
        color: Theme.of(context).colorScheme.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        TableRow(
          children: [
            TableCell(
              verticalAlignment: TableCellVerticalAlignment.middle,
              child: _ItemHeader(context.loc.jan),
            ),
            _ScanField(code: product?.code.code),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.freshProductName),
            _ItemValue(product?.name),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.specification),
            Row(
              children: [
                Expanded(child: _ItemValue(product?.specification)),
                Expanded(
                  child: _ItemHeader(context.loc.orderClose, needBorder: true),
                ),
                Expanded(child: _ItemValue(product?.orderCloseTime)),
              ],
            ),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.oneShot),
            Row(
              children: [
                Expanded(child: _ItemValue(product?.orderUnit.toString())),
                Expanded(
                  child: _ItemHeader(context.loc.quantity, needBorder: true),
                ),
                Expanded(
                  child: _ItemValue(product?.innerCaseQuantity.toString()),
                ),
              ],
            ),
          ],
        ),
        TableRow(
          children: [
            _ItemHeader(context.loc.salePrice),
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    child: _ItemValue(product?.salePrice.toStringAsFixed(0)),
                  ),
                  Expanded(
                    child: _ItemHeader(
                      context.loc.originPrice,
                      needBorder: true,
                    ),
                  ),
                  Expanded(
                    child: _ItemValue(product?.price.toStringAsFixed(0)),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _ItemHeader extends StatelessWidget {
  const _ItemHeader(this.text, {this.needBorder = false});

  final String text;
  final bool needBorder;

  @override
  Widget build(BuildContext context) {
    final border = BorderSide(color: Theme.of(context).colorScheme.line);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 1, vertical: 6),
      decoration: needBorder ? BoxDecoration(border: Border(left: border, right: border)) : null,
      child: switch (text.isEmpty) {
        true => const Text(''),
        false => FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              text,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.right,
            ),
          ),
      },
    );
  }
}

class _ItemValue extends StatelessWidget {
  const _ItemValue(this.text);

  final String? text;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        alignment: Alignment.centerLeft,
        child: Text(
          text ?? ' ',
          style: Theme.of(context).textTheme.titleMedium,
        ),
      ),
    );
  }
}

class _ScanField extends HookConsumerWidget {
  const _ScanField({this.code});

  final String? code;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textEditingController = useTextEditingController();

    useEffect(
      () {
        textEditingController.text = code ?? '';
        return;
      },
    );

    return ColoredBox(
      color: Theme.of(context).colorScheme.button,
      child: TextFormField(
        controller: textEditingController,
        keyboardType: TextInputType.number.withEnter(),
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        autovalidateMode: AutovalidateMode.onUserInteraction,
        decoration: const InputDecoration(
          contentPadding: EdgeInsets.symmetric(horizontal: 4),
          border: InputBorder.none,
        ),
        onFieldSubmitted: (code) => ref.read(globalLoadingServiceProvider.notifier).wrap(
              ref.read(freshOrderControllerProvider.notifier).getFreshOrder(ProductCode.parsed(code)),
            ),
      ),
    );
  }
}
