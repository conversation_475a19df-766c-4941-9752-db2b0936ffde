import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../localization/app_localizations_context.dart';
import '../../../../themes/app_color_scheme.dart';

/// 2週間分のデータを表示する共通テーブル
class TwoWeekTable extends ConsumerWidget {
  /// コンストラクタ関数
  TwoWeekTable({
    super.key,
    required this.title,
    List<int?>? values,
    this.startDate,
  }) : values = values == null
            ? List.filled(_numOfDaysInWeek * 2, null)
            : List.generate(
                _numOfDaysInWeek * 2,
                (index) => values.elementAtOrNull(index),
              );

  /// 表のタイトル
  final String title;

  /// 値のリスト
  final List<int?> values;

  /// 開始日
  final DateTime? startDate;

  static const int _numOfDaysInWeek = 7;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 8),
        Text(title, style: Theme.of(context).textTheme.bodyLarge),
        const SizedBox(height: 8),
        Table(
          columnWidths: Map.fromIterables(
            Iterable.generate(_numOfDaysInWeek),
            Iterable.generate(
              _numOfDaysInWeek,
              (_) => const FractionColumnWidth(1 / _numOfDaysInWeek),
            ),
          ),
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          border: TableBorder.all(
            color: Theme.of(context).colorScheme.line,
            borderRadius: const BorderRadius.all(Radius.circular(4)),
          ),
          children: [
            if (startDate case final DateTime nonNullDate)
              TableRow(
                children: List.generate(
                  _numOfDaysInWeek,
                  (i) => _ItemDate(nonNullDate.add(Duration(days: i))),
                ),
              ),
            TableRow(
              children: values.whereIndexed((index, element) => index < _numOfDaysInWeek).map(_ItemValue.new).toList(),
            ),
            TableRow(
              children: values.whereIndexed((index, element) => index >= _numOfDaysInWeek).map(_ItemValue.new).toList(),
            ),
          ],
        ),
      ],
    );
  }
}

class _ItemDate extends StatelessWidget {
  const _ItemDate(this.date);

  final DateTime date;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Text(
        DateFormat.E(context.loc.localeName).format(date),
        textAlign: TextAlign.center,
        style: Theme.of(context).textTheme.titleMedium,
      ),
    );
  }
}

class _ItemValue extends StatelessWidget {
  const _ItemValue(this.value);

  final int? value;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          switch (value) {
            null || 0 => ' ', // for fitted box working
            _ => value.toString(),
          },
          textAlign: TextAlign.right,
          style: Theme.of(context).textTheme.titleMedium,
        ),
      ),
    );
  }
}
