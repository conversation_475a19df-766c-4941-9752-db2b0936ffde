import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../localization/app_localizations_context.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../../branch/application/branch_service.dart';
import '../../application/fresh_order_service.dart';
import '../../domain/fresh_order.dart';
import '../../domain/fresh_order_update_detail.dart';
import '../fresh_order_controller.dart';

/// 発注数入力表示
/// 発注数01~07過去一週間の発注です。
/// 発注数08~14当日から未来一週間の発注です。
/// 当日+未来一週間の発注数更新ができる。
class InputOrderTable extends ConsumerWidget {
  /// コンストラクタ関数
  InputOrderTable({
    super.key,
    required this.productCode,
    required List<Order> orders,
    required this.startDate,
    required this.orderUnit,
    required this.bin,
  }) : orders = List.generate(
          _numOfDaysInWeek * 2,
          (index) => orders.elementAtOrNull(index),
        );

  /// 商品コード
  final ProductCode productCode;

  /// 今後 2 週間の注文数量
  final List<Order?> orders;

  /// 開始日 (通常は今日)
  final DateTime startDate;

  /// 発注数量は発注単位の整数倍である必要があります
  final int orderUnit;

  /// 推奨発注数の便
  final String bin;

  static const int _numOfDaysInWeek = 7;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dates = ref.watch(fetchWeathersProvider);
    final holidayList = ref.watch(listHolidaysProvider).value ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        Text(
          bin.isEmpty ? '・${context.loc.enterOrder}' : '・$bin便${context.loc.enterOrder}',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        const SizedBox(height: 8),
        Table(
          columnWidths: Map.fromIterables(
            Iterable.generate(_numOfDaysInWeek),
            Iterable.generate(
              _numOfDaysInWeek,
              (_) => const FractionColumnWidth(1 / _numOfDaysInWeek),
            ),
          ),
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          border: TableBorder.all(
            color: Theme.of(context).colorScheme.line,
            borderRadius: const BorderRadius.all(Radius.circular(4)),
          ),
          children: [
            TableRow(
              children: switch (dates) {
                AsyncData() => List.generate(
                    _numOfDaysInWeek,
                    (i) => _ItemDate(
                      date: startDate.add(Duration(days: i)),
                      isHoliday: i < holidayList.length && holidayList[i],
                    ),
                  ),
                _ => List.generate(
                    _numOfDaysInWeek,
                    (i) => const SizedBox.shrink(),
                  ),
              },
            ),
            TableRow(
              children: orders
                  .whereIndexed((index, element) => index < _numOfDaysInWeek)
                  .mapIndexed(
                    (i, e) => _ItemValue(
                      productCode,
                      e?.quantity,
                      e?.bin ?? '',
                      i,
                      orderUnit,
                      canUpdate: e?.canUpdate ?? false,
                    ),
                  )
                  .toList(),
            ),
            TableRow(
              children: orders
                  .whereIndexed((index, element) => index >= _numOfDaysInWeek)
                  .mapIndexed(
                    (i, e) => _ItemValue(
                      productCode,
                      e?.quantity,
                      e?.bin ?? '',
                      i + _numOfDaysInWeek,
                      orderUnit,
                      canEdit: true,
                      canUpdate: e?.canUpdate ?? false,
                    ),
                  )
                  .toList(),
            ),
          ],
        ),
      ],
    );
  }
}

class _ItemDate extends StatelessWidget {
  const _ItemDate({required this.date, this.isHoliday = false});

  final DateTime date;

  final bool isHoliday;

  @override
  Widget build(BuildContext context) {
    final texts = Theme.of(context).textTheme;
    final colors = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(1),
      constraints: const BoxConstraints(minHeight: 40),
      child: ColoredBox(
        color: isHoliday ? colors.error : colors.surface,
        child: Center(
          child: Text(
            DateFormat.E(context.loc.localeName).format(date),
            textAlign: TextAlign.center,
            style: texts.titleMedium,
          ),
        ),
      ),
    );
  }
}

class _ItemValue extends HookConsumerWidget {
  const _ItemValue(
    this.productCode,
    this.value,
    this.bin,
    this.index,
    this.orderUnit, {
    this.canEdit = false,
    this.canUpdate = false,
  });

  final ProductCode productCode;
  final int? value;
  final int index;
  final bool canEdit;
  final bool canUpdate;
  final String bin;
  final int orderUnit;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: () {
        if (canEdit && canUpdate) {
          final freshOrderUpdateDetail = FreshOrderUpdateDetail(
            productCode: productCode,
            bin: bin,
            dayIndex: index + 1,
            orderUnit: orderUnit,
            quantity: value,
          );
          ref.read(freshOrderDialogContentStateProvider.notifier).updateWith(freshOrderUpdateDetail);
        }
      },
      child: Container(
        height: 40,
        color: switch ((canUpdate, canEdit)) {
          // 第1行は過去一週間の発注です。LockFlgはなんでも更新不可
          // 第1行でLockFlgで色判断する
          // 2,3の色：GRAY
          // 2,3以外の色：blue
          (true, false) => Theme.of(context).colorScheme.sub,
          (true, true) => Theme.of(context).colorScheme.button,
          _ => Theme.of(context).colorScheme.disabled,
        },
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: Center(
          child: Text(value?.toString() ?? ''),
        ),
      ),
    );
  }
}
