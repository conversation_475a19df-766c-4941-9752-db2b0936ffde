import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../localization/app_localizations_context.dart';
import '../../../../themes/app_color_scheme.dart';
import '../../../branch/application/branch_service.dart';
import '../../../branch/domain/weather.dart';

/// 天気予報を表示する
class WeatherTable extends ConsumerWidget {
  /// コンストラクタ関数
  const WeatherTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dates = ref.watch(fetchWeathersProvider);

    return switch (dates) {
      AsyncLoading() => const Center(child: CircularProgressIndicator()),
      AsyncError() => const SizedBox.shrink(),
      AsyncData(:final value) => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 8),
            Text(
              context.loc.weeklyWeatherForecast,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 8),
            _TableContent(systemDates: value),
          ],
        ),
    };
  }
}

class _TableContent extends StatelessWidget {
  _TableContent({required List<StoreWeather> systemDates})
      : systemDates = List.generate(
          _numOfDaysInWeek,
          (index) => index < systemDates.length ? systemDates[index] : null,
        );

  static const int _numOfDaysInWeek = 7;
  final List<StoreWeather?> systemDates;

  @override
  Widget build(BuildContext context) {
    return Table(
      columnWidths: Map.fromIterables(
        Iterable.generate(_numOfDaysInWeek),
        Iterable.generate(
          _numOfDaysInWeek,
          (_) => const FractionColumnWidth(1 / _numOfDaysInWeek),
        ),
      ),
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      border: TableBorder.all(
        color: Theme.of(context).colorScheme.line,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      children: [
        TableRow(
          children: systemDates.map((e) => _ItemIcon(e?.weather.iconPath())).toList(),
        ),
        TableRow(
          children: systemDates
              .map(
                (e) => e == null ? const SizedBox.shrink() : _ItemTemperature(e.minTemp, e.maxTemp),
              )
              .toList(),
        ),
      ],
    );
  }
}

class _ItemIcon extends StatelessWidget {
  const _ItemIcon(this.iconPath);

  final String? iconPath;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: switch (iconPath) {
        final iconPath? => Image.asset(iconPath),
        _ => const SizedBox.shrink(),
      },
    );
  }
}

class _ItemTemperature extends StatelessWidget {
  const _ItemTemperature(this.min, this.max);

  final String? min;
  final String? max;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          '$min/$max',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
