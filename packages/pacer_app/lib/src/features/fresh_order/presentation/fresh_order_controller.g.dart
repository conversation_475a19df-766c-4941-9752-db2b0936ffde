// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fresh_order_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freshOrderControllerHash() => r'4804ee03d058e3ef04390d2a1b28c37d46b44047';

/// 生鮮発注コントローラ
///
/// Copied from [FreshOrderController].
@ProviderFor(FreshOrderController)
final freshOrderControllerProvider = AutoDisposeAsyncNotifierProvider<FreshOrderController, FreshOrder?>.internal(
  FreshOrderController.new,
  name: r'freshOrderControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshOrderControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshOrderController = AutoDisposeAsyncNotifier<FreshOrder?>;
String _$updateFreshOrderControllerHash() => r'78641f147b6b955c7b89fc89d6b9aac30b309cb1';

/// 更新Provider
///
/// Copied from [UpdateFreshOrderController].
@ProviderFor(UpdateFreshOrderController)
final updateFreshOrderControllerProvider =
    AutoDisposeAsyncNotifierProvider<UpdateFreshOrderController, FreshOrder?>.internal(
  UpdateFreshOrderController.new,
  name: r'updateFreshOrderControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$updateFreshOrderControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateFreshOrderController = AutoDisposeAsyncNotifier<FreshOrder?>;
String _$freshOrderDialogContentStateHash() => r'fb8248281ca1f45fed59ef2522886a22cb84cafa';

/// OverlayPortalControllerに表示する情報を管理する
///
/// Copied from [FreshOrderDialogContentState].
@ProviderFor(FreshOrderDialogContentState)
final freshOrderDialogContentStateProvider =
    AutoDisposeNotifierProvider<FreshOrderDialogContentState, FreshOrderUpdateDetail?>.internal(
  FreshOrderDialogContentState.new,
  name: r'freshOrderDialogContentStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshOrderDialogContentStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreshOrderDialogContentState = AutoDisposeNotifier<FreshOrderUpdateDetail?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
