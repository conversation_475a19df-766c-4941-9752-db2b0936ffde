import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../exceptions/app_exception.dart';
import '../application/fresh_order_service.dart';
import '../domain/fresh_order.dart';
import '../domain/fresh_order_update_detail.dart';

part 'fresh_order_controller.g.dart';

/// 生鮮発注コントローラ
@riverpod
class FreshOrderController extends _$FreshOrderController {
  @override
  FutureOr<FreshOrder?> build() {
    return null;
  }

  /// 製品および注文情報の取得
  Future<void> getFreshOrder(ProductCode? productCode) async {
    if (productCode == null || productCode.code.isEmpty) {
      state = const AsyncValue.data(null);
      return;
    }
    state = const AsyncLoading();
    state = await AsyncValue.guard(
      () => ref.read(freshOrderServiceProvider).getFreshOrder(productCode: productCode),
    );
  }

  /// 選択中返品Codeを更新
  Future<void> updateFreshOrder(FreshOrder? freshOrder) async {
    state = AsyncData(freshOrder);
  }
}

/// 更新Provider
@riverpod
class UpdateFreshOrderController extends _$UpdateFreshOrderController {
  static const int _maxValue = 999;

  /// 推奨発注数の便のdefault　value
  static const defaultBin = '9999';
  @override
  FutureOr<FreshOrder?> build() {
    return null;
  }

  /// 発注数登録（更新）
  Future<void> updateOrderAmount({
    required ProductCode productCode,
    required int quantity,
    required String bin,
    required int dayIndex,
    required int orderUnit,
  }) async {
    if (quantity > _maxValue) {
      state = AsyncError(FreshOrderQuantityOverException(), StackTrace.current);

      return;
    }
    if (quantity % orderUnit != 0) {
      state = AsyncError(
        FreshOrderQuantityNotMultiplesException,
        StackTrace.current,
      );
      return;
    }

    state = const AsyncLoading();
    try {
      final freshOrder = await ref.read(freshOrderServiceProvider).updateOrderAmount(
            productCode: productCode,
            order: Order(
              quantity: quantity,
              bin: bin.isEmpty ? defaultBin : bin,
            ),
            dayIndex: dayIndex,
          );

      if (freshOrder.recommendOrders.isEmpty) {
        state = AsyncError(
          FreshOrderQuantityNotMultiplesException,
          StackTrace.current,
        );
      } else {
        await ref.read(freshOrderControllerProvider.notifier).updateFreshOrder(freshOrder);
        state = AsyncData(freshOrder);
      }
    } on Exception catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }
}

/// OverlayPortalControllerに表示する情報を管理する
@riverpod
class FreshOrderDialogContentState extends _$FreshOrderDialogContentState {
  @override
  FreshOrderUpdateDetail? build() => null;

  /// エラーダイアログの内容を更新する
  void updateWith(FreshOrderUpdateDetail freshOrderUpdateDetail) {
    log('update freshOrderUpdateDetail: $freshOrderUpdateDetail');
    state = freshOrderUpdateDetail;
  }

  /// エラーダイアログの内容を初期化する
  void clear() => ref.invalidateSelf();
}
