// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fresh_sale_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freshSaleServiceHash() => r'478e39da144c22ee054b9443b58f1fe1fbf24e00';

/// 見切り
///
/// Copied from [freshSaleService].
@ProviderFor(freshSaleService)
final freshSaleServiceProvider = Provider<FreshSaleService>.internal(
  freshSaleService,
  name: r'freshSaleServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$freshSaleServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FreshSaleServiceRef = ProviderRef<FreshSaleService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
