// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fresh_sale_product_management_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$freshSaleProductManagementServiceHash() => r'2db563b105f4863a8e56e6121a387afe4c8a3a61';

/// (電子棚札)生鮮売変サービスのProvider
///
/// Copied from [freshSaleProductManagementService].
@ProviderFor(freshSaleProductManagementService)
final freshSaleProductManagementServiceProvider = Provider<FreshSaleProductManagementService>.internal(
  freshSaleProductManagementService,
  name: r'freshSaleProductManagementServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$freshSaleProductManagementServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FreshSaleProductManagementServiceRef = ProviderRef<FreshSaleProductManagementService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
