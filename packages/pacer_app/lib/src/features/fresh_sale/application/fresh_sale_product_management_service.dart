import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../constants/tax/tax_type.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/price_calculator.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/fresh_sale_product_management_repository.dart';
import '../domain/fresh_sale_confirmation.dart';
import '../domain/product_management/product_management_product_info.dart';
import '../domain/sale_price_update_reason.dart';

part 'fresh_sale_product_management_service.g.dart';

/// (電子棚札)生鮮売変サービスのProvider
@Riverpod(keepAlive: true)
FreshSaleProductManagementService freshSaleProductManagementService(
  FreshSaleProductManagementServiceRef ref,
) {
  return FreshSaleProductManagementService(ref);
}

/// (商品管理)生鮮売変サービス
class FreshSaleProductManagementService with PriceCalculator {
  /// 標準コンストラクタ
  FreshSaleProductManagementService(this.ref);

  /// 引用
  final Ref ref;

  /// (商品管理)売変理由選択一覧
  Future<List<SalePriceUpdateReason>> listSalePriceUpdateReason() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;
    return ref.read(freshSaleProductManagementRepositoryProvider(caller: caller)).listSalePriceUpdateReason(
          storeCode: storeCode,
        );
  }

  /// (商品管理)生鮮売変の商品基本情報取得
  Future<ProductManagementProductInfo> getProductInfo({
    required String productCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }
    final storeCode = caller.clockInStore.code;
    return ref.read(freshSaleProductManagementRepositoryProvider(caller: caller)).getProductInfo(
          storeCode: storeCode,
          productCode: productCode,
        );
  }

  /// (商品管理)生鮮売変のテーブルチェック
  Future<void> checkWorkTableStatus() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;
    return ref.read(freshSaleProductManagementRepositoryProvider(caller: caller)).checkWorkTableStatus(
          storeCode: storeCode,
        );
  }

  /// (商品管理)商品情報の追加処理
  ///
  /// [oldSalePrice] 税区分に対応
  /// [newSalePrice] 税区分に対応
  Future<void> inputProduct({
    required String productCode,
    required int reasonCode,
    required String productName,
    required String specification,
    required String brandName,
    required int oldSalePrice,
    required int newSalePrice,
    required int quantity,
    required TaxType taxType,
    required double taxRate,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;

    final includingTaxOldPrice = switch (taxType) {
      TaxType.exclusive => calculateTaxIncludedPrice(taxRate, oldSalePrice),
      _ => oldSalePrice,
    };
    final excludingTaxOldPrice = switch (taxType) {
      TaxType.exclusive => oldSalePrice,
      _ => calculateTaxExcludedPrice(taxRate, oldSalePrice),
    };
    final includingTaxNewPrice = switch (taxType) {
      TaxType.exclusive => calculateTaxIncludedPrice(taxRate, newSalePrice),
      _ => newSalePrice,
    };
    final excludingTaxNewPrice = switch (taxType) {
      TaxType.exclusive => newSalePrice,
      _ => calculateTaxExcludedPrice(taxRate, newSalePrice),
    };
    return ref.read(freshSaleProductManagementRepositoryProvider(caller: caller)).inputProduct(
          storeCode: storeCode,
          productCode: productCode,
          reasonCode: reasonCode,
          productName: productName,
          specification: specification,
          brandName: brandName,
          includingTaxOldPrice: includingTaxOldPrice,
          excludingTaxOldPrice: excludingTaxOldPrice,
          includingTaxNewPrice: includingTaxNewPrice,
          excludingTaxNewPrice: excludingTaxNewPrice,
          quantity: quantity,
          taxType: taxType,
          taxRate: taxRate,
        );
  }

  /// (商品管理)登録データ取得
  Future<List<FreshSaleConfirmationProduct>> listConfirmationPendingList({
    required int reasonCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;
    return ref.read(freshSaleProductManagementRepositoryProvider(caller: caller)).listConfirmationPendingList(
          storeCode: storeCode,
          reasonCode: reasonCode,
        );
  }

  /// 一括売変の伝票作成
  /// 失敗した場合、エラーが発生します。
  Future<void> confirmProducts({
    required int reasonCode,
    required int confirmCount,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;
    return ref.read(freshSaleProductManagementRepositoryProvider(caller: caller)).confirmProducts(
          storeCode: storeCode,
          reasonCode: reasonCode,
          confirmCount: confirmCount,
        );
  }

  /// データを削除する
  Future<void> deleteInputProduct({
    required int reasonCode,
    required String productCode,
    required int includingTaxOldSalePrice,
    required int includingTaxNewSalePrice,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;

    return ref.read(freshSaleProductManagementRepositoryProvider(caller: caller)).deleteInputProduct(
          storeCode: storeCode,
          reasonCode: reasonCode,
          productCode: productCode,
          oldSalePrice: includingTaxOldSalePrice,
          newSalePrice: includingTaxNewSalePrice,
        );
  }

  /// 数量を更新
  Future<void> updateQuantity({
    required int reasonCode,
    required String productCode,
    required int includingTaxOldSalePrice,
    required int includingTaxNewSalePrice,
    required int newQuantity,
    required TaxType taxType,
    required double taxRate,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;

    return ref.read(freshSaleProductManagementRepositoryProvider(caller: caller)).updateQuantity(
          storeCode: storeCode,
          reasonCode: reasonCode,
          productCode: productCode,
          oldSalePrice: includingTaxOldSalePrice,
          newSalePrice: includingTaxNewSalePrice,
          newQuantity: newQuantity,
        );
  }
}
