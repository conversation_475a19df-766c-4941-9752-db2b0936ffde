import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../constants/tax/tax_type.dart';
import '../../../exceptions/app_exception.dart';
import '../../../utils/price_calculator.dart';
import '../../authentication/data/auth_repository.dart';
import '../data/fresh_sale_repository.dart';
import '../data/fresh_sale_sqlite_repository.dart';
import '../domain/db/database.dart';
import '../domain/fresh_sale_price_alter_method.dart';
import '../domain/fresh_sale_product.dart';
import '../domain/product_code.dart';

part 'fresh_sale_service.g.dart';

/// 見切り
@Riverpod(keepAlive: true)
FreshSaleService freshSaleService(
  FreshSaleServiceRef ref,
) {
  return FreshSaleService(ref);
}

/// 見切り
class FreshSaleService with PriceCalculator {
  /// 標準コンストラクタ
  FreshSaleService(this.ref);

  /// 引用
  final Ref ref;

  /// 商品JANスキャン-商品情報を取得する(25、28以外JAN）
  Future<FreshSaleProduct> getProductInfo({
    required String productCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;
    final freshSaleRepository = ref.read(freshSaleRepositoryProvider(caller: caller));
    final is25Or28Jan = productCode.startsWith('25') || productCode.startsWith('28');
    final noPriceInProductCode = ProductCode.parseProductCode(productCode).noPriceInProductCode;
    final getProductInfoFuture = is25Or28Jan
        ? freshSaleRepository.getProductInfoWith25Or28Jan(
            storeCode: storeCode,
            productCode: productCode,
            noPriceInProductCode: noPriceInProductCode,
          )
        : freshSaleRepository.getProductInfo(
            storeCode: storeCode,
            productCode: productCode,
          );

    final checkIsFreshFuture = freshSaleRepository.checkIsFresh(
      storeCode: storeCode,
      productCode: productCode,
    );

    /// 販売価格をAPIから取得
    final getOldPriceFuture = freshSaleRepository.getOldPrice(
      storeCode: storeCode,
      productCode: productCode,
    );

    final (productInfo, oldPricePair, isFresh) = await (
      getProductInfoFuture,
      getOldPriceFuture,
      checkIsFreshFuture,
    ).wait;

    return FreshSaleProduct.fromGrpc(
      baseInfo: productInfo,
      isFresh: isFresh,
      includingTaxOldSalePrice: oldPricePair.includingTaxOldPrice,
      excludingTaxOldSalePrice: oldPricePair.excludingTaxOldPrice,
    );
  }

  /// スキャン商品履歴登録
  Future<int> inputProduct({
    required String productCode,
    required String lineCode,
    required String departmentCode,
    required String productName,
    required int oldSalePrice,
    required int newSalePrice,
    required TaxType taxType,
    required double taxRate,
    required int quantity,
    required FreshPriceAlterMethod priceFlag,
    required int reasonCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;

    final sqliteRepository = ref.read(
      freshSaleSQLiteRepositoryProvider(
        storeCode: storeCode,
        userCode: caller.userCode,
      ),
    );

    return sqliteRepository.insertOne(
      productCode: productCode,
      lineCode: lineCode,
      departmentCode: departmentCode,
      productName: productName,
      oldSalePrice: oldSalePrice,
      newSalePrice: newSalePrice,
      taxType: taxType,
      quantity: quantity,
      priceFlag: priceFlag,
      reasonCode: reasonCode,
      taxRate: taxRate,
    );
  }

  /// (值上值下)現在の店舗と現在のユーザーの入力リストを表示します（入力時間の降順でソート）
  Future<List<InputProduct>> listDecreaseOrIncreaseInputProduct() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;
    final sqliteRepository = ref.read(
      freshSaleSQLiteRepositoryProvider(
        storeCode: storeCode,
        userCode: caller.userCode,
      ),
    );
    return sqliteRepository.listAllOfDecreaseAndIncrease();
  }

  /// (電子棚札の一括売変)現在の店舗と現在のユーザーの入力リストを表示します（入力時間の降順でソート）
  Future<List<InputProduct>> listDigitalPriceTagInputProduct({
    required int reasonCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;
    final sqliteRepository = ref.read(
      freshSaleSQLiteRepositoryProvider(
        storeCode: storeCode,
        userCode: caller.userCode,
      ),
    );
    return sqliteRepository.listAllOfDigitalPriceTag(
      reasonCode: reasonCode,
    );
  }

  /// (值上值下)商品一览確認
  Future<void> confirmDecreaseOrIncreaseProducts() async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;
    final sqliteRepository = ref.read(
      freshSaleSQLiteRepositoryProvider(
        storeCode: storeCode,
        userCode: caller.userCode,
      ),
    );
    final freshSaleRepository = ref.read(freshSaleRepositoryProvider(caller: caller));
    final inputProducts = await sqliteRepository.listAllOfDecreaseAndIncrease();

    await freshSaleRepository.confirmProducts(
      storeCode: storeCode,
      inputProducts: inputProducts,
    );
    await sqliteRepository.deleteAll();
  }

  /// (電子棚札の一括売変)商品一览確認
  Future<void> confirmDigitalPriceTagProducts({
    required int reasonCode,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;
    final sqliteRepository = ref.read(
      freshSaleSQLiteRepositoryProvider(
        storeCode: storeCode,
        userCode: caller.userCode,
      ),
    );
    final freshSaleRepository = ref.read(freshSaleRepositoryProvider(caller: caller));
    final inputProducts = await sqliteRepository.listAllOfDigitalPriceTag(
      reasonCode: reasonCode,
    );

    await freshSaleRepository.confirmProducts(
      storeCode: storeCode,
      inputProducts: inputProducts,
    );
    await sqliteRepository.deleteAll();
  }

  /// データを更新する
  Future<void> updateQuantity({
    required PriceModificationJobId id,
    required int quantity,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;

    final sqliteRepository = ref.read(
      freshSaleSQLiteRepositoryProvider(
        storeCode: storeCode,
        userCode: caller.userCode,
      ),
    );

    await sqliteRepository.updateQuantity(id: id, quantity: quantity);
  }

  /// データを削除する
  Future<void> deleteInputProduct({
    required PriceModificationJobId id,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;

    final sqliteRepository = ref.read(
      freshSaleSQLiteRepositoryProvider(
        storeCode: storeCode,
        userCode: caller.userCode,
      ),
    );

    await sqliteRepository.deleteOne(id: id);
  }

  /// IDまたは1つのデータに基づいて
  Future<InputProduct?> getInputProductInfo({
    required PriceModificationJobId id,
  }) async {
    final caller = ref.read(authRepositoryProvider).currentUser;
    if (caller == null) {
      throw ParseAuthFailure(caller.toString());
    }

    final storeCode = caller.clockInStore.code;

    final sqliteRepository = ref.read(
      freshSaleSQLiteRepositoryProvider(
        storeCode: storeCode,
        userCode: caller.userCode,
      ),
    );

    return sqliteRepository.get(id: id);
  }
}
