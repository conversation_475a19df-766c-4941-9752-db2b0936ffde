// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'error_logger.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$errorLoggerHash() => r'9020cd58e56e6c6490fd2ff6cd3b8748bd0165dd';

/// See also [errorLogger].
@ProviderFor(errorLogger)
final errorLoggerProvider = AutoDisposeProvider<ErrorLogger>.internal(
  errorLogger,
  name: r'errorLoggerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$errorLoggerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ErrorLoggerRef = AutoDisposeProviderRef<ErrorLogger>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, inference_failure_on_uninitialized_variable, inference_failure_on_function_return_type, inference_failure_on_untyped_parameter, deprecated_member_use_from_same_package
