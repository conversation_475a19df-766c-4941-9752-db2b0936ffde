/// アプリ独自エラーの定義
/// dataレイヤーで発生したエラーは全てアプリ独自のエラーに変換すること
/// 独自エラーは[AppException]をextendsしたfinalクラスとすること
library;

import 'package:equatable/equatable.dart';

const String _deliveryQuantity = '納品数量';
const String _priceSum = '売価合計金額';
const String _costPriceSum = '原価合計金額';

String _overMaxValueMessage(String maxValue) => '''
最大値$maxValueを超えています。
登録できません！
''';

String _overNormalValueMessage(String normalValue) => '''
基準値$normalValueを超えています。
''';

sealed class AppException implements Exception {
  AppException(this.code, this.message);

  final String code;
  final String message;

  String details() => message;
}

final class AppCustomException extends AppException {
  AppCustomException(super.code, super.message);
}

// TODO: 以下の例外名とコード、メッセージは仮置きであり、エラーが発生するユースケースに合わせて修正すること
final class CancelledException extends AppException {
  CancelledException()
      : super(
          'cancelled',
          'キャンセルされました',
        );
}

// TODO: 以下の例外名とコード、メッセージは仮置きであり、エラーが発生するユースケースに合わせて修正すること
final class PermissionDeniedException extends AppException {
  PermissionDeniedException()
      : super(
          'permission-denied',
          '権限がありません',
        );
}

// TODO: 以下の例外名とコード、メッセージは仮置きであり、エラーが発生するユースケースに合わせて修正すること
final class AbortedException extends AppException {
  AbortedException()
      : super(
          'aborted',
          '処理が中断されました',
        );
}

// TODO: 以下の例外名とコード、メッセージは仮置きであり、エラーが発生するユースケースに合わせて修正すること
final class FailedPreconditionException extends AppException {
  FailedPreconditionException()
      : super(
          'failed-precondition',
          '事前条件が満たされていません',
        );
}

// TODO: 以下の例外名とコード、メッセージは仮置きであり、エラーが発生するユースケースに合わせて修正すること
final class ResourceExhaustedException extends AppException {
  ResourceExhaustedException()
      : super(
          'resource-exhausted',
          'リソースが枯渇しています',
        );
}

// TODO: 以下の例外名とコード、メッセージは仮置きであり、エラーが発生するユースケースに合わせて修正すること
final class GrpcUnimplementedException extends AppException {
  GrpcUnimplementedException()
      : super(
          'grpc-unimplemented',
          '未実装の機能です',
        );
}

// TODO: 以下の例外名とコード、メッセージは仮置きであり、エラーが発生するユースケースに合わせて修正すること
final class AlreadyExistsException extends AppException {
  AlreadyExistsException()
      : super(
          'already-exists',
          '既に存在しています',
        );
}

// TODO: 以下の例外名とコード、メッセージは仮置きであり、エラーが発生するユースケースに合わせて修正すること
final class OutOfRangeException extends AppException {
  OutOfRangeException()
      : super(
          'out-of-range',
          '範囲外です',
        );
}

// TODO: 以下の例外名とコード、メッセージは仮置きであり、エラーが発生するユースケースに合わせて修正すること
final class DataLossException extends AppException {
  DataLossException()
      : super(
          'data-loss',
          'データが失われました',
        );
}

final class FreshProductNotFoundException extends AppException {
  FreshProductNotFoundException()
      : super(
          'fresh-product-not-found',
          '対象商品がありません。',
        );
}

final class AssetNotFoundException extends AppException {
  AssetNotFoundException()
      : super(
          'asset-not-found',
          'この資産Noは登録がありません。',
        );
}

final class TimeOutException extends AppException {
  TimeOutException()
      : super(
          'time-out',
          'リクエストがタイムアウトしました',
        );
}

final class InternalException extends AppException {
  InternalException()
      : super(
          'internal',
          'サーバーエラー',
        );
}

final class UnknownException extends AppException with EquatableMixin {
  UnknownException(this.status) : super('unknown', status);
  final String status;

  @override
  bool? get stringify => true;

  @override
  List<Object?> get props => [status];
}

final class OutofWorkPlanException extends AppException {
  OutofWorkPlanException()
      : super(
          'OutofWorkPlanException',
          '退勤時間を過ぎました。引き続き利用する場合は所属長の残業命令が必要です。',
        );
}

final class ParseAuthFailure extends AppException {
  ParseAuthFailure(this.status)
      : super(
          'parse-auth-failure',
          status,
        );
  final String status;
}

final class ParseOrderFailure extends AppException {
  ParseOrderFailure(this.status)
      : super(
          'parse-order-failure',
          'Could not parse order status: $status',
        );
  final String status;
}

final class ProductNotFoundException extends AppException {
  ProductNotFoundException()
      : super(
          'product-not-found',
          '商品マスタに未登録の商品です。',
        );
}

final class ProductNotRegisteredInMasterByStore extends AppException {
  ProductNotRegisteredInMasterByStore()
      : super(
          'product-not-registered-in-master-by-store',
          '店別マスタに未登録の商品です。',
        );
}

final class WrongProductCodeException extends AppException {
  WrongProductCodeException()
      : super(
          'wrong-product-code',
          '正しい商品コードを入力してください',
        );
}

final class ParseOrderPrintFailure extends AppException {
  ParseOrderPrintFailure(this.status)
      : super(
          'parse-order-print-failure',
          status,
        );
  final String status;
}

final class TimeoutOrderPrintException extends AppException {
  TimeoutOrderPrintException()
      : super(
          'timeout-order-print',
          '印刷がタイムアウトしました',
        );
}

final class ParseWeatherFailure extends AppException {
  ParseWeatherFailure(this.status)
      : super(
          'parse-weather-failure',
          'Could not parse weather status: $status',
        );
  final String status;
}

final class WifiNotFoundException extends AppException {
  WifiNotFoundException()
      : super(
          'wifi-not-found',
          'wifi ipが取得できませんでした',
        );
}

final class ParseBeveragePopFailure extends AppException {
  ParseBeveragePopFailure(this.status)
      : super(
          'parse-beverage-pop-failure',
          'POP印刷エラー $status',
        );
  final String status;
}

final class NotFreshProductException extends AppException {
  NotFreshProductException()
      : super(
          'not-fresh-product',
          '生鮮商品ではありません',
        );
}

final class IsFreshPopException extends AppException {
  IsFreshPopException()
      : super(
          'is-fresh-pop',
          'これは生鮮商品です。生鮮POP画面で作業してください',
        );
}

final class CantPrint28Exception extends AppException {
  CantPrint28Exception()
      : super(
          '28JAN',
          '28から始まるJANコードは印刷できません',
        );
}

final class SameBundleCode extends AppException {
  SameBundleCode()
      : super(
          'sameBundleCode',
          '両方に同じ商品は設定できません',
        );
}

final class PopInvalidZeroPriceException extends AppException {
  PopInvalidZeroPriceException(this.productCode)
      : super(
          'popInvalidZeroPrice',
          '商品 $productCode は 売価が登録されていません。サポートに連絡してください',
        );
  final String productCode;
}

final class RefusedDiscountException extends AppException {
  RefusedDiscountException(this.productCode)
      : super(
          'refusedDiscount',
          '商品 $productCode は値下登録できません',
        );
  final String productCode;
}

final class MaxDiscountRateNotSetException extends AppException {
  MaxDiscountRateNotSetException(this.productCode)
      : super(
          'maxDiscountRateNotSet',
          '商品 $productCode は、最大値下率が設定されていません',
        );
  final String productCode;
}

final class MissingDiscountReasonException extends AppException {
  MissingDiscountReasonException(this.productCode)
      : super(
          'missingDiscountReason',
          '商品 $productCode は、値下理由が設定されていません',
        );
  final String productCode;
}

final class OverMaxDiscountRateException extends AppException {
  OverMaxDiscountRateException(this.maxDiscountPrice) : super('', '最大値下額 $maxDiscountPriceを超えています');
  final int maxDiscountPrice;
}

final class FreePriceException extends AppException {
  FreePriceException() : super('', '売価を0円以下に設定することはできません。');
}

final class FailConnectTecPrinter extends AppException {
  FailConnectTecPrinter()
      : super(
          'failConnectTecPrinter',
          'プリンターとの接続に失敗しました。プリンターの電源を入れ直してください。',
        );
}

final class FailDisconnectTecPrinter extends AppException {
  FailDisconnectTecPrinter()
      : super(
          'failDisconnectTecPrinter',
          'プリンターとの接続を切断できませんでした。プリンターの電源を入れ直してください。',
        );
}

final class FailPrintTecPrinter extends AppException {
  FailPrintTecPrinter()
      : super(
          'failPrintTecPrinter',
          'プリンターで印刷できませんでした。設定を確認するか、プリンターの電源を入れ直してください。',
        );
}

/// 納品数量を０で修正しようとした時にスローする。
/// ただし、納品伝票（データ登録）の導線のみで再発行の導線ではスローしない。
final class DeliveryQuantityZeroUpdateException extends AppException {
  DeliveryQuantityZeroUpdateException()
      : super(
          'delivery-quantity-zero-update-exception',
          '納品数量は０で更新できません。',
        );
}

/// 納品伝票再発行時に指定した納品日の範囲が、納品数量修正可能範囲外を超えた時にスロー。
final class DenyEditDeliveryQuantityException extends AppException {
  DenyEditDeliveryQuantityException()
      : super(
          '-deny-edit-delivery-quantity-exception',
          '納品日が過去３日以内ではないため、納品数量は編集できません。',
        );
}

/// 納品数量やその他の商品の数量が最大値を超えた場合にスローされる例外。
final class OverMaxQuantityException extends AppException {
  OverMaxQuantityException({
    required this.quantity,
    required this.maxQuantity,
  }) : super('', '''
$_deliveryQuantity: $quantity

${_overMaxValueMessage(maxQuantity)}
''');

  /// ユーザが入力した納品数量
  final String quantity;

  /// APIから取得した納品数量の基準値
  final String maxQuantity;
}

/// 商品の売価合計（商品の売価 * 納品数量 など）が最大値を超えた場合にスローされる例外。
final class OverMaxPriceSumException extends AppException {
  OverMaxPriceSumException({
    required this.priceSum,
    required this.maxPriceSum,
  }) : super('', '''
$_priceSum: $priceSum

${_overMaxValueMessage(maxPriceSum)}
''');

  /// ユーザが入力した納品数量 * APIから取得した商品の売価金額
  final String priceSum;

  /// APIから取得した売価合計金額の最大値
  final String maxPriceSum;
}

/// 商品の原価合計（商品の原価 * 納品数量 など）が最大値を超えた場合にスローされる例外。
final class OverMaxCostPriceSumException extends AppException {
  OverMaxCostPriceSumException({
    required this.costPriceSum,
    required this.maxCostPriceSum,
  }) : super('', '''
$_costPriceSum: $costPriceSum

${_overMaxValueMessage(maxCostPriceSum)}
''');

  /// ユーザが入力した納品数量 * APIから取得した商品の原価金額
  final String costPriceSum;

  /// APIから取得した原価合計金額の最大値
  final String maxCostPriceSum;
}

/// 次のケースで投げられるエラー。
/// 1. 納品数量やその他の商品の数量が最大値を超えていないが、基準値を超えている場合。
/// 2. 商品の売価合計（商品の売価 * 納品数量 など）が最大値を超えていないが、基準値を超えている場合。
/// 3. 商品の原価合計（商品の原価 * 納品数量 など）が最大値を超えていないが、基準値を超えている場合。
///
/// メッセージは複数同時に表示することがあるため改行して表示する。
final class OverDeliveryNormalQuantityException extends AppException {
  OverDeliveryNormalQuantityException({
    required this.isOverNormalQuantity,
    required this.isOverNormalPriceSum,
    required this.isOverNormalCostPriceSum,
    required this.quantity,
    required this.normalQuantity,
    required this.priceSum,
    required this.normalPriceSum,
    required this.costPriceSum,
    required this.normalCostPriceSum,
  }) : super('', '');

  /// 1. 納品数量やその他の商品の数量が最大値を超えていないが、基準値を超えている。
  final bool isOverNormalQuantity;

  /// 2. 商品の売価合計（商品の売価 * 納品数量 など）が最大値を超えていないが、基準値を超えている
  final bool isOverNormalPriceSum;

  /// 3. 商品の原価合計（商品の原価 * 納品数量 など）が最大値を超えていないが、基準値を超えている
  final bool isOverNormalCostPriceSum;

  /// ユーザが入力した納品数量
  final String quantity;

  /// APIから取得した納品数量の基準値
  final String normalQuantity;

  /// ユーザが入力した納品数量 * APIから取得した商品の売価金額
  final String priceSum;

  /// APIから取得した売価合計金額の基準値
  final String normalPriceSum;

  /// ユーザが入力した納品数量 * APIから取得した商品の原価金額
  final String costPriceSum;

  /// APIから取得した原価合計金額の基準値
  final String normalCostPriceSum;

  @override
  String get message => '''
$_deliveryQuantityを確定しますか？

${[
        if (isOverNormalQuantity)
          _overNormalQuantityMessage(
            quantity: quantity,
            normalQuantity: normalQuantity,
          ),
        if (isOverNormalPriceSum)
          _overNormalPriceSumMessage(
            normalPriceSum: normalPriceSum,
            priceSum: priceSum,
          ),
        if (isOverNormalCostPriceSum)
          _overNormalCostPriceSumMessage(
            costPriceSum: costPriceSum,
            normalCostPriceSum: normalCostPriceSum,
          ),
      ].join('\n')}
''';

  static String _overNormalQuantityMessage({
    required String quantity,
    required String normalQuantity,
  }) =>
      '''
$_deliveryQuantity: $quantity
${_overNormalValueMessage(normalQuantity)}
''';

  static String _overNormalPriceSumMessage({
    required String priceSum,
    required String normalPriceSum,
  }) =>
      '''
$_priceSum: $priceSum
${_overNormalValueMessage(normalPriceSum)}
''';

  static String _overNormalCostPriceSumMessage({
    required String costPriceSum,
    required String normalCostPriceSum,
  }) =>
      '''
$_costPriceSum: $costPriceSum
${_overNormalValueMessage(normalCostPriceSum)}
''';
}

final class FreshOrderQuantityOverException extends AppException {
  FreshOrderQuantityOverException() : super('', '発注数が基準値を超えています。');
}

final class FreshOrderQuantityNotMultiplesException extends AppException {
  FreshOrderQuantityNotMultiplesException() : super('', '発注単位と不整合です。');
}

final class OmittingLocalityException extends AppException {
  OmittingLocalityException()
      : super(
          'ommiting-locality-exception',
          '産地を省略することはできません。',
        );
}

final class RequiredFieldIsNotFilledInException extends AppException {
  RequiredFieldIsNotFilledInException()
      : super(
          'required-field-is-not-filled-in-exception',
          '''
必須項目が入力されていないため、処理を実行できません。
必須項目を入力してから再度実行してください。''',
        );
}

/// 棚卸再カウント全重複エラー
final class AllRecountDuplicatedException extends AppException {
  AllRecountDuplicatedException()
      : super(
          'all-duplicated-exception',
          '登録が二重になっています！',
        );
}

/// 互換性がないモード同士を選択したことを示すエラー
final class IncompatibleModeException extends AppException {
  IncompatibleModeException(String message) : super('incompatible-mode-exception', message);
}
