/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsAudioGen {
  const $AssetsAudioGen();

  /// File path: assets/audio/button08.mp3
  String get button08 => 'assets/audio/button08.mp3';

  /// File path: assets/audio/button67.mp3
  String get button67 => 'assets/audio/button67.mp3';

  /// File path: assets/audio/buzzer.mp3
  String get buzzer => 'assets/audio/buzzer.mp3';

  /// File path: assets/audio/fail_search.mp3
  String get failSearch => 'assets/audio/fail_search.mp3';

  /// File path: assets/audio/scan.mp3
  String get scan => 'assets/audio/scan.mp3';

  /// File path: assets/audio/se_maoudamashii_onepoint33.mp3
  String get seMaoudamashiiOnepoint33 => 'assets/audio/se_maoudamashii_onepoint33.mp3';

  /// File path: assets/audio/seikai01.mp3
  String get seikai01 => 'assets/audio/seikai01.mp3';

  /// File path: assets/audio/taiko02.mp3
  String get taiko02 => 'assets/audio/taiko02.mp3';

  /// File path: assets/audio/tejat.ogg
  String get tejat => 'assets/audio/tejat.ogg';

  /// List of all assets
  List<String> get values =>
      [button08, button67, buzzer, failSearch, scan, seMaoudamashiiOnepoint33, seikai01, taiko02, tejat];
}

class $AssetsCommonIconGen {
  const $AssetsCommonIconGen();

  /// File path: assets/common_icon/icon_barcode_scanner.png
  AssetGenImage get iconBarcodeScanner => const AssetGenImage('assets/common_icon/icon_barcode_scanner.png');

  /// File path: assets/common_icon/icon_check_circle.png
  AssetGenImage get iconCheckCircle => const AssetGenImage('assets/common_icon/icon_check_circle.png');

  /// File path: assets/common_icon/icon_photo_camera.png
  AssetGenImage get iconPhotoCamera => const AssetGenImage('assets/common_icon/icon_photo_camera.png');

  /// File path: assets/common_icon/icon_status_caution.png
  AssetGenImage get iconStatusCaution => const AssetGenImage('assets/common_icon/icon_status_caution.png');

  /// File path: assets/common_icon/icon_status_price.png
  AssetGenImage get iconStatusPrice => const AssetGenImage('assets/common_icon/icon_status_price.png');

  /// File path: assets/common_icon/icon_status_warning.png
  AssetGenImage get iconStatusWarning => const AssetGenImage('assets/common_icon/icon_status_warning.png');

  /// File path: assets/common_icon/upload.png
  AssetGenImage get upload => const AssetGenImage('assets/common_icon/upload.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        iconBarcodeScanner,
        iconCheckCircle,
        iconPhotoCamera,
        iconStatusCaution,
        iconStatusPrice,
        iconStatusWarning,
        upload
      ];
}

class $AssetsDiscountPrintConfigGen {
  const $AssetsDiscountPrintConfigGen();

  /// File path: assets/discount_print_config/DiscMode1.lfm
  String get discMode1 => 'assets/discount_print_config/DiscMode1.lfm';

  /// File path: assets/discount_print_config/DiscMode1POP.lfm
  String get discMode1POP => 'assets/discount_print_config/DiscMode1POP.lfm';

  /// File path: assets/discount_print_config/DiscMode1off.lfm
  String get discMode1off => 'assets/discount_print_config/DiscMode1off.lfm';

  /// File path: assets/discount_print_config/DiscMode2.lfm
  String get discMode2 => 'assets/discount_print_config/DiscMode2.lfm';

  /// File path: assets/discount_print_config/DiscMode2POP.lfm
  String get discMode2POP => 'assets/discount_print_config/DiscMode2POP.lfm';

  /// File path: assets/discount_print_config/DiscMode3.lfm
  String get discMode3 => 'assets/discount_print_config/DiscMode3.lfm';

  /// File path: assets/discount_print_config/DiscMode3POP.lfm
  String get discMode3POP => 'assets/discount_print_config/DiscMode3POP.lfm';

  /// File path: assets/discount_print_config/DiscMode3off.lfm
  String get discMode3off => 'assets/discount_print_config/DiscMode3off.lfm';

  /// File path: assets/discount_print_config/DiscMode3offhalf.lfm
  String get discMode3offhalf => 'assets/discount_print_config/DiscMode3offhalf.lfm';

  /// File path: assets/discount_print_config/DiscMode3x5.lfm
  String get discMode3x5 => 'assets/discount_print_config/DiscMode3x5.lfm';

  /// File path: assets/discount_print_config/DiscMode3x5POP.lfm
  String get discMode3x5POP => 'assets/discount_print_config/DiscMode3x5POP.lfm';

  /// File path: assets/discount_print_config/ErrMsg0.ini
  String get errMsg0 => 'assets/discount_print_config/ErrMsg0.ini';

  /// File path: assets/discount_print_config/ErrMsg1.ini
  String get errMsg1 => 'assets/discount_print_config/ErrMsg1.ini';

  /// File path: assets/discount_print_config/FreshDiscMode1.lfm
  String get freshDiscMode1 => 'assets/discount_print_config/FreshDiscMode1.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode1POP.lfm
  String get freshDiscMode1POP => 'assets/discount_print_config/FreshDiscMode1POP.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode1off.lfm
  String get freshDiscMode1off => 'assets/discount_print_config/FreshDiscMode1off.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode2.lfm
  String get freshDiscMode2 => 'assets/discount_print_config/FreshDiscMode2.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode2POP.lfm
  String get freshDiscMode2POP => 'assets/discount_print_config/FreshDiscMode2POP.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode3.lfm
  String get freshDiscMode3 => 'assets/discount_print_config/FreshDiscMode3.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode3POP.lfm
  String get freshDiscMode3POP => 'assets/discount_print_config/FreshDiscMode3POP.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode3off.lfm
  String get freshDiscMode3off => 'assets/discount_print_config/FreshDiscMode3off.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode3offhalf.lfm
  String get freshDiscMode3offhalf => 'assets/discount_print_config/FreshDiscMode3offhalf.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode3x5.lfm
  String get freshDiscMode3x5 => 'assets/discount_print_config/FreshDiscMode3x5.lfm';

  /// File path: assets/discount_print_config/FreshDiscMode3x5POP.lfm
  String get freshDiscMode3x5POP => 'assets/discount_print_config/FreshDiscMode3x5POP.lfm';

  /// File path: assets/discount_print_config/PRTEP2G.ini
  String get prtep2g => 'assets/discount_print_config/PRTEP2G.ini';

  /// File path: assets/discount_print_config/PRTEP2GQM.ini
  String get prtep2gqm => 'assets/discount_print_config/PRTEP2GQM.ini';

  /// File path: assets/discount_print_config/PRTEP4GQM.ini
  String get prtep4gqm => 'assets/discount_print_config/PRTEP4GQM.ini';

  /// File path: assets/discount_print_config/PRTEP4T.ini
  String get prtep4t => 'assets/discount_print_config/PRTEP4T.ini';

  /// File path: assets/discount_print_config/PrtList.ini
  String get prtList => 'assets/discount_print_config/PrtList.ini';

  /// File path: assets/discount_print_config/resource.xml
  String get resource => 'assets/discount_print_config/resource.xml';

  /// List of all assets
  List<String> get values => [
        discMode1,
        discMode1POP,
        discMode1off,
        discMode2,
        discMode2POP,
        discMode3,
        discMode3POP,
        discMode3off,
        discMode3offhalf,
        discMode3x5,
        discMode3x5POP,
        errMsg0,
        errMsg1,
        freshDiscMode1,
        freshDiscMode1POP,
        freshDiscMode1off,
        freshDiscMode2,
        freshDiscMode2POP,
        freshDiscMode3,
        freshDiscMode3POP,
        freshDiscMode3off,
        freshDiscMode3offhalf,
        freshDiscMode3x5,
        freshDiscMode3x5POP,
        prtep2g,
        prtep2gqm,
        prtep4gqm,
        prtep4t,
        prtList,
        resource
      ];
}

class $AssetsFeatureGraphicGen {
  const $AssetsFeatureGraphicGen();

  /// File path: assets/feature_graphic/feature_graphic.png
  AssetGenImage get featureGraphic => const AssetGenImage('assets/feature_graphic/feature_graphic.png');

  /// List of all assets
  List<AssetGenImage> get values => [featureGraphic];
}

class $AssetsFeatureIconGen {
  const $AssetsFeatureIconGen();

  /// File path: assets/feature_icon/COMM-Live.png
  AssetGenImage get cOMMLive => const AssetGenImage('assets/feature_icon/COMM-Live.png');

  /// File path: assets/feature_icon/adjust_inventory.png
  AssetGenImage get adjustInventory => const AssetGenImage('assets/feature_icon/adjust_inventory.png');

  /// File path: assets/feature_icon/audit.png
  AssetGenImage get audit => const AssetGenImage('assets/feature_icon/audit.png');

  /// File path: assets/feature_icon/calculator.png
  AssetGenImage get calculator => const AssetGenImage('assets/feature_icon/calculator.png');

  /// File path: assets/feature_icon/checkLocation.png
  AssetGenImage get checkLocation => const AssetGenImage('assets/feature_icon/checkLocation.png');

  /// File path: assets/feature_icon/check_5s.png
  AssetGenImage get check5s => const AssetGenImage('assets/feature_icon/check_5s.png');

  /// File path: assets/feature_icon/check_expiry.png
  AssetGenImage get checkExpiry => const AssetGenImage('assets/feature_icon/check_expiry.png');

  /// File path: assets/feature_icon/check_fresh_floor.png
  AssetGenImage get checkFreshFloor => const AssetGenImage('assets/feature_icon/check_fresh_floor.png');

  /// File path: assets/feature_icon/check_rollout.png
  AssetGenImage get checkRollout => const AssetGenImage('assets/feature_icon/check_rollout.png');

  /// File path: assets/feature_icon/delivery_slip.png
  AssetGenImage get deliverySlip => const AssetGenImage('assets/feature_icon/delivery_slip.png');

  /// File path: assets/feature_icon/device_management.png
  AssetGenImage get deviceManagement => const AssetGenImage('assets/feature_icon/device_management.png');

  /// File path: assets/feature_icon/discount_label.png
  AssetGenImage get discountLabel => const AssetGenImage('assets/feature_icon/discount_label.png');

  /// File path: assets/feature_icon/emergency_price_alter.png
  AssetGenImage get emergencyPriceAlter => const AssetGenImage('assets/feature_icon/emergency_price_alter.png');

  /// File path: assets/feature_icon/expiry.png
  AssetGenImage get expiry => const AssetGenImage('assets/feature_icon/expiry.png');

  /// File path: assets/feature_icon/face_photo.png
  AssetGenImage get facePhoto => const AssetGenImage('assets/feature_icon/face_photo.png');

  /// File path: assets/feature_icon/fixed_assets.png
  AssetGenImage get fixedAssets => const AssetGenImage('assets/feature_icon/fixed_assets.png');

  /// File path: assets/feature_icon/fresh_order.png
  AssetGenImage get freshOrder => const AssetGenImage('assets/feature_icon/fresh_order.png');

  /// File path: assets/feature_icon/fresh_pairing.png
  AssetGenImage get freshPairing => const AssetGenImage('assets/feature_icon/fresh_pairing.png');

  /// File path: assets/feature_icon/fresh_price_alter.png
  AssetGenImage get freshPriceAlter => const AssetGenImage('assets/feature_icon/fresh_price_alter.png');

  /// File path: assets/feature_icon/fresh_take_inventory.png
  AssetGenImage get freshTakeInventory => const AssetGenImage('assets/feature_icon/fresh_take_inventory.png');

  /// File path: assets/feature_icon/inspection.png
  AssetGenImage get inspection => const AssetGenImage('assets/feature_icon/inspection.png');

  /// File path: assets/feature_icon/instruction_single.png
  AssetGenImage get instructionSingle => const AssetGenImage('assets/feature_icon/instruction_single.png');

  /// File path: assets/feature_icon/inveset_stockout.png
  AssetGenImage get invesetStockout => const AssetGenImage('assets/feature_icon/inveset_stockout.png');

  /// File path: assets/feature_icon/issue_seal.png
  AssetGenImage get issueSeal => const AssetGenImage('assets/feature_icon/issue_seal.png');

  /// File path: assets/feature_icon/lost_finder.png
  AssetGenImage get lostFinder => const AssetGenImage('assets/feature_icon/lost_finder.png');

  /// File path: assets/feature_icon/maintenande.png
  AssetGenImage get maintenande => const AssetGenImage('assets/feature_icon/maintenande.png');

  /// File path: assets/feature_icon/mvp_ranking.png
  AssetGenImage get mvpRanking => const AssetGenImage('assets/feature_icon/mvp_ranking.png');

  /// File path: assets/feature_icon/non_fresh_waste.png
  AssetGenImage get nonFreshWaste => const AssetGenImage('assets/feature_icon/non_fresh_waste.png');

  /// File path: assets/feature_icon/order.png
  AssetGenImage get orderPng => const AssetGenImage('assets/feature_icon/order.png');

  /// File path: assets/feature_icon/order.webp
  AssetGenImage get orderWebp => const AssetGenImage('assets/feature_icon/order.webp');

  /// File path: assets/feature_icon/order_daily.png
  AssetGenImage get orderDaily => const AssetGenImage('assets/feature_icon/order_daily.png');

  /// File path: assets/feature_icon/pairing.png
  AssetGenImage get pairing => const AssetGenImage('assets/feature_icon/pairing.png');

  /// File path: assets/feature_icon/photo_report.png
  AssetGenImage get photoReport => const AssetGenImage('assets/feature_icon/photo_report.png');

  /// File path: assets/feature_icon/pileup.png
  AssetGenImage get pileup => const AssetGenImage('assets/feature_icon/pileup.png');

  /// File path: assets/feature_icon/pop.png
  AssetGenImage get pop => const AssetGenImage('assets/feature_icon/pop.png');

  /// File path: assets/feature_icon/pop_signage.png
  AssetGenImage get popSignage => const AssetGenImage('assets/feature_icon/pop_signage.png');

  /// File path: assets/feature_icon/pos.png
  AssetGenImage get pos => const AssetGenImage('assets/feature_icon/pos.png');

  /// File path: assets/feature_icon/price_alter.png
  AssetGenImage get priceAlter => const AssetGenImage('assets/feature_icon/price_alter.png');

  /// File path: assets/feature_icon/price_down.webp
  AssetGenImage get priceDown => const AssetGenImage('assets/feature_icon/price_down.webp');

  /// File path: assets/feature_icon/print_pop.webp
  AssetGenImage get printPop => const AssetGenImage('assets/feature_icon/print_pop.webp');

  /// File path: assets/feature_icon/print_shelf_label.webp
  AssetGenImage get printShelfLabel => const AssetGenImage('assets/feature_icon/print_shelf_label.webp');

  /// File path: assets/feature_icon/report_complete_task.png
  AssetGenImage get reportCompleteTask => const AssetGenImage('assets/feature_icon/report_complete_task.png');

  /// File path: assets/feature_icon/rescue119.png
  AssetGenImage get rescue119 => const AssetGenImage('assets/feature_icon/rescue119.png');

  /// File path: assets/feature_icon/rescue_robo.png
  AssetGenImage get rescueRobo => const AssetGenImage('assets/feature_icon/rescue_robo.png');

  /// File path: assets/feature_icon/restore_shelf.png
  AssetGenImage get restoreShelf => const AssetGenImage('assets/feature_icon/restore_shelf.png');

  /// File path: assets/feature_icon/returns.png
  AssetGenImage get returns => const AssetGenImage('assets/feature_icon/returns.png');

  /// File path: assets/feature_icon/sales_narration.png
  AssetGenImage get salesNarration => const AssetGenImage('assets/feature_icon/sales_narration.png');

  /// File path: assets/feature_icon/scan_shelf.png
  AssetGenImage get scanShelf => const AssetGenImage('assets/feature_icon/scan_shelf.png');

  /// File path: assets/feature_icon/search.png
  AssetGenImage get search => const AssetGenImage('assets/feature_icon/search.png');

  /// File path: assets/feature_icon/search_clerk.png
  AssetGenImage get searchClerk => const AssetGenImage('assets/feature_icon/search_clerk.png');

  /// File path: assets/feature_icon/search_return_dest.png
  AssetGenImage get searchReturnDest => const AssetGenImage('assets/feature_icon/search_return_dest.png');

  /// File path: assets/feature_icon/ship_decision.png
  AssetGenImage get shipDecision => const AssetGenImage('assets/feature_icon/ship_decision.png');

  /// File path: assets/feature_icon/store_manager_price_alter.png
  AssetGenImage get storeManagerPriceAlter => const AssetGenImage('assets/feature_icon/store_manager_price_alter.png');

  /// File path: assets/feature_icon/take_inventory.png
  AssetGenImage get takeInventory => const AssetGenImage('assets/feature_icon/take_inventory.png');

  /// File path: assets/feature_icon/tana.png
  AssetGenImage get tana => const AssetGenImage('assets/feature_icon/tana.png');

  /// File path: assets/feature_icon/tanaoroshi.webp
  AssetGenImage get tanaoroshi => const AssetGenImage('assets/feature_icon/tanaoroshi.webp');

  /// File path: assets/feature_icon/task_complete_report.png
  AssetGenImage get taskCompleteReport => const AssetGenImage('assets/feature_icon/task_complete_report.png');

  /// File path: assets/feature_icon/transfer_expendables.png
  AssetGenImage get transferExpendables => const AssetGenImage('assets/feature_icon/transfer_expendables.png');

  /// File path: assets/feature_icon/transfer_raw_materials.png
  AssetGenImage get transferRawMaterials => const AssetGenImage('assets/feature_icon/transfer_raw_materials.png');

  /// File path: assets/feature_icon/waste.png
  AssetGenImage get waste => const AssetGenImage('assets/feature_icon/waste.png');

  /// File path: assets/feature_icon/wifi.png
  AssetGenImage get wifi => const AssetGenImage('assets/feature_icon/wifi.png');

  /// File path: assets/feature_icon/withdrawal.png
  AssetGenImage get withdrawal => const AssetGenImage('assets/feature_icon/withdrawal.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        cOMMLive,
        adjustInventory,
        audit,
        calculator,
        checkLocation,
        check5s,
        checkExpiry,
        checkFreshFloor,
        checkRollout,
        deliverySlip,
        deviceManagement,
        discountLabel,
        emergencyPriceAlter,
        expiry,
        facePhoto,
        fixedAssets,
        freshOrder,
        freshPairing,
        freshPriceAlter,
        freshTakeInventory,
        inspection,
        instructionSingle,
        invesetStockout,
        issueSeal,
        lostFinder,
        maintenande,
        mvpRanking,
        nonFreshWaste,
        orderPng,
        orderWebp,
        orderDaily,
        pairing,
        photoReport,
        pileup,
        pop,
        popSignage,
        pos,
        priceAlter,
        priceDown,
        printPop,
        printShelfLabel,
        reportCompleteTask,
        rescue119,
        rescueRobo,
        restoreShelf,
        returns,
        salesNarration,
        scanShelf,
        search,
        searchClerk,
        searchReturnDest,
        shipDecision,
        storeManagerPriceAlter,
        takeInventory,
        tana,
        tanaoroshi,
        taskCompleteReport,
        transferExpendables,
        transferRawMaterials,
        waste,
        wifi,
        withdrawal
      ];
}

class $AssetsImageGen {
  const $AssetsImageGen();

  /// File path: assets/image/label_printer.jpg
  AssetGenImage get labelPrinter => const AssetGenImage('assets/image/label_printer.jpg');

  /// List of all assets
  List<AssetGenImage> get values => [labelPrinter];
}

class $AssetsRescueRobotGen {
  const $AssetsRescueRobotGen();

  /// File path: assets/rescue_robot/otasuke_happy.gif
  AssetGenImage get otasukeHappy => const AssetGenImage('assets/rescue_robot/otasuke_happy.gif');

  /// File path: assets/rescue_robot/otasuke_question_1.gif
  AssetGenImage get otasukeQuestion1 => const AssetGenImage('assets/rescue_robot/otasuke_question_1.gif');

  /// File path: assets/rescue_robot/otasuke_standard_1.gif
  AssetGenImage get otasukeStandard1 => const AssetGenImage('assets/rescue_robot/otasuke_standard_1.gif');

  /// List of all assets
  List<AssetGenImage> get values => [otasukeHappy, otasukeQuestion1, otasukeStandard1];
}

class $AssetsStocktakePrintConfigGen {
  const $AssetsStocktakePrintConfigGen();

  /// File path: assets/stocktake_print_config/ErrMsg0.ini
  String get errMsg0 => 'assets/stocktake_print_config/ErrMsg0.ini';

  /// File path: assets/stocktake_print_config/ErrMsg1.ini
  String get errMsg1 => 'assets/stocktake_print_config/ErrMsg1.ini';

  /// File path: assets/stocktake_print_config/InventoryLabel.lfm
  String get inventoryLabel => 'assets/stocktake_print_config/InventoryLabel.lfm';

  /// File path: assets/stocktake_print_config/PRTEP2G.ini
  String get prtep2g => 'assets/stocktake_print_config/PRTEP2G.ini';

  /// File path: assets/stocktake_print_config/PRTEP2GQM.ini
  String get prtep2gqm => 'assets/stocktake_print_config/PRTEP2GQM.ini';

  /// File path: assets/stocktake_print_config/PRTEP4GQM.ini
  String get prtep4gqm => 'assets/stocktake_print_config/PRTEP4GQM.ini';

  /// File path: assets/stocktake_print_config/PRTEP4T.ini
  String get prtep4t => 'assets/stocktake_print_config/PRTEP4T.ini';

  /// File path: assets/stocktake_print_config/PRTFP2DG.ini
  String get prtfp2dg => 'assets/stocktake_print_config/PRTFP2DG.ini';

  /// File path: assets/stocktake_print_config/PrtList.ini
  String get prtList => 'assets/stocktake_print_config/PrtList.ini';

  /// List of all assets
  List<String> get values =>
      [errMsg0, errMsg1, inventoryLabel, prtep2g, prtep2gqm, prtep4gqm, prtep4t, prtfp2dg, prtList];
}

class $AssetsWeatherIconGen {
  const $AssetsWeatherIconGen();

  /// File path: assets/weather_icon/cloudy.webp
  AssetGenImage get cloudy => const AssetGenImage('assets/weather_icon/cloudy.webp');

  /// File path: assets/weather_icon/cloudyPartlyRainy.webp
  AssetGenImage get cloudyPartlyRainy => const AssetGenImage('assets/weather_icon/cloudyPartlyRainy.webp');

  /// File path: assets/weather_icon/cloudyPartlySnowy.webp
  AssetGenImage get cloudyPartlySnowy => const AssetGenImage('assets/weather_icon/cloudyPartlySnowy.webp');

  /// File path: assets/weather_icon/cloudyPartlySunny.webp
  AssetGenImage get cloudyPartlySunny => const AssetGenImage('assets/weather_icon/cloudyPartlySunny.webp');

  /// File path: assets/weather_icon/cloudyThenRainy.webp
  AssetGenImage get cloudyThenRainy => const AssetGenImage('assets/weather_icon/cloudyThenRainy.webp');

  /// File path: assets/weather_icon/cloudyThenSnowy.webp
  AssetGenImage get cloudyThenSnowy => const AssetGenImage('assets/weather_icon/cloudyThenSnowy.webp');

  /// File path: assets/weather_icon/cloudyThenSunny.webp
  AssetGenImage get cloudyThenSunny => const AssetGenImage('assets/weather_icon/cloudyThenSunny.webp');

  /// File path: assets/weather_icon/rainy.webp
  AssetGenImage get rainy => const AssetGenImage('assets/weather_icon/rainy.webp');

  /// File path: assets/weather_icon/rainyAndWindy.webp
  AssetGenImage get rainyAndWindy => const AssetGenImage('assets/weather_icon/rainyAndWindy.webp');

  /// File path: assets/weather_icon/rainyPartlyCloudy.webp
  AssetGenImage get rainyPartlyCloudy => const AssetGenImage('assets/weather_icon/rainyPartlyCloudy.webp');

  /// File path: assets/weather_icon/rainyPartlySnowy.webp
  AssetGenImage get rainyPartlySnowy => const AssetGenImage('assets/weather_icon/rainyPartlySnowy.webp');

  /// File path: assets/weather_icon/rainyPartlySunny.webp
  AssetGenImage get rainyPartlySunny => const AssetGenImage('assets/weather_icon/rainyPartlySunny.webp');

  /// File path: assets/weather_icon/rainyThenCloudy.webp
  AssetGenImage get rainyThenCloudy => const AssetGenImage('assets/weather_icon/rainyThenCloudy.webp');

  /// File path: assets/weather_icon/rainyThenSnowy.webp
  AssetGenImage get rainyThenSnowy => const AssetGenImage('assets/weather_icon/rainyThenSnowy.webp');

  /// File path: assets/weather_icon/rainyThenSunny.webp
  AssetGenImage get rainyThenSunny => const AssetGenImage('assets/weather_icon/rainyThenSunny.webp');

  /// File path: assets/weather_icon/snowStorm.webp
  AssetGenImage get snowStorm => const AssetGenImage('assets/weather_icon/snowStorm.webp');

  /// File path: assets/weather_icon/snowy.webp
  AssetGenImage get snowy => const AssetGenImage('assets/weather_icon/snowy.webp');

  /// File path: assets/weather_icon/snowyPartlyCloudy.webp
  AssetGenImage get snowyPartlyCloudy => const AssetGenImage('assets/weather_icon/snowyPartlyCloudy.webp');

  /// File path: assets/weather_icon/snowyPartlyRainy.webp
  AssetGenImage get snowyPartlyRainy => const AssetGenImage('assets/weather_icon/snowyPartlyRainy.webp');

  /// File path: assets/weather_icon/snowyPartlySunny.webp
  AssetGenImage get snowyPartlySunny => const AssetGenImage('assets/weather_icon/snowyPartlySunny.webp');

  /// File path: assets/weather_icon/snowyThenCloudy.webp
  AssetGenImage get snowyThenCloudy => const AssetGenImage('assets/weather_icon/snowyThenCloudy.webp');

  /// File path: assets/weather_icon/snowyThenRainy.webp
  AssetGenImage get snowyThenRainy => const AssetGenImage('assets/weather_icon/snowyThenRainy.webp');

  /// File path: assets/weather_icon/snowyThenSunny.webp
  AssetGenImage get snowyThenSunny => const AssetGenImage('assets/weather_icon/snowyThenSunny.webp');

  /// File path: assets/weather_icon/storm.webp
  AssetGenImage get storm => const AssetGenImage('assets/weather_icon/storm.webp');

  /// File path: assets/weather_icon/sunny.webp
  AssetGenImage get sunny => const AssetGenImage('assets/weather_icon/sunny.webp');

  /// File path: assets/weather_icon/sunnyPartlyCloudy.webp
  AssetGenImage get sunnyPartlyCloudy => const AssetGenImage('assets/weather_icon/sunnyPartlyCloudy.webp');

  /// File path: assets/weather_icon/sunnyPartlyRainy.webp
  AssetGenImage get sunnyPartlyRainy => const AssetGenImage('assets/weather_icon/sunnyPartlyRainy.webp');

  /// File path: assets/weather_icon/sunnyPartlySnowy.webp
  AssetGenImage get sunnyPartlySnowy => const AssetGenImage('assets/weather_icon/sunnyPartlySnowy.webp');

  /// File path: assets/weather_icon/sunnyThenCloudy.webp
  AssetGenImage get sunnyThenCloudy => const AssetGenImage('assets/weather_icon/sunnyThenCloudy.webp');

  /// File path: assets/weather_icon/sunnyThenRainy.webp
  AssetGenImage get sunnyThenRainy => const AssetGenImage('assets/weather_icon/sunnyThenRainy.webp');

  /// File path: assets/weather_icon/sunnyThenSnowy.webp
  AssetGenImage get sunnyThenSnowy => const AssetGenImage('assets/weather_icon/sunnyThenSnowy.webp');

  /// List of all assets
  List<AssetGenImage> get values => [
        cloudy,
        cloudyPartlyRainy,
        cloudyPartlySnowy,
        cloudyPartlySunny,
        cloudyThenRainy,
        cloudyThenSnowy,
        cloudyThenSunny,
        rainy,
        rainyAndWindy,
        rainyPartlyCloudy,
        rainyPartlySnowy,
        rainyPartlySunny,
        rainyThenCloudy,
        rainyThenSnowy,
        rainyThenSunny,
        snowStorm,
        snowy,
        snowyPartlyCloudy,
        snowyPartlyRainy,
        snowyPartlySunny,
        snowyThenCloudy,
        snowyThenRainy,
        snowyThenSunny,
        storm,
        sunny,
        sunnyPartlyCloudy,
        sunnyPartlyRainy,
        sunnyPartlySnowy,
        sunnyThenCloudy,
        sunnyThenRainy,
        sunnyThenSnowy
      ];
}

class Assets {
  Assets._();

  static const $AssetsAudioGen audio = $AssetsAudioGen();
  static const $AssetsCommonIconGen commonIcon = $AssetsCommonIconGen();
  static const $AssetsDiscountPrintConfigGen discountPrintConfig = $AssetsDiscountPrintConfigGen();
  static const $AssetsFeatureGraphicGen featureGraphic = $AssetsFeatureGraphicGen();
  static const $AssetsFeatureIconGen featureIcon = $AssetsFeatureIconGen();
  static const $AssetsImageGen image = $AssetsImageGen();
  static const $AssetsRescueRobotGen rescueRobot = $AssetsRescueRobotGen();
  static const $AssetsStocktakePrintConfigGen stocktakePrintConfig = $AssetsStocktakePrintConfigGen();
  static const $AssetsWeatherIconGen weatherIcon = $AssetsWeatherIconGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
