plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

// Load the key.properties file into the keystoreProperties object.
// https://docs.flutter.dev/deployment/android#reference-the-keystore-from-the-app
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
   keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    compileSdk = 35
    buildToolsVersion = '35.0.0'
    namespace = 'jp.co.trialnet.pacer4'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "jp.co.trialnet.pacer4"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode flutter.versionCode
        versionName flutter.versionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    ndkVersion = "27.0.12077973"
    buildTypes {
        release {
            // コードに関連したアプリの最適化を可能にする。
            minifyEnabled false
            // リソースの縮小を有効にする。
            shrinkResources false
            // 自動生成された最適化ルールを含むデフォルトファイル。
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            ndk {
                debugSymbolLevel 'full'
            }
        }
    }

    flavorDimensions "default"
    productFlavors {
        // app/src/{flavor}/google-services.json を勝手に読み込んでくれる
        // https://developers.google.com/android/guides/google-services-plugin
        nonmdmStg {
            dimension "default"
            applicationIdSuffix ".stg.nonmdm"
            resValue "string", "app_name", "PACER.stg.nonmdm"            
        }
        nonmdmProd {
            dimension "default"
            applicationIdSuffix ".nonmdm"
            resValue "string", "app_name", "PACER.nonmdm"            
        }
        dev {
            dimension "default"
            applicationIdSuffix ".dev"
            resValue "string", "app_name", "PACER.dev"
        }
        stg {
            dimension "default"
            applicationIdSuffix ".stg"
            resValue "string", "app_name", "PACER.stg"
        }
        prod {
            dimension "default"
            resValue "string", "app_name", "PACER"
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20"
    implementation platform('com.google.firebase:firebase-bom:29.1.0')
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
}
