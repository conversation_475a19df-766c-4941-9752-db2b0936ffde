buildscript {
    gradle.projectsEvaluated {
       tasks.withType(JavaCompile){
       options.compilerArgs << "-Xlint:deprecation"
     }
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url "${project(':itg_plugin').projectDir}/build"
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
