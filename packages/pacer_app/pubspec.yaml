name: pacer4
description: new app for real store
publish_to: "none"

version: 0.28.16+232

environment:
  sdk: ">=3.5.3 <4.0.0"
  flutter: ">=3.24.3 <4.0.0"

dependencies:
  archive: ^3.6.1
  async: ^2.11.0
  bcp_plugin:
    path: ../bcp_plugin
  camera: ^0.11.0
  checkdigit: ^0.3.1
  clock: ^1.1.1
  cloud_firestore: ^5.4.4
  collection: ^1.18.0
  connectivity_plus: ^6.0.5
  crypto: ^3.0.5
  device_info_plus: ^10.1.2
  dio: ^5.7.0

  drift: ^2.23.1
  equatable: ^2.0.5
  fake_async: ^1.3.1
  faker: ^2.1.0
  firebase_analytics: ^11.3.3
  firebase_core: ^3.6.0
  firebase_crashlytics: ^4.1.3
  fixnum: ^1.1.0
  fl_chart: ^0.68.0
  flutter:
    sdk: flutter
  flutter_adaptive_scaffold: ^0.2.1
  flutter_gen_runner: ^5.8.0
  flutter_hooks: ^0.20.5
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  fpdart: ^1.1.0
  gap: ^3.0.1
  geolocator: ^13.0.1
  get_phone_number: 3.0.10
  go_router: ^14.2.9
  go_router_builder: ^2.7.1
  google_fonts: ^6.2.1
  grpc: 4.0.1
  hooks_riverpod: ^3.0.0-dev.opp
  image: ^4.2.0
  in_app_update: ^4.2.3
  intl: ^0.19.0
  itg_plugin:
    path: ../itg_plugin
  logger: ^2.4.0
  media_kit: ^1.1.10
  media_kit_libs_audio: ^1.0.4
  mobile_scanner: 6.0.10
  network_info_plus: ^6.0.1
  package_info_plus: ^8.0.2
  path: ^1.9.0
  path_provider: ^2.1.5
  percent_indicator: ^4.2.3
  permission_handler: ^11.3.1
  # TODO: replace to flutter_jni & jnigen(https://github.com/dart-lang/native/milestone/6)
  # Reason: forkはメンテナンスが難しいため

  # Reason: [元々使ってたコード](https://github.com/utamori/register_broadcast_receiver.git)のgradleバージョンをあげる必要があったため、pacerプロジェクト内でmelosの子パッケージとして管理しgradleのバージョンを上げた。
  register_broadcast_receiver: 
    path: ../register_broadcast_receiver
  riverpod_annotation: ^3.0.0-dev.3
  rxdart: ^0.27.7
  scandit_flutter_datacapture_barcode: 7.3.0
  scandit_flutter_datacapture_core: 7.3.0
  scrollable_positioned_list: ^0.3.8
  shared_preferences: ^2.3.2
  shinise_core_client:
    path: ../shinise_core_client
  sqflite: ^2.3.3
  sqlite3: ^2.4.6
  sqlite3_flutter_libs: ^0.5.24
  talker_flutter: ^4.4.1
  trial_item_code:
    path: ../trial_item_code
  two_dimensional_scrollables: ^0.3.1
  universal_ble: ^0.13.0
  url_launcher: ^6.3.0
  uuid: ^4.5.1
  vibration: ^2.0.0
  webview_flutter: ^4.8.0

dev_dependencies:
  build_runner: ^2.4.13
  checks: ^0.3.0
  custom_lint: ^0.5.11
  dart_code_linter: ^1.1.3
  drift_dev: ^2.20.1
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.4
  riverpod_generator: ^3.0.0-dev.11
  riverpod_lint: ^3.0.0-dev.4
  test:
  very_good_analysis: ^6.0.0

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/audio/
    - assets/image/
    - assets/weather_icon/
    - assets/feature_icon/
    - assets/discount_print_config/
    - assets/common_icon/
    - assets/feature_graphic/
    - assets/rescue_robot/
    - assets/stocktake_print_config/

flutter_icons:
  android: true
  ios: true
