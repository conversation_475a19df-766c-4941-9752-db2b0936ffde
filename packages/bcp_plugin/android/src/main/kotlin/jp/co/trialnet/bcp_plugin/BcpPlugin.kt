package jp.co.trialnet.bcp_plugin

import android.annotation.SuppressLint
import android.content.Context
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import jp.co.toshibatec.bcp.library.BCPControl
import jp.co.toshibatec.bcp.library.LongRef
import jp.co.toshibatec.bcp.library.StringRef

class BcpPlugin : FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context

    override fun onAttachedToEngine(flutterPluginBiding: FlutterPlugin.FlutterPluginBinding) {
        context = flutterPluginBiding.applicationContext
        channel = MethodChannel(flutterPluginBiding.binaryMessenger, "bcp_plugin")
        channel.setMethodCallHandler(this)
    }

    override fun onMethodCall(call: <PERSON><PERSON>all, result: MethodChannel.Result) {
        println(call.method)
        when (call.method) {
            "setSystemPath" -> {
                val path = call.arguments as String
                BCPControlSingleton.instance.systemPath = path

                result.success(BCPControlSingleton.instance.systemPath)
            }

            "setPortSetting" -> {
                val port = call.arguments as String

                BCPControlSingleton.instance.portSetting = port

                result.success(BCPControlSingleton.instance.portSetting)
            }

            "openPort" -> {
                try {
                    val mode = call.argument<Int>("mode") ?: 1
                    val timeout = call.argument<Int>("timeout") ?: 20
                    val outcome = BCPControlSingleton.instance.OpenPort(mode, timeout, LongRef(0))

                    result.success(outcome)
                } catch (e: Exception) {
                    result.error("Error", e.message, e)
                }

            }

            "loadLfmFile" -> {
                val path = call.arguments as String
                val outcome = BCPControlSingleton.instance.LoadLfmFile(path, LongRef(0))

                result.success(outcome)
            }

            "feed" -> {

                val outcome = BCPControlSingleton.instance.Feed(1, StringRef(""), LongRef(0))

                result.success(outcome)
            }

            "issue" -> {
                val count = call.argument<Int>("IssueCnt") ?: 1
                val interval = call.argument<Int>("CutInterval") ?: 15

                val outcome =
                    BCPControlSingleton.instance.Issue(count, interval, StringRef(""), LongRef(0))

                result.success(outcome)
            }

            "setObjectDataEx" -> {
                val name = call.argument<String>("ObjectName")
                val data = call.argument<String>("ObjectData")

                val outcome = BCPControlSingleton.instance.SetObjectDataEx(name, data, LongRef(0))

                result.success(outcome)
            }

            "closePort" -> {
                val outcome = BCPControlSingleton.instance.ClosePort(LongRef(0))

                result.success(outcome)
            }

            else -> result.notImplemented()
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }

    object BCPControlSingleton : BCPControl.LIBBcpControlCallBack {

        @SuppressLint("StaticFieldLeak")
        val instance = BCPControl(this)

        override fun BcpControl_OnStatus(PrinterStatus: String?, Result: Long) {}
    }
}
