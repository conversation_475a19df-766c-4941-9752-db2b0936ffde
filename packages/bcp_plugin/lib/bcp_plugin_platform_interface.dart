import 'dart:io';

import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'bcp_plugin_method_channel.dart';

abstract class BcpPluginPlatform extends PlatformInterface {
  /// Constructs a BcpPluginPlatform.
  BcpPluginPlatform() : super(token: _token);

  static final Object _token = Object();

  static BcpPluginPlatform _instance = MethodChannelBcpPlugin();

  /// The default instance of [BcpPluginPlatform] to use.
  ///
  /// Defaults to [MethodChannelBcpPlugin].
  static BcpPluginPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [BcpPluginPlatform] when
  /// they register themselves.
  static set instance(BcpPluginPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<void> setSystemPath(Directory directory) {
    throw UnimplementedError('setSystemPath() has not been implemented.');
  }

  Future<void> setPortSetting(String port) {
    throw UnimplementedError('setPortSetting() has not been implemented.');
  }

  Future<bool?> openPort(int issueMode, int timeout) {
    throw UnimplementedError('openPort() has not been implemented.');
  }

  Future<bool?> loadLfmFile(File lfmFile) {
    throw UnimplementedError('loadLfmFile() has not been implemented.');
  }

  Future<bool?> setObjectDataEx(String fieldName, String fieldValue) {
    throw UnimplementedError('setObjectDataEx() has not been implemented.');
  }

  Future<bool?> issue(int issueCount, {int cutInterval = 15}) {
    throw UnimplementedError('issue() has not been implemented.');
  }

  Future<bool?> closePort() {
    throw UnimplementedError('closePort() has not been implemented.');
  }

  Future<bool?> feed() {
    throw UnimplementedError('feed() has not been implemented.');
  }
}
