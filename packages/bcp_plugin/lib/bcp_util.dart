class BcpUtil {
  /// '0016A444E23D'が与えられたら、'Bluetooth:00:16:A4:44:E2:3D'を返す

  static String createConnectionAddress(String address) => 'Bluetooth:${createMacAddress(address)}';

  /// 0016A444E23D -> 00:16:A4:44:E2:3D
  static String createMacAddress(String address) {
    final addressList = address.split('');
    final formattedAddressList = <String>[];
    for (var i = 0; i < addressList.length; i++) {
      if (i != 0 && i.isEven) formattedAddressList.add(':');

      formattedAddressList.add(addressList[i]);
    }
    return formattedAddressList.join().toUpperCase();
  }

  /// 0016A444E23D
  /// 12桁かつ、英数字のみで構成されているかどうか
  static bool isValidMacAddress(String macAddress) {
    final RegExp macExp = RegExp(r'^[0-9A-Fa-f]{12}$');
    return macExp.hasMatch(macAddress);
  }
}
