import 'dart:io';

import 'bcp_plugin_platform_interface.dart';

/// TEC ラベルプリンタープラグイン
class BcpPlugin {
  /// プリンタ情報ファイルが格納されているパスを設定
  Future<void> setSystemPath(Directory directory) => BcpPluginPlatform.instance.setSystemPath(directory);

  /// Bluetoothアドレスを設定  e.g.'Bluetooth:00:15:B5:92:74'
  Future<void> setPortSetting(String port) => BcpPluginPlatform.instance.setPortSetting(port);

  /// 通信ポートを開く。 3~5秒程度必要。
  /// issueModeは 1 送信完了復帰　2 発行完了復帰
  /// [setSystemPath],[setPortSetting]を先に呼び出してから使用すること
  Future<bool?> openPort(int issueMode, int timeout) => BcpPluginPlatform.instance.openPort(issueMode, timeout);

  /// ラベルフォーマットファイルを読みこむ
  /// [openPort]の後に実行すること
  Future<bool?> loadLfmFile(File lfmFile) => BcpPluginPlatform.instance.loadLfmFile(lfmFile);

  /// ラベルフォーマットに対しデータを設定
  Future<bool?> setObjectDataEx(String fieldName, String fieldValue) =>
      BcpPluginPlatform.instance.setObjectDataEx(fieldName, fieldValue);

  Future<bool?> issue(int issueCount, {int cutInterval = 15}) =>
      BcpPluginPlatform.instance.issue(issueCount, cutInterval: cutInterval);

  Future<bool?> closePort() => BcpPluginPlatform.instance.closePort();

  Future<bool?> feed() => BcpPluginPlatform.instance.feed();
}
