import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'bcp_plugin_platform_interface.dart';

/// An implementation of [BcpPluginPlatform] that uses method channels.
class MethodChannelBcpPlugin extends BcpPluginPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final channel = const MethodChannel('bcp_plugin');

  @override
  Future<void> setSystemPath(Directory directory) async {
    try {
      await channel.invokeMethod<String>('setSystemPath', directory.path);
    } on PlatformException catch (e) {
      log(e.toString());
    }
  }

  @override
  Future<void> setPortSetting(String port) async {
    try {
      await channel.invokeMethod<String>('setPortSetting', port);
    } on PlatformException catch (e) {
      log(e.toString());
    }
  }

  @override
  Future<bool?> openPort(int issueMode, int timeout) async {
    try {
      final result = await channel.invokeMethod<bool>('openPort', {"mode": issueMode, "timeout": timeout});
      debugPrint('openPort result: $result');
      return result;
    } on PlatformException catch (e) {
      log(e.toString());
      return false;
    }
  }

  @override
  Future<bool?> loadLfmFile(File lfmFile) async {
    try {
      return channel.invokeMethod<bool>('loadLfmFile', lfmFile.path);
    } on PlatformException catch (e) {
      log(e.toString());
      return false;
    }
  }

  @override
  Future<bool?> setObjectDataEx(String fieldName, String fieldValue) async {
    try {
      return channel.invokeMethod<bool>('setObjectDataEx', {
        'ObjectName': fieldName,
        'ObjectData': fieldValue,
      });
    } on PlatformException catch (e) {
      log('MethodChannelBcpPlugin setObjectDataEx 失敗 $fieldName $fieldValue $e}');
      return false;
    }
  }

  @override
  Future<bool?> issue(int issueCount, {int cutInterval = 15}) async {
    try {
      return channel.invokeMethod<bool>('issue', {
        'IssueCnt': issueCount,
        'CutInterval': cutInterval,
      });
    } on PlatformException catch (e) {
      log(e.toString());

      return false;
    }
  }

  @override
  Future<bool?> closePort() async {
    try {
      return channel.invokeMethod<bool>('closePort');
    } on PlatformException catch (e) {
      log(e.toString());
      return false;
    }
  }

  @override
  Future<bool?> feed() async {
    try {
      return channel.invokeMethod<bool>('feed');
    } on PlatformException catch (e) {
      log(e.toString());

      return false;
    }
  }
}
