import 'dart:convert';
import 'dart:io';

// import 'package:dart_firebase_admin/dart_firebase_admin.dart';
// import 'package:dart_firebase_admin/firestore.dart';
import 'package:json/json.dart';

@JsonCodable()
class Device {
  final String ip;
  final String name;

  static List<Device> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => Device.fromJson(json)).toList();
  }

  @override
  String toString() {
    return 'Device{ip: $ip, name: $name}';
  }
}

Future<void> main() async {
  try {
    final file = File('device.json');
    final json = file.readAsStringSync();
    final List<dynamic> devices = jsonDecode(json);
    final typedDevices = Device.fromJsonList(devices);

    print(typedDevices.toString());
    // final admin = FirebaseAdminApp.initializeApp(
    //   'pacer-355706',
    //   // Log-in using the newly downloaded file.
    //   Credential.fromServiceAccount(File('serviceAccountKey.json')),
    // );
    // print("a");

    // final firestore = Firestore(admin);
    // final deviceCollection = firestore.collection('devices');
    // final target = deviceCollection.where('storeName', WhereFilter.isIn, []);
    // print("b");

    // final targetSnapshot = await target.get();
    // print(targetSnapshot.docs.length);

    // for (final doc in targetSnapshot.docs) {
    //   /// ドキュメントを削除
    //   try {
    //     await doc.ref.delete();
    //     print('deleted');
    //     // ignore: empty_catches
    //   } catch (e) {}
    // }
    // await admin.close();
  } catch (e) {
    print(e);
  }
}
