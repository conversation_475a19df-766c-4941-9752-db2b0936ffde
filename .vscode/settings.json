{
  "dart.runPubGetOnPubspecChanges": "never",
  "dart.flutterGenerateLocalizationsOnSave": "manual",
  "dart.previewSdkDaps": true,
  "files.associations": {
    // Flutter 多言語化対応ファイル
    "*.arb": "json"
  },
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "pubspec.yaml": ".flutter-plugins, .packages, .dart_tool, .flutter-plugins-dependencies, .metadata, .packages, pubspec.lock, build.yaml, analysis_options.yaml, all_lint_rules.yaml, pubspec_overrides.yaml, flutter_launcher_icons*.yaml,analysis_options_v6.yaml",
    ".gitignore": ".gitattributes, .gitmodules, .gitmessage, .mailmap, .git-blame*",
    "readme.*": "authors, backers.md, changelog*, citation*, code_of_conduct.md, codeowners, contributing.md, contributors, copying, credits, governance.md, history.md, license*, maintainers, readme*, security.md, sponsors.md",
    "*.dart": "$(capture).g.dart, $(capture).freezed.dart"
  },
  "git.branchProtection": [
    "main"
  ],
  "dart.flutterSdkPath": "~/.local/share/mise/installs/flutter/3.24.3-stable",
  "[dart]": {
    // がoffじゃないと、formatOnSaveが効かない
    "files.autoSave": "off",
    "editor.formatOnSave": true,
    "editor.rulers": [
      120
    ]
  },
  "dart.lineLength": 120,
}
